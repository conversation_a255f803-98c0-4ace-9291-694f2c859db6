package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 检测指标
 * @Author: jeecg-boot
 * @Date:   2025-03-27
 * @Version: V1.0
 */
public interface ISysMethodAnalyteService extends IService<SysMethodAnalyte> {

    /**
     * 通过主表id查询子表数据
     *
     * @param mainId 主表id
     * @return List<SysMethodAnalyte>
     */
    public List<SysMethodAnalyte> selectByMainId(String mainId);
}
