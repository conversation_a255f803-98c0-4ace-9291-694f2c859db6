package org.jeecg.modules.lims_order.controller;

import java.util.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.crm.entity.SysCustomer;
import org.jeecg.modules.crm.service.ISysCustomerService;
import org.jeecg.modules.lims_core.entity.Sample;
import org.jeecg.modules.lims_core.mapper.ReportMapper;
import org.jeecg.modules.lims_core.service.IHolidayCalendarService;
import org.jeecg.modules.lims_core.service.ISampleService;
import org.jeecg.modules.lims_order.entity.BizOrder;
import org.jeecg.modules.lims_order.entity.Quotation;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_order.service.IBizOrderService;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_order.vo.QuotationPage;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 报价单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Tag(name="报价单")
@RestController
@RequestMapping("/lims_order/quotation")
@Slf4j
public class QuotationController extends JeecgController<Quotation, IQuotationService> {
	 @Autowired
	 private IQuotationService quotationService;
     @Autowired
     private ISampleService iSampleService;
     @Autowired
     private ISysCustomerService iSysCustomerService;
     @Autowired
     private IHolidayCalendarService iHolidayCalendarService;
	 @Autowired
	 private ReportMapper reportMapper;
     @Autowired
     private IBizOrderService iBizOrderService;

	 /**
	  * 分页列表查询
	  *
	  * @param quotation
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "报价单-分页列表查询")
	 @Operation(summary = "报价单-分页列表查询")
	 @GetMapping(value = "/list")
	 public Result<IPage<Quotation>> queryPageList(QuotationPage quotation,
												   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
												   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
												   HttpServletRequest req) {
		 QueryWrapper<QuotationPage> queryWrapper = QueryGenerator.initQueryWrapper(quotation, req.getParameterMap());
		// 1. 收集 quotationId
		 Set<String> quotationIdSet = new HashSet<>();
		 if (req.getParameterMap().containsKey("sampleNo")) {
			 String sampleNo = req.getParameter("sampleNo");
			 if (sampleNo != null && !sampleNo.isEmpty()) {
				 QueryWrapper<Sample> sampleQueryWrapper = new QueryWrapper<>();
				 sampleQueryWrapper.like("sample_no", sampleNo);
				 List<Sample> samples = iSampleService.list(sampleQueryWrapper);
				 for (Sample sample : samples) {
					 if (sample.getQuotationId() != null) {
						 quotationIdSet.add(sample.getQuotationId());
					 }
				 }
			 }
		 }

		// 2. 如果有结果，则添加 in 条件
		 if (!quotationIdSet.isEmpty()) {
			 queryWrapper.in("id", quotationIdSet);
		 }

		 Page<Quotation> page = new Page<Quotation>(pageNo, pageSize);
		 IPage<Quotation> pageList = quotationService.queryPageList(page, queryWrapper);
		 return Result.OK(pageList);
	 }


	 /**
	  * 编辑
	  *
	  * @param quotation
	  * @return
	  */
	 @AutoLog(value = "报价单-编辑")
	 @Operation(summary = "报价单-编辑")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:edit')")
	 @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
	 public Result<String> edit(@RequestBody Quotation quotation) {
		 Quotation oldQ = quotationService.getById(quotation.getId());




		 quotationService.updateById(quotation);
		 iSampleService.getBaseMapper().selectList(new QueryWrapper<Sample>().eq("quotation_id", quotation.getId()))
				 .forEach(sample -> {
					 //sample.setPmLeadTime(quotation.getLeadTime());
					 sample.setCustomerId(quotation.getCustomerId());
					 sample.setCustomerContactId(quotation.getCustomerContactId());
					 iSampleService.updateById(sample);
				 });
		 iBizOrderService.list(new QueryWrapper<BizOrder>().eq("quotation_id", quotation.getId())).forEach(order -> {
			 order.setLeadTime(String.valueOf(quotation.getLeadTime()));
			 order.setCustomerId(quotation.getCustomerId());
			 order.setCustomerContactId(quotation.getCustomerContactId());
			 iBizOrderService.updateById(order);
		 });
		 return Result.OK("编辑成功!");
	 }

	 /**
	  * 通过id删除
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "报价单-通过id删除")
	 @Operation(summary = "报价单-通过id删除")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:delete')")
	 @DeleteMapping(value = "/delete")
	 public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
		 quotationService.removeById(id);
		 return Result.OK("删除成功!");
	 }

	 /**
	  * 批量删除
	  *
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "报价单-批量删除")
	 @Operation(summary = "报价单-批量删除")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:deleteBatch')")
	 @DeleteMapping(value = "/deleteBatch")
	 public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		 this.quotationService.removeByIds(Arrays.asList(ids.split(",")));
		 return Result.OK("批量删除成功!");
	 }

	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "报价单-通过id查询")
	 @Operation(summary = "报价单-通过id查询")
	 @GetMapping(value = "/queryById")
	 public Result<Quotation> queryById(@RequestParam(name = "id", required = true) String id) {
		 Quotation quotation = quotationService.getById(id);
		 if (quotation == null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(quotation);
	 }

	 /**
	  * 导出excel
	  *
	  * @param request
	  * @param quotation
	  */
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:exportXls')")
	 @RequestMapping(value = "/exportXls")
	 public ModelAndView exportXls(HttpServletRequest request, Quotation quotation) {
		 return super.exportXls(request, quotation, Quotation.class, "报价单");
	 }


	 /**
	  * 通过excel导入数据
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:importExcel')")
	 @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	 public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		 return super.importExcel(request, response, Quotation.class);
	 }

	 /**
	  * 发起审批
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-发起审批")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:add')")
	 @RequestMapping(value = "/apply", method = RequestMethod.GET)
	 public Result<String> apply(@RequestParam(name = "id", required = true) String id) throws WxErrorException {
		 quotationService.apply(id, ApplyType.QUOTATION_APPLY);
		 return Result.OK("发起审批成功!");
	 }

	 /**
	  * 解锁
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-解锁")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:add')")
	 @RequestMapping(value = "/unlock", method = RequestMethod.GET)
	 public Result<String> unlock(@RequestParam(name = "id", required = true) String id) throws WxErrorException {
		 quotationService.apply(id, ApplyType.QUOTATION_UNLOCK);
		 return Result.OK("发起解锁审批成功!");
	 }

	 /**
	  * 操作记录
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-操作记录")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:add')")
	 @RequestMapping(value = "/listDataLog", method = RequestMethod.GET)
	 public Result<List<Map>> listDataLog(@RequestParam(name = "id", required = true) String id) {
		 List<Map> dataLogList = quotationService.listDataLog(id);
		 return Result.OK(dataLogList);
	 }


	 /**
	  * 添加
	  *
	  * @param quotation
	  * @return
	  */
	 @AutoLog(value = "报价单-添加")
	 @Operation(summary = "报价单-添加")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:add')")
	 @PostMapping(value = "/add")
	 public Result<String> add(@RequestBody Quotation quotation) {
		 quotation.setQuotationNo(FillRuleUtil.executeRule(FillRuleConstant.QUOTATION, null).toString());
		 SysCustomer customer = iSysCustomerService.getById(quotation.getCustomerId());
		 quotation.setResponsiblePerson(customer.getSalerId());
		 quotationService.save(quotation);
		 quotationService.updateSampleQuotationId(quotation);
		 return Result.OK("添加成功！");
	 }


	 /**
	  * 转合同前预判断
	  *
	  * @param
	  * @param
	  * @return
	  */
	 @AutoLog(value = "报价单-转合同前预判断")
	 @RequestMapping(value = "/preCheck", method = RequestMethod.GET)
	 public Result<String> preCheck(@RequestParam(name = "id", required = true) String id) {
		 quotationService.preCheck(id);
		 return Result.OK("检查通过");
	 }

	 /**
	  * 取消报价单
	  *
	  * @param
	  * @param
	  * @return
	  */
	 @AutoLog(value = "报价单-取消报价单")
	 @RequestMapping(value = "/cancelQuotation", method = RequestMethod.GET)
	 public Result<String> cancelQuotation(@RequestParam(name = "id", required = true) String id,@RequestParam(name = "revert", required = true) Boolean revert) {
		 quotationService.cancelQuotation(id,revert);
		 return Result.OK(revert ? "恢复成功":"取消成功");
	 }

 }
