package org.jeecg.modules.lims_core.controller;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.word.WordUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.service.IInventoryService;
import org.jeecg.modules.lims_core.service.IReportService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.ISysWarehouseBoxService;
import org.jeecg.modules.lims_core.service.IWarehouseInOutService;
import org.jeecg.modules.lims_core.vo.ReportRequestVO;
import org.jeecg.modules.lims_core.vo.TaskVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 报告
 * @Author: jeecg-boot
 * @Date:   2025-03-03
 * @Version: V1.0
 */
@Tag(name="报告")
@RestController
@RequestMapping("/lims_order/report")
@Slf4j
public class ReportController extends JeecgController<Report, IReportService> {
	@Autowired
	private IReportService reportService;
	@Autowired
	private IInventoryService inventoryService;
	@Autowired
	private IWarehouseInOutService warehouseInOutService;
	@Autowired
	private ISysWarehouseBoxService sysWarehouseBox;

	/**
	 * 分页列表查询
	 *
	 * @param reportRequestVO
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "报告-分页列表查询")
	@Operation(summary="报告-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ReportRequestVO>> queryPageList(ReportRequestVO reportRequestVO,
											   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											   HttpServletRequest req) {
		Map<String,String[]> queryParams = req.getParameterMap();
		String[] column = queryParams.get("column");
		column[0] = "deadLine";
		queryParams.put("column",column);
		String[] order = queryParams.get("order");
		order[0] ="asc";
		queryParams.put("order",order);
        QueryWrapper<ReportRequestVO> queryWrapper = QueryGenerator.initQueryWrapper(reportRequestVO, queryParams);
		Page<Report> page = new Page<Report>(pageNo, pageSize);
		IPage<ReportRequestVO> pageList = reportService.queryPageList(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "测试项目结果-通过报告ID查询")
	 @Operation(summary="测试项目结果-通过报告ID查询")
	 @GetMapping(value = "/queryTestResultByMainReportId")
	 public Result<IPage<TaskVo>> queryTestResultByMainReportId(@RequestParam(name="id",required=true) String id) {
		 List<TaskVo> List = reportService.selectByMainId(id);
		 IPage <TaskVo> page = new Page<>();
		 page.setRecords(List);
		 page.setTotal(List.size());
		 return Result.OK(page);
	 }

	/**
	 *   添加
	 *
	 * @param report
	 * @return
	 */
	@AutoLog(value = "报告-添加")
	@Operation(summary="报告-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_lab:report:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Report report) {
		reportService.save(report);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param report
	 * @return
	 */
	@AutoLog(value = "报告-编辑")
	@Operation(summary="报告-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_lab:report:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Report report) {
		reportService.updateById(report);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "报告-通过id删除")
	@Operation(summary="报告-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_lab:report:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		reportService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "报告-批量删除")
	@Operation(summary="报告-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_lab:report:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.reportService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "报告-通过id查询")
	@Operation(summary="报告-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Report> queryById(@RequestParam(name="id",required=true) String id) {
		Report report = reportService.getById(id);
		if(report==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(report);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param report
    */
    @PreAuthorize("@jps.requiresPermissions('lims_lab:report:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Report report) {
        return super.exportXls(request, report, Report.class, "报告");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_lab:report:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Report.class);
    }

	 /**
	  *  修订
	  *
	  * @param report
	  * @return
	  */
	 @AutoLog(value = "报告-修订")
	 @Operation(summary="报告-修订")
	 @RequestMapping(value = "/reviseReport", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> reviseReport(@RequestBody Report report) {
		 LoginUser curUser = SecureUtil.currentUser();
		 Report reportOld = reportService.getById(report.getId());
		 Report reportNew =new Report();
		 BeanUtils.copyProperties(reportOld, reportNew);
		 String newVersion = incrementVersion(reportOld.getVersion());
		 reportNew.setVersion(newVersion);
		 reportNew.setReviseMemo(report.getReviseMemo());
		 reportNew.setSupercededId(reportOld.getId());
		 reportNew.setId(null);
		 reportNew.setCreateBy(curUser.getUsername());
		 reportNew.setCreateTime(new Date());
		 reportNew.setUpdateBy(null);
		 reportNew.setUpdateTime(null);
		 reportService.save(reportNew);
		 return Result.OK("修订成功!");
	 }
	 private String incrementVersion(String currentVersion) {
		 if(Objects.equals(currentVersion, "") || currentVersion == null){
			 return "A01";
		 }
		 if (currentVersion == null || !currentVersion.matches("^A\\d{2}$")) {
			 throw new IllegalArgumentException("Invalid version format: " + currentVersion);
		 }
		 String numericPart = currentVersion.substring(1);
		 int number = Integer.parseInt(numericPart);
		 number++;
		 if (number > 99) {
			 throw new IllegalArgumentException("Version number exceeds maximum limit (99)");
		 }
		 return "A" + String.format("%02d", number);
	 }

	 /**
	  *   报告归档
	  *
	  * @param inventory
	  * @return
	  */
	 @AutoLog(value = "库存表-报告归档")
	 @Operation(summary="库存表-报告归档")
	 @PostMapping(value = "/archiveReport")
	 public Result<String> archiveReport(@RequestBody Inventory inventory) {
		 SysWarehouseBox box = sysWarehouseBox.getById(inventory.getBoxId());
		 LoginUser sysUser = SecureUtil.currentUser();
		 LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.and(item->item.eq(Inventory::getArticleNo,inventory.getReportCode()).eq(Inventory::getArticleTypeId,"RA"));
		 Inventory inve = inventoryService.getOne(queryWrapper);
		 if (inventory.getReportCode().startsWith("YPT")|| inventory.getReportCode().startsWith("BCC")|| inventory.getReportCode().startsWith("GBP")|| inventory.getReportCode().startsWith("QCT")){
			 LambdaQueryWrapper<Report> reportWrapper = new LambdaQueryWrapper<>();
			 reportWrapper.and(item->item.eq(Report::getReportNo,inventory.getReportCode()));
			 Report report = reportService.getOne(reportWrapper);
			 if (inve == null){
				 inventory.setWarehouseId(box.getWarehouseId());
				 inventory.setBoxId(inventory.getBoxId());
				 inventory.setArticleNo(inventory.getReportCode());
				 inventory.setArticleTypeId("RA");
				 inventory.setAmount("1");
				 inventory.setUnitId("");
				 inventory.setCreateBy(sysUser.getUsername());
				 inventory.setCreateTime(DateUtil.date());
				 inventoryService.save(inventory);
			 }else{
				 return Result.error("不需要重复归档！");
			 }
			 //生成出入库记录
			 WarehouseInOut warehouseInOut = new WarehouseInOut();
			 warehouseInOut.setWarehouseId(box.getWarehouseId());//库存id
			 warehouseInOut.setArticleNo(inventory.getReportCode());//物品编号
			 warehouseInOut.setArticleTypeId("RA");//物品类型id
			 warehouseInOut.setAmount("1");
			 warehouseInOut.setUnitId("");
			 warehouseInOut.setBoxId(inventory.getBoxId());
			 warehouseInOut.setOperationTypeId("in" );//操作类型id
			 warehouseInOut.setOperationReasonId("入库");//操作原因id
			 warehouseInOut.setCreateBy(sysUser.getUsername());
			 warehouseInOut.setCreateTime(DateUtil.date());
			 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());//部门id
			 warehouseInOutService.save(warehouseInOut);
			 //修改报告状态
		 }
		 return Result.OK("归档成功！");
	 }
}
