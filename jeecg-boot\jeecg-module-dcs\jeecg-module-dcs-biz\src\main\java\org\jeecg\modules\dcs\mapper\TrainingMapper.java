package org.jeecg.modules.dcs.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.dcs.entity.Training;
import org.jeecg.modules.dcs.vo.TrainingVo;

/**
 * @Description: 培训
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
public interface TrainingMapper extends BaseMapper<Training> {
    IPage<TrainingVo> queryPageList(Page<TrainingVo> page,
                                    @Param(Constants.WRAPPER) Wrapper<Training> wrapper);
    TrainingVo getVoById(String id);
}
