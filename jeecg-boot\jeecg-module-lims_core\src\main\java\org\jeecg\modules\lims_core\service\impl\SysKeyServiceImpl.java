package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysKey;
import org.jeecg.modules.lims_core.mapper.SysKeyMapper;
import org.jeecg.modules.lims_core.service.ISysKeyService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 序号管理表
 * @Author: jeecg-boot
 * @Date:   2025-05-15
 * @Version: V1.0
 */
@Service
public class SysKeyServiceImpl extends ServiceImpl<SysKeyMapper, SysKey> implements ISysKeyService {

}
