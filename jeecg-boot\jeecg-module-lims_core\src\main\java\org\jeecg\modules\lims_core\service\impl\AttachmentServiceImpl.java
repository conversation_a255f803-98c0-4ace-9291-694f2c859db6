package org.jeecg.modules.lims_core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.Attachment;
import org.jeecg.modules.lims_core.mapper.AttachmentMapper;
import org.jeecg.modules.lims_core.service.IAttachmentService;
import org.jeecg.modules.lims_core.vo.AttachQueryVo;
import org.jeecg.modules.system.model.TreeModel;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 附件表
 * @Author: jeecg-boot
 * @Date:   2025-05-12
 * @Version: V1.0
 */
@Service
public class AttachmentServiceImpl extends ServiceImpl<AttachmentMapper, Attachment> implements IAttachmentService {

    @Override
    public void saveAttach(Attachment attachment) {
        if (attachment.getId() != null){
            this.removeById(attachment.getId());
        }
        String[] urls = attachment.getUrl().split(",");

        for (String url : urls) {
            Attachment attach = new Attachment();
            attach.setSourceTable(attachment.getSourceTable());
            attach.setSourceId(attachment.getSourceId());
            attach.setUrl(url);
            this.save(attach);
        }

    }

    @Override
    public List<TreeModel> listSourceOptions(AttachQueryVo attachQueryVo) {
        List<TreeModel> list = new ArrayList<>();
        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean(oConvertUtils.camelName(attachQueryVo.getSourceTable())+"ServiceImpl");
        if (attachQueryVo.getParentField() != null){
            impl.getBaseMapper().selectList((Wrapper) new QueryWrapper().eq(attachQueryVo.getParentField(),attachQueryVo.getParentFieldId())).forEach(
                    item -> {
                        com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(JSONObject.toJSONString(item));
                        TreeModel treeModel = new TreeModel();
                        treeModel.setValue(jsonObject.getString("id"));
                        if(attachQueryVo.getDisplayText() != null && !attachQueryVo.getDisplayText().isEmpty()) {
                            String[] split = attachQueryVo.getDisplayText().split(",");
                            StringBuilder label = new StringBuilder();
                            for (String field : split) {
                                if (jsonObject.containsKey(field)) {
                                    label.append(jsonObject.getString(field)).append("-");
                                }
                            }
                            // 去掉最后一个分隔符
                            if (label.length() > 0) {
                                label.deleteCharAt(label.length() - 1);
                            }
                            treeModel.setLabel(label.toString().trim());

                        } else {
                            treeModel.setLabel(jsonObject.getString("name"));
                        }


                        list.add(treeModel);
                    }
            );
        }else{
            impl.getBaseMapper().selectList((Wrapper) new QueryWrapper().eq("id",attachQueryVo.getSourceId())).forEach(
                    item -> {
                        com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSON.parseObject(JSONObject.toJSONString(item));
                        TreeModel treeModel = new TreeModel();
                        treeModel.setValue(jsonObject.getString("id"));
                        if(attachQueryVo.getDisplayText() != null && !attachQueryVo.getDisplayText().isEmpty()) {
                            String[] split = attachQueryVo.getDisplayText().split(",");
                            StringBuilder label = new StringBuilder();
                            for (String field : split) {
                                if (jsonObject.containsKey(field)) {
                                    label.append(jsonObject.getString(field)).append("-");
                                }
                            }
                            // 去掉最后一个分隔符
                            if (label.length() > 0) {
                                label.deleteCharAt(label.length() - 1);
                            }
                            treeModel.setLabel(label.toString().trim());

                        } else {
                            treeModel.setLabel(jsonObject.getString("name"));
                        }


                        list.add(treeModel);
                    }
            );
        }



        return list;
    }
}
