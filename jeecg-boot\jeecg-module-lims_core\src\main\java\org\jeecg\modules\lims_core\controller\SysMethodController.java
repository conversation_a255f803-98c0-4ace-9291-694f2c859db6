package org.jeecg.modules.lims_core.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecg.modules.lims_core.service.*;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysMethodConsumptive;
import org.jeecg.modules.lims_core.entity.SysMethodStdMaterial;
import org.jeecg.modules.lims_core.entity.SysMethodInstrumentType;
import org.jeecg.modules.lims_core.entity.SysMethodTestingPara;
import org.jeecg.modules.lims_core.entity.SysMethodRepeatType;
import org.jeecg.modules.lims_core.entity.SysMethodWorkflow;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.vo.SysMethodPage;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 方法
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Tag(name="方法")
@RestController
@RequestMapping("/lims_core/sysMethod")
@Slf4j
public class SysMethodController {
	@Autowired
	private ISysMethodService sysMethodService;
	@Autowired
	private ISysMethodConsumptiveService sysMethodConsumptiveService;
	@Autowired
	private ISysMethodStdMaterialService sysMethodStdMaterialService;
	@Autowired
	private ISysMethodInstrumentTypeService sysMethodInstrumentTypeService;
	@Autowired
	private ISysMethodTestingParaService sysMethodTestingParaService;
	@Autowired
	private ISysMethodRepeatTypeService sysMethodRepeatTypeService;
	@Autowired
	private ISysMethodWorkflowService sysMethodWorkflowService;
	@Autowired
	private ISysMethodAnalyteService sysMethodAnalyteService;
	@Autowired
	private ISysStandardService sysStandardService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysMethod
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "方法-分页列表查询")
	@Operation(summary="方法-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysMethod>> queryPageList(SysMethod sysMethod,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysMethod> queryWrapper = QueryGenerator.initQueryWrapper(sysMethod, req.getParameterMap());
		Page<SysMethod> page = new Page<SysMethod>(pageNo, pageSize);
		IPage<SysMethod> pageList = sysMethodService.page(page, queryWrapper);
		List<Map<String, String>> standards = sysStandardService.selectOptions();
		pageList.getRecords().forEach(
			item -> {
				standards.forEach(
					standard -> {
						if (item.getStandardId().equals(standard.get("value"))) {
							item.setStandardName(standard.get("label"));
						}
					}
				);
			}
		);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysMethodPage
	 * @return
	 */
	@AutoLog(value = "方法-添加")
	@Operation(summary="方法-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysMethodPage sysMethodPage) {
		SysMethod sysMethod = new SysMethod();
		BeanUtils.copyProperties(sysMethodPage, sysMethod);
		sysMethodService.saveMain(sysMethod, sysMethodPage.getSysMethodConsumptiveList(),sysMethodPage.getSysMethodStdMaterialList(),sysMethodPage.getSysMethodInstrumentTypeList(),sysMethodPage.getSysMethodTestingParaList(),sysMethodPage.getSysMethodRepeatTypeList(),sysMethodPage.getSysMethodWorkflowList(),sysMethodPage.getSysMethodAnalyteList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysMethodPage
	 * @return
	 */
	@AutoLog(value = "方法-编辑")
	@Operation(summary="方法-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysMethodPage sysMethodPage) {
		SysMethod sysMethod = new SysMethod();
		BeanUtils.copyProperties(sysMethodPage, sysMethod);
		SysMethod sysMethodEntity = sysMethodService.getById(sysMethod.getId());
		if(sysMethodEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysMethodService.updateMain(sysMethod, sysMethodPage.getSysMethodConsumptiveList(),sysMethodPage.getSysMethodStdMaterialList(),sysMethodPage.getSysMethodInstrumentTypeList(),sysMethodPage.getSysMethodTestingParaList(),sysMethodPage.getSysMethodRepeatTypeList(),sysMethodPage.getSysMethodWorkflowList(),sysMethodPage.getSysMethodAnalyteList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "方法-通过id删除")
	@Operation(summary="方法-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysMethodService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "方法-批量删除")
	@Operation(summary="方法-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysMethodService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	 /**
	  *   复制方法，通过原方法id,新标准id，新方法名称
	  *
	  * @param sysMethodPage
	  * @return
	  */
	 @AutoLog(value = "方法-复制方法")
	 @Operation(summary="方法-复制方法")
	 @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:add')")
	 @PostMapping(value = "/clonemethods")
	 public Result<String> clonemethods(@RequestBody SysMethodPage sysMethodPage) {
		 LoginUser sysUser = SecureUtil.currentUser();
		 SysMethod sysMethod = new SysMethod();
		 BeanUtils.copyProperties(sysMethodPage, sysMethod);
		 SysMethod sysMethodNew = sysMethodService.getById(sysMethod.getId());
		 sysMethodNew.setId(null);
		 sysMethodNew.setStandardId(sysMethod.getStandardId());
         sysMethodNew.setName(sysMethod.getName());
		 sysMethodNew.setCreateBy(sysUser.getUsername());
		 sysMethodNew.setUpdateBy(sysUser.getUsername());
		 sysMethodNew.setCreateTime(new Date());
		 sysMethodNew.setUpdateTime(new Date());
		 sysMethodService.saveCopyMain(sysMethodNew,sysMethod.getId(),sysMethodPage.getSysMethodConsumptiveList(),sysMethodPage.getSysMethodStdMaterialList(),sysMethodPage.getSysMethodInstrumentTypeList(),sysMethodPage.getSysMethodTestingParaList(),sysMethodPage.getSysMethodRepeatTypeList(),sysMethodPage.getSysMethodWorkflowList(),sysMethodPage.getSysMethodAnalyteList());
		 return Result.OK("添加成功！");
	 }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "方法-通过id查询")
	@Operation(summary="方法-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysMethod> queryById(@RequestParam(name="id",required=true) String id) {
		SysMethod sysMethod = sysMethodService.getById(id);
		if(sysMethod==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysMethod);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "试剂耗材通过主表ID查询")
	@Operation(summary="试剂耗材-通主表ID查询")
	@GetMapping(value = "/querySysMethodConsumptiveByMainId")
	public Result<List<SysMethodConsumptive>> querySysMethodConsumptiveListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodConsumptive> sysMethodConsumptiveList = sysMethodConsumptiveService.selectByMainId(id);
		return Result.OK(sysMethodConsumptiveList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "标品通过主表ID查询")
	@Operation(summary="标品-通主表ID查询")
	@GetMapping(value = "/querySysMethodStdMaterialByMainId")
	public Result<List<SysMethodStdMaterial>> querySysMethodStdMaterialListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodStdMaterial> sysMethodStdMaterialList = sysMethodStdMaterialService.selectByMainId(id);
		return Result.OK(sysMethodStdMaterialList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "设备类别通过主表ID查询")
	@Operation(summary="设备类别-通主表ID查询")
	@GetMapping(value = "/querySysMethodInstrumentTypeByMainId")
	public Result<List<SysMethodInstrumentType>> querySysMethodInstrumentTypeListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodInstrumentType> sysMethodInstrumentTypeList = sysMethodInstrumentTypeService.selectByMainId(id);
		return Result.OK(sysMethodInstrumentTypeList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "实验过程参数通过主表ID查询")
	@Operation(summary="实验过程参数-通主表ID查询")
	@GetMapping(value = "/querySysMethodTestingParaByMainId")
	public Result<List<SysMethodTestingPara>> querySysMethodTestingParaListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodTestingPara> sysMethodTestingParaList = sysMethodTestingParaService.selectByMainId(id);
		return Result.OK(sysMethodTestingParaList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "重复性通过主表ID查询")
	@Operation(summary="重复性-通主表ID查询")
	@GetMapping(value = "/querySysMethodRepeatTypeByMainId")
	public Result<List<SysMethodRepeatType>> querySysMethodRepeatTypeListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodRepeatType> sysMethodRepeatTypeList = sysMethodRepeatTypeService.selectByMainId(id);
		return Result.OK(sysMethodRepeatTypeList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检测流程通过主表ID查询")
	@Operation(summary="检测流程-通主表ID查询")
	@GetMapping(value = "/querySysMethodWorkflowByMainId")
	public Result<List<SysMethodWorkflow>> querySysMethodWorkflowListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodWorkflow> sysMethodWorkflowList = sysMethodWorkflowService.selectByMainId(id);
		return Result.OK(sysMethodWorkflowList);
	}
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检测指标通过主表ID查询")
	@Operation(summary="检测指标-通主表ID查询")
	@GetMapping(value = "/querySysMethodAnalyteByMainId")
	public Result<List<SysMethodAnalyte>> querySysMethodAnalyteListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysMethodAnalyte> sysMethodAnalyteList = null;////sysMethodAnalyteService.selectByMainId(id);
		return Result.OK(sysMethodAnalyteList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysMethod
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysMethod sysMethod) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysMethod> queryWrapper = QueryGenerator.initQueryWrapper(sysMethod, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

      //配置选中数据查询条件
       String selections = request.getParameter("selections");
       if(oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id",selectionList);
       }
       //Step.2 获取导出数据
       List<SysMethod> sysMethodList = sysMethodService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysMethodPage> pageList = new ArrayList<SysMethodPage>();
      for (SysMethod main : sysMethodList) {
          SysMethodPage vo = new SysMethodPage();
          BeanUtils.copyProperties(main, vo);
          List<SysMethodConsumptive> sysMethodConsumptiveList = sysMethodConsumptiveService.selectByMainId(main.getId());
          vo.setSysMethodConsumptiveList(sysMethodConsumptiveList);
          List<SysMethodStdMaterial> sysMethodStdMaterialList = sysMethodStdMaterialService.selectByMainId(main.getId());
          vo.setSysMethodStdMaterialList(sysMethodStdMaterialList);
          List<SysMethodInstrumentType> sysMethodInstrumentTypeList = sysMethodInstrumentTypeService.selectByMainId(main.getId());
          vo.setSysMethodInstrumentTypeList(sysMethodInstrumentTypeList);
          List<SysMethodTestingPara> sysMethodTestingParaList = sysMethodTestingParaService.selectByMainId(main.getId());
          vo.setSysMethodTestingParaList(sysMethodTestingParaList);
          List<SysMethodRepeatType> sysMethodRepeatTypeList = sysMethodRepeatTypeService.selectByMainId(main.getId());
          vo.setSysMethodRepeatTypeList(sysMethodRepeatTypeList);
          List<SysMethodWorkflow> sysMethodWorkflowList = sysMethodWorkflowService.selectByMainId(main.getId());
          vo.setSysMethodWorkflowList(sysMethodWorkflowList);
          List<SysMethodAnalyte> sysMethodAnalyteList = null;////sysMethodAnalyteService.selectByMainId(main.getId());
          vo.setSysMethodAnalyteList(sysMethodAnalyteList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "方法列表");
      mv.addObject(NormalExcelConstants.CLASS, SysMethodPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("方法数据", "导出人:"+sysUser.getRealname(), "方法"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysMethodPage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysMethodPage.class, params);
              for (SysMethodPage page : list) {
                  SysMethod po = new SysMethod();
                  BeanUtils.copyProperties(page, po);
                  sysMethodService.saveMain(po, page.getSysMethodConsumptiveList(),page.getSysMethodStdMaterialList(),page.getSysMethodInstrumentTypeList(),page.getSysMethodTestingParaList(),page.getSysMethodRepeatTypeList(),page.getSysMethodWorkflowList(),page.getSysMethodAnalyteList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
