package org.jeecg.modules.gitlab.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.gitlab.entity.GitlabSchedule;
import org.jeecg.modules.gitlab.service.**********************;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 存储gitlab issue与企微日程的关系
 * @Author: jeecg-boot
 * @Date:   2025-01-03
 * @Version: V1.0
 */
@Tag(name="存储gitlab issue与企微日程的关系")
@RestController
@RequestMapping("/gitlab/gitlabSchedule")
@Slf4j
public class GitlabScheduleController extends JeecgController<GitlabSchedule, **********************> {
	@Autowired
	private ********************** *********************;
	
	/**
	 * 分页列表查询
	 *
	 * @param gitlabSchedule
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "存储gitlab issue与企微日程的关系-分页列表查询")
	@Operation(summary="存储gitlab issue与企微日程的关系-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<GitlabSchedule>> queryPageList(GitlabSchedule gitlabSchedule,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<GitlabSchedule> queryWrapper = QueryGenerator.initQueryWrapper(gitlabSchedule, req.getParameterMap());
		Page<GitlabSchedule> page = new Page<GitlabSchedule>(pageNo, pageSize);
		IPage<GitlabSchedule> pageList = *********************.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param gitlabSchedule
	 * @return
	 */
	@AutoLog(value = "存储gitlab issue与企微日程的关系-添加")
	@Operation(summary="存储gitlab issue与企微日程的关系-添加")
	@PreAuthorize("@jps.requiresPermissions('gitlab:gitlab_schedule:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody GitlabSchedule gitlabSchedule) {
		*********************.save(gitlabSchedule);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param gitlabSchedule
	 * @return
	 */
	@AutoLog(value = "存储gitlab issue与企微日程的关系-编辑")
	@Operation(summary="存储gitlab issue与企微日程的关系-编辑")
    @PreAuthorize("@jps.requiresPermissions('gitlab:gitlab_schedule:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody GitlabSchedule gitlabSchedule) {
		*********************.updateById(gitlabSchedule);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "存储gitlab issue与企微日程的关系-通过id删除")
	@Operation(summary="存储gitlab issue与企微日程的关系-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('gitlab:gitlab_schedule:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		*********************.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "存储gitlab issue与企微日程的关系-批量删除")
	@Operation(summary="存储gitlab issue与企微日程的关系-批量删除")
    @PreAuthorize("@jps.requiresPermissions('gitlab:gitlab_schedule:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.*********************.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "存储gitlab issue与企微日程的关系-通过id查询")
	@Operation(summary="存储gitlab issue与企微日程的关系-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<GitlabSchedule> queryById(@RequestParam(name="id",required=true) String id) {
		GitlabSchedule gitlabSchedule = *********************.getById(id);
		if(gitlabSchedule==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(gitlabSchedule);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param gitlabSchedule
    */
    @PreAuthorize("@jps.requiresPermissions('gitlab:gitlab_schedule:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, GitlabSchedule gitlabSchedule) {
        return super.exportXls(request, gitlabSchedule, GitlabSchedule.class, "存储gitlab issue与企微日程的关系");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('gitlab:gitlab_schedule:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, GitlabSchedule.class);
    }

}
