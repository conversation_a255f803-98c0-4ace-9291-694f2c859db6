package org.jeecg.modules.lims_core.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpOaService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.oa.SummaryInfo;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApplyEventRequest;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplate;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplateResult;
import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateContent;
import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateControls;
import org.aspectj.weaver.IClassFileProvider;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.InventoryMapper;
import org.jeecg.modules.lims_core.mapper.SysWarehouseMapper;
import org.jeecg.modules.lims_core.mapper.WarehouseInOutMapper;
import org.jeecg.modules.lims_core.mapper.WarehouseOutApplyMapper;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.vo.WarehouseOutApplyVo;
import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.wx.entity.WecomSp;
import org.jeecg.modules.wx.service.IWecomSpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 出库申请
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Service
public class WarehouseOutApplyServiceImpl extends ServiceImpl<WarehouseOutApplyMapper, WarehouseOutApply> implements IWarehouseOutApplyService {

    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IWecomSpService wecomSpService;
    @Autowired
    private InventoryMapper inventoryMapper;
    @Autowired
    private InventoryServiceImpl inventoryService;
    @Autowired
    private ISysWarehouseService sysWarehouseService;
    @Autowired
    private ISysWarehouseBoxService sysWarehouseBoxService;
    @Autowired
    private WarehouseInOutMapper warehouseInOutMapper;
    @Autowired
    private WarehouseOutApplyMapper warehouseOutApplyMapper;
    @Autowired
    private ISysDictItemService sysDictItemService;
    @Autowired
    private ISysUnitService sysUnitService;

    @Override
    public IPage<WarehouseOutApplyVo> queryPageList(Page<WarehouseOutApplyVo> page, Wrapper<WarehouseOutApplyVo> wrapper) {
        return warehouseOutApplyMapper.queryPageList(page,wrapper);
    }

//    @Override
//    public void apply(WarehouseOutApply obj, ApplyType applyType) throws WxErrorException {
//        if ("in".equals(obj.getOperationReasonId())) {
//            applyType = ApplyType.WAREHOUSE_APPLY_IN;
//        } else if ( "test".equals(obj.getOperationReasonId())) {
//            applyType = ApplyType.WAREHOUSE_APPLY_OUT;
//        } else if ("destroy".equals(obj.getOperationReasonId())) {
//            applyType = ApplyType.WAREHOUSE_APPLY_DESTROY;
//        }
//        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
//        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
//        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
//        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
//        WxCpOaService oaService = cpService.getOaService();
//        String templateId = "C4ZXKAvfhFBwC8YFh2YJrR12TeyZoqMZdigCzQPuG";
//        WxCpOaApprovalTemplateResult templateDetail = oaService.getTemplateDetail(templateId);
//        System.out.println(templateDetail.toJson());
//        updateTemplateTest(oaService,templateDetail,templateId,applyType);
//
//        WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
//        LoginUser curUser = SecureUtil.currentUser();
//        request.setCreatorUserId(sysBaseAPI.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
//        // template_id	 模板id
//        request.setTemplateId(templateId);
//        // use_template_approver	 0-通过接口指定审批人、抄送人（此时process参数必填）; 1-使用此模板在管理后台设置的审批流程(需要保证审批流程中没有“申请人自选”节点)，支持条件审批。
//        request.setUseTemplateApprover(0);
//        // apply_data     申请数据
//        String articleTypeId_Text = "";
//        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("warehouse_goods_type");
//        for (SysDictItem item : sysDictItems) {
//            if (obj.getArticleTypeId().equals(item.getItemValue())) {
//                articleTypeId_Text = item.getItemText();
//                break;
//            }
//        }
//        String OperationReason_Text = "";
//        sysDictItems = sysDictItemService.selectItemsByDictCode("warehouse_operation_reason");
//        for (SysDictItem item : sysDictItems) {
//            if (obj.getOperationReasonId().equals(item.getItemValue())) {
//                OperationReason_Text = item.getItemText();
//                break;
//            }
//        }
//        SysUnit Unit = sysUnitService.getById(obj.getUnitId());
//        request.setApplyData(getApplyData(
//                obj.getArticleNo(),
//                articleTypeId_Text,
//                obj.getAmount(),
//                Unit.getUnitName(),
//                OperationReason_Text,
//                obj.getStatus()
//        ));
//        // summary_list    摘要信息
//        if (applyType == ApplyType.WAREHOUSE_APPLY_IN) {
//            request.setSummaryList(getSummaryList("物品编号:" + obj.getArticleNo(), "申请入库物品类型:" + articleTypeId_Text, "数量:" + obj.getAmount() + ",单位:" + Unit.getUnitName() + ",操作类型:" + OperationReason_Text));
//        }
//        if (applyType == ApplyType.WAREHOUSE_APPLY_OUT) {
//            request.setSummaryList(getSummaryList("物品编号:" + obj.getArticleNo(), "物品类型:" + articleTypeId_Text, "数量:" + obj.getAmount() + ",单位:" + Unit.getUnitName() + ",操作类型:" + OperationReason_Text));
//        }
//        if (applyType == ApplyType.WAREHOUSE_APPLY_DESTROY) {
//            request.setSummaryList(getSummaryList("物品编号:" + obj.getArticleNo(), "物品类型:" + articleTypeId_Text, "数量:" + obj.getAmount() + ",单位:" + Unit.getUnitName() + ",操作类型:" + OperationReason_Text));
//        }
//        // process	 审批流程信息
//        request.setApprovers(getApprovers());
//        try {
//            String sp_no = oaService.apply(request);
////            obj.setStatusId("1");
////            obj.setIsLocked(1);
// //           updateById(obj);
//
//            //插入审批表
//            WecomSp wecomSp = new WecomSp();
//            wecomSp.setSpNo(sp_no);
//            wecomSp.setTargetId(obj.getId());
//            wecomSp.setTargetImpl("warehouseOutApplyServiceImpl");
//            wecomSpService.save(wecomSp);
//        } catch (WxErrorException e) {
//            throw new RuntimeException(e);
//        }
//    }

@Override
public void apply(WarehouseOutApply obj, ApplyType applyType) throws WxErrorException {
    WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
    String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
    int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
    WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
    WxCpMessageService messageService = cpService.getMessageService();

    LoginUser curUser = SecureUtil.currentUser();
    WxCpMessage message = new WxCpMessage();
    message.setAgentId(agentId);
    ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);

    List<String> userIds = Arrays.asList("1863417432833499138");
    List<String> wechatUserIds = userIds.stream()
            .map(id -> sysBaseApi.getThirdUserIdByUserId(id, "wechat_enterprise"))
            .filter(id -> id != null && !id.isEmpty())
            .collect(Collectors.toList());
    String toUsers = String.join("|", wechatUserIds);
    message.setToUser(toUsers);
    //message.setToUser(sysBaseApi.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
    message.setMsgType("news");
    String articleTypeId_Text = "";
    List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("warehouse_goods_type");
    for (SysDictItem item : sysDictItems) {
        if (obj.getArticleTypeId().equals(item.getItemValue())) {
            articleTypeId_Text = item.getItemText();
            break;
        }
    }

    String operationReason_Text = "";
    sysDictItems = sysDictItemService.selectItemsByDictCode("warehouse_operation_reason");
    for (SysDictItem item : sysDictItems) {
        if (obj.getOperationReasonId().equals(item.getItemValue())) {
            operationReason_Text = item.getItemText();
            break;
        }
    }
    SysUnit unit = sysUnitService.getById(obj.getUnitId());
    List<NewArticle> articles = new ArrayList<>();
    NewArticle article = new NewArticle();
    article.setTitle("仓库操作通知");
    article.setDescription(String.format("物品编号: %s\n物品类型: %s\n数量: %s\n单位: %s\n操作类型: %s",
            obj.getArticleNo(), articleTypeId_Text, obj.getAmount(), unit ==null? "" : unit.getUnitName(), operationReason_Text));
    articles.add(article);
    message.setArticles(articles);
    try {
        messageService.send(message);
    } catch (WxErrorException e) {
        throw new RuntimeException(e);
    }
}

    public void updateTemplateTest(WxCpOaService oaService, WxCpOaApprovalTemplateResult templateDetail, String templateid, ApplyType applyType) throws WxErrorException {
        System.out.println(templateDetail.toJson());
        TemplateContent templateContent = new TemplateContent();
        List<TemplateControls> templateControlsList = new ArrayList<>();
        templateDetail.getTemplateContent().getControls().forEach(control -> {
            //control转成json字符串,再转成control对象
            String controlJson = JSONObject.toJSONString(control);
            TemplateControls templateControls = JSONObject.parseObject(controlJson, TemplateControls.class);
            templateControlsList.add(templateControls);
        });
        templateContent.setControls(templateControlsList);
        templateDetail.getTemplateNames().get(0).setText(applyType.getType());
        WxCpOaApprovalTemplate wxCpOaApprovalTemplate = new WxCpOaApprovalTemplate().setTemplateId(templateid).setTemplateContent(templateContent).setTemplateName(templateDetail.getTemplateNames());
        oaService.updateOaApprovalTemplate(wxCpOaApprovalTemplate);
    }

    private WxCpOaApplyEventRequest.ApplyData getApplyData(
            String ArticleNo,
            String tArticleTypeId,
            String Amount,
            String Unit,
            String OperationReason,
            String Status
    ) {
        return new WxCpOaApplyEventRequest.ApplyData()
                .setContents(Arrays.asList(
                        new ApplyDataContent().setControl("Text").setId("Text-1744869184796").setValue(new ContentValue().setText(ArticleNo != null ? ArticleNo : "默认默认名称")),
                        new ApplyDataContent().setControl("Textarea").setId("Textarea-1744869213123").setValue(new ContentValue().setText(tArticleTypeId != null ? tArticleTypeId : "默认物品类型")),
                        new ApplyDataContent().setControl("Text").setId("Text-1640339319582").setValue(new ContentValue().setText(Amount != null ? Amount : "默认数量")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868258243").setValue(new ContentValue().setText(Unit != null ? Unit : "默认单位")),
                        new ApplyDataContent().setControl("Text").setId("Text-1745389293582").setValue(new ContentValue().setText(OperationReason != null ? OperationReason : "默认操作类型"))
//                      new ApplyDataContent().setControl("Text").setId("Text-1744868261172").setValue(new ContentValue().setText(Status != null ? Status : "默认状态"))
                ));
    }

    private List<SummaryInfo> getSummaryList(String text1, String text2, String text3) {
        return new ArrayList<SummaryInfo>() {{
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text1))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text2))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text3))));
        }};
    }

    private List<WxCpOaApplyEventRequest.Approver> getApprovers() {
        ArrayList<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>() {{
            add(new WxCpOaApplyEventRequest.Approver().setAttr(1).setUserIds(new String[]{"xiaoxuebin", "wuxianzheng", "wangjiujun"}));
        }};
        return approvers;

    }

    public void wxCallback(WxCpXmlMessage wxCpXmlMessage, String id, String typeId) {
        WarehouseOutApply obj = this.baseMapper.selectById(id);
        if(wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.WAREHOUSE_APPLY_IN.getType())){
            // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
            // 审批中	UnderApproval	已通过	AlreadyPassed	已驳回	Rejected	状态异常	Anomaly
            String status = "";
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("1")){
                status = "UnderApproval";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("2")){
                status = "AlreadyPassed";
                LambdaQueryWrapper<SysWarehouse> warehousequeryWrapper = new LambdaQueryWrapper<>();
                warehousequeryWrapper.and(item->item.eq(SysWarehouse::getCode,obj.getArticleTypeId()));
                SysWarehouse sysWarehouse = sysWarehouseService.getOne(warehousequeryWrapper);
                //判断库存对象是否存在
                if (sysWarehouse == null){
                    status = "notExist";
                    obj.setStatus(status);
                    updateById(obj);
                    return;
                }
                LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.and(item->item.eq(Inventory::getWarehouseId,sysWarehouse.getId()).eq(
                        Inventory::getArticleNo,obj.getArticleNo()
                ).eq(
                        Inventory::getArticleTypeId,obj.getArticleTypeId()
                ));
                Inventory inventory = inventoryService.getOne(queryWrapper);
                //判断库存对象是否存在
                if (inventory == null){
                    status = "notExist";
                    obj.setStatus(status);
                    updateById(obj);
                    return;
                }
                WarehouseInOut entity = new WarehouseInOut();
                entity.setWarehouseId(sysWarehouse.getId());//库存id
                entity.setArticleNo(obj.getArticleNo());//物品编号
                entity.setArticleTypeId(obj.getArticleTypeId());//物品类型id
                entity.setAmount(obj.getAmount());
                entity.setUnitId(obj.getUnitId());
                entity.setBoxId(inventory.getBoxId());
                entity.setOperationReasonId(obj.getOperationReasonId());//操作原因id
                entity.setOperationTypeId(wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.WAREHOUSE_APPLY_IN.getType())? "in" : "out" );//操作类型id
                entity.setCreateBy(obj.getCreateBy());
                entity.setCreateTime(DateUtil.date());
                entity.setSysOrgCode(obj.getSysOrgCode());//部门id
                warehouseInOutMapper.insert(entity);
                //更新库存
                Inventory inventoryNew = inventoryService.getById(inventory.getId());
                BigDecimal result = new BigDecimal(inventory.getAmount())
                        .add(new BigDecimal(obj.getAmount()));
                inventoryNew.setAmount(result.toPlainString());
                inventoryService.updateById(inventoryNew);
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("3")){
                status = "Rejected";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("4")){
                status = "Anomaly";
            }
            obj.setStatus(status);
            updateById(obj);
        }
        if ((wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.WAREHOUSE_APPLY_OUT.getType())
                || wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.WAREHOUSE_APPLY_DESTROY.getType()))
                && wxCpXmlMessage.getEvent().equals("sys_approval_change")) {
            // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
            // 审批中 UnderApproval	已通过	AlreadyPassed	已驳回	Rejected	状态异常	Anomaly 库存不足	InsufficientInventory
            // 库存中不存该物品编号	notExist
            String status = "";
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("1")){
                status = "UnderApproval";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("2")){
                status = "AlreadyPassed";
                //先根据出库申请查询到库存表对应物品信息，仓位，数量
                //对比数量，如果大于库存数量，则驳回申请，提示库存不足，如果小于库存数量，则更新库存数量，更新库存表，并且生成出库如记录
                LambdaQueryWrapper<SysWarehouse> warehousequeryWrapper = new LambdaQueryWrapper<>();
                warehousequeryWrapper.and(item->item.eq(SysWarehouse::getCode,obj.getArticleTypeId()));
                SysWarehouse sysWarehouse = sysWarehouseService.getOne(warehousequeryWrapper);
                //判断库存对象是否存在
                if (sysWarehouse == null){
                    status = "notExist";
                    obj.setStatus(status);
                    updateById(obj);
                    return;
                }
                LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.and(item->item.eq(Inventory::getWarehouseId,sysWarehouse.getId()).eq(
                        Inventory::getArticleNo,obj.getArticleNo()
                ).eq(
                        Inventory::getArticleTypeId,obj.getArticleTypeId()
                ));
                Inventory inventory = inventoryService.getOne(queryWrapper);
               //判断库存对象是否存在
                if (inventory == null){
                    status = "notExist";
                    obj.setStatus(status);
                    updateById(obj);
                    return;
                }
                BigDecimal inventoryAmount = new BigDecimal(inventory.getAmount());
                BigDecimal objAmount = new BigDecimal(obj.getAmount());
                if (inventoryAmount.compareTo(objAmount) > 0) {
                    WarehouseInOut entity = new WarehouseInOut();
                    entity.setWarehouseId(sysWarehouse.getId());//库id
                    entity.setArticleNo(obj.getArticleNo());//物品编号
                    entity.setArticleTypeId(obj.getArticleTypeId());//物品类型id
                    entity.setAmount(obj.getAmount());
                    entity.setUnitId(obj.getUnitId());
                    entity.setBoxId(inventory.getBoxId());
                    entity.setOperationReasonId(obj.getOperationReasonId());//操作原因id
                    entity.setOperationTypeId(wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.WAREHOUSE_APPLY_IN.getType())? "in" : "out" );//操作类型id
                    entity.setCreateBy(obj.getCreateBy());
                    entity.setCreateTime(DateUtil.date());
                    entity.setSysOrgCode(obj.getSysOrgCode());//部门id
                    warehouseInOutMapper.insert(entity);
                    //更新库存
                    Inventory inventoryNew = inventoryService.getById(inventory.getId());
                    BigDecimal result = new BigDecimal(inventory.getAmount())
                            .subtract(new BigDecimal(obj.getAmount()));
                    inventoryNew.setAmount(result.toPlainString());
                    inventoryService.updateById(inventoryNew);
                }else {
                    status = "InsufficientInventory";
                }
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("3")){
                status = "Rejected";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("4")){
                status = "Anomaly";
            }
            obj.setStatus(status);
            updateById(obj);
        }

    }

    @Override
    public String getwarehouseboxBycode(String code) {
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(item->item.eq(Inventory::getArticleNo,code));
        Inventory inve = inventoryService.getOne(queryWrapper);
        if (inve == null){
            return "";
        }
        SysWarehouseBox box = sysWarehouseBoxService.getById(inve.getBoxId());
        if (box == null){
            return "";
        }
        return box.getCode();
    }
    @Override
    public String getwarehouseboxidBycode(String code) {
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(item->item.eq(Inventory::getArticleNo,code));
        Inventory inve = inventoryService.getOne(queryWrapper);
        if (inve == null){
            return "";
        }
        SysWarehouseBox box = sysWarehouseBoxService.getById(inve.getBoxId());
        if (box == null){
            return "";
        }
        return box.getId();
    }
}
