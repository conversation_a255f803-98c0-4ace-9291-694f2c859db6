package org.jeecg.modules.lims_order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.modules.lims_order.entity.BizOrder;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;

import java.util.List;
import java.util.Map;

/**
 * @Description: 订单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
public interface IBizOrderService extends IService<BizOrder> {


     List<Map> listDataLog(String id);


     void apply(String contractNo, ApplyType applyType) throws WxErrorException;

     void transferToLab(String id);

     void saveOrder(BizOrder bizOrder);
}
