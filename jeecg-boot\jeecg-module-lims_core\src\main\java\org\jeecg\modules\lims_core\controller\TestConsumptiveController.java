package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.Test;
import org.jeecg.modules.lims_core.entity.TestConsumptive;
import org.jeecg.modules.lims_core.service.ITestConsumptiveService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.ITestService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 耗材使用记录
 * @Author: jeecg-boot
 * @Date:   2025-02-13
 * @Version: V1.0
 */
@Tag(name="耗材使用记录")
@RestController
@RequestMapping("/lims_core/testConsumptive")
@Slf4j
public class TestConsumptiveController extends JeecgController<TestConsumptive, ITestConsumptiveService> {
	@Autowired
	private ITestConsumptiveService testConsumptiveService;
	 @Autowired
	 private ITestService testService;
	
	/**
	 * 分页列表查询
	 *
	 * @param testConsumptive
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "耗材使用记录-分页列表查询")
	@Operation(summary="耗材使用记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestConsumptive>> queryPageList(TestConsumptive testConsumptive,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<TestConsumptive> queryWrapper = QueryGenerator.initQueryWrapper(testConsumptive, req.getParameterMap());
		Page<TestConsumptive> page = new Page<TestConsumptive>(pageNo, pageSize);
		IPage<TestConsumptive> pageList = testConsumptiveService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param testConsumptive
	 * @return
	 */
	@AutoLog(value = "耗材使用记录-添加")
	@Operation(summary="耗材使用记录-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:test_consumptive:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestConsumptive testConsumptive) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testConsumptive.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test = testService.getOne(qwTest);
		if (test != null){
			testConsumptive.setTestId(test.getId());
		}
		testConsumptiveService.save(testConsumptive);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param testConsumptive
	 * @return
	 */
	@AutoLog(value = "耗材使用记录-编辑")
	@Operation(summary="耗材使用记录-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_consumptive:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestConsumptive testConsumptive) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testConsumptive.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test = testService.getOne(qwTest);
		if (test != null){
			testConsumptive.setTestId(test.getId());
		}
		testConsumptiveService.updateById(testConsumptive);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "耗材使用记录-通过id删除")
	@Operation(summary="耗材使用记录-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_consumptive:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testConsumptiveService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "耗材使用记录-批量删除")
	@Operation(summary="耗材使用记录-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_consumptive:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testConsumptiveService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "耗材使用记录-通过id查询")
	@Operation(summary="耗材使用记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestConsumptive> queryById(@RequestParam(name="id",required=true) String id) {
		TestConsumptive testConsumptive = testConsumptiveService.getById(id);
		if(testConsumptive==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testConsumptive);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testConsumptive
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_consumptive:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestConsumptive testConsumptive) {
        return super.exportXls(request, testConsumptive, TestConsumptive.class, "耗材使用记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_consumptive:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestConsumptive.class);
    }

}
