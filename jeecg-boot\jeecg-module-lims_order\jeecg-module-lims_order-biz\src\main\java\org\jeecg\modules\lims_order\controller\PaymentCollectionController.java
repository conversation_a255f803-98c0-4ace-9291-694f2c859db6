package org.jeecg.modules.lims_order.controller;

import java.util.Arrays;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.lims_order.entity.PaymentCollection;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_order.service.IPaymentCollectionService;
import org.jeecg.modules.lims_order.vo.PaymentAddOrEditVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 回款计划
 * @Author: jeecg-boot
 * @Date:   2025-03-21
 * @Version: V1.0
 */
@Tag(name="回款计划")
@RestController
@RequestMapping("/lims_order/paymentCollection")
@Slf4j
public class PaymentCollectionController extends JeecgController<PaymentCollection, IPaymentCollectionService> {
	@Autowired
	private IPaymentCollectionService paymentCollectionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param paymentCollection
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "回款计划-分页列表查询")
	@Operation(summary="回款计划-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PaymentCollection>> queryPageList(PaymentCollection paymentCollection,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<PaymentCollection> queryWrapper = QueryGenerator.initQueryWrapper(paymentCollection, req.getParameterMap());
		Page<PaymentCollection> page = new Page<PaymentCollection>(pageNo, pageSize);
		IPage<PaymentCollection> pageList = paymentCollectionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param paymentCollection
	 * @return
	 */
	@AutoLog(value = "回款计划-添加")
	@Operation(summary="回款计划-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PaymentCollection paymentCollection) {
		paymentCollectionService.save(paymentCollection);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param paymentCollection
	 * @return
	 */
	@AutoLog(value = "回款计划-编辑")
	@Operation(summary="回款计划-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PaymentCollection paymentCollection) {
		paymentCollectionService.updateById(paymentCollection);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "回款计划-通过id删除")
	@Operation(summary="回款计划-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		paymentCollectionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "回款计划-批量删除")
	@Operation(summary="回款计划-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.paymentCollectionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "回款计划-通过id查询")
	@Operation(summary="回款计划-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PaymentCollection> queryById(@RequestParam(name="id",required=true) String id) {
		PaymentCollection paymentCollection = paymentCollectionService.getById(id);
		if(paymentCollection==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(paymentCollection);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param paymentCollection
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PaymentCollection paymentCollection) {
        return super.exportXls(request, paymentCollection, PaymentCollection.class, "回款计划");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PaymentCollection.class);
    }


	 /**
	  *  增减回款计划
	  *
	  * @param paymentCollection
	  * @return
	  */
	 @AutoLog(value = "回款计划-增减回款计划")
	 @Operation(summary="回款计划-增减回款计划")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:payment_collection:edit')")
	 @RequestMapping(value = "/addOrEdit", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> addOrEdit(@RequestBody PaymentAddOrEditVo paymentAddOrEditVo) {
		 paymentCollectionService.addOrEdit(paymentAddOrEditVo);
		 return Result.OK("编辑成功!");
	 }

}
