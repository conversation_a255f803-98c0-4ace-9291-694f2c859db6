package org.jeecg.modules.lims_lab.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TestResultVO {
    private String id;
    /**标本ID*/
    @Schema(description = "标本条码")
    private String testId;
    @Schema(description = "样品编号")
    private String sampleNo;
    @Schema(description = "方法")
    private String methodId;
    @Schema(description = "方法名称")
    private String methodName;
    @Schema(description = "已提交")
    private boolean isSubmitted;
    @Schema(description = "序号")
    private java.lang.Integer sortNum;
    /**指标*/
    @Schema(description = "指标")
    private String analyte;
    @Schema(description = "实验序号")
    private Integer trialNo;
    /**指标*/
    @Schema(description = "结果类型")
    private Integer resultType;
    /**标识量*/
    @Schema(description = "标识量")
    private String labelResult;
    /**检测结果*/
    @Schema(description = "检测结果")
    private String rawResult;
    /**检测单位*/
    @Schema(description = "检测单位")
    private String rawUnit;
    /**检出限*/
    @Schema(description = "检出限")
    private java.lang.String lod;
    /**定量限*/
    @Schema(description = "定量限")
    private java.lang.String loq;
    @Schema(description = "修约算法")
    private java.lang.Integer roundingAlgorithmId;
    @Schema(description = "修约方式")
    private java.lang.Integer roundingWayId;
    @Schema(description = "修约精度")
    private java.lang.Integer roundingPrecision;
    @Schema(description = "精密度类型")
    private java.lang.Integer precisionTypeId;
    @Schema(description = "精密度要求")
    private java.lang.String precisionReq;
    /**平行结果*/
    @Schema(description = "平行结果")
    private String dupResult;
    /**报告结果*/
    @Schema(description = "报告结果")
    private String repResult;
    /**报告单位*/
    @Schema(description = "报告单位")
    private String repUnit;
    /**报告单位*/
    @Schema(description = "评价要求")
    private String limit;
    /**多个限值出报告用于评定的评价要求*/
    private String repLimit;
    /**报告单位*/
    @Schema(description = "结论")
    private String conclusion;
    /**单位转换系数*/
    @Schema(description = "单位转换系数")
    private BigDecimal UCF;
    @Schema(description = "计算公式")
    private String calcExpr;
    /**检测人员*/
    @Schema(description = "检测人员")
    private String updateBy;
    /**检测时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检测时间")
    private Date updateTime;
    @Schema(description = "审核人员")
    private String checkedBy;
    /**检测时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审核时间")
    private Date checkedTime;
    /**复核人员*/
    @Schema(description = "复核人员")
    private String checker;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "复核时间")
    private Date checkerTime;
    /**检验人员*/
    @Schema(description = "检验人员")
    private String tester;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检测时间")
    private Date testerTime;

    /**部门*/
    @Schema(description = "部门")
    private String deptId;

    /**任务*/
    @Schema(description = "任务")
    private String taskId;

    /**是否出报告*/
    @Schema(description = "是否出报告")
    private String reportable;

    /**任务指定复核人*/
    @Schema(description = "任务指定复核人")
    private String preChecker;

    @Schema(description = "技术性质类别")
    private String natureTypeId;
}
