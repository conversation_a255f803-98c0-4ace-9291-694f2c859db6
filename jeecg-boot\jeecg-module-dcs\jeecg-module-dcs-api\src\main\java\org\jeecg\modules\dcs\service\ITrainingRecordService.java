package org.jeecg.modules.dcs.service;

import org.jeecg.modules.dcs.entity.TrainingRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 培训记录
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
public interface ITrainingRecordService extends IService<TrainingRecord> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<TrainingRecord>
	 */
	List<TrainingRecord> selectByMainId(String mainId);
	List<String> getAttendeeDeparts(String mainId);
}
