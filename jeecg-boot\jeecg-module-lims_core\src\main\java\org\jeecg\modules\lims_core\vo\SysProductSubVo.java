package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.entity.SysProduct;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SysProductSubVo {
    public static final String CATEGORY_STANDARD = "STANDARD";
    public static final String CATEGORY_CAPABILITY = "CAPABILITY";
    private String id;
    private String name;
    private String method;
    private BigDecimal stdPrice;
    private String methodId;
    private String analyteId;
    private String evaluationId;
    private List<SysMethod> methods;
    private String productPackageId;
    private String capabilityId;
    private String ppdId;
    private Integer leadTime;
    private String category;
    private Integer sortNum;

}
