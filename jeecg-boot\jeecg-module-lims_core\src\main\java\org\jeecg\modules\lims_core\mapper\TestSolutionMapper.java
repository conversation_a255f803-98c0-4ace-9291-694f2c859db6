package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.dto.MethodNeedMakeupModel;
import org.jeecg.modules.lims_core.entity.TestSolution;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 溶液使用记录
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
public interface TestSolutionMapper extends BaseMapper<TestSolution> {
    /**
     * 通过测试ID查询
     * @param testId
     * @return
     */
    List<MethodNeedMakeupModel> selectMethodNeedMakeupByTestId(@Param("testId") String testId);
}
