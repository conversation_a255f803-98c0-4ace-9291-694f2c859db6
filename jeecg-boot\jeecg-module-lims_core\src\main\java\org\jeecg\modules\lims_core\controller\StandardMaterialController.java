package org.jeecg.modules.lims_core.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import cn.hutool.poi.excel.cell.CellSetter;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.StandardMaterial;
import org.jeecg.modules.lims_core.service.IStandardMaterialService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.StandardMaterialVO;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 标准物质台账
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Tag(name="标准物质台账")
@RestController
@RequestMapping("/lims_core/standardMaterial")
@Slf4j
public class StandardMaterialController extends JeecgController<StandardMaterial, IStandardMaterialService> {
	@Autowired
	private IStandardMaterialService standardMaterialService;
	
	/**
	 * 分页列表查询
	 *
	 * @param standardMaterial
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "标准物质台账-分页列表查询")
	@Operation(summary="标准物质台账-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<StandardMaterialVO>> queryPageList(StandardMaterial standardMaterial,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String sampleId = req.getParameter("sampleId");
		String InventoryPositive = req.getParameter("InventoryPositive");
		if (StringUtils.isNotBlank(sampleId)) {
			QueryWrapper<StandardMaterial> queryWrapper = new QueryWrapper<>();
			List<String> sampleIdList = Arrays.asList(sampleId.split(","));
			String inCondition = sampleIdList.stream()
						.map(id -> "FIND_IN_SET('" + id + "', sample_id)")
						.collect(Collectors.joining(" OR "));
			queryWrapper.apply(inCondition)
					.and(wrapper -> wrapper.notLike("destroy_reason", "销毁").or().isNull("destroy_reason"));
			Page<StandardMaterial> page = new Page<>(pageNo, pageSize);
			IPage<StandardMaterialVO> pageList = standardMaterialService.queryPageList(page, queryWrapper);
			return Result.OK(pageList);
		}else if(StringUtils.isNotBlank(InventoryPositive)){
			if (InventoryPositive.equals("库存大于零")){
				QueryWrapper<StandardMaterial> queryWrapper = new QueryWrapper<>();
				queryWrapper.gt("inventoryquantity", 0);
				Page<StandardMaterial> page = new Page<>(pageNo, pageSize);
				IPage<StandardMaterialVO> pageList = standardMaterialService.queryPageList(page, queryWrapper);
				return Result.OK(pageList);
			}else {
				QueryWrapper<StandardMaterial> queryWrapper = new QueryWrapper<>();
				queryWrapper.gt("vaild_date", new Date());
				Page<StandardMaterial> page = new Page<>(pageNo, pageSize);
				IPage<StandardMaterialVO> pageList = standardMaterialService.queryPageList(page, queryWrapper);
				return Result.OK(pageList);
			}
		}else {
			QueryWrapper<StandardMaterial> queryWrapper = QueryGenerator.initQueryWrapper(standardMaterial, req.getParameterMap());
			Page<StandardMaterial> page = new Page<StandardMaterial>(pageNo, pageSize);
			IPage<StandardMaterialVO> pageList = standardMaterialService.queryPageList(page, queryWrapper);
			return Result.OK(pageList);
		}
	}
	
	/**
	 *   添加
	 *
	 * @param standardMaterial
	 * @return
	 */
	@AutoLog(value = "标准物质台账-添加")
	@Operation(summary="标准物质台账-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:standard_material:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody StandardMaterial standardMaterial) {
		if(standardMaterial.getSpec()>0 && standardMaterial.getSpecUnit()!=null && standardMaterial.getQty()>0 && standardMaterial.getUnit()!=null) {
			for (int i=1;i<=standardMaterial.getQty();i++){
				StandardMaterial singleRecord = new StandardMaterial();
				BeanUtils.copyProperties(standardMaterial, singleRecord);
				singleRecord.setCode(FillRuleUtil.executeRule(FillRuleConstant.STANDARDMATERIAL,null).toString());
				singleRecord.setQty(1);
				singleRecord.setSmStatus("未入库");
				standardMaterialService.save(singleRecord);
			}
		}else{
			standardMaterial.setCode(FillRuleUtil.executeRule(FillRuleConstant.STANDARDMATERIAL,null).toString());
			standardMaterialService.save(standardMaterial);
		}
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param standardMaterial
	 * @return
	 */
	@AutoLog(value = "标准物质台账-编辑")
	@Operation(summary="标准物质台账-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:standard_material:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody StandardMaterial standardMaterial) {
		standardMaterialService.updateById(standardMaterial);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "标准物质台账-通过id删除")
	@Operation(summary="标准物质台账-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:standard_material:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		standardMaterialService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "标准物质台账-批量删除")
	@Operation(summary="标准物质台账-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:standard_material:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.standardMaterialService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "标准物质台账-通过id查询")
	@Operation(summary="标准物质台账-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<StandardMaterial> queryById(@RequestParam(name="id",required=true) String id) {
		StandardMaterial standardMaterial = standardMaterialService.getById(id);
		if(standardMaterial==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(standardMaterial);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param standardMaterial
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:standard_material:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, StandardMaterial standardMaterial) {
        return super.exportXls(request, standardMaterial, StandardMaterial.class, "标准物质台账");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:standard_material:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, StandardMaterial.class);
    }

}
