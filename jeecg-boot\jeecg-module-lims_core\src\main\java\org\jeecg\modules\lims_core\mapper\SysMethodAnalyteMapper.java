package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 检测指标
 * @Author: jeecg-boot
 * @Date:   2025-03-27
 * @Version: V1.0
 */
public interface SysMethodAnalyteMapper extends BaseMapper<SysMethodAnalyte> {


    /**
     * 通过主表id查询子表数据
     *
     * @param mainId 主表id
     * @return List<SysMethodAnalyte>
     */
    public List<SysMethodAnalyte> selectByMainId(@Param("mainId") String mainId);
}
