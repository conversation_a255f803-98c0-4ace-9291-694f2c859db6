package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "SysMethodAnalyte",drillDown = "analyte_id->sys_analyte.id",description = "方法参数关联表")
public class SysMethodAnalyteVO {
    @TemplateField(description = "ID")
    private String id;
    @TemplateField(description = "方法id")
    private String methodId;
    @TemplateField(description = "参数id")
    private String analyteId;
    @TemplateField(description = "单位id")
    private String unitId;
}