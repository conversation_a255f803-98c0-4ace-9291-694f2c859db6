//import cn.hutool.core.date.DateTime;
//import com.alibaba.fastjson.JSONObject;
//import com.aspose.words.Document;
//import com.aspose.words.IMailMergeDataSource;
//import com.aspose.words.ref.Ref;
//import me.chanjar.weixin.common.error.WxErrorException;
//import me.chanjar.weixin.cp.api.*;
//import me.chanjar.weixin.cp.bean.article.NewArticle;
//import me.chanjar.weixin.cp.bean.message.WxCpMessage;
//import me.chanjar.weixin.cp.bean.oa.*;
//import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
//import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
//import me.chanjar.weixin.cp.bean.oa.meeting.WxCpMeeting;
//import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateContent;
//import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateControls;
//import org.jeecg.config.WxCpConfiguration;
//import org.jeecg.config.WxCpProperties;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.io.IOException;
//import java.util.*;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2024/12/17 13:48
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = OOApplication.class)
//public class test {
//    @Test
//    public void scheduleTest() throws WxErrorException {
////        JSONObject jsonObject = JsonFileReader.readJsonFileAsJSONObject("qm_template.json");
////        System.out.println(jsonObject);
//
//
//
//
//        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
//        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
//        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
//        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
//        WxCpOaScheduleService oaScheduleService = cpService.getOaScheduleService();
//
//        // 创建日程对象
//        WxCpOaSchedule schedule = new WxCpOaSchedule();
//        schedule.setStartTime(1571274600L); // 设置开始时间（时间戳）
//        schedule.setEndTime(1571320210L); // 设置结束时间（时间戳）
//        schedule.setSummary("会议"); // 设置日程摘要
//        schedule.setDescription("讨论项目进展"); // 设置日程描述
//        schedule.setLocation("会议室A"); // 设置日程地点
//
//        WxCpOaSchedule.Reminder reminder = new WxCpOaSchedule.Reminder();
//        reminder.setIsRemind(1);
//        reminder.setRemindTimeDiffs(new ArrayList<>(){{add(-86400);}});
//
//        schedule.setReminders(reminder);
//        WxCpOaSchedule. Attendee attendee = new WxCpOaSchedule.Attendee();
//        attendee.setUserid("xiaoxuebin");
//        schedule.setAttendees(new ArrayList<>(){{add(attendee);}});
//        // 添加日程
//        System.out.println(oaScheduleService.add(schedule, null));
//
//    }
//
//    @Test
//    public void messageTest() throws WxErrorException, IOException {
//        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
//        String corpId = wxCpProperties.getAppConfigs().get(1).getCorpId();
//        int agentId = wxCpProperties.getAppConfigs().get(1).getAgentId();
//        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
//        WxCpMessageService messageService = cpService.getMessageService();
//
////        ClassPathResource resource = new ClassPathResource("banner.jpg");
////        InputStream inputStream = resource.getInputStream();
////        WxMediaUploadResult image = cpService.getMediaService().upload("image", inputStream, "banner.jpg");
//        WxCpMessage message = new WxCpMessage();
//        message.setAgentId(agentId);
//        message.setToUser("xiaoxuebin");
//
//
//        //        message.setMsgType("mpnews");
////        List<MpnewsArticle> mpAs = new ArrayList<>();
////        MpnewsArticle mpnewArticle = new MpnewsArticle();
////        mpnewArticle.setTitle("月度评审清单");
////        mpnewArticle.setContent("月度评审清单");
////        mpnewArticle.setContentSourceUrl("http://gbjc.cc/jeecg-boot/jmreport/shareView/1029930079551053824?shareToken=c2de75577e3a80270af20b6a4018011c");
////
////        mpnewArticle.setThumbMediaId(image.getMediaId());
////        mpAs.add(mpnewArticle);
//
//        message.setMsgType("news");
//        List<NewArticle> mpAs = new ArrayList<>();
//        NewArticle newArticle = new NewArticle();
//        newArticle.setTitle("月度评审清单");
//        newArticle.setDescription("月度评审清单");
//        newArticle.setUrl("https://gbjc.cc/jeecg-boot/jmreport/shareView/1029930079551053824?shareToken=c2de75577e3a80270af20b6a4018011c");
//
//        newArticle.setPicUrl("http://nwzimg.wezhan.cn/contents/sitefiles2049/10245330/images/48843504.jpg");
//        mpAs.add(newArticle);
//
//
//        message.setArticles(mpAs);
//        messageService.send(message);
//
//        //---------------------
//
//        newArticle = new NewArticle();
//        newArticle.setTitle("季度评审清单");
//        newArticle.setDescription("季度评审清单");
//        newArticle.setPicUrl("http://nwzimg.wezhan.cn/contents/sitefiles2049/10245330/images/48843504.jpg");
//        newArticle.setUrl("https://gbjc.cc/jeecg-boot/jmreport/shareView/1029917173632159744?shareToken=7b887e4a5d79716f59a86c9c664738ce");
//        mpAs.clear();
//        mpAs.add(newArticle);
//        message.setArticles(mpAs);
//        messageService.send(message);
//    }
//
//    @Test
//    public void meetingTest() {
//        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
//        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
//        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
//        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
//        WxCpMeetingService meetingService = cpService.getMeetingService();
//
//        // 创建会议
//        WxCpMeeting wxCpMeeting = new WxCpMeeting();
//        wxCpMeeting.setAdminUserId("xiaoxuebin");
//        wxCpMeeting.setTitle("{测试}培训会议");
//        wxCpMeeting.setMeetingStart(new DateTime().getTime() / 1000 + 1000L);
//        wxCpMeeting.setMeetingDuration(3600);
//        WxCpMeeting.Attendees attendee = new WxCpMeeting.Attendees();
//        String[] userids = {"xiaoxuebin", "zhaojiafu", "wangjiujun"};
//        attendee.setUserId(Arrays.asList(userids));
//        wxCpMeeting.setAttendees(attendee);
//        try {
//            meetingService.create(wxCpMeeting);
//        } catch (WxErrorException e) {
//            throw new RuntimeException(e);
//        }
//
//    }
//
//    @Test
//    public void test1() throws Exception {
//        // 加载模板文档
//        Document doc = new Document("/Users/<USER>/Downloads/DynamicSignInTemplate.docx");
//
//
//        // 准备动态数据
//        List<Map<String, String>> data = prepareMultiColumnData();
//
//        // 创建自定义数据源
//        MailMergeDataSource dataSource = new MailMergeDataSource(data, "Names");
//
//        // 执行MailMerge with Regions
//        doc.getMailMerge().executeWithRegions(dataSource);
//        // 保存生成的文档
//        doc.save("/Users/<USER>/Downloads/DynamicSignInTable_Filled.docx");
//
//        System.out.println("动态多列签到表生成成功！");
//    }
//    private static List<Map<String, String>> prepareMultiColumnData() {
//        List<Map<String, String>> data = new ArrayList<>();
//        String[] names = { "张三", "李四", "王五", "赵六", "孙七", "周八", "陈九", "杨十" };
//
//        // 按每行3列组织数据
//        int columns = 3;
//        for (int i = 0; i < names.length; i += columns) {
//            Map<String, String> row = new HashMap<>();
//            for (int j = 0; j < columns; j++) {
//                if (i + j < names.length) {
//                    row.put("Name" + (j + 1), names[i + j]);
//                } else {
//                    row.put("Name" + (j + 1), ""); // 空白填充
//                }
//            }
//            data.add(row);
//        }
//        return data;
//    }
//
//    // 自定义数据源类
//    static class MailMergeDataSource implements IMailMergeDataSource {
//        private List<Map<String, String>> data;
//        private int index = -1;
//        private String tableName;
//
//        public MailMergeDataSource(List<Map<String, String>> data, String tableName) {
//            this.data = data;
//            this.tableName = tableName;
//        }
//
//        @Override
//        public String getTableName() {
//            return tableName;
//        }
//
//        @Override
//        public boolean moveNext() {
//            if (index < data.size() - 1) {
//                index++;
//                return true;
//            }
//            return false;
//        }
//
//
//
//        // 获取当前字段的值
//        @Override
//        public boolean getValue(String fieldName, Ref<Object> fieldValue) {
//            if (data.get(index).containsKey(fieldName)) {
//                fieldValue.set(data.get(index).get(fieldName));
//                return true;
//            }
//            return false;
//        }
//
//        @Override
//        public IMailMergeDataSource getChildDataSource(String tableName) {
//            return null;
//        }
//    }
//
//    @Test
//    public void applyTest() throws WxErrorException {
//        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
//        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
//        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
//        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
//        WxCpOaService oaService = cpService.getOaService();
//        String templateId = "C4ZVWZXc1GaoXbe2etB2em6Q6eqf86a4fQhFYCTCd";
//        WxCpOaApprovalTemplateResult templateDetail = oaService.getTemplateDetail(templateId);
//        System.out.println(templateDetail.toJson());
//        updateTemplateTest(oaService,templateDetail,templateId,"Tips-1732010198545","IT调试测试1","https://gbjc.cc/jeecg-boot/jmreport/shareView/1029930079551053824?shareToken=c2de75577e3a80270af20b6a4018011c");
//
//        WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
//        // creator_userid	 申请人userid，此审批申请将以此员工身份提交，申请人需在应用可见范围内
//        request.setCreatorUserId("xiaoxuebin");
//        // template_id	 模板id
//        request.setTemplateId(templateId);
//        // use_template_approver	 0-通过接口指定审批人、抄送人（此时process参数必填）; 1-使用此模板在管理后台设置的审批流程(需要保证审批流程中没有“申请人自选”节点)，支持条件审批。
//        request.setUseTemplateApprover(0);
//        // apply_data     审批申请数据
//        request.setApplyData(getApplyData());
//        // summary_list    摘要信息
//        request.setSummaryList(getSummaryList("IT调试测试1","IT调试测试2","IT调试测试3"));
//        // process	 审批流程信息
//        request.setApprovers(getApprovers());
//        try {
//            oaService.apply(request);
//        } catch (WxErrorException e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    private List<WxCpOaApplyEventRequest.Approver> getApprovers() {
//        ArrayList<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>() {{
//            add(new WxCpOaApplyEventRequest.Approver().setAttr(1).setUserIds(new String[]{"xiaoxuebin","wuxianzheng","wangjiujun"}));
//        }};
//        return approvers;
//
//    }
//
//    private List<SummaryInfo> getSummaryList(String text1,String text2,String text3) {
//        return new ArrayList<SummaryInfo>() {{
//            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text1))));
//            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text2))));
//            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text3))));
//        }};
//    }
//
//    private WxCpOaApplyEventRequest.ApplyData getApplyData() {
//        return new WxCpOaApplyEventRequest.ApplyData()
//                .setContents(Arrays.asList(
//                        new ApplyDataContent()
//                                .setControl("Text").setId("Text-1640339319582").setValue(new ContentValue().setText("文本填写的内容")),
//                        new ApplyDataContent().setControl("Textarea").setId("Textarea-1640339335659").setValue(new ContentValue().setText("文本填写的内容")),
//                    new ApplyDataContent().setControl("Tips").setId("Tips-1732010198545")
//                ));
//    }
//
//
////    @Test
//    public void updateTemplateTest(WxCpOaService oaService,WxCpOaApprovalTemplateResult templateDetail,String templateid,String tipsid,String title,String url) throws WxErrorException {
//        System.out.println(templateDetail.toJson());
//        List<WxCpOaApprovalTemplateResult.TemplateControls> list = templateDetail.getTemplateContent().getControls().stream().filter(control -> control.getProperty().getId().equals(tipsid)).toList();
//        if (list.size()>0){
//            WxCpOaApprovalTemplateResult.TemplateControls templateControls = list.get(0);
//            templateControls.getConfig().getTips().getTipsContent().get(0).getText().getSubText().get(1).getContent().getLink().setUrl(url);
//            templateControls.getConfig().getTips().getTipsContent().get(0).getText().getSubText().get(1).getContent().getLink().setTitle(title);
//        }
//        TemplateContent templateContent = new TemplateContent();
//        List<TemplateControls> templateControlsList = new ArrayList<>();
//
//        templateDetail.getTemplateContent().getControls().forEach(control -> {
//            //control转成json字符串,再转成control对象
//            String controlJson = JSONObject.toJSONString(control);
//            TemplateControls templateControls = JSONObject.parseObject(controlJson, TemplateControls.class);
//            templateControlsList.add(templateControls);
//        });
//        templateContent.setControls(templateControlsList);
////        templateDetail.getTemplateNames().get(0).setText("IT调试测试1");
//        WxCpOaApprovalTemplate wxCpOaApprovalTemplate = new WxCpOaApprovalTemplate().setTemplateId(templateid).setTemplateContent(templateContent).setTemplateName(templateDetail.getTemplateNames());
//        oaService.updateOaApprovalTemplate(wxCpOaApprovalTemplate);
//    }
//
//}
