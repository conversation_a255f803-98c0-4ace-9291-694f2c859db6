package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodTestingPara;
import org.jeecg.modules.lims_core.mapper.SysMethodTestingParaMapper;
import org.jeecg.modules.lims_core.service.ISysMethodTestingParaService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 实验过程参数
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodTestingParaServiceImpl extends ServiceImpl<SysMethodTestingParaMapper, SysMethodTestingPara> implements ISysMethodTestingParaService {
	
	@Autowired
	private SysMethodTestingParaMapper sysMethodTestingParaMapper;
	
	@Override
	public List<SysMethodTestingPara> selectByMainId(String mainId) {
		return sysMethodTestingParaMapper.selectByMainId(mainId);
	}
}
