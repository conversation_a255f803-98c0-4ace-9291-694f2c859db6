package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.RdProject;
import org.jeecg.modules.lims_core.service.IRdProjectService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 研发项目
 * @Author: jeecg-boot
 * @Date:   2025-05-15
 * @Version: V1.0
 */
@Tag(name="研发项目")
@RestController
@RequestMapping("/lims_core/rdProject")
@Slf4j
public class RdProjectController extends JeecgController<RdProject, IRdProjectService> {
	@Autowired
	private IRdProjectService rdProjectService;
     @Autowired
     private IQuotationService iQuotationService;

	 /**
	 * 分页列表查询
	 *
	 * @param rdProject
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "研发项目-分页列表查询")
	@Operation(summary="研发项目-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<RdProject>> queryPageList(RdProject rdProject,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<RdProject> queryWrapper = QueryGenerator.initQueryWrapper(rdProject, req.getParameterMap());
		Page<RdProject> page = new Page<RdProject>(pageNo, pageSize);
		IPage<RdProject> pageList = rdProjectService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rdProject
	 * @return
	 */
	@AutoLog(value = "研发项目-添加")
	@Operation(summary="研发项目-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:rd_project:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RdProject rdProject) {
		Quotation q = iQuotationService.getById(rdProject.getQuotationId());
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("biz_type_id", q.getBizTypeId());
		rdProject.setRdNo(FillRuleUtil.executeRule(FillRuleConstant.RD, jsonObject).toString());
		rdProjectService.save(rdProject);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rdProject
	 * @return
	 */
	@AutoLog(value = "研发项目-编辑")
	@Operation(summary="研发项目-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:rd_project:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RdProject rdProject) {

		rdProjectService.updateById(rdProject);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "研发项目-通过id删除")
	@Operation(summary="研发项目-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:rd_project:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rdProjectService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "研发项目-批量删除")
	@Operation(summary="研发项目-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:rd_project:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rdProjectService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "研发项目-通过id查询")
	@Operation(summary="研发项目-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RdProject> queryById(@RequestParam(name="id",required=true) String id) {
		RdProject rdProject = rdProjectService.getById(id);
		if(rdProject==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rdProject);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rdProject
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:rd_project:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RdProject rdProject) {
        return super.exportXls(request, rdProject, RdProject.class, "研发项目");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:rd_project:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RdProject.class);
    }

}
