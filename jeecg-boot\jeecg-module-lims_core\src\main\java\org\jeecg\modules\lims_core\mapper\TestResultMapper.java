package org.jeecg.modules.lims_core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.lims_core.entity.TestResult;

import java.util.List;

/**
 * @Description: 检测结果
 * @Author: jeecg-boot
 * @Date:   2025-02-08
 * @Version: V1.0
 */
public interface TestResultMapper extends BaseMapper<TestResult> {
        @Select("SELECT * FROM test_result WHERE test_id = #{testId}")
        List<TestResult> selectByTestId(@Param("testId") String testId);
}
