package org.jeecg.modules.lims_core.service.impl;

import aj.org.objectweb.asm.TypeReference;
import cn.hutool.json.ObjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.jeecg.modules.lims_core.entity.SysCapability;
import org.jeecg.modules.lims_core.entity.SysTranslation;
import org.jeecg.modules.lims_core.mapper.SysCapabilityMapper;
import org.jeecg.modules.lims_core.service.ISysCapabilityService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description: 检测能力库
 * @Author: jeecg-boot
 * @Date:   2025-04-10
 * @Version: V1.0
 */
@Service
public class SysCapabilityServiceImpl extends ServiceImpl<SysCapabilityMapper, SysCapability> implements ISysCapabilityService {

    @Override
    public void batchUpdate(Map<String, Object> map) {
        if (map.get("field") == null || "".equals(map.get("field"))) {
            throw new RuntimeException("field不能为空");
        }
        if (map.get("baseTable") == null || "".equals(map.get("baseTable"))) {
            throw new RuntimeException("baseTable不能为空");
        }
        if (map.get("ids") == null) {
            throw new RuntimeException("ids不能为空且必须为数组");
        }
        if (map.get("value") == null) {
            throw new RuntimeException("value不能为空");
        }
        String field = (String) map.get("field");// 字段名
        String baseTable = (String) map.get("baseTable");// 表名
        String value;
        Object rawValue = map.get("value");
        if (rawValue instanceof List) {
            List<?> valueList = (List<?>) rawValue;
            if (valueList.isEmpty()) {
                throw new RuntimeException("value列表不能为空");
            }
            value = String.join(",", valueList.stream().map(Object::toString).toList());
        } else if (rawValue instanceof String) {
            value = (String) rawValue;
            if (value.isEmpty()) {
                throw new RuntimeException("value字符串不能为空");
            }
        } else {
            throw new RuntimeException("value必须为String或List类型");
        }
        List<Object> rawIds = (List<Object>) map.get("ids");
        if (rawIds.isEmpty()) {
            throw new RuntimeException("ids数组不能为空");
        }
        // 转成 List<String>
        List<String> ids = rawIds.stream()
                .map(Object::toString)
                .toList();//需要修改的数据的id

        String idListStr = ids.stream()
                .map(id -> "'" + id + "'")
                .reduce((a, b) -> a + "," + b)
                .orElse("");

        String sql = String.format("UPDATE %s SET %s = '%s' WHERE id IN (%s)",
                baseTable, field, value, idListStr);
        // 执行 SQL
        this.baseMapper.updateBySql(sql);
    }
}
