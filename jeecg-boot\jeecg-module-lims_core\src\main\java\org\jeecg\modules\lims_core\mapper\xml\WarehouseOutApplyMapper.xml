<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.WarehouseOutApplyMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.WarehouseOutApplyVo">
        select * from (  SELECT i.*,COALESCE(s.name, c.name, sm.name) AS goodsname,sm.opener_id as openerId,sm.open_date AS openDate,sm.confirm_valid_date AS confirmValidDate,
                                sm.lot_No as lotno ,CONCAT(COALESCE(sm.spec, ''), '', COALESCE(su.unit_name, '')) AS spec,u.realname,s.rd_id
                         FROM     warehouse_out_apply i
                                      LEFT JOIN sample s ON i.article_no = s.sample_no
                                      LEFT JOIN consumptive c ON i.article_no = c.code
                                      LEFT JOIN standard_material sm ON i.article_no = sm.code
                                      LEFT JOIN sys_unit su on sm.spec_Unit=su.id
                                      LEFT JOIN Sys_user u on i.create_by=u.username
                         ) t
            ${ew.customSqlSegment}
    </select>
</mapper>