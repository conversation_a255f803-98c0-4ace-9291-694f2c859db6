package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.dto.MethodNeedMakeupModel;
import org.jeecg.modules.lims_core.entity.TestSolution;
import org.jeecg.modules.lims_core.mapper.TestSolutionMapper;
import org.jeecg.modules.lims_core.service.ITestSolutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 溶液使用记录
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
@Service
public class TestSolutionServiceImpl extends ServiceImpl<TestSolutionMapper, TestSolution> implements ITestSolutionService {
    @Autowired
    private TestSolutionMapper testSolutionMapper;

    @Override
    public List<MethodNeedMakeupModel> selectMethodNeedMakeupByTestId(String testId) {
        return testSolutionMapper.selectMethodNeedMakeupByTestId(testId);
    }
}
