package org.jeecg.modules.dcs.util;

import jakarta.servlet.http.HttpServletRequest;
import org.jeecg.common.system.query.QueryCondition;
import org.jeecg.common.system.query.QueryRuleEnum;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class QueryConditionUtils {

    /**
     * 动态解析查询条件
     *
     * @param request        HttpServletRequest 对象
     * @param customRuleMap  自定义规则映射
     * @return 查询条件列表
     */
    public static List<QueryCondition> parseConditions(HttpServletRequest request,
                                                       Map<String, QueryRuleEnum> customRuleMap) {
        List<QueryCondition> conditions = new ArrayList<>();

        request.getParameterMap().forEach((key, values) -> {
            if (!key.equals("_t") && !key.equals("column") && (!key.equals("order")) && (!key.equals("pageNo")) &&  (!key.equals("pageSize")) && values.length > 0 && values[0] != null && !values[0].isEmpty()) {
                String value = values[0];

                // 如果 customRuleMap 中有规则，使用它；否则使用默认的 EQ 规则
                QueryRuleEnum rule = customRuleMap.getOrDefault(key, QueryRuleEnum.EQ);

                // 生成查询条件，假设前端传递的参数名和数据库字段一致
                conditions.add(new QueryCondition(key, null, null, rule.getValue(), value));
            }
        });

        return conditions;
    }
}
