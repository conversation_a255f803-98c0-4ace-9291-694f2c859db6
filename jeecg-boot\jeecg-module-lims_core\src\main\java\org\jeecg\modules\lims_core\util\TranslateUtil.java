package org.jeecg.modules.lims_core.util;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 腾讯云翻译工具类
 */
public class TranslateUtil {

    private static final Logger logger = LoggerFactory.getLogger(TranslateUtil.class);

    // 腾讯云API密钥配置
    private static final String SECRET_ID = "AKIDw0a1EDyCF5h5GDxUowrX0HLAmBNOLKLJ";
    private static final String SECRET_KEY = "tY2LQIwMYXIp8PaejhT2QaWxHOJ0Tq21";
    private static final String REGION = "ap-guangzhou"; // 默认区域

    /**
     * 文本翻译方法
     *
     * @param sourceText 源文本
     * @param sourceLanguage 源语言，如"zh"(中文)、"en"(英文)、"auto"(自动检测)
     * @param targetLanguage 目标语言，如"zh"(中文)、"en"(英文)
     * @return 翻译后的文本
     */
    public static String translate(String sourceText, String sourceLanguage, String targetLanguage) {
        if (sourceText == null || sourceText.trim().isEmpty()) {
            return "";
        }

        try {
            // 实例化一个认证对象
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);

            // 实例化一个HTTP选项
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("tmt.tencentcloudapi.com");

            // 实例化一个客户端配置对象
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 实例化要请求产品的client对象
            TmtClient client = new TmtClient(cred, REGION, clientProfile);

            // 实例化一个请求对象
            TextTranslateRequest req = new TextTranslateRequest();
            req.setSourceText(sourceText);
            req.setSource(sourceLanguage);
            req.setTarget(targetLanguage);
            req.setProjectId(0L); // 项目ID，可根据实际情况设置

            // 返回的resp是一个TextTranslateResponse的实例，与请求对象对应
            TextTranslateResponse resp = client.TextTranslate(req);

            // 返回翻译结果
            return resp.getTargetText();

        } catch (TencentCloudSDKException e) {
            logger.error("翻译失败: " + e.getMessage(), e);
            return sourceText; // 翻译失败时返回原文
        }
    }

    /**
     * 将文本翻译为中文
     *
     * @param sourceText 源文本
     * @return 翻译后的中文文本
     */
    public static String translateToChinese(String sourceText) {
        return translate(sourceText, "auto", "zh");
    }

    /**
     * 将文本翻译为英文
     *
     * @param sourceText 源文本
     * @return 翻译后的英文文本
     */
    public static String translateToEnglish(String sourceText) {
        return translate(sourceText, "zh", "en");
    }

    /**
     * 中英互译（自动检测语言并翻译）
     *
     * @param text 需要翻译的文本
     * @return 如果是中文则翻译为英文，如果是英文则翻译为中文
     */
    public static String translateBetweenChineseAndEnglish(String text) {
        // 翻译为中文，如果原文是英文，结果会变成中文；如果原文是中文，则会保持不变
        String resultZh = translate(text, "auto", "zh");

        // 如果翻译结果与原文相同，说明原文可能是中文，需要翻译为英文
        if (resultZh.equals(text)) {
            return translate(text, "zh", "en");
        } else {
            return resultZh;
        }
    }
}