package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.WarehouseInOut;
import org.jeecg.modules.lims_core.service.IWarehouseInOutService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.IWarehouseOutApplyService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 出入库记录
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Tag(name="出入库记录")
@RestController
@RequestMapping("/lims_core/warehouseInOut")
@Slf4j
public class WarehouseInOutController extends JeecgController<WarehouseInOut, IWarehouseInOutService> {
	@Autowired
	private IWarehouseInOutService warehouseInOutService;
	 @Autowired
	 private ISysBaseAPI iSysBaseAPI;
	 @Autowired
	 private IWarehouseOutApplyService warehouseOutApplyService;
	/**
	 * 分页列表查询
	 *
	 * @param warehouseInOut
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "出入库记录-分页列表查询")
	@Operation(summary="出入库记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WarehouseInOut>> queryPageList(WarehouseInOut warehouseInOut,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<WarehouseInOut> queryWrapper = QueryGenerator.initQueryWrapper(warehouseInOut, req.getParameterMap());
		Page<WarehouseInOut> page = new Page<WarehouseInOut>(pageNo, pageSize);
		IPage<WarehouseInOut> pageList = warehouseInOutService.queryPageList(page, queryWrapper);
		pageList.getRecords().stream().forEach(item -> {
			item.setDeptName(String.join(",", iSysBaseAPI.getDepartNamesByUsername(item.getCreateBy())));
		});

		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param warehouseInOut
	 * @return
	 */
	@AutoLog(value = "出入库记录-添加")
	@Operation(summary="出入库记录-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_out:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WarehouseInOut warehouseInOut) {
		warehouseInOutService.save(warehouseInOut);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param warehouseInOut
	 * @return
	 */
	@AutoLog(value = "出入库记录-编辑")
	@Operation(summary="出入库记录-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_out:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WarehouseInOut warehouseInOut) {
		warehouseInOutService.updateById(warehouseInOut);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "出入库记录-通过id删除")
	@Operation(summary="出入库记录-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_out:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		warehouseInOutService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "出入库记录-批量删除")
	@Operation(summary="出入库记录-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_out:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.warehouseInOutService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "出入库记录-通过id查询")
	@Operation(summary="出入库记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WarehouseInOut> queryById(@RequestParam(name="id",required=true) String id) {
		WarehouseInOut warehouseInOut = warehouseInOutService.getById(id);
		if(warehouseInOut==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(warehouseInOut);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param warehouseInOut
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_out:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WarehouseInOut warehouseInOut) {
        return super.exportXls(request, warehouseInOut, WarehouseInOut.class, "出入库记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_out:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WarehouseInOut.class);
    }

}
