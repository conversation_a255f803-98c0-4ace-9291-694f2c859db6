package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 报告
 * @Author: jeecg-boot
 * @Date:   2025-05-21
 * @Version: V1.0
 */
@Data
@TableName("report")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="报告")
public class Report implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**报告编号*/
	@Excel(name = "报告编号", width = 15)
    @Schema(description = "报告编号")
    private String reportNo;
	/**合同*/
	@Excel(name = "合同", width = 15, dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
	@Dict(dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
    @Schema(description = "合同")
    private String orderId;
	/**样品*/
	@Excel(name = "样品", width = 15, dictTable = "sample", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sample", dicText = "name", dicCode = "id")
    @Schema(description = "样品")
    private String sampleId;
	/**测试任务*/
	@Excel(name = "测试任务", width = 15, dictTable = "test", dicText = "id", dicCode = "id")
	@Dict(dictTable = "test", dicText = "id", dicCode = "id")
    @Schema(description = "测试任务")
    private String testTaskIds;
	/**重复周期*/
	@Excel(name = "重复周期", width = 15, dictTable = "sys_method_repeat_type", dicText = "cycle_name", dicCode = "id")
	@Dict(dictTable = "sys_method_repeat_type", dicText = "cycle_name", dicCode = "id")
    @Schema(description = "重复周期")
    private String repeatTypeId;
	/**模板*/
	@Excel(name = "模板", width = 15, dictTable = "sys_template", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_template", dicText = "name", dicCode = "id")
    @Schema(description = "模板")
    private String templateId;
	/**到期日*/
	@Excel(name = "到期日", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "到期日")
    private Date deadLine;
	/**草稿*/
	@Excel(name = "草稿", width = 15)
    @Schema(description = "草稿")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String url;
	/**编制人*/
    @Schema(description = "编制人")
    private String createBy;
	/**编制日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "编制日期")
    private Date createTime;
	/**审核人*/
    @Schema(description = "审核人")
    private String updateBy;
	/**审核日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审核日期")
    private Date updateTime;
	/**签发人*/
    @Excel(name = "签发人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "签发人")
    private String signBy;
	/**签发日期*/
	@Excel(name = "签发日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "签发日期")
    private Date signTime;
	/**电子报告*/
	@Excel(name = "电子报告", width = 15)
    @Schema(description = "电子报告")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String finalUrl;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**结论*/
	@Excel(name = "结论", width = 15)
    @Schema(description = "结论")
    private String conclusion;
	/**附注*/
	@Excel(name = "附注", width = 15)
    @Schema(description = "附注")
    private String remark;
	/**附加说明*/
	@Excel(name = "附加说明", width = 15)
    @Schema(description = "附加说明")
    private String description;
    /**审核人*/
    @Schema(description = "审核人")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @Excel(name = "审核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String approveBy;
    /**审核时间*/
    @Excel(name = "审核时间", width = 15)
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @Schema(description = "审核时间")
    private Date approveTime;
    /**已发送*/
    @Excel(name = "已发送", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "已发送")
    private java.lang.String isSent;

    /**编制人*/
    @Excel(name = "编制人", width = 15)
    @Schema(description = "编制人")
    private java.lang.String makeBy;
    /**编制时间*/
    @Excel(name = "编制时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "编制时间")
    private java.util.Date makeTime;
    /**版本*/
    @Excel(name = "版本", width = 15)
    @Schema(description = "版本")
    private java.lang.String version;
    /**版本修订*/
    @Excel(name = "版本修订", width = 15)
    @Schema(description = "版本修订")
    private java.lang.String reviseMemo;
    /**取代的报告id*/
    @Excel(name = "取代的报告id", width = 15)
    @Schema(description = "取代的报告id")
    private java.lang.String supercededId;
}
