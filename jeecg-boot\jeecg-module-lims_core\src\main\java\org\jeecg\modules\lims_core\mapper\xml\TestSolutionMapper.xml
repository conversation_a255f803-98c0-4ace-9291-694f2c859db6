<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.TestSolutionMapper">

    <select id="selectMethodNeedMakeupByTestId" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.dto.MethodNeedMakeupModel">
        SELECT 'sys_method_consumptive' AS sourceTable, syc.id,method_id AS methodId,makeup_req AS makeupReq,
               solution_type_id as solutionTypeId,item_text AS solutionType
        FROM sys_method_consumptive syc LEFT JOIN sys_dict_item sdi on syc.solution_type_id=sdi.item_value
            and sdi.dict_id=1889929518796984321
        WHERE method_id = (SELECT method_id FROM test_task WHERE id = (select task_id from test where id = #{testId} LIMIT 1) LIMIT 1)
          AND need_makeup = 1
        UNION ALL
        SELECT 'sys_method_std_material' AS sourceTable,ssm.id,method_id AS methodId,makeup_req AS makeupReq,
               solution_type_id as solutionTypeId,item_text AS solutionType
        FROM sys_method_std_material  ssm LEFT JOIN sys_dict_item sdi on ssm.solution_type_id=sdi.item_value
            and sdi.dict_id=1889929518796984321
        WHERE method_id = (SELECT method_id FROM test_task WHERE id = (select task_id from test where id = #{testId} LIMIT 1) LIMIT 1)
          AND need_makeup = 1;
    </select>
</mapper>