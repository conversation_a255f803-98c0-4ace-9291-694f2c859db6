<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crm.mapper.SysCustomerContactMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  sys_customer_contact 
		WHERE
			 customer_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.crm.entity.SysCustomerContact">
		SELECT * 
		FROM  sys_customer_contact
		WHERE
			 customer_id = #{mainId} 	</select>
</mapper>
