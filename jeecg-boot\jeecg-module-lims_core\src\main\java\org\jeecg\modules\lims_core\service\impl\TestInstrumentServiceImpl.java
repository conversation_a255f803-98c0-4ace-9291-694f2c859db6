package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.TestInstrument;
import org.jeecg.modules.lims_core.mapper.TestInstrumentMapper;
import org.jeecg.modules.lims_core.service.ITestInstrumentService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 仪器使用记录
 * @Author: jeecg-boot
 * @Date:   2025-02-13
 * @Version: V1.0
 */
@Service
public class TestInstrumentServiceImpl extends ServiceImpl<TestInstrumentMapper, TestInstrument> implements ITestInstrumentService {

}
