package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "SysAnalyte",drillDown = "sys_method_analyte.analyte_id",description = "参数信息")
public class SysAnalyteVO {
    @TemplateField(description = "编号")
    private String id;
    @TemplateField(description = "参数code")
    private String code;
    @TemplateField(description = "参数名称")
    private String name;

}