package org.jeecg.modules.lims_core.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.service.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.WarehouseInApplyVo;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 余物退回申请
 * @Author: jeecg-boot
 * @Date:   2025-05-27
 * @Version: V1.0
 */
@Tag(name="余物退回申请")
@RestController
@RequestMapping("/lims_core/warehouseInApply")
@Slf4j
public class WarehouseInApplyController extends JeecgController<WarehouseInApply, IWarehouseInApplyService> {
	@Autowired
	private IWarehouseInApplyService warehouseInApplyService;
	 @Autowired
	 private ISampleService sampleService;
	 @Autowired
	 private IConsumptiveService consumptiveService;
	 @Autowired
	 private IStandardMaterialService standardMaterialService;
	 @Autowired
	 private IWarehouseOutApplyService warehouseOutApplyService;

	 private static final String OUT_IN_APPLY_STATUS_1 = "申请中";
	 private static final String OUT_IN_APPLY_STATUS_2 = "已领用";
	 private static final String OUT_IN_APPLY_STATUS_3 = "余量退回中";
	 private static final String OUT_IN_APPLY_STATUS_4 = "余量已退回";
	 private static final String OUT_IN_APPLY_STATUS_5 = "退回-数据有误-仓库拒绝";

	 /**
	 * 分页列表查询
	 *
	 * @param warehouseInApply
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "余物退回申请-分页列表查询")
	@Operation(summary="余物退回申请-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WarehouseInApplyVo>> queryPageList(WarehouseInApplyVo warehouseInApply,
														   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														   HttpServletRequest req) {
        QueryWrapper<WarehouseInApplyVo> queryWrapper = QueryGenerator.initQueryWrapper(warehouseInApply, req.getParameterMap());
		Page<WarehouseInApplyVo> page = new Page<WarehouseInApplyVo>(pageNo, pageSize);
		IPage<WarehouseInApplyVo> pageList = warehouseInApplyService.queryPageList(page, queryWrapper);
		pageList.getRecords().stream().forEach(item -> {
			item.setWarehouseboxid(warehouseOutApplyService.getwarehouseboxidBycode(item.getArticleNo()));
			item.setWarehousebox(warehouseOutApplyService.getwarehouseboxBycode(item.getArticleNo()));
		});
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param warehouseInApply
	 * @return
	 */
	@AutoLog(value = "余物退回申请-添加")
	@Operation(summary="余物退回申请-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_apply:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WarehouseInApply warehouseInApply) throws WxErrorException {
		String[] articleNos = warehouseInApply.getArticleNo().split(",");
		String[] amounts = warehouseInApply.getAmount().split(",");
		String[] ids = warehouseInApply.getId().split(",");
		String[] articleTypeIdS = warehouseInApply.getArticleTypeId().split(",");
		LoginUser sysUser = SecureUtil.currentUser();
		if (articleNos.length != amounts.length || articleTypeIdS.length!= articleNos.length) {
			return Result.error("编号和数量不对应,对应数量需要用英文逗号隔开");
		}
		for (int i = 0; i < articleNos.length; i++) {
			String articleNo = articleNos[i].trim();
			String amount = amounts[i].trim();
			String articleTypeId = articleTypeIdS[i].trim();
			String id = ids[i].trim();
			if (articleNo.isEmpty() || amount.isEmpty()) {
				continue;
			}
			LambdaQueryWrapper<WarehouseInApply> Wrapper = new LambdaQueryWrapper<>();
			Wrapper.and(item->item.eq(WarehouseInApply::getArticleNo,articleNo).
					eq(WarehouseInApply::getCreateBy,sysUser.getUsername()).eq(WarehouseInApply::getStatus,"余量退回中"));
			List<WarehouseInApply> list = warehouseInApplyService.list(Wrapper);
			if (!list.isEmpty()) {
				return Result.error("该物品已在退回中");
			}
			WarehouseInApply  warehouseIn = new WarehouseInApply();
			BeanUtils.copyProperties(warehouseInApply, warehouseIn);
			warehouseIn.setArticleNo(articleNo);
			warehouseIn.setArticleTypeId(articleTypeId);
			if (articleNo.startsWith("YPT")|| articleNo.startsWith("BCC") || articleNo.startsWith("GBP")|| articleNo.startsWith("QCT")){//样品
				LambdaQueryWrapper<Sample> sampleWrapper = new LambdaQueryWrapper<>();
				sampleWrapper.and(item->item.eq(Sample::getSampleNo,articleNo));
				Sample sample = sampleService.getOne(sampleWrapper);
				warehouseIn.setUnitId(sample.getReceiveCountUnit());
			}
			else if(articleNo.startsWith("FZB")){
				LambdaQueryWrapper<Consumptive> consumptiveWrapper = new LambdaQueryWrapper<>();
				consumptiveWrapper.and(item->item.eq(Consumptive::getCode,articleNo));
				Consumptive consumptive = consumptiveService.getOne(consumptiveWrapper);
				warehouseIn.setUnitId(consumptive.getUnit());
			}
			else if(articleNo.startsWith("GBT")){
				LoginUser curUser = SecureUtil.currentUser();
				LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
				standardMaterialWrapper.and(item->item.eq(StandardMaterial::getCode,articleNo));
				StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);
				warehouseIn.setUnitId(standardMaterial.getSpecUnit());
				//回写StandardMaterial 的开启人，开启时间，确认效期
				//开启人不为空，则回写
				if (oConvertUtils.isNotEmpty(warehouseInApply.getOpenerId()) && !oConvertUtils.isNotEmpty(standardMaterial.getOpenerId())){
					standardMaterial.setOpenerId(curUser.getRealname());
				}
				if (oConvertUtils.isNotEmpty(warehouseIn.getOpenDate()) && !oConvertUtils.isNotEmpty(standardMaterial.getOpenDate())){
					standardMaterial.setOpenDate(warehouseIn.getOpenDate());
				}
				if (oConvertUtils.isNotEmpty(warehouseIn.getConfirmValidDate()) && !oConvertUtils.isNotEmpty(standardMaterial.getConfirmValidDate())){
					standardMaterial.setConfirmValidDate(warehouseIn.getConfirmValidDate());
				}
				standardMaterialService.updateById(standardMaterial);
			}
			WarehouseOutApply warehouseOutAppl = warehouseOutApplyService.getById(id);
			warehouseIn.setStatus(OUT_IN_APPLY_STATUS_3);
			if (articleNo.startsWith("YPT")|| articleNo.startsWith("BCC") || articleNo.startsWith("GBP")|| articleNo.startsWith("QCT")){//样品
				warehouseIn.setAmount(amount);//样品直接填退回数量
			}else {
				BigDecimal result = new BigDecimal(warehouseOutAppl.getAmount())
						.subtract(new BigDecimal(amount));
				warehouseIn.setUsageQuantity(amount);//记录使用数量
				warehouseIn.setAmount(result.toString());//自动用领用的数量减去 用户使用的数量，得到最后应该入库的数量
				if (result.compareTo(BigDecimal.ZERO) <= 0) {
					return Result.error("已使用完了，不需要退回！");
				}
			}
			warehouseIn.setId("");
			warehouseInApplyService.save(warehouseIn);
			//回写outapply数据
			warehouseOutAppl.setReturnId(warehouseIn.getId());
			warehouseOutApplyService.updateById(warehouseOutAppl);
			//发起入库申请
			warehouseInApplyService.apply(warehouseIn,ApplyType.WAREHOUSE_APPLY_IN);
		}
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param warehouseInApply
	 * @return
	 */
	@AutoLog(value = "余物退回申请-编辑")
	@Operation(summary="余物退回申请-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_apply:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WarehouseInApply warehouseInApply) {
		warehouseInApplyService.updateById(warehouseInApply);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "余物退回申请-通过id删除")
	@Operation(summary="余物退回申请-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_apply:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		warehouseInApplyService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "余物退回申请-批量删除")
	@Operation(summary="余物退回申请-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_apply:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.warehouseInApplyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "余物退回申请-通过id查询")
	@Operation(summary="余物退回申请-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WarehouseInApply> queryById(@RequestParam(name="id",required=true) String id) {
		WarehouseInApply warehouseInApply = warehouseInApplyService.getById(id);
		if(warehouseInApply==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(warehouseInApply);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param warehouseInApply
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_apply:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WarehouseInApply warehouseInApply) {
        return super.exportXls(request, warehouseInApply, WarehouseInApply.class, "余物退回申请");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_in_apply:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WarehouseInApply.class);
    }

	 /**
	  *  仓库入库，可能原因是申请数量问题之类
	  *
	  * @param warehouseInApply
	  * @return
	  */
	 @AutoLog(value = "入库申请-编辑")
	 @Operation(summary="入库申请-编辑")
	 @RequestMapping(value = "/updatestatus", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> updatestatus(@RequestBody WarehouseInApply warehouseInApply) {
		 warehouseInApplyService.update()
				 .set("status", OUT_IN_APPLY_STATUS_5)
				 .eq("id",warehouseInApply.getId())
				 .update();
		 return Result.OK("编辑成功!");
	 }

}
