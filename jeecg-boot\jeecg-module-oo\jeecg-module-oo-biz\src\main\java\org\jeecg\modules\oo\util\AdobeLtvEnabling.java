package org.jeecg.modules.oo.util;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.cert.CRLException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509CRL;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1Enumerated;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.ASN1OctetString;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.ASN1TaggedObject;
import org.bouncycastle.asn1.DEROctetString;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.asn1.DERTaggedObject;
import org.bouncycastle.asn1.ocsp.BasicOCSPResponse;
import org.bouncycastle.asn1.ocsp.OCSPObjectIdentifiers;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cert.ocsp.BasicOCSPResp;
import org.bouncycastle.cert.ocsp.OCSPException;
import org.bouncycastle.cert.ocsp.OCSPResp;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.provider.X509CertParser;
import org.bouncycastle.operator.ContentVerifierProvider;
import org.bouncycastle.operator.OperatorCreationException;
import org.bouncycastle.operator.OperatorException;
import org.bouncycastle.operator.jcajce.JcaContentVerifierProviderBuilder;
import org.bouncycastle.x509.util.StreamParsingException;

import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.source.ByteBuffer;
import com.itextpdf.io.util.StreamUtil;
import com.itextpdf.kernel.exceptions.PdfException;
import com.itextpdf.kernel.pdf.CompressionConstants;
import com.itextpdf.kernel.pdf.PdfArray;
import com.itextpdf.kernel.pdf.PdfCatalog;
import com.itextpdf.kernel.pdf.PdfDate;
import com.itextpdf.kernel.pdf.PdfDeveloperExtension;
import com.itextpdf.kernel.pdf.PdfDictionary;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfName;
import com.itextpdf.kernel.pdf.PdfStream;
import com.itextpdf.kernel.pdf.PdfString;
import com.itextpdf.kernel.pdf.PdfVersion;
import com.itextpdf.signatures.ICrlClient;
import com.itextpdf.signatures.IOcspClient;
import com.itextpdf.signatures.PdfPKCS7;
import com.itextpdf.signatures.PdfSignature;
import com.itextpdf.signatures.SignatureUtil;
import com.itextpdf.signatures.exceptions.SignExceptionMessageConstant;

/**
 * <a href="https://stackoverflow.com/questions/51370965/how-can-i-add-pades-ltv-using-itext">
 * how can I add PAdES-LTV using itext
 * </a>
 * <br/>
 * <a href="https://stackoverflow.com/questions/51639464/itext7-ltvverification-addverification-not-enabling-ltv">
 * iText7 LtvVerification.addVerification not enabling LTV
 * </a>
 * <p>
 * This class adds LTV information to a signed PDF to make it LTV enabled
 * as reported by Adobe Acrobat.
 * </p>
 * <p>
 * It has originally been written for iText 5 in the context of the former
 * question. In the context of the latter one it has been ported to iText 7.
 * As a side effect some iText-5-isms may be contained in this code.
 * </p>
 *
 * <AUTHOR>
 */
public class AdobeLtvEnabling {
    /**
     * Use this constructor with a {@link PdfDocument} in append mode. Otherwise
     * the existing signatures will be damaged.
     */
    public AdobeLtvEnabling(PdfDocument pdfDocument) {
        this.pdfDocument = pdfDocument;
    }

    /**
     * Call this method to have LTV information added to the {@link PdfDocument}
     * given in the constructor.
     */
    public void enable(IOcspClient ocspClient, ICrlClient crlClient) throws OperatorException, GeneralSecurityException, IOException, StreamParsingException, OCSPException {
        SignatureUtil signatureUtil = new SignatureUtil(pdfDocument);

        List<String> names = signatureUtil.getSignatureNames();
        for (String name : names) {
            PdfPKCS7 pdfPKCS7 = signatureUtil.readSignatureData(name, BouncyCastleProvider.PROVIDER_NAME);
            PdfSignature sig = signatureUtil.getSignature(name);
            List<X509Certificate> certificatesToCheck = new ArrayList<>();
            certificatesToCheck.add(pdfPKCS7.getSigningCertificate());
            while (!certificatesToCheck.isEmpty()) {
                X509Certificate certificate = certificatesToCheck.remove(0);
                addLtvForChain(certificate, ocspClient, crlClient, getSignatureHashKey(sig));
            }
        }

        outputDss();
    }

    //
    // the actual LTV enabling methods
    //
    void addLtvForChain(X509Certificate certificate, IOcspClient ocspClient, ICrlClient crlClient, PdfName key) throws GeneralSecurityException, IOException, StreamParsingException, OperatorCreationException, OCSPException {
        ValidationData validationData = new ValidationData();

        while (certificate != null) {
            System.out.println(certificate.getSubjectX500Principal().getName());
            X509Certificate issuer = getIssuerCertificate(certificate);
            validationData.certs.add(certificate.getEncoded());
            byte[] ocspResponse = ocspClient.getEncoded(certificate, issuer, null);
            if (ocspResponse != null) {
                System.out.println("  with OCSP response");
                validationData.ocsps.add(ocspResponse);
                X509Certificate ocspSigner = getOcspSignerCertificate(ocspResponse);
                if (ocspSigner != null) {
                    System.out.printf("  signed by %s\n", ocspSigner.getSubjectX500Principal().getName());
                }
                addLtvForChain(ocspSigner, ocspClient, crlClient, getOcspHashKey(ocspResponse));
            } else {
                Collection<byte[]> crl = crlClient.getEncoded(certificate, null);
                if (crl != null && !crl.isEmpty()) {
                    System.out.printf("  with %s CRLs\n", crl.size());
                    validationData.crls.addAll(crl);
                    for (byte[] crlBytes : crl) {
                        addLtvForChain(null, ocspClient, crlClient, getCrlHashKey(crlBytes));
                    }
                }
            }
            certificate = issuer;
        }

        validated.put(key, validationData);
    }

    void outputDss() throws IOException {
        PdfDictionary dss = new PdfDictionary();
        PdfDictionary vrim = new PdfDictionary();
        PdfArray ocsps = new PdfArray();
        PdfArray crls = new PdfArray();
        PdfArray certs = new PdfArray();

        PdfCatalog catalog = pdfDocument.getCatalog();
        if (pdfDocument.getPdfVersion().compareTo(PdfVersion.PDF_2_0) < 0) {
            catalog.addDeveloperExtension(PdfDeveloperExtension.ESIC_1_7_EXTENSIONLEVEL5);
            catalog.addDeveloperExtension(new PdfDeveloperExtension(PdfName.ADBE, new PdfName("1.7"), 8));
        }

        for (PdfName vkey : validated.keySet()) {
            PdfArray ocsp = new PdfArray();
            PdfArray crl = new PdfArray();
            PdfArray cert = new PdfArray();
            PdfDictionary vri = new PdfDictionary();
            for (byte[] b : validated.get(vkey).crls) {
                PdfStream ps = new PdfStream(b);
                ps.setCompressionLevel(CompressionConstants.DEFAULT_COMPRESSION);
                ps.makeIndirect(pdfDocument);
                crl.add(ps);
                crls.add(ps);
                crls.setModified();
            }
            for (byte[] b : validated.get(vkey).ocsps) {
                b = buildOCSPResponse(b);
                PdfStream ps = new PdfStream(b);
                ps.setCompressionLevel(CompressionConstants.DEFAULT_COMPRESSION);
                ps.makeIndirect(pdfDocument);
                ocsp.add(ps);
                ocsps.add(ps);
                ocsps.setModified();
            }
            for (byte[] b : validated.get(vkey).certs) {
                PdfStream ps = new PdfStream(b);
                ps.setCompressionLevel(CompressionConstants.DEFAULT_COMPRESSION);
                ps.makeIndirect(pdfDocument);
                cert.add(ps);
                certs.add(ps);
                certs.setModified();
            }
            if (ocsp.size() > 0) {
                ocsp.makeIndirect(pdfDocument);
                vri.put(PdfName.OCSP, ocsp);
            }
            if (crl.size() > 0) {
                crl.makeIndirect(pdfDocument);
                vri.put(PdfName.CRL, crl);
            }
            if (cert.size() > 0) {
                cert.makeIndirect(pdfDocument);
                vri.put(PdfName.Cert, cert);
            }
            vri.put(PdfName.TU, new PdfDate().getPdfObject());
            vri.makeIndirect(pdfDocument);
            vrim.put(vkey, vri);
        }
        vrim.makeIndirect(pdfDocument);
        vrim.setModified();
        dss.put(PdfName.VRI, vrim);
        if (ocsps.size() > 0) {
            ocsps.makeIndirect(pdfDocument);
            dss.put(PdfName.OCSPs, ocsps);
        }
        if (crls.size() > 0) {
            crls.makeIndirect(pdfDocument);
            dss.put(PdfName.CRLs, crls);
        }
        if (certs.size() > 0) {
            certs.makeIndirect(pdfDocument);
            dss.put(PdfName.Certs, certs);
        }

        dss.makeIndirect(pdfDocument);
        dss.setModified();
        catalog.put(PdfName.DSS, dss);
    }

    //
    // VRI signature hash key calculation
    //
    static PdfName getCrlHashKey(byte[] crlBytes) throws NoSuchAlgorithmException, IOException, CRLException, CertificateException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        X509CRL crl = (X509CRL)cf.generateCRL(new ByteArrayInputStream(crlBytes));
        byte[] signatureBytes = crl.getSignature();
        DEROctetString octetString = new DEROctetString(signatureBytes);
        byte[] octetBytes = octetString.getEncoded();
        byte[] octetHash = hashBytesSha1(octetBytes);
        PdfName octetName = new PdfName(convertToHex(octetHash));
        return octetName;
    }

    static PdfName getOcspHashKey(byte[] basicResponseBytes) throws NoSuchAlgorithmException, IOException {
        BasicOCSPResponse basicResponse = parseBasicOcspResponse(basicResponseBytes);
        if (basicResponse == null) {
            System.err.println("无法解析BasicOCSPResponse用于计算哈希，使用字节数组哈希");
            // 如果无法解析，直接使用字节数组计算哈希
            byte[] hash = hashBytesSha1(basicResponseBytes);
            return new PdfName(convertToHex(hash));
        }
        byte[] signatureBytes = basicResponse.getSignature().getBytes();
        DEROctetString octetString = new DEROctetString(signatureBytes);
        byte[] octetBytes = octetString.getEncoded();
        byte[] octetHash = hashBytesSha1(octetBytes);
        PdfName octetName = new PdfName(convertToHex(octetHash));
        return octetName;
    }

    static PdfName getSignatureHashKey(PdfSignature sig) throws NoSuchAlgorithmException, IOException {
        PdfString contents = sig.getContents();
        byte[] bc = PdfEncodings.convertToBytes(contents.getValue(), null);
        /*
        if (PdfName.ETSI_RFC3161.equals(sig.getSubFilter())) {
            try (   ASN1InputStream din = new ASN1InputStream(new ByteArrayInputStream(bc)) ) {
                ASN1Primitive pkcs = din.readObject();
                bc = pkcs.getEncoded();
            }
        }
        */
        byte[] bt = hashBytesSha1(bc);
        return new PdfName(convertToHex(bt));
    }

    static byte[] hashBytesSha1(byte[] b) throws NoSuchAlgorithmException {
        MessageDigest sh = MessageDigest.getInstance("SHA1");
        return sh.digest(b);
    }

    static String convertToHex(byte[] bytes) {
        ByteBuffer buf = new ByteBuffer();
        for (byte b : bytes) {
            buf.appendHex(b);
        }
        return PdfEncodings.convertToString(buf.toByteArray(), null).toUpperCase();
    }

    //
    // OCSP response helpers
    //
    
    /**
     * 增强的BasicOCSPResponse解析方法，处理包含ASN1Enumerated的响应
     * @param ocspResponseBytes OCSP响应字节数组
     * @return BasicOCSPResponse对象，如果解析失败返回null
     */
    private static BasicOCSPResponse parseBasicOcspResponse(byte[] ocspResponseBytes) {
        if (ocspResponseBytes == null || ocspResponseBytes.length == 0) {
            return null;
        }
        
        // 策略1: 直接使用BasicOCSPResponse.getInstance()
        try {
            return BasicOCSPResponse.getInstance(ocspResponseBytes);
        } catch (Exception e) {
            System.err.println("  直接解析BasicOCSPResponse失败: " + e.getMessage());
        }
        
        // 策略2: 通过OCSPResp解析
        try {
            OCSPResp ocspResp = new OCSPResp(ocspResponseBytes);
            if (ocspResp.getResponseObject() instanceof BasicOCSPResp) {
                BasicOCSPResp basicResp = (BasicOCSPResp) ocspResp.getResponseObject();
                byte[] basicRespBytes = basicResp.getEncoded();
                return BasicOCSPResponse.getInstance(basicRespBytes);
            }
        } catch (Exception e) {
            System.err.println("  通过OCSPResp解析失败: " + e.getMessage());
        }
        
        // 策略3: 手动解析ASN.1结构，跳过ASN1Enumerated
        try {
            ASN1InputStream asn1InputStream = new ASN1InputStream(ocspResponseBytes);
            ASN1Primitive asn1Primitive = asn1InputStream.readObject();
            asn1InputStream.close();
            
            if (asn1Primitive instanceof ASN1Sequence) {
                ASN1Sequence sequence = (ASN1Sequence) asn1Primitive;
                ASN1EncodableVector vector = new ASN1EncodableVector();
                
                for (int i = 0; i < sequence.size(); i++) {
                    ASN1Primitive element = sequence.getObjectAt(i).toASN1Primitive();
                    if (element instanceof ASN1Enumerated) {
                        System.out.println("  跳过ASN1Enumerated元素: " + element);
                        continue; // 跳过ASN1Enumerated元素
                    }
                    vector.add(element);
                }
                
                if (vector.size() > 0) {
                    DERSequence newSequence = new DERSequence(vector);
                    return BasicOCSPResponse.getInstance(newSequence);
                }
            }
        } catch (Exception e) {
            System.err.println("  手动ASN.1解析失败: " + e.getMessage());
        }
        
        // 策略4: 字节级清理，移除ASN1Enumerated标记
        try {
            byte[] cleanedBytes = removeAsn1EnumeratedFromBytes(ocspResponseBytes);
            if (cleanedBytes != null && cleanedBytes.length != ocspResponseBytes.length) {
                return BasicOCSPResponse.getInstance(cleanedBytes);
            }
        } catch (Exception e) {
            System.err.println("  字节级清理解析失败: " + e.getMessage());
        }
        
        System.err.println("  所有BasicOCSPResponse解析方法都失败");
        return null;
    }
    
    /**
     * 从字节数组中移除ASN1Enumerated标记
     */
    private static byte[] removeAsn1EnumeratedFromBytes(byte[] bytes) {
        if (bytes == null || bytes.length < 2) {
            return bytes;
        }
        
        try {
            // ASN1Enumerated的标记是0x0A
            // 这是一个简化的实现，实际情况可能更复杂
            for (int i = 0; i < bytes.length - 1; i++) {
                if (bytes[i] == 0x0A) { // ASN1Enumerated标记
                    // 找到长度字节
                    int length = bytes[i + 1] & 0xFF;
                    if (i + 1 + length < bytes.length) {
                        // 创建新的字节数组，跳过ASN1Enumerated部分
                        byte[] result = new byte[bytes.length - (2 + length)];
                        System.arraycopy(bytes, 0, result, 0, i);
                        System.arraycopy(bytes, i + 2 + length, result, i, bytes.length - i - 2 - length);
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("字节级ASN1Enumerated移除失败: " + e.getMessage());
        }
        
        return bytes;
    }
    
    static X509Certificate getOcspSignerCertificate(byte[] basicResponseBytes) throws CertificateException, OCSPException, OperatorCreationException {
        JcaX509CertificateConverter converter = new JcaX509CertificateConverter().setProvider(BouncyCastleProvider.PROVIDER_NAME);
        BasicOCSPResponse borRaw = parseBasicOcspResponse(basicResponseBytes);
        if (borRaw == null) {
            System.err.println("无法解析BasicOCSPResponse，返回null");
            return null;
        }
        BasicOCSPResp bor = new BasicOCSPResp(borRaw);

        for (final X509CertificateHolder x509CertificateHolder : bor.getCerts()) {
            X509Certificate x509Certificate = converter.getCertificate(x509CertificateHolder);

            JcaContentVerifierProviderBuilder jcaContentVerifierProviderBuilder = new JcaContentVerifierProviderBuilder();
            jcaContentVerifierProviderBuilder.setProvider(BouncyCastleProvider.PROVIDER_NAME);
            final PublicKey publicKey = x509Certificate.getPublicKey();
            ContentVerifierProvider contentVerifierProvider = jcaContentVerifierProviderBuilder.build(publicKey);

            if (bor.isSignatureValid(contentVerifierProvider))
                return x509Certificate;
        }

        return null;
    }

    static byte[] buildOCSPResponse(byte[] BasicOCSPResponse) throws IOException {
        DEROctetString doctet = new DEROctetString(BasicOCSPResponse);
        ASN1EncodableVector v2 = new ASN1EncodableVector();
        v2.add(OCSPObjectIdentifiers.id_pkix_ocsp_basic);
        v2.add(doctet);
        ASN1Enumerated den = new ASN1Enumerated(0);
        ASN1EncodableVector v3 = new ASN1EncodableVector();
        v3.add(den);
        v3.add(new DERTaggedObject(true, 0, new DERSequence(v2)));
        DERSequence seq = new DERSequence(v3);
        return seq.getEncoded();
    }

    //
    // X509 certificate related helpers
    //
    public static X509Certificate getIssuerCertificate(X509Certificate certificate) throws IOException, StreamParsingException {
        System.out.println("  开始获取颁发者证书...");
        System.out.println("  当前证书主体: " + certificate.getSubjectX500Principal().getName());
        System.out.println("  当前证书颁发者: " + certificate.getIssuerX500Principal().getName());
        
        // 策略1: 检查是否为自签名证书（根证书）
        if (isSelfSigned(certificate)) {
            System.out.println("  检测到自签名证书（根证书），无需获取颁发者证书");
            return null;
        }
        
        // 策略2: 尝试从AIA扩展获取颁发者证书
        X509Certificate issuerFromAIA = getIssuerFromAIA(certificate);
        if (issuerFromAIA != null) {
            System.out.println("  ✓ 通过AIA扩展成功获取颁发者证书");
            return issuerFromAIA;
        }
        
        // 策略3: 尝试从本地证书存储获取
        X509Certificate issuerFromStore = getIssuerFromLocalStore(certificate);
        if (issuerFromStore != null) {
            System.out.println("  ✓ 从本地证书存储成功获取颁发者证书");
            return issuerFromStore;
        }
        
        // 策略4: 尝试从常见的CA证书库获取
        X509Certificate issuerFromCommonCAs = getIssuerFromCommonCAs(certificate);
        if (issuerFromCommonCAs != null) {
            System.out.println("  ✓ 从常见CA证书库成功获取颁发者证书");
            return issuerFromCommonCAs;
        }
        
        System.err.println("  ✗ 所有策略都无法获取颁发者证书");
        return null;
    }
    
    /**
     * 检查证书是否为自签名证书
     */
    private static boolean isSelfSigned(X509Certificate certificate) {
        try {
            // 检查颁发者和主体是否相同
            if (certificate.getSubjectX500Principal().equals(certificate.getIssuerX500Principal())) {
                // 进一步验证签名
                certificate.verify(certificate.getPublicKey());
                return true;
            }
        } catch (Exception e) {
            // 如果验证失败，不是自签名证书
        }
        return false;
    }
    
    /**
     * 从AIA扩展获取颁发者证书（原有逻辑）
     */
    private static X509Certificate getIssuerFromAIA(X509Certificate certificate) {
        try {
            String url = getCACURL(certificate);
            if (url != null && url.length() > 0) {
                System.out.println("  尝试从AIA URL获取颁发者证书: " + url);
                
                HttpURLConnection con = (HttpURLConnection)new URL(url).openConnection();
                con.setConnectTimeout(10000); // 10秒连接超时
                con.setReadTimeout(15000);    // 15秒读取超时
                
                if (con.getResponseCode() / 100 != 2) {
                    System.err.println("  AIA URL响应错误: " + con.getResponseCode());
                    return null;
                }
                
                InputStream inp = (InputStream) con.getContent();
                X509CertParser parser = new X509CertParser();
                parser.engineInit(new ByteArrayInputStream(StreamUtil.inputStreamToArray(inp)));
                return (X509Certificate) parser.engineRead();
            }
        } catch (Exception e) {
            System.err.println("  从AIA获取颁发者证书失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 从本地证书存储获取颁发者证书
     */
    private static X509Certificate getIssuerFromLocalStore(X509Certificate certificate) {
        try {
            System.out.println("  尝试从本地证书存储获取颁发者证书...");
            
            // 尝试从Java默认的信任存储获取
            KeyStore trustStore = KeyStore.getInstance("JKS");
            String javaHome = System.getProperty("java.home");
            String trustStorePath = javaHome + "/lib/security/cacerts";
            
            try (FileInputStream fis = new FileInputStream(trustStorePath)) {
                trustStore.load(fis, "changeit".toCharArray());
                
                java.util.Enumeration<String> aliases = trustStore.aliases();
                while (aliases.hasMoreElements()) {
                    String alias = aliases.nextElement();
                    java.security.cert.Certificate cert = trustStore.getCertificate(alias);
                    
                    if (cert instanceof X509Certificate) {
                        X509Certificate x509Cert = (X509Certificate) cert;
                        // 检查是否为目标证书的颁发者
                        if (isIssuerOf(x509Cert, certificate)) {
                            System.out.println("  在本地信任存储中找到颁发者证书: " + alias);
                            return x509Cert;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("  从本地证书存储获取颁发者证书失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 从常见CA证书库获取颁发者证书
     */
    private static X509Certificate getIssuerFromCommonCAs(X509Certificate certificate) {
        try {
            System.out.println("  尝试从常见CA证书库获取颁发者证书...");
            
            String issuerName = certificate.getIssuerX500Principal().getName();
            
            // 检查是否为GDCA证书
            if (issuerName.contains("GDCA") || issuerName.contains("GUANG DONG CERTIFICATE AUTHORITY")) {
                return getGDCARootCertificate(issuerName);
            }
            
            // 可以添加更多常见CA的处理逻辑
            // 例如：DigiCert, VeriSign, GlobalSign等
            
        } catch (Exception e) {
            System.err.println("  从常见CA证书库获取颁发者证书失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取GDCA根证书
     */
    private static X509Certificate getGDCARootCertificate(String issuerName) {
        try {
            System.out.println("  检测到GDCA证书，尝试获取GDCA根证书...");
            
            // 这里可以内置GDCA根证书，或者从GDCA官方网站获取
            // 由于安全考虑，建议将常用的根证书内置到应用中
            
            // 示例：尝试从GDCA官方网站获取根证书
            String[] gdcaRootUrls = {
                "http://www.gdca.com.cn/files/GDCA_TrustAUTH_R5_ROOT.crt",
                "https://www.gdca.com.cn/files/GDCA_TrustAUTH_R5_ROOT.crt"
            };
            
            for (String url : gdcaRootUrls) {
                try {
                    System.out.println("  尝试从GDCA官方获取根证书: " + url);
                    HttpURLConnection con = (HttpURLConnection) new URL(url).openConnection();
                    con.setConnectTimeout(5000);
                    con.setReadTimeout(10000);
                    
                    if (con.getResponseCode() == 200) {
                        InputStream inp = con.getInputStream();
                        CertificateFactory cf = CertificateFactory.getInstance("X.509");
                        X509Certificate rootCert = (X509Certificate) cf.generateCertificate(inp);
                        
                        if (rootCert != null) {
                            System.out.println("  ✓ 成功获取GDCA根证书");
                            return rootCert;
                        }
                    }
                } catch (Exception e) {
                    System.err.println("  从 " + url + " 获取GDCA根证书失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("  获取GDCA根证书失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 检查证书A是否为证书B的颁发者
     */
    private static boolean isIssuerOf(X509Certificate issuerCandidate, X509Certificate certificate) {
        try {
            // 检查颁发者名称是否匹配
            if (!issuerCandidate.getSubjectX500Principal().equals(certificate.getIssuerX500Principal())) {
                return false;
            }
            
            // 验证签名
            certificate.verify(issuerCandidate.getPublicKey());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    static String getCACURL(X509Certificate certificate) {
        ASN1Primitive obj;
        try {
            obj = getExtensionValue(certificate, Extension.authorityInfoAccess.getId());
            if (obj == null) {
                return null;
            }
            ASN1Sequence AccessDescriptions = (ASN1Sequence) obj;
            for (int i = 0; i < AccessDescriptions.size(); i++) {
                ASN1Sequence AccessDescription = (ASN1Sequence) AccessDescriptions.getObjectAt(i);
                if ( AccessDescription.size() != 2 ) {
                    continue;
                }
                else if (AccessDescription.getObjectAt(0) instanceof ASN1ObjectIdentifier) {
                    ASN1ObjectIdentifier id = (ASN1ObjectIdentifier)AccessDescription.getObjectAt(0);
                    if ("*******.********.2".equals(id.getId())) {
                        ASN1Primitive description = (ASN1Primitive)AccessDescription.getObjectAt(1);
                        String AccessLocation =  getStringFromGeneralName(description);
                        if (AccessLocation == null) {
                            return "" ;
                        }
                        else {
                            return AccessLocation ;
                        }
                    }
                }
            }
        } catch (IOException e) {
            return null;
        }
        return null;
    }

    static ASN1Primitive getExtensionValue(X509Certificate certificate, String oid) throws IOException {
        byte[] bytes = certificate.getExtensionValue(oid);
        if (bytes == null) {
            return null;
        }
        ASN1InputStream aIn = new ASN1InputStream(new ByteArrayInputStream(bytes));
        ASN1OctetString octs = (ASN1OctetString) aIn.readObject();
        aIn = new ASN1InputStream(new ByteArrayInputStream(octs.getOctets()));
        return aIn.readObject();
    }

    private static String getStringFromGeneralName(ASN1Primitive names) throws IOException {
        ASN1TaggedObject taggedObject = (ASN1TaggedObject) names ;
        return new String(ASN1OctetString.getInstance(taggedObject, false).getOctets(), "ISO-8859-1");
    }

    //
    // inner class
    //
    static class ValidationData {
        final List<byte[]> crls = new ArrayList<byte[]>();
        final List<byte[]> ocsps = new ArrayList<byte[]>();
        final List<byte[]> certs = new ArrayList<byte[]>();
    }

    //
    // member variables
    //
    final PdfDocument pdfDocument;

    final Map<PdfName,ValidationData> validated = new HashMap<PdfName,ValidationData>();
}
