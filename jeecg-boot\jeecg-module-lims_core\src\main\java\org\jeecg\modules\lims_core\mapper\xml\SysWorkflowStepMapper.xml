<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.SysWorkflowStepMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  sys_workflow_step 
		WHERE
			 workflow_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.entity.SysWorkflowStep">
		SELECT * 
		FROM  sys_workflow_step
		WHERE
			 workflow_id = #{mainId} order by sort_num 	</select>
</mapper>
