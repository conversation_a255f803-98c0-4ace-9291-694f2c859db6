package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 试验标本
 * @Author: jeecg-boot
 * @Date:   2025-03-24
 * @Version: V1.0
 */
@Data
@TableName("test")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="试验标本")
public class Test implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**试样类型*/
    @Excel(name = "试样类型", width = 15, dicCode = "test_type")
    @Dict(dicCode = "test_type")
    @Schema(description = "试样类型")
    private java.lang.String testTypeId;
    /**试样序号*/
    @Excel(name = "试样序号", width = 15)
    @Schema(description = "试样序号")
    private java.lang.Integer trialNo;
    /**试样量*/
    @Excel(name = "试样量", width = 15)
    @Schema(description = "试样量")
    private java.lang.String amount;
    /**定容体积*/
    @Excel(name = "定容体积", width = 15)
    @Schema(description = "定容体积")
    private java.lang.String volume;
    /**序列号*/
    @Excel(name = "序列号", width = 15)
    @Schema(description = "序列号")
    private java.lang.String sequenceNo;
    /**序列路径*/
    @Excel(name = "序列路径", width = 15)
    @Schema(description = "序列路径")
    private java.lang.String sequenceUrl;
    /**采集方法路径*/
    @Excel(name = "采集方法路径", width = 15)
    @Schema(description = "采集方法路径")
    private java.lang.String acquisitionMethodUrl;
    /**采集数据路径*/
    @Excel(name = "采集数据路径", width = 15)
    @Schema(description = "采集数据路径")
    private java.lang.String rawDataUrl;
    /**定量方法路径*/
    @Excel(name = "定量方法路径", width = 15)
    @Schema(description = "定量方法路径")
    private java.lang.String quantMethodUrl;
    /**原始检测结果路径*/
    @Excel(name = "原始检测结果路径", width = 15)
    @Schema(description = "原始检测结果路径")
    private java.lang.String rawResultUrl;
    /**电子记录*/
    @Excel(name = "电子记录", width = 15)
    @Schema(description = "电子记录")
    private java.lang.String recordUrl;
    /**已提交*/
    @Excel(name = "已提交", width = 15)
    @Schema(description = "已提交")
    private java.lang.Integer isSubmitted;
    /**审核人*/
    @Excel(name = "审核人", width = 15)
    @Schema(description = "审核人")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private java.lang.String checkedBy;
    /**审核时间*/
    @Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审核时间")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private java.util.Date checkedTime;
    /**检验人*/
    @Schema(description = "检验人")
    private String tester;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检验时间")
    private Date testerTime;
    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**任务*/
    @Excel(name = "任务", width = 15)
    @Schema(description = "任务")
    private java.lang.String taskId;
}
