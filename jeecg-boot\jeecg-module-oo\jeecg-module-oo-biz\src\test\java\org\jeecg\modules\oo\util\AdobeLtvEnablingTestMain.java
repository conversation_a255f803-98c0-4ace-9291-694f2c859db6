package org.jeecg.modules.oo.util;

import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1Enumerated;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1OctetString;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.asn1.DEROctetString;
import org.bouncycastle.asn1.ocsp.BasicOCSPResponse;

import java.lang.reflect.Method;

/**
 * 测试AdobeLtvEnabling中的BasicOCSPResponse解析修复
 */
public class AdobeLtvEnablingTestMain {
    
    public static void main(String[] args) {
        System.out.println("开始AdobeLtvEnabling BasicOCSPResponse解析测试...");
        
        try {
            // 测试正常的BasicOCSPResponse解析
            testNormalOcspResponse();
            
            // 测试包含ASN1Enumerated的OCSP响应解析
            testOcspResponseWithAsn1Enumerated();
            
            // 测试getOcspHashKey方法
            testGetOcspHashKey();
            
            // 测试getOcspSignerCertificate方法
            testGetOcspSignerCertificate();
            
        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("所有测试完成！");
    }
    
    private static void testNormalOcspResponse() {
        System.out.println("\n=== 测试正常的BasicOCSPResponse解析 ===");
        try {
            // 创建一个简单的ASN.1序列作为模拟OCSP响应
            ASN1EncodableVector vector = new ASN1EncodableVector();
            vector.add(new ASN1Integer(0)); // 版本
            vector.add(new DEROctetString("mock response".getBytes())); // 模拟响应数据
            
            DERSequence sequence = new DERSequence(vector);
            byte[] mockOcspResponse = sequence.getEncoded();
            
            // 使用反射调用私有方法
            Method parseMethod = AdobeLtvEnabling.class.getDeclaredMethod("parseBasicOcspResponse", byte[].class);
            parseMethod.setAccessible(true);
            
            BasicOCSPResponse result = (BasicOCSPResponse) parseMethod.invoke(null, mockOcspResponse);
            System.out.println("正常OCSP响应解析测试完成，结果: " + (result != null ? "成功" : "失败（预期）"));
            
        } catch (Exception e) {
            System.err.println("正常OCSP响应解析测试失败: " + e.getMessage());
        }
    }
    
    private static void testOcspResponseWithAsn1Enumerated() {
        System.out.println("\n=== 测试包含ASN1Enumerated的OCSP响应解析 ===");
        try {
            // 创建包含ASN1Enumerated的ASN.1序列
            ASN1EncodableVector vector = new ASN1EncodableVector();
            vector.add(new ASN1Integer(0)); // 版本
            vector.add(new ASN1Enumerated(1)); // 这个会导致标准解析失败
            vector.add(new DEROctetString("mock response with enum".getBytes())); // 模拟响应数据
            
            DERSequence sequence = new DERSequence(vector);
            byte[] mockOcspResponse = sequence.getEncoded();
            
            System.out.println("原始字节数组长度: " + mockOcspResponse.length);
            System.out.print("原始字节内容: ");
            for (byte b : mockOcspResponse) {
                System.out.printf("%02X ", b);
            }
            System.out.println();
            
            // 使用反射调用私有方法
            Method parseMethod = AdobeLtvEnabling.class.getDeclaredMethod("parseBasicOcspResponse", byte[].class);
            parseMethod.setAccessible(true);
            
            BasicOCSPResponse result = (BasicOCSPResponse) parseMethod.invoke(null, mockOcspResponse);
            System.out.println("包含ASN1Enumerated的OCSP响应解析测试完成，结果: " + (result != null ? "成功" : "失败"));
            
        } catch (Exception e) {
            System.err.println("包含ASN1Enumerated的OCSP响应解析测试失败: " + e.getMessage());
        }
    }
    
    private static void testGetOcspHashKey() {
        System.out.println("\n=== 测试getOcspHashKey方法 ===");
        try {
            // 创建包含ASN1Enumerated的测试数据
            ASN1EncodableVector vector = new ASN1EncodableVector();
            vector.add(new ASN1Integer(1));
            vector.add(new ASN1Enumerated(2));
            vector.add(new DEROctetString("test".getBytes()));
            
            DERSequence sequence = new DERSequence(vector);
            byte[] testBytes = sequence.getEncoded();
            
            // 调用getOcspHashKey方法
            com.itextpdf.kernel.pdf.PdfName hashKey = AdobeLtvEnabling.getOcspHashKey(testBytes);
            
            System.out.println("getOcspHashKey测试完成");
            System.out.println("测试结果: " + (hashKey != null ? "成功" : "失败"));
            if (hashKey != null) {
                System.out.println("生成的哈希键: " + hashKey.getValue());
            }
            
        } catch (Exception e) {
            System.err.println("getOcspHashKey测试失败: " + e.getMessage());
        }
    }
    
    private static void testGetOcspSignerCertificate() {
        System.out.println("\n=== 测试getOcspSignerCertificate方法 ===");
        try {
            // 创建包含ASN1Enumerated的测试数据
            ASN1EncodableVector vector = new ASN1EncodableVector();
            vector.add(new ASN1Integer(1));
            vector.add(new ASN1Enumerated(2));
            vector.add(new DEROctetString("test".getBytes()));
            
            DERSequence sequence = new DERSequence(vector);
            byte[] testBytes = sequence.getEncoded();
            
            // 调用getOcspSignerCertificate方法
            java.security.cert.X509Certificate cert = AdobeLtvEnabling.getOcspSignerCertificate(testBytes);
            
            System.out.println("getOcspSignerCertificate测试完成");
            System.out.println("测试结果: " + (cert == null ? "成功（预期返回null）" : "意外成功"));
            
        } catch (Exception e) {
            System.err.println("getOcspSignerCertificate测试失败: " + e.getMessage());
        }
    }
}