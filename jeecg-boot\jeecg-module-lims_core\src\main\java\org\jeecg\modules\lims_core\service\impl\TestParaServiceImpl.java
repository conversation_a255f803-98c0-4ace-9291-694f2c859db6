package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.TestPara;
import org.jeecg.modules.lims_core.mapper.TestParaMapper;
import org.jeecg.modules.lims_core.service.ITestParaService;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.Wrapper;

/**
 * @Description: 检测过程参数
 * @Author: jeecg-boot
 * @Date:   2025-01-20
 * @Version: V1.0
 */
@Service
public class TestParaServiceImpl extends ServiceImpl<TestParaMapper, TestPara> implements ITestParaService {

    @Autowired
    private TestParaMapper  testParaMapper;

    @Override
    public IPage<TestParaVoNew> queryPageList(Page<TestParaVoNew> page, Wrapper<TestParaVoNew> wrapper) {
        return testParaMapper.queryPageList(page,wrapper);
    }
}
