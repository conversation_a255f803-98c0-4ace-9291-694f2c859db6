package org.jeecg.modules.lims_core.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.modules.lims_core.entity.TestTask;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;

@Data
public class TaskVo extends TestTask {
    private String contractNo;
    @Excel(name = "检品编号", width = 15)
    private String sampleNo;
    private String orderId;
    private String sampleName;

    private String typename;

    private String methodname;;
    private String code;
    private String materialstypename;
    private String supplier;
    private String purity;
    private String deptId;
    @Excel(name = "部门名称", width = 15)
    private String departName;
    private String conclusion;
    @Excel(name = "历史操作人", width = 15)
    private String historyOperator;
    private String cid;
    @Excel(name = "受理时间", width = 15,format = "yyyy-MM-dd")
    private Date receiveDate;
    @Excel(name = "到期时间", width = 15,format = "yyyy-MM-dd")
    private Date deadLineDate;
    @Excel(name = "研发项目名称", width = 15)
    private String rdName;
    @Excel(name = "业务类型", width = 15)
    private String serviceType;
    @Excel(name = "是否补发项目", width = 15)
    private String isSupplementary;
    private String customerId;
    @Excel(name = "方法名称", width = 15)
    private String standardMethodName;
    private String quotationId;
    private String bizOrderId;
    private String bizTypeId;
    @Excel(name = "客户名称", width = 15)
    private String customerName;
    private String lotNo;
    private String specification;
    private String testRemark;
    private String remark;
    private String manufacturer;
    private String rdNo;
    private String repResult;
    private String translation;
    private String receiveCount;
    private String unitName;
    private String phone;
    @Excel(name = "是否标检", width = 15)
    private String isBiao;
    private String analyteName;
}
