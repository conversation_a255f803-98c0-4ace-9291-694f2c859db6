package org.jeecg.modules.lims_core.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.lims_core.entity.Sample;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.TaskVo;

/**
 * @Description: 样品
 * @Author: jeecg-boot
 * @Date:   2025-01-06
 * @Version: V1.0
 */
public interface SampleMapper extends BaseMapper<Sample> {
    /**
     * 编辑节点状态
     * @param id
     * @param status
     */
    void updateTreeNodeStatus(@Param("id") String id,@Param("status") String status);

    /**
     * 【vue3专用】根据父级ID查询树节点数据
     *
     * @param pid
     * @param query
     * @return
     */
    List<SelectTreeModel> queryListByPid(@Param("pid") String pid, @Param("query") Map<String, String> query);

    @Select("select * from sample where quotation_id = #{quotationId}")
    List<Sample> selectByQuotationId(@Param("quotationId") String quotationId);

    @Select("select * from sample where order_id = #{orderId}")
    List<Sample> selectByOrderId(@Param("orderId") String  orderId);

    List<TaskVo> getSampleCountAndUnit(String Id);
}
