package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysInstrumentTypeSettings;
import org.jeecg.modules.lims_core.mapper.SysInstrumentTypeSettingsMapper;
import org.jeecg.modules.lims_core.service.ISysInstrumentTypeSettingsService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 数据采集设置
 * @Author: jeecg-boot
 * @Date:   2025-04-28
 * @Version: V1.0
 */
@Service
public class SysInstrumentTypeSettingsServiceImpl extends ServiceImpl<SysInstrumentTypeSettingsMapper, SysInstrumentTypeSettings> implements ISysInstrumentTypeSettingsService {

}
