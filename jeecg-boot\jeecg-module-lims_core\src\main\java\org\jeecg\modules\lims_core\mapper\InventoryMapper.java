package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.Inventory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.InventoryVo;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2025-04-23
 * @Version: V1.0
 */
public interface InventoryMapper extends BaseMapper<Inventory> {
    IPage<InventoryVo> queryPageList(Page<InventoryVo> page,
                                     @Param(Constants.WRAPPER) Wrapper<InventoryVo> wrapper);
}
