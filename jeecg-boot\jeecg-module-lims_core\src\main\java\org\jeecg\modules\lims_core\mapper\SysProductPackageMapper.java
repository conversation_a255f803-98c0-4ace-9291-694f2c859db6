package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.SysProductPackage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;

/**
 * @Description: 产品套餐
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
public interface SysProductPackageMapper extends BaseMapper<SysProductPackage> {
    IPage<SysProductPackage> queryPageList(Page<SysProductPackage> page,
                                       @Param(Constants.WRAPPER) Wrapper<SysProductPackage> wrapper);
}
