package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.Instrument;
import org.jeecg.modules.lims_core.mapper.InstrumentMapper;
import org.jeecg.modules.lims_core.service.IInstrumentService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 设备
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Service
public class InstrumentServiceImpl extends ServiceImpl<InstrumentMapper, Instrument> implements IInstrumentService {

}
