package org.jeecg.modules.dcs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 培训
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
@Schema(description="培训")
@Data
@TableName("training")
public class Training implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**培训名称*/
	@Excel(name = "培训名称", width = 15)
    @Schema(description = "培训名称")
    private String name;
    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "training_status")
    @Dict(dicCode = "training_status")
    @Schema(description = "状态")
    private String status;
    /**课件*/
    @Excel(name = "课件", width = 15)
    @Schema(description = "课件")
    private String courseware;
	/**开始时间*/
	@Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private java.util.Date startTime;
	/**培训时长(秒)*/
	@Excel(name = "培训时长(秒)", width = 15)
    @Schema(description = "培训时长(秒)")
    private Integer duration;
    /**地点*/
    @Excel(name = "地点", width = 15, dicCode = "meeting_room")
    @Dict(dicCode = "meeting_room")
    @Schema(description = "地点")
    private String location;
    @Schema(description = "培训记录")
    private String recordUrl;
	/**会议id*/
	@Excel(name = "会议id", width = 15)
    @Schema(description = "会议id")
    private String meetingIds;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
    @Dict(dictTable = "dcs_doc", dicText = "doc_no", dicCode = "id")
    @Schema(description = "培训文档")
    private String docIds;
}
