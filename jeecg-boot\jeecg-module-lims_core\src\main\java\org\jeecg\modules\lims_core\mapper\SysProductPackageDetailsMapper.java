package org.jeecg.modules.lims_core.mapper;

import java.util.List;
import org.jeecg.modules.lims_core.entity.SysProductPackageDetails;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 套餐详情表
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
public interface SysProductPackageDetailsMapper extends BaseMapper<SysProductPackageDetails> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<SysProductPackageDetails>
   */
	public List<SysProductPackageDetails> selectByMainId(@Param("mainId") String mainId);
}
