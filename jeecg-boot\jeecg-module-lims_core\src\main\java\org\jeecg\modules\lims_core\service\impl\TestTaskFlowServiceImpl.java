package org.jeecg.modules.lims_core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.vo.FlowSubmitVo;
import org.jeecg.modules.lims_core.vo.Step;
import org.jeecg.modules.lims_core.vo.Steps;
import org.jeecg.modules.lims_core.vo.enums.StepStatus;
import org.jeecg.modules.lims_order.entity.BizOrder;
import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.service.IBizOrderService;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 测试任务流转
 * @Author: jeecg-boot
 * @Date:   2025-03-10
 * @Version: V1.0
 */
@Service
public class TestTaskFlowServiceImpl extends ServiceImpl<TestTaskFlowMapper, TestTaskFlow> implements ITestTaskFlowService {

    @Autowired
    private TestTaskMapper testTaskMapper;
    @Autowired
    private SysMethodWorkflowMapper sysMethodWorkflowMapper;
    @Autowired
    private SysWorkflowStepMapper sysWorkflowStepMapper;
    @Autowired
    private SampleMapper sampleMapper;
    @Autowired
    private TestTaskFlowMapper testTaskFlowMapper;
    @Autowired
    private SysMethodMapper sysMethodMapper;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private ReportMapper reportMapper;

    private static final String FLOW_STEP_1 = "业务受理";
    private static final String FLOW_STEP_2 = "仓库入库";
    private static final String FLOW_STEP_3 = "PM确认";
    private static final String FLOW_STEP_4 = "任务指派";
    private static final String FLOW_STEP_5 = "结果录入";
    private static final String FLOW_STEP_6 = "结果复核";
    private static final String FLOW_STEP_7 = "报告编制";
    private static final String FLOW_STEP_8 = "报告审核";
    @Autowired
    private IQuotationService iQuotationService;
    @Autowired
    private ISysCapabilityService iSysCapabilityService;
    @Autowired
    private IHolidayCalendarService iHolidayCalendarService;
    @Autowired
    private IBizTypeService iBizTypeService;
    @Autowired
    private ISysKeyService iSysKeyService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private IBizOrderService iBizOrderService;

    @Override
    public IPage<TestTaskFlow> submit(FlowSubmitVo flowSubmitVo, Page<TestTaskFlow> page) {
        ArrayList<TestTaskFlow> testTaskFlowList = new ArrayList<>();
        if(flowSubmitVo.getSampleId() != null){
            Sample sample = sampleMapper.selectById(flowSubmitVo.getSampleId());

            List<BizOrder> order = iBizOrderService.list(new QueryWrapper<BizOrder>().eq("quotation_id", sample.getQuotationId()));
            if(order.size() == 0){
                throw new RuntimeException("报价单未转合同,请点击转合同");
            }
            BizType biztype = iBizTypeService.getById(sample.getBizTypeId());
            SysKey key = iSysKeyService.getById(biztype.getSamplePrefix());
            if (testTaskMapper.selectBySampleId(flowSubmitVo.getSampleId()).size() == 0) {
                throw new RuntimeException("样品[" + sample.getSampleNo() + "]没有测试任务!!");
            }
            testTaskMapper.selectBySampleId(flowSubmitVo.getSampleId()).forEach(task -> {
                SysCapability capa = iSysCapabilityService.getById(task.getCapabilityId());
                Boolean isbiao = Boolean.FALSE;
                if(capa !=null){
                    isbiao = capa.getIsBiao().equals("Y");
                }
                if(task.getDepartmentId() == null){
                    throw new RuntimeException("任务[" + task.getName() + "]没有指派实验组!!");
                }
                if (task.getMethodId() == null && isbiao && task.getTestControlStatus().equals("正常")) {
                    throw new RuntimeException("----"+capa.getName()+"---,需要指定方法!!");
                }
                flowSubmitVo.setTaskId(task.getId());
                if(task.getCurStep().equals(FLOW_STEP_1)){
                    submitFlow(flowSubmitVo, testTaskFlowList);
                }

            });
            List<Sample> samples = sampleMapper.selectByQuotationId(sample.getQuotationId());
            //如果存在sample种的testtask存在业务受理,就不能转
            boolean allInStock = true;
            for (Sample s : samples) {
                List<TestTask> testTasks = testTaskMapper.selectBySampleId(s.getId());
                for (TestTask testTask : testTasks) {
                    if (testTask.getCurStep().equalsIgnoreCase(FLOW_STEP_1)) {
                        allInStock = false;
                        break;
                    }
                }
                if (!allInStock) {
                    break;
                }
            }
            if(allInStock){
                Quotation quotation = iQuotationService.getById(sample.getQuotationId());
                quotation.setStatusId("5"); // 设置为已转合同
                iQuotationService.updateById(quotation);
            }

            //----检测有没有生成报告
            if(flowSubmitVo.getFlowStep().equals(FLOW_STEP_3)){
                List<Report> reports = reportMapper.selectList(new QueryWrapper<Report>().eq("sample_id", sample.getId()));
                if(reports.size()==0){
                    JSONObject orderJson = null;
                    IService bizOrderServiceImpl = (IService)SpringContextUtils.getBean("bizOrderServiceImpl");
                    List quotations = bizOrderServiceImpl.list(new QueryWrapper<Object>().eq("quotation_id", sample.getQuotationId()));
                    if (quotations.size() > 0) {
                        Object o = quotations.get(0);
                        String sJoson = com.alibaba.fastjson.JSON.toJSONString(o);
                        orderJson = JSON.parseObject(sJoson);
                    }

                    Report report = new Report();
                    report.setTemplateId("1925447702706597889");
                    report.setVersion("");
                    report.setReportNo(sample.getSampleNo());
                    if(orderJson != null)
                        report.setOrderId(orderJson.getString("id"));
                    report.setSampleId(sample.getId());
                    report.setTestTaskIds(testTaskMapper.selectBySampleId(sample.getId()).stream().map(TestTask::getId).reduce((a, b) -> a + "," + b).get());
                    //sample.getReceiveDate() + 7days
                    if(orderJson != null &&  orderJson.getString("dayType").equals("工作日")){
                        Date workDate = iHolidayCalendarService.getWorkDate(sample.getReceiveDate(), Integer.parseInt(orderJson.getString("leadTime")));
                        report.setDeadLine(workDate);
                    }else if(orderJson != null){
                        report.setDeadLine(new Date(sample.getReceiveDate().getTime() + Integer.parseInt(orderJson.getString("leadTime")) * 24 * 60 * 60 * 1000));
                    }
                    reportMapper.insert(report);
                }
            }

        }else {
            submitFlow(flowSubmitVo, testTaskFlowList);
        }
        page.setRecords(testTaskFlowList);
        return page;

    }

    private void submitFlow(FlowSubmitVo flowSubmitVo, ArrayList<TestTaskFlow> testTaskFlowList) {
        TestTask testTask = testTaskMapper.selectById(flowSubmitVo.getTaskId());
        if(testTask == null){
            throw  new RuntimeException("测试不存在");
        }
        if(flowSubmitVo.getFlowStep().equals(FLOW_STEP_7)){
            testMapper.selectByTaskId(flowSubmitVo.getTaskId()).forEach(test -> {
                if(test.getCheckedBy() == null && (test.getTestTypeId().equals("0") || test.getTestTypeId().equals("1"))){
                    throw new RuntimeException("测试未审核，不能扫报告节点");
                }
            });
        }
        if(testTask.getCurStep().equalsIgnoreCase(flowSubmitVo.getFlowStep())){
            throw new RuntimeException("当前节点已扫过");
        }
        List<SysWorkflowStep> steps = sysWorkflowStepMapper.selectByMainId("1890214524119957505");
        SysWorkflowStep step = steps.stream().filter(sysWorkflowStep -> sysWorkflowStep.getName().equalsIgnoreCase(testTask.getCurStep())).findFirst().orElseThrow(() -> new RuntimeException("节点不存在"));
        Optional<SysWorkflowStep> first = steps.stream().filter(sysWorkflowStep -> sysWorkflowStep.getId().equals(step.getNextId())).findFirst();
        if(!first.isPresent()){
            throw new RuntimeException("错误的交接节点");
        }
        if(!first.get().getName().equalsIgnoreCase(flowSubmitVo.getFlowStep())){
            throw new RuntimeException("错误的交接节点");
        }
        testTask.setCurStep(first.get().getName());
        testTaskMapper.updateById(testTask);
        TestTaskFlow testTaskFlow = new TestTaskFlow();
        testTaskFlow.setTaskId(testTask.getId());
        testTaskFlow.setStepId(first.get().getName());
        this.save(testTaskFlow);
        testTaskFlowList.add(testTaskFlow);
    }

    @Override
    public List<Steps> listProgress(String orderId, String sampleId) {
        List<DictModel> stepName = sysBaseAPI.getDictItems("flow_step");
        //stepName转成map
        Map<String, String> stepNameMap = new HashMap<>();
        for (DictModel dictModel : stepName) {
            stepNameMap.put(dictModel.getValue(), dictModel.getText());
        }
        List<Sample> samples = new ArrayList<>();
        if(orderId != null) samples = sampleMapper.selectByOrderId(orderId);
        if(sampleId != null) {
            Sample sample = sampleMapper.selectById(sampleId);
            if(sample != null) samples.add(sample);
        }
        List<Steps> stepList = new ArrayList<>();
        List<SysWorkflowStep> flowSteps = sysWorkflowStepMapper.selectByMainId("1890214524119957505");
        for (Sample sample : samples) {
            List<TestTask> testTasks = testTaskMapper.selectBySampleId(sample.getId());
            Date receiveDate = sample.getReceiveDate();
            //Quotation q = iQuotationService.getById(sample.getQuotationId());

            if (receiveDate == null) {
               continue;
            }
            if(sample.getPmLeadTime() == null){
                continue;
            }
            Integer pmLeadTime=0;
            if(sample.getDayType() != null && sample.getDayType().equals("工作日")){
                pmLeadTime = iHolidayCalendarService.getActualWorkDays(receiveDate, sample.getPmLeadTime()) * 24;
            }else
                pmLeadTime = sample.getPmLeadTime() * 24;
            Integer[] hoursByPmLeadTime = getHoursByPmLeadTime(pmLeadTime / 24, flowSteps);
            for (TestTask testTask : testTasks) {
                List<TestTaskFlow> testTaskFlows = testTaskFlowMapper.selectByTaskId(testTask.getId());

                Steps steps = new Steps();
                steps.setMethod(testTask.getName());
                steps.setSampleNo(sample.getSampleNo());
                if(testTask.getRepeatType() != null && testTask.getRepeatName() != null) {
                    List<DictModel> dictItems = sysBaseAPI.getDictItems(testTask.getRepeatType());
                    dictItems.stream().filter(dictModel -> dictModel.getValue().equalsIgnoreCase(testTask.getRepeatName())).findFirst().ifPresent(dictModel -> {
                        steps.setSampleNo(steps.getSampleNo() + '-' + dictModel.getText());
                    });
                }
                steps.setTask(testTask);

                List<Step> items = new ArrayList<>();
                int addHours = 0;


                for (int i = 0; i < flowSteps.size(); i++) {
                    SysWorkflowStep flowStep = flowSteps.get(i);
                    Step step = new Step();
                    addHours += hoursByPmLeadTime[i];
                    step.setTitle(stepNameMap.get(flowStep.getName()));
                    if(i>0) {
                        // receiveDate + (pmLeadTime * percentage / 100) hours
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(receiveDate);
                        calendar.add(Calendar.HOUR, addHours);
                        step.setPlanDate(calendar.getTime());
                        step.setSubTitle("预:" + new SimpleDateFormat("yyyy-MM-dd HH:mm").format(calendar.getTime()));
                    }
                    int finalI = i;

                    //指定步骤显示人员
                    if(flowStep.getName().equalsIgnoreCase(FLOW_STEP_4)) {
                        step.setDescription("");
                        List<String> deptLeadersByDepIds = iSysUserService.getDeptLeadersByDepIds(Collections.singletonList(testTask.getDepartmentId()));
                        iSysUserService.list(new QueryWrapper<SysUser>().in("id", deptLeadersByDepIds)).forEach(sysUser -> {
                            if(sysUser != null)
                                step.setDescription(step.getDescription()+" "+sysUser.getRealname());
                        });

                    }
                    if(flowStep.getName().equalsIgnoreCase(FLOW_STEP_5)) {
                        SysUser userByName = iSysUserService.getUserByName(testTask.getAssignee());
                        if(userByName != null)
                            step.setDescription(userByName.getRealname());
                    }
                    if(flowStep.getName().equalsIgnoreCase(FLOW_STEP_6)) {
                        SysUser userByName = iSysUserService.getUserByName(testTask.getChecker());
                        if(userByName != null)
                            step.setDescription(userByName.getRealname());
                    }

                    testTaskFlows.stream().filter(testTaskFlow -> testTaskFlow.getStepId().equalsIgnoreCase(flowStep.getName())).findFirst().ifPresent(testTaskFlow -> {
                        step.setActualDate(testTaskFlow.getCreateTime());
                        steps.setCurrent(finalI);
                        LoginUser userByName = sysBaseAPI.getUserByName(testTaskFlow.getCreateBy());
                        step.setDescription(userByName.getRealname()+":" + new SimpleDateFormat("yyyy-MM-dd HH:mm").format(testTaskFlow.getCreateTime()));
                    });
                    if(step.getPlanDate() != null&& step.getActualDate() != null && step.getActualDate().getTime() > step.getPlanDate().getTime()){
                        step.setStatus(StepStatus.error);

                    }
                    items.add(step);
                }
                steps.setItems(items);
                stepList.add(steps);
            }

        }
        return stepList;
    }

    Integer[] getHoursByPmLeadTime(Integer pmLeadTime, List<SysWorkflowStep> flowSteps) {
        String stdTaskTimeExpr = flowSteps.get(0).getStdTaskTimeExpr();
        JSONObject jsonObject = JSONObject.parseObject(stdTaskTimeExpr);

        // pmLeadTime 属于哪个 key 的范围
        String key = null;
        for (String k : jsonObject.keySet()) {
            if (k.startsWith("<")) {
                // 处理 "<5" 的情况
                if (pmLeadTime < Integer.parseInt(k.substring(1))) {
                    key = k;
                    break;
                }
            } else if (k.startsWith("≥")) {
                // 处理 "≥5&&<10" 的情况
                String[] parts = k.substring(1).split("&&");
                boolean inRange = true;
                for (String part : parts) {
                    if (part.contains("<")) {
                        // 处理 "<10"
                        if (pmLeadTime >= Integer.parseInt(part.substring(1))) {
                            inRange = false;
                            break;
                        }
                    } else {
                        // 处理 "≥5"
                        if (pmLeadTime < Integer.parseInt(part)) {
                            inRange = false;
                            break;
                        }
                    }
                }
                if (inRange) {
                    key = k;
                    break;
                }
            } else if (k.startsWith("≥") && !k.contains("&&")) {
                // 处理 "≥20" 的情况
                if (pmLeadTime >= Integer.parseInt(k.substring(1))) {
                    key = k;
                    break;
                }
            }
        }

        if (key == null) {
            throw new IllegalArgumentException("无法匹配到有效的 key 范围");
        }

        Integer[] hours = new Integer[flowSteps.size()];
        int totalHours = 0;
        for (int i = 0; i < flowSteps.size(); i++) {
            SysWorkflowStep sysWorkflowStep = flowSteps.get(i);
            String stdTaskTimeExpr1 = sysWorkflowStep.getStdTaskTimeExpr();
            if (stdTaskTimeExpr1 == null) {
                continue;
            }
            JSONObject jsonObject1 = JSONObject.parseObject(stdTaskTimeExpr1);
            if (jsonObject1.containsKey(key)) {
                hours[i] = (int) Math.round(jsonObject1.getDouble(key) * 24);
                totalHours += hours[i];
            }
        }
        // hours[3] = pmLeadTime - 其他所有 hours 综合
        hours[3] = pmLeadTime * 24 - totalHours;
        return hours;
    }

}
