package org.jeecg.modules.lims_order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_order.entity.BizOrder;
import org.jeecg.modules.lims_order.entity.PaymentCollection;
import org.jeecg.modules.lims_order.mapper.BizOrderMapper;
import org.jeecg.modules.lims_order.mapper.PaymentCollectionMapper;
import org.jeecg.modules.lims_order.service.IPaymentCollectionService;
import org.jeecg.modules.lims_order.vo.PaymentAddOrEditVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 回款计划
 * @Author: jeecg-boot
 * @Date:   2025-03-21
 * @Version: V1.0
 */
@Service
public class PaymentCollectionServiceImpl extends ServiceImpl<PaymentCollectionMapper, PaymentCollection> implements IPaymentCollectionService {

    @Autowired
    private BizOrderMapper bizOrderMapper;


    @Override
    public void addOrEdit(PaymentAddOrEditVo paymentAddOrEditVo) {
        BizOrder bizOrder = bizOrderMapper.selectById(paymentAddOrEditVo.getOrderId());

        List<PaymentCollection> collection = this.baseMapper.selectList(new QueryWrapper<PaymentCollection>().eq("order_id", paymentAddOrEditVo.getOrderId()).orderByAsc("sort_num"));
        if (collection != null && collection.size() > 0) {
            if(collection.size() > paymentAddOrEditVo.getPercentages().size()) {
                for (int i = 0; i < paymentAddOrEditVo.getPercentages().size(); i++) {
                    PaymentCollection paymentCollection = collection.get(i);
                    paymentCollection.setPlanAmount(bizOrder.getContractAmountRmb() * paymentAddOrEditVo.getPercentages().get(i) / 100);
                    if(i == 0) paymentCollection.setTypeId("INITIAL_PAYMENT");
                    else if (i == paymentAddOrEditVo.getPercentages().size() - 1) paymentCollection.setTypeId("FINAL_PAYMENT");
                    else paymentCollection.setTypeId("REGULAR_PAYMENT");
                    paymentCollection.setPercentage(paymentAddOrEditVo.getPercentages().get(i));
                    this.updateById(paymentCollection);
                }
                for (int i = paymentAddOrEditVo.getPercentages().size(); i < collection.size(); i++) {
                    this.removeById(collection.get(i).getId());
                }
            }else if (collection.size() < paymentAddOrEditVo.getPercentages().size()) {
                for (int i = 0; i < collection.size(); i++) {
                    PaymentCollection paymentCollection = collection.get(i);
                    paymentCollection.setPlanAmount(bizOrder.getContractAmountRmb() * paymentAddOrEditVo.getPercentages().get(i) / 100);
                    if(i > 0) paymentCollection.setTypeId("REGULAR_PAYMENT");
                    paymentCollection.setPercentage(paymentAddOrEditVo.getPercentages().get(i));
                    this.updateById(paymentCollection);
                }
                for (int i = collection.size(); i < paymentAddOrEditVo.getPercentages().size(); i++) {
                    double p = paymentAddOrEditVo.getPercentages().get(i);
                    PaymentCollection paymentCollection = new PaymentCollection();
                    paymentCollection.setOrderId(paymentAddOrEditVo.getOrderId());
                    paymentCollection.setPlanAmount(bizOrder.getContractAmountRmb() * p / 100);
                    paymentCollection.setResponsibleBy(SecureUtil.currentUser().getUsername());
                    paymentCollection.setSortNum(i + 1);
                    if(i == 0) paymentCollection.setTypeId("INITIAL_PAYMENT");
                    else if (i == paymentAddOrEditVo.getPercentages().size() - 1) paymentCollection.setTypeId("FINAL_PAYMENT");
                    else paymentCollection.setTypeId("REGULAR_PAYMENT");
                    paymentCollection.setPercentage(paymentAddOrEditVo.getPercentages().get(i));
                    this.save(paymentCollection);
                }
            }else{
                for (int i = 0; i < collection.size(); i++) {
                    PaymentCollection paymentCollection = collection.get(i);
                    paymentCollection.setPlanAmount(bizOrder.getContractAmountRmb() * paymentAddOrEditVo.getPercentages().get(i) / 100);
                    if(i == 0) paymentCollection.setTypeId("INITIAL_PAYMENT");
                    else if (i == paymentAddOrEditVo.getPercentages().size() - 1) paymentCollection.setTypeId("FINAL_PAYMENT");
                    else paymentCollection.setTypeId("REGULAR_PAYMENT");
                    paymentCollection.setPercentage(paymentAddOrEditVo.getPercentages().get(i));
                    this.updateById(paymentCollection);
                }
            }
        }else{
            for (int i = 0; i < paymentAddOrEditVo.getPercentages().size(); i++) {

                    PaymentCollection paymentCollection = new PaymentCollection();
                    paymentCollection.setOrderId(paymentAddOrEditVo.getOrderId());
                    paymentCollection.setPlanAmount(bizOrder.getContractAmountRmb() * paymentAddOrEditVo.getPercentages().get(i) / 100);
                    paymentCollection.setPercentage(paymentAddOrEditVo.getPercentages().get(i));
                    paymentCollection.setResponsibleBy(SecureUtil.currentUser().getUsername());
                    paymentCollection.setSortNum(i+1);
                    if(i == 0) paymentCollection.setTypeId("INITIAL_PAYMENT");
                    else if (i == paymentAddOrEditVo.getPercentages().size() - 1) paymentCollection.setTypeId("FINAL_PAYMENT");
                    else paymentCollection.setTypeId("REGULAR_PAYMENT");
                    this.save(paymentCollection);
                }
        }

    }
}
