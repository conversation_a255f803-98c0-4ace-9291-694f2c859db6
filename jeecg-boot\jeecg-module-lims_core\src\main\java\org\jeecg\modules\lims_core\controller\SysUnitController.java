package org.jeecg.modules.lims_core.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import org.jeecg.modules.lims_core.entity.SysUnit;
import org.jeecg.modules.lims_core.vo.SysUnitPage;
import org.jeecg.modules.lims_core.service.ISysUnitService;
import org.jeecg.modules.lims_core.service.ISysUnitConversionService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 计量单位
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Tag(name="计量单位")
@RestController
@RequestMapping("/lims_core/sysUnit")
@Slf4j
public class SysUnitController {
	@Autowired
	private ISysUnitService sysUnitService;
	@Autowired
	private ISysUnitConversionService sysUnitConversionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysUnit
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "计量单位-分页列表查询")
	@Operation(summary="计量单位-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysUnit>> queryPageList(SysUnit sysUnit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysUnit> queryWrapper = QueryGenerator.initQueryWrapper(sysUnit, req.getParameterMap());
		Page<SysUnit> page = new Page<SysUnit>(pageNo, pageSize);
		IPage<SysUnit> pageList = sysUnitService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysUnitPage
	 * @return
	 */
	@AutoLog(value = "计量单位-添加")
	@Operation(summary="计量单位-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_unit:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysUnitPage sysUnitPage) {
		SysUnit sysUnit = new SysUnit();
		BeanUtils.copyProperties(sysUnitPage, sysUnit);
		sysUnitService.saveMain(sysUnit, sysUnitPage.getSysUnitConversionList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysUnitPage
	 * @return
	 */
	@AutoLog(value = "计量单位-编辑")
	@Operation(summary="计量单位-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_unit:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysUnitPage sysUnitPage) {
		SysUnit sysUnit = new SysUnit();
		BeanUtils.copyProperties(sysUnitPage, sysUnit);
		SysUnit sysUnitEntity = sysUnitService.getById(sysUnit.getId());
		if(sysUnitEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysUnitService.updateMain(sysUnit, sysUnitPage.getSysUnitConversionList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "计量单位-通过id删除")
	@Operation(summary="计量单位-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_unit:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysUnitService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "计量单位-批量删除")
	@Operation(summary="计量单位-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_unit:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysUnitService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "计量单位-通过id查询")
	@Operation(summary="计量单位-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysUnit> queryById(@RequestParam(name="id",required=true) String id) {
		SysUnit sysUnit = sysUnitService.getById(id);
		if(sysUnit==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysUnit);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "单位转换-通过主表ID查询")
	@Operation(summary="单位转换-通过主表ID查询")
	@GetMapping(value = "/querySysUnitConversionByMainId")
	public Result<IPage<SysUnitConversion>> querySysUnitConversionListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysUnitConversion> sysUnitConversionList = sysUnitConversionService.selectByMainId(id);
		IPage <SysUnitConversion> page = new Page<>();
		page.setRecords(sysUnitConversionList);
		page.setTotal(sysUnitConversionList.size());
		return Result.OK(page);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysUnit
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_unit:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysUnit sysUnit) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysUnit> queryWrapper = QueryGenerator.initQueryWrapper(sysUnit, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<SysUnit>  sysUnitList = sysUnitService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysUnitPage> pageList = new ArrayList<SysUnitPage>();
      for (SysUnit main : sysUnitList) {
          SysUnitPage vo = new SysUnitPage();
          BeanUtils.copyProperties(main, vo);
          List<SysUnitConversion> sysUnitConversionList = sysUnitConversionService.selectByMainId(main.getId());
          vo.setSysUnitConversionList(sysUnitConversionList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "计量单位列表");
      mv.addObject(NormalExcelConstants.CLASS, SysUnitPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("计量单位数据", "导出人:"+sysUser.getRealname(), "计量单位"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_unit:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysUnitPage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysUnitPage.class, params);
              for (SysUnitPage page : list) {
                  SysUnit po = new SysUnit();
                  BeanUtils.copyProperties(page, po);
                  sysUnitService.saveMain(po, page.getSysUnitConversionList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
