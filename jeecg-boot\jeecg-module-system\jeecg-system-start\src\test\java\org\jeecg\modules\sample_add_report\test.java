package org.jeecg.modules.sample_add_report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.lims_core.entity.Report;
import org.jeecg.modules.lims_core.entity.Sample;
import org.jeecg.modules.lims_core.entity.TestTask;
import org.jeecg.modules.lims_core.mapper.ReportMapper;
import org.jeecg.modules.lims_core.service.IHolidayCalendarService;
import org.jeecg.modules.lims_core.service.impl.SampleServiceImpl;
import org.jeecg.modules.system.service.impl.SysBaseApiImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
public class test {

    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SampleServiceImpl sampleService;
    @Autowired
    private IHolidayCalendarService iHolidayCalendarService;

    @Test
    public void test1() {
        List<Report> reports = reportMapper.selectList(new QueryWrapper<>());
        List<String> list1 = reports.stream().map(item -> item.getSampleId()).toList();
        sampleService.list(new QueryWrapper<Sample>().notIn("id", list1)).forEach(
            sample -> {
                JSONObject orderJson = null;
                IService bizOrderServiceImpl = (IService) SpringContextUtils.getBean("bizOrderServiceImpl");
                List quotations = bizOrderServiceImpl.list(new QueryWrapper<Object>().eq("quotation_id", sample.getQuotationId()));
                if (quotations.size() > 0) {
                    Object o = quotations.get(0);
                    String sJoson = com.alibaba.fastjson.JSON.toJSONString(o);
                    orderJson = JSON.parseObject(sJoson);
                }
                if(orderJson != null && sample.getReceiveDate() != null){
                    Report report = new Report();
                    report.setTemplateId("1925447702706597889");
                    report.setReportNo(sample.getSampleNo());

                    report.setOrderId(orderJson.getString("id"));
                    report.setSampleId(sample.getId());
                    report.setCreateBy(orderJson.getString("createBy"));
                    report.setCreateTime(new Date());
                    //report.setTestTaskIds(testTaskMapper.selectBySampleId(sample.getId()).stream().map(TestTask::getId).reduce((a, b) -> a + "," + b).get());
                    //sample.getReceiveDate() + 7days
                    if(orderJson != null && orderJson.getString("dayType") != null &&orderJson.getString("dayType").equals("工作日")){
                        Date workDate = iHolidayCalendarService.getWorkDate(sample.getReceiveDate(), Integer.parseInt(orderJson.getString("leadTime")));
                        report.setDeadLine(workDate);
                    }else if(orderJson != null){
                        report.setDeadLine(new Date(sample.getReceiveDate().getTime() + Integer.parseInt(orderJson.getString("leadTime")) * 24 * 60 * 60 * 1000));
                    }
                    reportMapper.insert(report);
                }

            }
        );




    }
}
