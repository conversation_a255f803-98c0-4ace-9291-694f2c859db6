package org.jeecg.modules.oo.controller.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TemplateFieldInfo {
    private String fieldName;      // Java字段名
    private Class<?> javaType;     // Java类型
    private String entityFieldName; // 实体字段名
    private String dictTable;      // 字典表
    private String dictText;       // 字典文本字段
    private String dictKey;        // 字典键字段
    private String drillChain;     // 钻取链
    private String description;    // 描述
}
