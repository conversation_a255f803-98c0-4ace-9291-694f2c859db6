package org.jeecg.modules.oo.util;

import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.ASN1OctetString;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.ASN1TaggedObject;
import org.bouncycastle.asn1.DERIA5String;
import org.bouncycastle.asn1.DEROctetString;
import org.bouncycastle.asn1.DERUTF8String;
import org.bouncycastle.asn1.ocsp.OCSPResponse;
import org.bouncycastle.asn1.ocsp.OCSPResponseStatus;
import org.bouncycastle.asn1.x509.Extension;
import org.bouncycastle.cert.jcajce.JcaX509CertificateHolder;
import org.bouncycastle.cert.ocsp.*;
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder;
import com.itextpdf.io.util.StreamUtil;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.Properties;

/**
 * OCSP工具类
 * 提供增强的OCSP响应获取和处理功能
 */
public class OcspUtil {
    
    private static final int DEFAULT_CONNECT_TIMEOUT = 10000; // 10秒
    private static final int DEFAULT_READ_TIMEOUT = 10000; // 10秒
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY = 1000; // 1秒
    
    private static Properties ocspConfig;
    
    static {
        loadOcspConfiguration();
    }
    
    /**
     * 尝试从多个源获取OCSP响应
     */
    public static byte[] getOcspResponse(X509Certificate certificate, X509Certificate issuer) {
        if (issuer == null) {
            System.err.println("  OCSP处理失败：颁发者证书为空");
            return null;
        }
        
        System.out.println("  开始OCSP响应获取流程...");
        System.out.println("  证书主体: " + certificate.getSubjectX500Principal().getName());
        System.out.println("  证书颁发者: " + certificate.getIssuerX500Principal().getName());
        System.out.println("  证书序列号: " + certificate.getSerialNumber().toString(16));
        
        // 检查证书有效期
        try {
            certificate.checkValidity();
            System.out.println("  证书有效期检查通过");
        } catch (Exception e) {
            System.err.println("  警告：证书有效期检查失败: " + e.getMessage());
        }
        
        // 1. 尝试从证书的AIA扩展获取OCSP URL
        String primaryOcspUrl = getOcspUrlFromCertificate(certificate);
        if (primaryOcspUrl != null && !primaryOcspUrl.isEmpty()) {
            System.out.println("  找到主要OCSP URL: " + primaryOcspUrl);
            byte[] response = getOcspResponseDirect(certificate, issuer, primaryOcspUrl);
            if (response != null && response.length > 0) {
                System.out.println("  ✓ 主要OCSP请求成功，响应大小: " + response.length + " bytes");
                return response;
            } else {
                System.err.println("  ✗ 主要OCSP请求失败");
            }
        } else {
            System.err.println("  证书中未找到OCSP URL");
        }
        
        // 2. 尝试备用OCSP服务器
        String[] backupUrls = getBackupOcspUrls(certificate);
        System.out.println("  找到 " + backupUrls.length + " 个备用OCSP服务器");
        
        for (int i = 0; i < backupUrls.length; i++) {
            String backupUrl = backupUrls[i];
            if (backupUrl != null && !backupUrl.trim().isEmpty()) {
                System.out.println("  尝试备用OCSP URL [" + (i+1) + "/" + backupUrls.length + "]: " + backupUrl.trim());
                byte[] response = getOcspResponseDirect(certificate, issuer, backupUrl.trim());
                if (response != null && response.length > 0) {
                    System.out.println("  ✓ 备用OCSP请求成功，响应大小: " + response.length + " bytes");
                    return response;
                } else {
                    System.err.println("  ✗ 备用OCSP请求失败");
                }
            }
        }
        
        System.err.println("  所有OCSP服务器都无法提供有效响应");
        return null;
    }
    
    /**
     * 从证书的AIA扩展获取OCSP URL
     */
    public static String getOcspUrlFromCertificate(X509Certificate certificate) {
        try {
            byte[] aiaExtension = certificate.getExtensionValue(Extension.authorityInfoAccess.getId());
            if (aiaExtension == null) {
                return null;
            }
            
            ASN1Primitive obj = ASN1Primitive.fromByteArray(aiaExtension);
            if (obj instanceof DEROctetString) {
                obj = ASN1Primitive.fromByteArray(((DEROctetString) obj).getOctets());
            }
            
            ASN1Sequence accessDescriptions = (ASN1Sequence) obj;
            for (int i = 0; i < accessDescriptions.size(); i++) {
                ASN1Sequence accessDescription = (ASN1Sequence) accessDescriptions.getObjectAt(i);
                if (accessDescription.size() != 2) {
                    continue;
                }
                
                if (accessDescription.getObjectAt(0) instanceof ASN1ObjectIdentifier) {
                    ASN1ObjectIdentifier id = (ASN1ObjectIdentifier) accessDescription.getObjectAt(0);
                    // OCSP的OID是*******.********.1
                    if ("*******.********.1".equals(id.getId())) {
                        ASN1Primitive description = (ASN1Primitive) accessDescription.getObjectAt(1);
                        return getStringFromGeneralName(description);
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("  获取OCSP URL失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取备用OCSP服务器URL
     */
    public static String[] getBackupOcspUrls(X509Certificate certificate) {
        String issuerDN = certificate.getIssuerX500Principal().getName().toLowerCase();
        
        // 根据颁发者DN确定CA类型
        String caType = determineCaType(issuerDN);
        if (caType != null && ocspConfig != null) {
            String backupKey = "ca." + caType + ".backup";
            String backupUrls = ocspConfig.getProperty(backupKey);
            if (backupUrls != null && !backupUrls.trim().isEmpty()) {
                return backupUrls.split(",");
            }
        }
        
        // 如果配置文件加载失败，使用硬编码的备用服务器
        if (issuerDN.contains("gdca")) {
            return new String[]{
                "http://ocsp.gdca.com.cn/ocsp",
                "http://ocsp1.gdca.com.cn/ocsp",
                "http://ocsp3.gdca.com.cn/ocsp"
            };
        } else if (issuerDN.contains("cfca")) {
            return new String[]{
                "http://ocsp1.cfca.com.cn",
                "http://ocsp2.cfca.com.cn"
            };
        }
        
        return new String[0];
    }
    
    /**
     * 直接向OCSP服务器发送请求
     */
    public static byte[] getOcspResponseDirect(X509Certificate certificate, X509Certificate issuer, String ocspUrl) {
        System.out.println("    开始OCSP直接请求: " + ocspUrl);
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                System.out.println("    尝试 " + attempt + "/" + MAX_RETRY_ATTEMPTS);
                
                // 创建OCSP请求
                OCSPReqBuilder reqBuilder = new OCSPReqBuilder();
                CertificateID certId = new CertificateID(
                    new JcaDigestCalculatorProviderBuilder().build().get(CertificateID.HASH_SHA1),
                    new JcaX509CertificateHolder(issuer),
                    certificate.getSerialNumber()
                );
                reqBuilder.addRequest(certId);
                OCSPReq ocspReq = reqBuilder.build();
                
                System.out.println("    OCSP请求创建成功，大小: " + ocspReq.getEncoded().length + " bytes");
                
                // 发送HTTP请求
                URL url = new URL(ocspUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/ocsp-request");
                connection.setRequestProperty("Accept", "application/ocsp-response");
                connection.setRequestProperty("User-Agent", "iText-OCSP-Client/1.0");
                connection.setRequestProperty("Cache-Control", "no-cache");
                connection.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
                connection.setReadTimeout(DEFAULT_READ_TIMEOUT);
                connection.setDoOutput(true);
                
                System.out.println("    连接OCSP服务器...");
                
                // 发送请求数据
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(ocspReq.getEncoded());
                    os.flush();
                    System.out.println("    OCSP请求数据发送完成");
                }
                
                // 读取响应
                int responseCode = connection.getResponseCode();
                String responseMessage = connection.getResponseMessage();
                System.out.println("    HTTP响应码: " + responseCode + " " + responseMessage);
                
                if (responseCode == 200) {
                    try (InputStream is = connection.getInputStream()) {
                        byte[] response = StreamUtil.inputStreamToArray(is);
                        System.out.println("    接收到响应数据，大小: " + (response != null ? response.length : 0) + " bytes");
                        
                        if (response != null && response.length > 0) {
                            // 验证响应格式
                            if (isValidOcspResponse(response)) {
                                System.out.println("    ✓ OCSP响应格式验证通过");
                                
                                // 进一步分析OCSP响应内容
                                analyzeOcspResponse(response);
                                return response;
                            } else {
                                System.err.println("    ✗ OCSP响应格式验证失败");
                                // 尝试分析无效响应的原因
                                analyzeInvalidOcspResponse(response);
                            }
                        } else {
                            System.err.println("    ✗ 接收到空响应");
                        }
                    }
                } else {
                    System.err.println("    ✗ OCSP服务器返回错误: " + responseCode + " " + responseMessage);
                    
                    // 尝试读取错误响应内容
                    try (InputStream errorStream = connection.getErrorStream()) {
                        if (errorStream != null) {
                            byte[] errorResponse = StreamUtil.inputStreamToArray(errorStream);
                            if (errorResponse != null && errorResponse.length > 0) {
                                String errorContent = new String(errorResponse, "UTF-8");
                                System.err.println("    错误响应内容: " + errorContent.substring(0, Math.min(200, errorContent.length())));
                            }
                        }
                    } catch (Exception e) {
                        // 忽略错误流读取异常
                    }
                }
                
            } catch (java.net.ConnectException e) {
                System.err.println("    ✗ 连接失败 (尝试 " + attempt + "/" + MAX_RETRY_ATTEMPTS + "): " + e.getMessage());
            } catch (java.net.SocketTimeoutException e) {
                System.err.println("    ✗ 连接超时 (尝试 " + attempt + "/" + MAX_RETRY_ATTEMPTS + "): " + e.getMessage());
            } catch (java.net.UnknownHostException e) {
                System.err.println("    ✗ 主机名解析失败 (尝试 " + attempt + "/" + MAX_RETRY_ATTEMPTS + "): " + e.getMessage());
            } catch (Exception e) {
                System.err.println("    ✗ OCSP请求异常 (尝试 " + attempt + "/" + MAX_RETRY_ATTEMPTS + "): " + e.getClass().getSimpleName() + " - " + e.getMessage());
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        System.out.println("    等待 " + RETRY_DELAY + "ms 后重试...");
                        Thread.sleep(RETRY_DELAY);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        System.err.println("    所有重试尝试都失败");
        return null;
    }
    
    /**
     * 验证OCSP响应格式
     */
    public static boolean isValidOcspResponse(byte[] response) {
        try {
            OCSPResp ocspResp = new OCSPResp(response);
            int status = ocspResp.getStatus();
            System.out.println("    OCSP响应状态: " + getOcspStatusDescription(status));
            return status == OCSPResponseStatus.SUCCESSFUL;
        } catch (Exception e) {
            System.err.println("    OCSP响应解析失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 分析OCSP响应内容
     */
    private static void analyzeOcspResponse(byte[] response) {
        try {
            OCSPResp ocspResp = new OCSPResp(response);
            System.out.println("    OCSP响应分析:");
            System.out.println("      状态: " + getOcspStatusDescription(ocspResp.getStatus()));
            
            if (ocspResp.getStatus() == OCSPResponseStatus.SUCCESSFUL) {
                BasicOCSPResp basicResp = (BasicOCSPResp) ocspResp.getResponseObject();
                if (basicResp != null) {
                    SingleResp[] responses = basicResp.getResponses();
                    System.out.println("      单个响应数量: " + responses.length);
                    
                    for (int i = 0; i < responses.length; i++) {
                        SingleResp singleResp = responses[i];
                        System.out.println("      响应 " + (i+1) + ":");
                        System.out.println("        证书状态: " + getCertStatusDescription(singleResp.getCertStatus()));
                        System.out.println("        本次更新: " + singleResp.getThisUpdate());
                        if (singleResp.getNextUpdate() != null) {
                            System.out.println("        下次更新: " + singleResp.getNextUpdate());
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("    OCSP响应分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析无效OCSP响应
     */
    private static void analyzeInvalidOcspResponse(byte[] response) {
        try {
            System.err.println("    无效OCSP响应分析:");
            System.err.println("      响应大小: " + response.length + " bytes");
            
            // 尝试解析为OCSP响应
            try {
                OCSPResp ocspResp = new OCSPResp(response);
                System.err.println("      OCSP状态: " + getOcspStatusDescription(ocspResp.getStatus()));
            } catch (Exception e) {
                System.err.println("      无法解析为OCSP响应: " + e.getMessage());
                
                // 显示响应的前几个字节（十六进制）
                StringBuilder hex = new StringBuilder();
                int maxBytes = Math.min(32, response.length);
                for (int i = 0; i < maxBytes; i++) {
                    hex.append(String.format("%02X ", response[i]));
                }
                System.err.println("      响应开头(hex): " + hex.toString());
                
                // 尝试作为文本解析
                try {
                    String textContent = new String(response, "UTF-8");
                    if (textContent.length() > 100) {
                        textContent = textContent.substring(0, 100) + "...";
                    }
                    System.err.println("      响应内容(text): " + textContent);
                } catch (Exception textEx) {
                    System.err.println("      无法解析为文本");
                }
            }
        } catch (Exception e) {
            System.err.println("    响应分析异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取OCSP状态描述
     */
    private static String getOcspStatusDescription(int status) {
        switch (status) {
            case OCSPResponseStatus.SUCCESSFUL:
                return "成功 (SUCCESSFUL)";
            case OCSPResponseStatus.MALFORMED_REQUEST:
                return "请求格式错误 (MALFORMED_REQUEST)";
            case OCSPResponseStatus.INTERNAL_ERROR:
                return "内部错误 (INTERNAL_ERROR)";
            case OCSPResponseStatus.TRY_LATER:
                return "稍后重试 (TRY_LATER)";
            case OCSPResponseStatus.SIG_REQUIRED:
                return "需要签名 (SIG_REQUIRED)";
            case OCSPResponseStatus.UNAUTHORIZED:
                return "未授权 (UNAUTHORIZED)";
            default:
                return "未知状态 (" + status + ")";
        }
    }
    
    /**
     * 获取证书状态描述
     */
    private static String getCertStatusDescription(org.bouncycastle.cert.ocsp.CertificateStatus certStatus) {
        if (certStatus == org.bouncycastle.cert.ocsp.CertificateStatus.GOOD) {
            return "良好 (GOOD)";
        } else if (certStatus instanceof org.bouncycastle.cert.ocsp.RevokedStatus) {
            return "已撤销 (REVOKED)";
        } else if (certStatus instanceof org.bouncycastle.cert.ocsp.UnknownStatus) {
            return "未知 (UNKNOWN)";
        } else {
            return "其他状态";
        }
    }
    
    /**
     * 创建模拟OCSP响应
     */
    public static byte[] createMockOcspResponse(X509Certificate certificate, X509Certificate issuer) {
        try {
            if (issuer == null) {
                return null;
            }
            
            System.out.println("  创建模拟OCSP响应...");
            
            // 创建一个最基本的OCSP响应，表示服务不可用
            OCSPResponse basicResponse = new OCSPResponse(
                new OCSPResponseStatus(OCSPResponseStatus.TRY_LATER), null);
            byte[] simpleResponse = basicResponse.getEncoded();
            System.out.println("  模拟OCSP响应创建成功，大小: " + simpleResponse.length + " bytes");
            return simpleResponse;
            
        } catch (Exception e) {
            System.err.println("  创建模拟OCSP响应失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 加载OCSP配置文件
     */
    private static void loadOcspConfiguration() {
        try {
            ocspConfig = new Properties();
            try (InputStream is = OcspUtil.class.getClassLoader().getResourceAsStream("ocsp-servers.properties")) {
                if (is != null) {
                    ocspConfig.load(is);
                    System.out.println("OCSP配置文件加载成功");
                } else {
                    System.err.println("OCSP配置文件未找到，使用默认配置");
                }
            }
        } catch (Exception e) {
            System.err.println("加载OCSP配置失败: " + e.getMessage());
            ocspConfig = null;
        }
    }
    
    /**
     * 根据颁发者DN确定CA类型
     */
    private static String determineCaType(String issuerDN) {
        if (issuerDN.contains("gdca")) {
            return "gdca";
        } else if (issuerDN.contains("cfca")) {
            return "cfca";
        } else if (issuerDN.contains("wosign")) {
            return "wosign";
        } else if (issuerDN.contains("digicert")) {
            return "digicert";
        } else if (issuerDN.contains("verisign")) {
            return "verisign";
        }
        return null;
    }
    
    /**
     * 从GeneralName中提取字符串
     */
    public static String getStringFromGeneralName(ASN1Primitive name) {
        try {
            if (name instanceof ASN1TaggedObject) {
                ASN1TaggedObject taggedObject = (ASN1TaggedObject) name;
                // uniformResourceIdentifier的标签是6
                if (taggedObject.getTagNo() == 6) {
                    ASN1Primitive obj = null;
                    
                    try {
                        // 首先尝试显式标记
                        if (taggedObject.isExplicit()) {
                            obj = taggedObject.getExplicitBaseObject().toASN1Primitive();
                        } else {
                            // 处理隐式标记 - 使用ASN1OctetString.getInstance
                            obj = ASN1OctetString.getInstance(taggedObject, false).toASN1Primitive();
                        }
                    } catch (Exception e1) {
                        try {
                            // 备用方案：直接从编码字节解析
                            byte[] encoded = taggedObject.getEncoded();
                            // 跳过标签和长度字节，直接获取内容
                            if (encoded.length > 2) {
                                int contentStart = 2; // 通常标签1字节+长度1字节
                                if ((encoded[1] & 0x80) != 0) {
                                    // 长格式长度
                                    int lengthBytes = encoded[1] & 0x7F;
                                    contentStart = 2 + lengthBytes;
                                }
                                
                                if (contentStart < encoded.length) {
                                    byte[] content = new byte[encoded.length - contentStart];
                                    System.arraycopy(encoded, contentStart, content, 0, content.length);
                                    return new String(content, "UTF-8");
                                }
                            }
                        } catch (Exception e2) {
                            System.err.println("    备用解析方案也失败: " + e2.getMessage());
                        }
                        
                        // 最后的备用方案：从字符串表示中提取
                        String nameStr = taggedObject.toString();
                        if (nameStr.contains("#")) {
                            String[] parts = nameStr.split("#", 2);
                            if (parts.length == 2) {
                                String hexPart = parts[1];
                                if (hexPart.matches("^[0-9a-fA-F]+$") && hexPart.length() % 2 == 0) {
                                    return hexToString(hexPart);
                                }
                                return hexPart;
                            }
                        }
                        throw e1; // 重新抛出原始异常
                    }
                    
                    if (obj instanceof ASN1OctetString) {
                        byte[] urlBytes = ((ASN1OctetString) obj).getOctets();
                        return new String(urlBytes, "UTF-8");
                    } else if (obj instanceof DERIA5String) {
                        return ((DERIA5String) obj).getString();
                    } else if (obj instanceof DERUTF8String) {
                        return ((DERUTF8String) obj).getString();
                    } else {
                        // 尝试直接转换为字符串
                        String objStr = obj.toString();
                        // 检查是否是十六进制编码
                        if (objStr.matches("^[0-9a-fA-F]+$") && objStr.length() % 2 == 0) {
                            return hexToString(objStr);
                        }
                        return objStr;
                    }
                }
            } else if (name instanceof DERIA5String) {
                return ((DERIA5String) name).getString();
            } else if (name instanceof DERUTF8String) {
                return ((DERUTF8String) name).getString();
            } else {
                // 处理其他情况，包括可能的十六进制编码
                String nameStr = name.toString();
                
                // 移除ASN.1标签前缀，如 [CONTEXT 6]# 或 [6]#
                if (nameStr.contains("#")) {
                    String[] parts = nameStr.split("#", 2);
                    if (parts.length == 2) {
                        String hexPart = parts[1];
                        // 检查是否是十六进制编码
                        if (hexPart.matches("^[0-9a-fA-F]+$") && hexPart.length() % 2 == 0) {
                            return hexToString(hexPart);
                        }
                        return hexPart;
                    }
                }
                
                // 移除可能的标签前缀
                if (nameStr.startsWith("[6]")) {
                    return nameStr.substring(3);
                }
                
                return nameStr;
            }
        } catch (Exception e) {
            System.err.println("  解析GeneralName失败: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 将十六进制字符串转换为普通字符串
     */
    private static String hexToString(String hex) {
        try {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < hex.length(); i += 2) {
                String hexByte = hex.substring(i, i + 2);
                int decimal = Integer.parseInt(hexByte, 16);
                result.append((char) decimal);
            }
            return result.toString();
        } catch (Exception e) {
            System.err.println("  十六进制转换失败: " + e.getMessage());
            return hex; // 返回原始字符串
        }
    }
}