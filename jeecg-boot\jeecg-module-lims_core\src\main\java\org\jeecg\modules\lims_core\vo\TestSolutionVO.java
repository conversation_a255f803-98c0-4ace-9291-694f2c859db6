package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "TestSolution",drillUp = "testTaskId->test_task.id",description = "溶液配制信息")
public class TestSolutionVO {
        @TemplateField(entityFieldName = "solution_id",drillChain="solution.id->solution.solution_type_id" ,dict = "solution_type",description = "溶液类型")
        private String solutionType;
        @TemplateField(entityFieldName = "solution_id",drillChain="solution.id->solution.record" ,description = "配制记录" )
        private String conc;
}
