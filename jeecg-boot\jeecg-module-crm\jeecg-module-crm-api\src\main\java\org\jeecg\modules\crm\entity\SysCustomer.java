package org.jeecg.modules.crm.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 客户
 * @Author: jeecg-boot
 * @Date:   2025-05-06
 * @Version: V1.0
 */
@Schema(description="客户")
@Data
@TableName("sys_customer")
public class SysCustomer implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**名称*/
    @Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
    /**客户代码*/
    @Excel(name = "客户代码", width = 15)
    @Schema(description = "客户代码")
    private java.lang.String code;
    /**销售人员*/
    @Excel(name = "销售人员", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "销售人员")
    private java.lang.String salerId;
    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**日期值*/
    @Excel(name = "日期值", width = 15)
    @Schema(description = "日期值")
    private java.lang.String snDate;
    /**累进值*/
    @Excel(name = "累进值", width = 15)
    @Schema(description = "累进值")
    private java.lang.Integer sn;
    /**当前编号*/
    @Excel(name = "当前编号", width = 15)
    @Schema(description = "当前编号")
    private java.lang.String currentNo;
    /**成交状态*/
    @Excel(name = "成交状态", width = 15, dicCode = "deal_status")
    @Dict(dicCode = "deal_status")
    @Schema(description = "成交状态")
    private java.lang.String dealStatus;
    /**客户进度*/
    @Excel(name = "客户进度", width = 15, dicCode = "customer_progress")
    @Dict(dicCode = "customer_progress")
    @Schema(description = "客户进度")
    private java.lang.String customerProgress;
    /**客户编号*/
    @Excel(name = "客户编号", width = 15)
    @Schema(description = "客户编号")
    private java.lang.String customerCode;
    /**客户来源*/
    @Excel(name = "客户来源", width = 15, dicCode = "opportunity_source")
    @Dict(dicCode = "opportunity_source")
    @Schema(description = "客户来源")
    private java.lang.String customerSource;
    /**客户状态*/
    @Excel(name = "客户状态", width = 15, dicCode = "customer_status")
    @Dict(dicCode = "customer_status")
    @Schema(description = "客户状态")
    private java.lang.String customerStatus;
    /**负责人*/
    @Excel(name = "负责人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "负责人")
    private java.lang.String responsiblePerson;
    /**是否归档*/
    @Excel(name = "是否归档", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "是否归档")
    private java.lang.String isArchived;
    /**分配状态*/
    @Excel(name = "分配状态", width = 15, dicCode = "allocation_status")
    @Dict(dicCode = "allocation_status")
    @Schema(description = "分配状态")
    private java.lang.String allocationStatus;
    /**锁定状态*/
    @Excel(name = "锁定状态", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "锁定状态")
    private java.lang.String lockStatus;
    /**业务类型*/
    @Excel(name = "业务类型", width = 15)
    @Schema(description = "业务类型")
    private java.lang.String businessType;
    /**大客户分类*/
    @Excel(name = "大客户分类", width = 15, dicCode = "customer_type")
    @Dict(dicCode = "customer_type")
    @Schema(description = "大客户分类")
    private java.lang.String majorCustomerCategory;
    /**客户级别*/
    @Excel(name = "客户级别", width = 15, dicCode = "customer_grade")
    @Dict(dicCode = "customer_grade")
    @Schema(description = "客户级别")
    private java.lang.String customerLevel;
    /**企业性质*/
    @Excel(name = "企业性质", width = 15, dicCode = "enterprise_nature")
    @Dict(dicCode = "enterprise_nature")
    @Schema(description = "企业性质")
    private java.lang.String enterpriseNature;
    /**客户关系*/
    @Excel(name = "客户关系", width = 15, dicCode = "customer_relationship")
    @Dict(dicCode = "customer_relationship")
    @Schema(description = "客户关系")
    private java.lang.String customerRelationship;
    /**产品剂型*/
    @Excel(name = "产品剂型", width = 15, dicCode = "product_dosage_form")
    @Dict(dicCode = "product_dosage_form")
    @Schema(description = "产品剂型")
    private java.lang.String productDosageForm;
    /**成交金额*/
    @Excel(name = "成交金额", width = 15)
    @Schema(description = "成交金额")
    private java.lang.String transactionAmount;
    /**潜在业务机会金额*/
    @Excel(name = "潜在业务机会金额", width = 15)
    @Schema(description = "潜在业务机会金额")
    private java.lang.String potentialAmount;
    /**成单几率*/
    @Excel(name = "成单几率", width = 15)
    @Schema(description = "成单几率")
    private java.lang.String successProbability;
    /**决策者态度*/
    @Excel(name = "决策者态度", width = 15)
    @Schema(description = "决策者态度")
    private java.lang.String decisionMakerAttitude;
    /**关键人员态度*/
    @Excel(name = "关键人员态度", width = 15)
    @Schema(description = "关键人员态度")
    private java.lang.String keyPersonAttitude;
    /**价格敏感度*/
    @Excel(name = "价格敏感度", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "价格敏感度")
    private java.lang.String priceSensitivity;
    /**国家*/
    @Excel(name = "国家", width = 15)
    @Schema(description = "国家")
    private java.lang.String country;
    /**省*/
    @Excel(name = "省", width = 15,exportConvert=true,importConvert = true )
    @Schema(description = "省")
    private java.lang.String province;

    public String convertisProvince() {
        return SpringContextUtils.getBean(ProvinceCityArea.class).getText(province);
    }

    public void convertsetProvince(String text) {
        this.province = SpringContextUtils.getBean(ProvinceCityArea.class).getCode(text);
    }
    /**市*/
    @Excel(name = "市", width = 15,exportConvert=true,importConvert = true )
    @Schema(description = "市")
    private java.lang.String city;

    public String convertisCity() {
        return SpringContextUtils.getBean(ProvinceCityArea.class).getText(city);
    }

    public void convertsetCity(String text) {
        this.city = SpringContextUtils.getBean(ProvinceCityArea.class).getCode(text);
    }
    /**区*/
    @Excel(name = "区", width = 15,exportConvert=true,importConvert = true )
    @Schema(description = "区")
    private java.lang.String district;

    public String convertisDistrict() {
        return SpringContextUtils.getBean(ProvinceCityArea.class).getText(district);
    }

    public void convertsetDistrict(String text) {
        this.district = SpringContextUtils.getBean(ProvinceCityArea.class).getCode(text);
    }
    /**地址*/
    @Excel(name = "地址", width = 15)
    @Schema(description = "地址")
    private java.lang.String detailedAddress;
}
