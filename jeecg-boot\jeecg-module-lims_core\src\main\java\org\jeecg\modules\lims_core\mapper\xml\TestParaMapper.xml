<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.TestParaMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.TestParaVoNew">
        select * from ( select tp.*,smtp.type_id as paratypeid ,sdi.item_text as  paratypeidtext
        from test_para tp , sys_method_testing_para smtp , sys_dict_item sdi
        where tp.para_id = smtp.id  and sdi.dict_Id=1870038380080676866  and smtp.type_id = sdi.item_value ) t
        ${ew.customSqlSegment}
    </select>

</mapper>