package org.jeecg.modules.lims_core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.lims_core.entity.Sample;
import org.jeecg.modules.lims_core.entity.SysAnalyte;
import org.jeecg.modules.lims_core.mapper.SysAnalyteMapper;
import org.jeecg.modules.lims_core.service.ISysAnalyteService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 待测物
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Service
public class SysAnalyteServiceImpl extends ServiceImpl<SysAnalyteMapper, SysAnalyte> implements ISysAnalyteService {


}
