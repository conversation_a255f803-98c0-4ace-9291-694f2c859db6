package org.jeecg.config.mybatis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.jeecg.common.api.dto.DataLogDTO;
import org.jeecg.common.config.TenantContext;
import org.jeecg.common.constant.TenantConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.SqlInjectionUtil;
import org.jeecg.common.util.TokenUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.base.mapper.BaseCommonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * mybatis拦截器，自动注入创建人、创建时间、修改人、修改时间
 * <AUTHOR>
 * @Date  2019-01-19
 *
 */
@Slf4j
@Component
@Intercepts({ @Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class }) })
public class MybatisInterceptor implements Interceptor {

	// 正则匹配表名（适用于简单 SQL 语句）
	private static final Pattern TABLE_NAME_PATTERN = Pattern.compile(
			"(?i)\\s*(?:INSERT INTO|UPDATE|DELETE\\s+FROM)\\s+([`\\w\\.]+)"
	);

	// 正则匹配条件字段名
	private static final Pattern CONDITION_FIELD_PATTERN = Pattern.compile(
	  "(?i)\\bWHERE\\s+(\\w+)\\s*=\\s*\\?"
	);

	public static void convert(Object obj) {
		if (obj instanceof JSONArray) {
			JSONArray jsonArray = (JSONArray) obj;
			for (Object json : jsonArray) {
				convert(json);
			}
		} else if (obj instanceof JSONObject) {
			JSONObject json = (JSONObject) obj;
			Set<String> keySet = json.keySet();
			String[] keyArray = keySet.toArray(new String[keySet.size()]);
			for (String key : keyArray) {
				Object value = json.get(key);
				String[] keyStrs = key.split("_");
				if (keyStrs.length > 1) {
					StringBuilder sb = new StringBuilder();
					for (int i = 0; i < keyStrs.length; i++) {
						String keyStr = keyStrs[i];
						if (!keyStr.isEmpty()) {
							if (i == 0) {
								sb.append(keyStr);
							} else {
								int c = keyStr.charAt(0);
								if (c >= 97 && c <= 122) {
									int v = c - 32;
									sb.append((char) v);
									if (keyStr.length() > 1) {
										sb.append(keyStr.substring(1));
									}
								} else {
									sb.append(keyStr);
								}
							}
						}
					}
					json.remove(key);
					json.put(sb.toString(), value);
				}
				convert(value);
			}
		}
	}


	// 提取 SQL 语句中的表名
	private String extractTableName(String sql) {
		Matcher matcher = TABLE_NAME_PATTERN.matcher(sql);
		if (matcher.find()) {
			return matcher.group(1); // 获取匹配的表名
		}
		return "未知表"; // 如果匹配不到表名
	}

	// 提取 SQL 语句中的条件字段名
	private String extractConditionField(String sql) {
		Matcher matcher = CONDITION_FIELD_PATTERN.matcher(sql);
		if (matcher.find()) {
			return matcher.group(1); // 获取匹配的字段名
		}
		return "未知字段"; // 如果匹配不到字段名
	}



	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
		String sqlId = mappedStatement.getId();
		log.debug("------sqlId------" + sqlId);
		SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
		Object parameter = invocation.getArgs()[1];
		log.debug("------sqlCommandType------" + sqlCommandType);

		Executor executor = (Executor) invocation.getTarget();
		BoundSql boundSql = mappedStatement.getBoundSql(parameter);
		String sql = boundSql.getSql();
		log.debug("------Executing SQL------" + sql);
		String tableName = extractTableName(sql);
		if (parameter == null) {
			return invocation.proceed();
		}
		if (SqlCommandType.INSERT == sqlCommandType) {
			LoginUser sysUser = this.getLoginUser();
			Field[] fields = oConvertUtils.getAllFields(parameter);
			for (Field field : fields) {
				log.debug("------field.name------" + field.getName());
				try {
					if ("createBy".equals(field.getName())) {
						field.setAccessible(true);
						Object localCreateBy = field.get(parameter);
						field.setAccessible(false);
						if (localCreateBy == null || "".equals(localCreateBy)) {
							if (sysUser != null) {
								// 登录人账号
								field.setAccessible(true);
								field.set(parameter, sysUser.getUsername());
								field.setAccessible(false);
							}
						}
					}
					// 注入创建时间
					if ("createTime".equals(field.getName())) {
						field.setAccessible(true);
						Object localCreateDate = field.get(parameter);
						field.setAccessible(false);
						if (localCreateDate == null || "".equals(localCreateDate)) {
							field.setAccessible(true);
							field.set(parameter, new Date());
							field.setAccessible(false);
						}
					}
					//注入部门编码
					if ("sysOrgCode".equals(field.getName())) {
						field.setAccessible(true);
						Object localSysOrgCode = field.get(parameter);
						field.setAccessible(false);
						if (localSysOrgCode == null || "".equals(localSysOrgCode)) {
							// 获取登录用户信息
							if (sysUser != null) {
								field.setAccessible(true);
								field.set(parameter, sysUser.getOrgCode());
								field.setAccessible(false);
							}
						}
					}

					//------------------------------------------------------------------------------------------------
					//注入租户ID（是否开启系统管理模块的多租户数据隔离【SAAS多租户模式】）
					if(MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL) {
						if (TenantConstant.TENANT_ID.equals(field.getName())) {
							field.setAccessible(true);
							Object localTenantId = field.get(parameter);
							field.setAccessible(false);
							if (localTenantId == null) {
								field.setAccessible(true);

								String tenantId = TenantContext.getTenant();
								//如果通过线程获取租户ID为空，则通过当前请求的request获取租户（shiro排除拦截器的请求会获取不到租户ID）
								if(oConvertUtils.isEmpty(tenantId) && MybatisPlusSaasConfig.OPEN_SYSTEM_TENANT_CONTROL){
									try {
										tenantId = TokenUtils.getTenantIdByRequest(SpringContextUtils.getHttpServletRequest());
									} catch (Exception e) {
										//e.printStackTrace();
									}
								}

								if (field.getType().equals(String.class)) {
									// 字段类型为String
									field.set(parameter, tenantId);
								} else {
									// 字段类型不是String
									field.set(parameter, oConvertUtils.getInt(tenantId, 0));
								}
								field.setAccessible(false);
							}
						}
					}
					//------------------------------------------------------------------------------------------------
					
				} catch (Exception e) {
				}
			}
		}
		if (SqlCommandType.UPDATE == sqlCommandType) {
			LoginUser sysUser = this.getLoginUser();
			Field[] fields = null;
			if (parameter instanceof ParamMap) {
				ParamMap<?> p = (ParamMap<?>) parameter;
				//update-begin-author:scott date:20190729 for:批量更新报错issues/IZA3Q--
                String et = "et";
				if (p.containsKey(et)) {
					parameter = p.get(et);
				} else {
					parameter = p.get("param1");
				}
				//update-end-author:scott date:20190729 for:批量更新报错issues/IZA3Q-

				//update-begin-author:scott date:20190729 for:更新指定字段时报错 issues/#516-
				if (parameter == null) {
					return invocation.proceed();
				}
				//update-end-author:scott date:20190729 for:更新指定字段时报错 issues/#516-

				fields = oConvertUtils.getAllFields(parameter);
			} else {
				fields = oConvertUtils.getAllFields(parameter);
			}




			for (Field field : fields) {
				log.debug("------field.name------" + field.getName());
				try {
					if("id".equals(field.getName())){
						field.setAccessible(true);
						Object idVal = field.get(parameter);
						field.setAccessible(false);
						if(idVal!=null){
							saveUpdateDataLog(tableName, idVal.toString(), JSON.toJSONString(parameter));
						}
					}
					if ("updateBy".equals(field.getName())) {
						//获取登录用户信息
						if (sysUser != null) {
							// 登录账号
							field.setAccessible(true);
							field.set(parameter, sysUser.getUsername());
							field.setAccessible(false);
						}
					}
					if ("updateTime".equals(field.getName())) {
						field.setAccessible(true);
						field.set(parameter, new Date());
						field.setAccessible(false);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		if (SqlCommandType.DELETE == sqlCommandType) {
			try {
				Object sysBaseApiImpl = SpringContextUtils.getBean("sysBaseApiImpl");
				if(parameter instanceof String) {
					JSONObject a = a(tableName, parameter.toString());

					convert(a);
					String jsonStr = JSON.toJSONString(a);

					String name = JSONObject.parseObject(jsonStr).getString("name");
					DataLogDTO dataLogDTO = new DataLogDTO(tableName, parameter.toString(), "删除了记录" + (name == null ? "" : ("【" + name + "】")), "comment", jsonStr);
					sysBaseApiImpl.getClass().getMethod("saveDataLog", DataLogDTO.class).invoke(sysBaseApiImpl, dataLogDTO);
				}else{
					JSONObject jObj = JSONObject.parseObject(JSON.toJSONString(parameter));
					String mainId = jObj.getString("mainId");
					if(oConvertUtils.isNotEmpty(mainId)){
						List<JSONObject> a = b(tableName, mainId,extractConditionField(sql));
						convert(a);
						for (int i = 0; i < a.size(); i++) {
							JSONObject j = a.get(i);
							String name = j.getString("name");
							String id = j.getString("id");
							DataLogDTO dataLogDTO = new DataLogDTO(tableName, id, "删除了记录" + (name == null ? "" : ("【" + name + "】")), "comment", j.toJSONString());
							sysBaseApiImpl.getClass().getMethod("saveDataLog", DataLogDTO.class).invoke(sysBaseApiImpl, dataLogDTO);
						}
					}


				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		//执行SQL
		Object result = invocation.proceed();

		//执行后操作
		if (SqlCommandType.INSERT == sqlCommandType) {
			if(tableName.equals("sys_data_log")){
				return result;
			}
			Field[] fields = oConvertUtils.getAllFields(parameter);
			Object sysBaseApiImpl = SpringContextUtils.getBean("sysBaseApiImpl");



			for (Field field : fields) {
				log.debug("------field.name------" + field.getName());
				try {

					if ("id".equals(field.getName())) {
						field.setAccessible(true);
						Object idVal = field.get(parameter);
						field.setAccessible(false);
						if(idVal!=null){
							String name = JSONObject.parseObject(JSON.toJSONString(parameter)).getString("name");
							DataLogDTO dataLogDTO = new DataLogDTO(tableName, idVal.toString(), "创建了记录"+(name == null ? "": ("【"+name+"】") ), "comment", JSON.toJSONString(parameter));
							sysBaseApiImpl.getClass().getMethod("saveDataLog", DataLogDTO.class).invoke(sysBaseApiImpl, dataLogDTO);
						}
					}
				} catch (Exception e) {
				}
			}
		}

		//执行后操作


		return result;
	}

	void saveUpdateDataLog(String tableName, String dataId, String dataContent){

		//onlCgformFieldServiceImpl
		Object onlCgformFieldServiceImpl = SpringContextUtils.getBean("onlCgformFieldServiceImpl");

		try {
			ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("onlCgformHeadServiceImpl");
			//sysBaseAPI
			Object sysBaseApiImpl = SpringContextUtils.getBean("sysBaseApiImpl");
			// 根据 ruleCode 查询出实体
			QueryWrapper queryWrapper = new QueryWrapper();
			queryWrapper.eq("table_name", tableName);
			JSONObject entity = JSON.parseObject(JSON.toJSONString(impl.getOne(queryWrapper)));

			QueryWrapper var2 = new QueryWrapper();
			var2.eq("cgform_head_id", entity.getString("id"));
			var2.orderByAsc("order_num");
			List<Object> list = (List<Object>) onlCgformFieldServiceImpl.getClass().getSuperclass().getMethod("list", Wrapper.class).invoke(onlCgformFieldServiceImpl, var2);

			//addOnlineUpdateDataLog(String tableName, String dataId, List<OnlCgformField> fieldList, JSONObject json)
//			onlCgformFieldServiceImpl.getClass().getMethod("addOnlineUpdateDataLog", String.class, String.class, List.class, JSONObject.class).invoke(onlCgformFieldServiceImpl, tableName, dataId, list, convertKeysToSnakeCase(JSONObject.parseObject(dataContent)));

			JSONObject jNew = convertKeysToSnakeCase(JSONObject.parseObject(dataContent));
			JSONObject jOld = a(tableName, dataId);

			List<Map<String, String>> changes = new ArrayList<>();
			for (String key : jNew.keySet()) {
				String newValue = jNew.getString(key);
				String oldValue = jOld.getString(key);
				if(newValue != null && newValue.contains(".00")){
					newValue = newValue.replace(".00", "");
				}
				if(oldValue != null && oldValue.contains(".00")){
					oldValue = oldValue.replace(".00", "");
				}

				if(key.equals("create_by") || key.equals("create_time") || key.equals("update_by") || key.equals("update_time") || key.equals("sys_org_code") || key.equals("tenant_id")){
					continue;
				}


				if (!Objects.equals(newValue, oldValue)) {
					Map<String, String> change = new HashMap<>();
					change.put("key", key);
					change.put("newValue", newValue);
					change.put("oldValue", oldValue);
					changes.add(change);
				}
			}
			StringBuffer contentBuffer = new StringBuffer();

			changes.forEach(change -> {
				list.forEach(field -> {
					JSONObject f = JSONObject.parseObject(JSON.toJSONString(field)) ;
					if (f.getString("dbFieldName").equals(change.get("key"))) {
						System.out.println(f);
						//var4.append("  将名称为【" + var7.getDbFieldTxt() + "】的字段内容 " + var10 + " 修改为 " + var11 + "；  ");
						if(oConvertUtils.isNotEmpty(f.getString("dictField"))){
							String dictCode = "";
							if (oConvertUtils.isNotEmpty(f.getString("dictTable"))) {
								String var10000 = f.getString("dictTable");
								dictCode = var10000 + "," + f.getString("dictText") + "," + f.getString("dictField");
							} else {
								dictCode = f.getString("dictField");
							}
							//sysBaseApiImpl.getDictItems(dictCode);
							List<Object> listDict = new ArrayList<>();
                            try {
								listDict = (List<Object>) sysBaseApiImpl.getClass().getMethod("getDictItems", String.class).invoke(sysBaseApiImpl, dictCode);
                            } catch (Exception e) {
                            }

							// ---- 处理分类字典树	----
							if(f.getString("fieldShowType").equals("cat_tree")){
                                try {
									Object invoke = SpringContextUtils.getBean("sysCategoryController").getClass().getMethod("loadAllData", String.class).invoke(SpringContextUtils.getBean("sysCategoryController"), dictCode);
									JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(invoke));
									listDict = jsonObject.getJSONArray("result");
								} catch (IllegalAccessException e) {
                                    throw new RuntimeException(e);
                                } catch (InvocationTargetException e) {
                                    throw new RuntimeException(e);
                                } catch (NoSuchMethodException e) {
                                    throw new RuntimeException(e);
                                }
                            }
							StringBuffer buffer = new StringBuffer();
						if(change.get("oldValue") == null){
							change.put("oldValue", "<空>");
							buffer.append("<空>,");
						}
						String[] split = change.get("oldValue").toString().split(",");

						for (String s : split) {
							listDict.forEach(d -> {
								JSONObject parse1 = JSONObject.parseObject(JSON.toJSONString(d));
								if(parse1.getString("value").equals(s)){
									buffer.append(parse1.getString("text") + ",");
								}else
								if (parse1.getString("text").equals(s)) {
									buffer.append(parse1.getString("text") + ",");
								}
							});
						}

							change.put("oldValue", buffer.toString().substring(0, buffer.toString().length() - 1));




						String[] splitNew = change.get("newValue").toString().split(",");
						StringBuffer bufferNew = new StringBuffer();
						for (String s : splitNew) {
							listDict.forEach(d -> {
								JSONObject parse1 = JSONObject.parseObject(JSON.toJSONString(d));
								if (parse1.getString("value").equals(s)) {
									bufferNew.append(parse1.getString("text") + ",");
								}else
								if (parse1.getString("text").equals(s)) {
									bufferNew.append(parse1.getString("text") + ",");
								}
							});
						}
						change.put("newValue", bufferNew.toString().substring(0, bufferNew.toString().length() - 1));

						}
						contentBuffer.append("  【" + f.getString("dbFieldTxt") + "】 " + change.get("oldValue") + " -> " + change.get("newValue") + "；  ");
					}
				});
			});
			String content = contentBuffer.toString();
			if(content.length() > 0){
				String name = jNew.getString("name");
				if(name == null){
					name = "";
				}else{
					name = "【"+name+"】修改记录:";
				}
				DataLogDTO dataLogDTO = new DataLogDTO(tableName, dataId, name+content, "comment", dataContent);
				sysBaseApiImpl.getClass().getMethod("saveDataLog", DataLogDTO.class).invoke(sysBaseApiImpl, dataLogDTO);
			}


		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	JSONObject convertKeysToSnakeCase(JSONObject jsonObject) {
		JSONObject result = new JSONObject();
		for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
			String snakeKey = camelToSnake(entry.getKey());
			Object value = entry.getValue();

			// 如果值还是JSONObject，则递归处理
			if (value instanceof JSONObject) {
				value = convertKeysToSnakeCase((JSONObject) value);
			}
			result.put(snakeKey, value);
		}
		return result;
	}

	String camelToSnake(String camelStr) {
		StringBuilder result = new StringBuilder();
		for (int i = 0; i < camelStr.length(); i++) {
			char c = camelStr.charAt(i);
			if (Character.isUpperCase(c) && i>0) {
				result.append("_").append(Character.toLowerCase(c));
			} else {
				result.append(c);
			}
		}
		return result.toString();
	}

	@Override
	public Object plugin(Object target) {
		return Plugin.wrap(target, this);
	}

	@Override
	public void setProperties(Properties properties) {
		// TODO Auto-generated method stub
	}

	//update-begin--Author:scott  Date:20191213 for：关于使用Quzrtz 开启线程任务， #465
    /**
     * 获取登录用户
     * @return
     */
	private LoginUser getLoginUser() {
		LoginUser sysUser = null;
		try {
			sysUser = SecureUtil.currentUser() != null ? SecureUtil.currentUser() : null;
		} catch (Exception e) {
			//e.printStackTrace();
			sysUser = null;
		}
		return sysUser;
	}
	//update-end--Author:scott  Date:20191213 for：关于使用Quzrtz 开启线程任务， #465


	private JSONObject a(String tableame, String id) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("id", id);
		return (JSONObject)this.a((String)tableame, (String)null, (QueryWrapper)wrapper,JSONObject.class);
	}

	private List<JSONObject> b(String tableame, String id,String conditionField) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq(conditionField, id);
		return (List<JSONObject>)this.a((String)tableame, (String)null, (QueryWrapper)wrapper,List.class);
	}

	public <T> T a(String tableame, String var2, QueryWrapper<?> wrapper,Class<T> type) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
		if (oConvertUtils.isNotEmpty(var2)) {
			var2 = SqlInjectionUtil.getSqlInjectField(var2);
		} else {
			var2 = "*";
		}

		wrapper.select(new String[]{var2});
		return this.a(tableame, wrapper,type);
	}

	public <T> T a(String var1, QueryWrapper<?> var2,Class<T> type) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
		var1 = SqlInjectionUtil.getSqlInjectTableName(var1);

		//OnlCgformFieldMapper
		Object onlCgformFieldMapper = SpringContextUtils.getBean("onlCgformFieldMapper");
		//onlCgformFieldMapper.doSelect

		return type == JSONObject.class ? (T) onlCgformFieldMapper.getClass().getMethod("doSelect", String.class, QueryWrapper.class).invoke(onlCgformFieldMapper, var1, var2)
				: (T) onlCgformFieldMapper.getClass().getMethod("doSelectList", String.class, QueryWrapper.class).invoke(onlCgformFieldMapper, var1, var2);
	}

}
