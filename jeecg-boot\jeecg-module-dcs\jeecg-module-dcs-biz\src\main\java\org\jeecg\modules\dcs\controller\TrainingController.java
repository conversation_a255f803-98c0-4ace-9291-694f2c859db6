package org.jeecg.modules.dcs.controller;

import java.io.IOException;
import java.util.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.modules.dcs.entity.Training;
import org.jeecg.modules.dcs.service.ITrainingService;
import org.jeecg.modules.dcs.vo.TrainingVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.dcs.entity.TrainingRecord;
import org.jeecg.modules.dcs.vo.TrainingPage;
import org.jeecg.modules.dcs.service.ITrainingRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;


/**
 * @Description: 培训
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
@Tag(name="培训")
@RestController
@RequestMapping("/dcs/training")
@Slf4j
public class TrainingController {
	@Autowired
	private ITrainingService trainingService;
	@Autowired
	private ITrainingRecordService trainingRecordService;

	/**
	 * 分页列表查询
	 *
	 * @param training
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "培训-分页列表查询")
	@Operation(summary="培训-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TrainingVo>> queryPageList(Training training,
												   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												   HttpServletRequest req) {
        QueryWrapper<Training> queryWrapper = QueryGenerator.initQueryWrapper(training, req.getParameterMap());
		Page<TrainingVo> page = new Page<TrainingVo>(pageNo, pageSize);
		IPage<TrainingVo> pageList = trainingService.queryPageList(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param trainingPage
	 * @return
	 */
	@AutoLog(value = "培训-添加")
	@Operation(summary="培训-添加")
    @PreAuthorize("@jps.requiresPermissions('dcs:training:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TrainingPage trainingPage) {
		TrainingVo training = new TrainingVo();
		BeanUtils.copyProperties(trainingPage, training);
		trainingService.saveMain(training, trainingPage.getTrainingRecordList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param trainingPage
	 * @return
	 */
	@AutoLog(value = "培训-编辑")
	@Operation(summary="培训-编辑")
    @PreAuthorize("@jps.requiresPermissions('dcs:training:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TrainingPage trainingPage) {
		TrainingVo training = new TrainingVo();
		BeanUtils.copyProperties(trainingPage, training);
		TrainingVo trainingEntity = trainingService.getVoById(training.getId());
		if(trainingEntity==null) {
			return Result.error("未找到对应数据");
		}
		trainingService.updateMain(training, trainingPage.getTrainingRecordList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   发起会议
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "培训-发起会议")
	@Operation(summary="培训-发起会议")
    @PreAuthorize("@jps.requiresPermissions('dcs:training:edit')")
	@GetMapping(value = "/createMeeting")
	public Result<String> createMeeting(@RequestParam(name="id",required=true) String id) {
		trainingService.createMeeting(id);
		return Result.OK("发起会议成功!");
	}

	 /**
	  *   发起会议
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "培训-发起会议")
	 @Operation(summary="培训-发起会议")
	 @PreAuthorize("@jps.requiresPermissions('dcs:training:edit')")
	 @GetMapping(value = "/raiseCheackIn")
	 public Result<String> raiseCheackIn(@RequestParam(name="id",required=true) String id) {
		 trainingService.raiseCheackIn(id);
		 return Result.OK("发起签到成功!");
	 }

	 /**
	  *   发起签到
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "培训-签到")
	 @Operation(summary="培训-签到")
	 @PreAuthorize("@jps.requiresPermissions('dcs:training:edit')")
	 @GetMapping(value = "/checkin")
	 public Result<String> checkin(@RequestParam(name="id",required=true) String id) {
		 trainingService.checkin(id);
		 return Result.OK("签到成功!");
	 }

	 /**
	  *   通过id删除
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "培训-通过id删除")
	 @Operation(summary="培训-通过id删除")
	 @PreAuthorize("@jps.requiresPermissions('dcs:training:delete')")
	 @GetMapping(value = "/delete")
	 public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		 trainingService.delMain(id);
		 return Result.OK("删除成功!");
	 }


	 /**
	  *   文件生效
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "培训-文件生效")
	 @Operation(summary="培训-文件生效")
	 @PreAuthorize("@jps.requiresPermissions('dcs:training:edit')")
	 @GetMapping(value = "/activateDoc")
	 public Result<String> activateDoc(@RequestParam(name="id",required=true) String id) {
		 trainingService.activateDoc(id);
		 return Result.OK("文件生效成功!");
	 }


	 /**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "培训-批量删除")
	@Operation(summary="培训-批量删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:training:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.trainingService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "培训-通过id查询")
	@Operation(summary="培训-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Training> queryById(@RequestParam(name="id",required=true) String id) {
		Training training = trainingService.getById(id);
		if(training==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(training);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "培训记录通过主表ID查询")
	@Operation(summary="培训记录-通主表ID查询")
	@GetMapping(value = "/queryTrainingRecordByMainId")
	public Result<List<TrainingRecord>> queryTrainingRecordListByMainId(@RequestParam(name="id",required=true) String id) {
		List<TrainingRecord> trainingRecordList = trainingRecordService.selectByMainId(id);
		return Result.OK(trainingRecordList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param training
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:training:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Training training) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<Training> queryWrapper = QueryGenerator.initQueryWrapper(training, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<Training> trainingList = trainingService.list(queryWrapper);

      // Step.3 组装pageList
      List<TrainingPage> pageList = new ArrayList<TrainingPage>();
      for (Training main : trainingList) {
          TrainingPage vo = new TrainingPage();
          BeanUtils.copyProperties(main, vo);
          List<TrainingRecord> trainingRecordList = trainingRecordService.selectByMainId(main.getId());
          vo.setTrainingRecordList(trainingRecordList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "培训列表");
      mv.addObject(NormalExcelConstants.CLASS, TrainingPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("培训数据", "导出人:"+sysUser.getRealname(), "培训"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:training:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<TrainingPage> list = ExcelImportUtil.importExcel(file.getInputStream(), TrainingPage.class, params);
              for (TrainingPage page : list) {
                  TrainingVo po = new TrainingVo();
                  BeanUtils.copyProperties(page, po);
                  trainingService.saveMain(po, page.getTrainingRecordList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

	 /**
	  *  批量删除
	  *
	  * @param ids
	  * @return
	  */
	 @AutoLog(value = "培训-批量确认")
	 @Operation(summary="培训-批量确认")
	 @PreAuthorize("@jps.requiresPermissions('dcs:training:edit')")
	 @PostMapping(value = "/confirmBatch")
	 public Result<String> confirmBatch(@RequestParam(name="ids",required=true) String ids) {
		 this.trainingService.confirmBatch(Arrays.asList(ids.split(",")));
		 return Result.OK("确认成功！");
	 }
}