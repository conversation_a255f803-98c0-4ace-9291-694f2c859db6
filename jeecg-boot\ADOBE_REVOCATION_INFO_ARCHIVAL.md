# Adobe adbe-revocationInfoArchival属性分析

## 🎯 关键发现

您找到的信息非常重要：

> "实际上，以可互操作的方式将撤销数据作为嵌入式CMS签名容器的无符号属性嵌入是不可能的:您可以在CMS容器中使用签名的 adbe-revocationInfoArchival属性，或者在CMS容器之外使用DSS。"

这揭示了PDF签名中嵌入撤销信息的**两种标准方法**。

## 🔍 技术分析

### 1. adbe-revocationInfoArchival属性
- **位置**：CMS签名容器**内部**
- **类型**：**有签名的属性**（signed attribute）
- **标准**：Adobe定义的标准CMS属性
- **优势**：撤销信息与签名一起被保护

### 2. DSS字典方法
- **位置**：CMS容器**外部**
- **类型**：PDF文档级别的字典
- **标准**：PDF规范定义
- **优势**：实现相对简单

## 💡 为什么adbe-revocationInfoArchival更好？

### 1. 标准性
- Adobe官方定义的标准属性
- 被Adobe Reader原生支持
- 符合CMS/PKCS#7规范

### 2. 安全性
- 撤销信息被包含在签名保护范围内
- 防止撤销信息被篡改
- 与签名数据一体化

### 3. 兼容性
- Adobe Reader优先识别这种方式
- 更好的跨平台兼容性

## 🔧 实现挑战

### 当前问题
```java
// 这需要在CMS签名容器内添加signed attributes
// iText的标准API可能不直接支持这个功能
```

### 技术难点
1. **CMS容器修改**：需要在签名过程中修改CMS容器
2. **Signed Attributes**：需要添加到签名的属性中
3. **iText限制**：iText可能不直接支持这个功能

## 🎯 可能的解决方案

### 方案1：使用BouncyCastle直接操作CMS
```java
// 需要使用BouncyCastle的CMS API
// 在签名过程中添加adbe-revocationInfoArchival属性
CMSSignedDataGenerator generator = new CMSSignedDataGenerator();
// 添加signed attributes包含撤销信息
```

### 方案2：自定义签名容器
```java
// 创建自定义的签名容器
// 在签名过程中嵌入撤销信息
public class CustomSignatureContainer implements ISignatureContainer {
    @Override
    public byte[] sign(InputStream data) throws GeneralSecurityException {
        // 在这里添加adbe-revocationInfoArchival属性
    }
}
```

### 方案3：继续使用DSS方法
- 虽然不是最优方案，但是可行的
- 我们的技术实现已经很完整
- 可能需要额外的Adobe Reader配置

## 🔍 进一步研究方向

### 1. 查找adbe-revocationInfoArchival的OID
- 需要找到这个属性的具体OID标识符
- 确定属性的数据结构

### 2. 研究iText的扩展能力
- 是否可以通过自定义签名容器实现
- 是否有其他API可以添加CMS属性

### 3. 分析Adobe Reader的验证逻辑
- Adobe Reader如何识别LTV
- 优先级：adbe-revocationInfoArchival vs DSS

## 💡 当前建议

### 短期方案
1. **继续使用DSS方法**：我们的实现已经很完整
2. **移除无效的自定义标记**：专注于标准实现
3. **测试当前实现**：看看DSS方法是否足够

### 长期方案
1. **研究adbe-revocationInfoArchival实现**
2. **可能需要更底层的CMS操作**
3. **考虑使用专门的PDF签名库**

## 🎯 总结

**您的发现非常有价值！**

1. ✅ **确认了标准方法**：adbe-revocationInfoArchival是Adobe标准
2. ✅ **解释了我们的问题**：DSS方法可能不是最优选择
3. ✅ **指明了正确方向**：需要在CMS容器内嵌入撤销信息

**建议**：
1. 先测试当前的DSS实现
2. 如果仍有问题，再研究adbe-revocationInfoArchival的具体实现
3. 可能需要使用更底层的CMS API

**这个发现让我们更接近问题的根本解决方案！**
