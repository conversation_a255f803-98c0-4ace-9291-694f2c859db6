package org.jeecg.modules.lims_order.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 回款计划
 * @Author: jeecg-boot
 * @Date:   2025-03-21
 * @Version: V1.0
 */
@Data
@TableName("payment_collection")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="回款计划")
public class PaymentCollection implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**合同编号*/
	@Excel(name = "合同编号", width = 15, dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
	@Dict(dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
    @Schema(description = "合同编号")
    private String orderId;
	/**关联报告*/
	@Excel(name = "关联报告", width = 15, dictTable = "report", dicText = "report_no", dicCode = "id")
	@Dict(dictTable = "report", dicText = "report_no", dicCode = "id")
    @Schema(description = "关联报告")
    private String reportIds;
	/**回款类型*/
	@Excel(name = "回款类型", width = 15, dicCode = "payment_collection_type")
	@Dict(dicCode = "payment_collection_type")
    @Schema(description = "回款类型")
    private String typeId;
	/**计划回款日期*/
	@Excel(name = "计划回款日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "计划回款日期")
    private Date planDate;
	/**计划回款金额*/
	@Excel(name = "计划回款金额", width = 15)
    @Schema(description = "计划回款金额")
    private Double planAmount;
	/**已回款金额*/
	@Excel(name = "已回款金额", width = 15)
    @Schema(description = "已回款金额")
    private Double actualAmount;
	/**回款是否已核销*/
    @Excel(name = "回款是否已核销", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "回款是否已核销")
    private String checked;
	/**是否完成工单*/
    @Excel(name = "是否完成工单", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "是否完成工单")
    private String finished;
	/**负责人*/
	@Excel(name = "负责人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "负责人")
    private String responsibleBy;
    /**期数*/
    @Excel(name = "期数", width = 15)
    @Schema(description = "期数")
    private java.lang.Integer sortNum;
    /**比例*/
    @Excel(name = "比例", width = 15)
    @Schema(description = "比例")
    private java.lang.Integer percentage;
}
