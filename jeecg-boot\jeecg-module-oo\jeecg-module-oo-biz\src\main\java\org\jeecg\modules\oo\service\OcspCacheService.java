package org.jeecg.modules.oo.service;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.cert.X509Certificate;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * OCSP响应缓存服务
 * 提供OCSP响应的缓存功能，减少网络请求，提高性能
 */
@Service
public class OcspCacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(OcspCacheService.class);
    
    // 内存缓存，用于临时存储OCSP响应
    private final ConcurrentHashMap<String, CachedOcspResponse> memoryCache = new ConcurrentHashMap<>();
    
    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRATION_MS = TimeUnit.HOURS.toMillis(1); // 1小时
    
    /**
     * 缓存的OCSP响应
     */
    public static class CachedOcspResponse {
        private final byte[] response;
        private final long timestamp;
        private final String certificateSerial;
        
        public CachedOcspResponse(byte[] response, String certificateSerial) {
            this.response = response.clone();
            this.timestamp = System.currentTimeMillis();
            this.certificateSerial = certificateSerial;
        }
        
        public byte[] getResponse() {
            return response.clone();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRATION_MS;
        }
        
        public String getCertificateSerial() {
            return certificateSerial;
        }
        
        public long getAge() {
            return System.currentTimeMillis() - timestamp;
        }
    }
    
    /**
     * 获取缓存的OCSP响应
     */
    @Cacheable(value = "ocspResponses", key = "#certificateKey")
    public byte[] getCachedOcspResponse(String certificateKey) {
        CachedOcspResponse cached = memoryCache.get(certificateKey);
        
        if (cached != null) {
            if (!cached.isExpired()) {
                logger.debug("OCSP缓存命中 - 证书: {}, 缓存年龄: {}ms", 
                    certificateKey, cached.getAge());
                return cached.getResponse();
            } else {
                // 缓存过期，移除
                memoryCache.remove(certificateKey);
                logger.debug("OCSP缓存过期并移除 - 证书: {}", certificateKey);
            }
        }
        
        return null;
    }
    
    /**
     * 缓存OCSP响应
     */
    public void cacheOcspResponse(String certificateKey, byte[] ocspResponse, String certificateSerial) {
        if (ocspResponse != null && ocspResponse.length > 0) {
            CachedOcspResponse cached = new CachedOcspResponse(ocspResponse, certificateSerial);
            memoryCache.put(certificateKey, cached);
            logger.debug("OCSP响应已缓存 - 证书: {}, 响应大小: {} bytes", 
                certificateKey, ocspResponse.length);
        }
    }
    
    /**
     * 生成证书缓存键
     */
    public String generateCertificateKey(X509Certificate certificate) {
        if (certificate == null) {
            return null;
        }
        
        // 使用证书序列号和颁发者DN作为缓存键
        String serial = certificate.getSerialNumber().toString(16).toUpperCase();
        String issuer = certificate.getIssuerX500Principal().getName();
        
        return String.format("%s_%s", serial, issuer.hashCode());
    }
    
    /**
     * 清除过期的缓存条目
     */
    @CacheEvict(value = "ocspResponses", allEntries = true)
    public void cleanupExpiredEntries() {
        int removedCount = 0;
        
        for (String key : memoryCache.keySet()) {
            CachedOcspResponse cached = memoryCache.get(key);
            if (cached != null && cached.isExpired()) {
                memoryCache.remove(key);
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            logger.info("清理过期OCSP缓存条目: {} 个", removedCount);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        int totalEntries = memoryCache.size();
        int expiredEntries = 0;
        long totalSize = 0;
        
        for (CachedOcspResponse cached : memoryCache.values()) {
            if (cached.isExpired()) {
                expiredEntries++;
            }
            totalSize += cached.getResponse().length;
        }
        
        return new CacheStatistics(totalEntries, expiredEntries, totalSize);
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStatistics {
        private final int totalEntries;
        private final int expiredEntries;
        private final long totalSizeBytes;
        
        public CacheStatistics(int totalEntries, int expiredEntries, long totalSizeBytes) {
            this.totalEntries = totalEntries;
            this.expiredEntries = expiredEntries;
            this.totalSizeBytes = totalSizeBytes;
        }
        
        public int getTotalEntries() { return totalEntries; }
        public int getExpiredEntries() { return expiredEntries; }
        public long getTotalSizeBytes() { return totalSizeBytes; }
        public int getValidEntries() { return totalEntries - expiredEntries; }
        
        @Override
        public String toString() {
            return String.format("CacheStatistics{total=%d, valid=%d, expired=%d, size=%d bytes}", 
                totalEntries, getValidEntries(), expiredEntries, totalSizeBytes);
        }
    }
}