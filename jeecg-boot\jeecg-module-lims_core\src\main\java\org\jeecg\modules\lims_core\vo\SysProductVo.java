package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.entity.SysProduct;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SysProductVo extends SysProduct {
    public static final String CATEGORY_STANDARD = "STANDARD";
    public static final String CATEGORY_CAPABILITY = "CAPABILITY";
    private List<SysProductSubVo> children;
    private java.lang.String hasChild;
    private String method;
    private String methodId;
    private List<SysMethod> methods;
    private String productId;
}
