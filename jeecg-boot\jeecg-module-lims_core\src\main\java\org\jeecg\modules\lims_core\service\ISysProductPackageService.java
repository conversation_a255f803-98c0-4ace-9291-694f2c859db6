package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import org.jeecg.modules.lims_core.entity.SysProductPackageDetails;
import org.jeecg.modules.lims_core.entity.SysProductPackage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.SysProductPackageVo;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 产品套餐
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
public interface ISysProductPackageService extends IService<SysProductPackage> {

	/**
	 * 添加一对多
	 *
	 * @param sysProductPackage
	 * @param sysProductPackageDetailsList
	 */
	public void saveMain(SysProductPackage sysProductPackage,List<SysProductPackageDetails> sysProductPackageDetailsList) ;
	
	/**
	 * 修改一对多
	 *
   * @param sysProductPackage
   * @param sysProductPackageDetailsList
	 */
	public void updateMain(SysProductPackage sysProductPackage,List<SysProductPackageDetails> sysProductPackageDetailsList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	IPage<SysProductPackageVo> listVo(Page<SysProductPackage> page, QueryWrapper<SysProductPackage> queryWrapper);
	public IPage<SysProductPackage> queryPageList(Page<SysProductPackage> page,
											  @Param(Constants.WRAPPER) Wrapper<SysProductPackage> wrapper);
}
