package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.TestTaskFlow;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.FlowSubmitVo;
import org.jeecg.modules.lims_core.vo.Steps;

import java.util.List;

/**
 * @Description: 测试任务流转
 * @Author: jeecg-boot
 * @Date:   2025-03-10
 * @Version: V1.0
 */
public interface ITestTaskFlowService extends IService<TestTaskFlow> {

    IPage<TestTaskFlow> submit(FlowSubmitVo flowSubmitVo, Page<TestTaskFlow> page);

    List<Steps> listProgress(String orderId, String sampleId);
}
