package org.jeecg.modules.dcs.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.dcs.entity.SystemDocTypeDeptPermission;
import org.jeecg.modules.dcs.service.ISystemDocTypeDeptPermissionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: system_doc_type_dept_permission
 * @Author: jeecg-boot
 * @Date:   2024-11-29
 * @Version: V1.0
 */
@Tag(name="system_doc_type_dept_permission")
@RestController
@RequestMapping("/dcs/systemDocTypeDeptPermission")
@Slf4j
public class SystemDocTypeDeptPermissionController extends JeecgController<SystemDocTypeDeptPermission, ISystemDocTypeDeptPermissionService> {
	@Autowired
	private ISystemDocTypeDeptPermissionService systemDocTypeDeptPermissionService;

     /**
	 * 分页列表查询
	 *
	 * @param systemDocTypeDeptPermission
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "system_doc_type_dept_permission-分页列表查询")
	@Operation(summary="system_doc_type_dept_permission-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SystemDocTypeDeptPermission>> queryPageList(SystemDocTypeDeptPermission systemDocTypeDeptPermission,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SystemDocTypeDeptPermission> queryWrapper = QueryGenerator.initQueryWrapper(systemDocTypeDeptPermission, req.getParameterMap());
		Page<SystemDocTypeDeptPermission> page = new Page<SystemDocTypeDeptPermission>(pageNo, pageSize);
		IPage<SystemDocTypeDeptPermission> pageList = systemDocTypeDeptPermissionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param systemDocTypeDeptPermission
	 * @return
	 */
	@AutoLog(value = "system_doc_type_dept_permission-添加")
	@Operation(summary="system_doc_type_dept_permission-添加")
	@PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type_dept_permission:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SystemDocTypeDeptPermission systemDocTypeDeptPermission) {
		systemDocTypeDeptPermissionService.save(systemDocTypeDeptPermission);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param systemDocTypeDeptPermission
	 * @return
	 */
	@AutoLog(value = "system_doc_type_dept_permission-编辑")
	@Operation(summary="system_doc_type_dept_permission-编辑")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type_dept_permission:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SystemDocTypeDeptPermission systemDocTypeDeptPermission) {
		systemDocTypeDeptPermissionService.updateById(systemDocTypeDeptPermission);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "system_doc_type_dept_permission-通过id删除")
	@Operation(summary="system_doc_type_dept_permission-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type_dept_permission:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		systemDocTypeDeptPermissionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "system_doc_type_dept_permission-批量删除")
	@Operation(summary="system_doc_type_dept_permission-批量删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type_dept_permission:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.systemDocTypeDeptPermissionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "system_doc_type_dept_permission-通过id查询")
	@Operation(summary="system_doc_type_dept_permission-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SystemDocTypeDeptPermission> queryById(@RequestParam(name="id",required=true) String id) {
		SystemDocTypeDeptPermission systemDocTypeDeptPermission = systemDocTypeDeptPermissionService.getById(id);
		if(systemDocTypeDeptPermission==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(systemDocTypeDeptPermission);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param systemDocTypeDeptPermission
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type_dept_permission:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SystemDocTypeDeptPermission systemDocTypeDeptPermission) {
        return super.exportXls(request, systemDocTypeDeptPermission, SystemDocTypeDeptPermission.class, "system_doc_type_dept_permission");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type_dept_permission:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SystemDocTypeDeptPermission.class);
    }

	 @GetMapping("/get")
	 public Result<List<SystemDocTypeDeptPermission>> getSystemDocTypeDeptPermission(@RequestParam("typeId") String typeId) {
		 List<SystemDocTypeDeptPermission> systemDocTypeDeptPermissionList = systemDocTypeDeptPermissionService.getSystemDocTypeDeptPermission(typeId);
		 return Result.ok(systemDocTypeDeptPermissionList);
	 }

	 @PostMapping("/save")
	 public Result<Boolean> saveSysDeptPermission(@Valid @RequestBody List<SystemDocTypeDeptPermission> updateReqVOs) {
		 for(SystemDocTypeDeptPermission vo : updateReqVOs){
			 if(vo.getId()==null)
				 systemDocTypeDeptPermissionService.save(vo);
			 else
				 systemDocTypeDeptPermissionService.updateById(vo);
		 }
		 return Result.ok(true);
	 }
}