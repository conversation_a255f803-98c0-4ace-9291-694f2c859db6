package org.jeecg.modules.dcs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;

@TemplateDesigner(value = "TrainingDoc",description = "培训文件")
@Data
public class TrainingDocInfo {
    private String trainingId;
    @Schema(description = "培训题目或内容概要")
    private String name;
    @Schema(description = "文件编号")
    private String docNo;
    @Schema(description = "文件版本")
    private int ver;
    @Schema(description = "课时（H）")
    private int duration;
    @Schema(description = "培训时间")
    private java.util.Date startTime;
    @Schema(description = "授课人")
    private String teacher;
}