package org.jeecg.modules.oo.util;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;

/**
 * OCSP诊断工具
 * 用于诊断OCSP连接问题和网络状态
 */
public class OcspDiagnosticTool {
    
    /**
     * 运行完整的OCSP诊断
     */
    public static void runFullDiagnostic(X509Certificate certificate, X509Certificate issuer) {
        System.out.println("=== OCSP诊断工具 ===");
        System.out.println();
        
        // 1. 证书信息诊断
        diagnoseCertificateInfo(certificate, issuer);
        
        // 2. 网络连接诊断
        diagnoseNetworkConnectivity();
        
        // 3. OCSP服务器诊断
        diagnoseOcspServers(certificate);
        
        // 4. OCSP请求诊断
        diagnoseOcspRequest(certificate, issuer);
        
        System.out.println("=== 诊断完成 ===");
    }
    
    /**
     * 诊断证书信息
     */
    private static void diagnoseCertificateInfo(X509Certificate certificate, X509Certificate issuer) {
        System.out.println("1. 证书信息诊断:");
        
        try {
            System.out.println("   证书主体: " + certificate.getSubjectX500Principal().getName());
            System.out.println("   证书颁发者: " + certificate.getIssuerX500Principal().getName());
            System.out.println("   证书序列号: " + certificate.getSerialNumber().toString(16));
            System.out.println("   证书有效期: " + certificate.getNotBefore() + " 至 " + certificate.getNotAfter());
            
            // 检查证书有效期
            try {
                certificate.checkValidity();
                System.out.println("   ✓ 证书有效期正常");
            } catch (Exception e) {
                System.err.println("   ✗ 证书有效期异常: " + e.getMessage());
            }
            
            // 检查颁发者证书
            if (issuer != null) {
                System.out.println("   ✓ 颁发者证书可用");
                System.out.println("   颁发者主体: " + issuer.getSubjectX500Principal().getName());
            } else {
                System.err.println("   ✗ 颁发者证书不可用");
            }
            
            // 检查AIA扩展
            String ocspUrl = OcspUtil.getOcspUrlFromCertificate(certificate);
            if (ocspUrl != null && !ocspUrl.isEmpty()) {
                System.out.println("   ✓ 找到OCSP URL: " + ocspUrl);
            } else {
                System.err.println("   ✗ 证书中未找到OCSP URL");
            }
            
        } catch (Exception e) {
            System.err.println("   证书信息诊断失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 诊断网络连接
     */
    private static void diagnoseNetworkConnectivity() {
        System.out.println("2. 网络连接诊断:");
        
        // 测试基本网络连接
        List<String> testHosts = Arrays.asList(
            "www.baidu.com",
            "www.google.com",
            "ocsp.gdca.com.cn",
            "ocsp2.gdca.com.cn"
        );
        
        for (String host : testHosts) {
            try {
                InetAddress address = InetAddress.getByName(host);
                System.out.println("   ✓ " + host + " -> " + address.getHostAddress());
            } catch (Exception e) {
                System.err.println("   ✗ " + host + " 解析失败: " + e.getMessage());
            }
        }
        
        System.out.println();
    }
    
    /**
     * 诊断OCSP服务器
     */
    private static void diagnoseOcspServers(X509Certificate certificate) {
        System.out.println("3. OCSP服务器诊断:");
        
        // 获取主要OCSP URL
        String primaryUrl = OcspUtil.getOcspUrlFromCertificate(certificate);
        if (primaryUrl != null) {
            System.out.println("   主要OCSP服务器: " + primaryUrl);
            testOcspServerConnectivity(primaryUrl);
        }
        
        // 获取备用OCSP URL
        String[] backupUrls = OcspUtil.getBackupOcspUrls(certificate);
        System.out.println("   备用OCSP服务器数量: " + backupUrls.length);
        
        for (int i = 0; i < backupUrls.length; i++) {
            String backupUrl = backupUrls[i];
            System.out.println("   备用服务器 " + (i+1) + ": " + backupUrl);
            testOcspServerConnectivity(backupUrl);
        }
        
        System.out.println();
    }
    
    /**
     * 测试OCSP服务器连接性
     */
    private static void testOcspServerConnectivity(String ocspUrl) {
        try {
            URL url = new URL(ocspUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("     HTTP连接测试: " + responseCode + " " + connection.getResponseMessage());
            
            if (responseCode == 405) {
                System.out.println("     ✓ 服务器响应正常 (405 Method Not Allowed 是OCSP服务器的正常响应)");
            } else if (responseCode >= 200 && responseCode < 300) {
                System.out.println("     ✓ 服务器响应正常");
            } else if (responseCode >= 400 && responseCode < 500) {
                System.err.println("     ⚠ 客户端错误，但服务器可达");
            } else if (responseCode >= 500) {
                System.err.println("     ✗ 服务器错误");
            }
            
        } catch (java.net.ConnectException e) {
            System.err.println("     ✗ 连接失败: " + e.getMessage());
        } catch (java.net.SocketTimeoutException e) {
            System.err.println("     ✗ 连接超时: " + e.getMessage());
        } catch (java.net.UnknownHostException e) {
            System.err.println("     ✗ 主机名解析失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("     ✗ 连接异常: " + e.getMessage());
        }
    }
    
    /**
     * 诊断OCSP请求
     */
    private static void diagnoseOcspRequest(X509Certificate certificate, X509Certificate issuer) {
        System.out.println("4. OCSP请求诊断:");
        
        if (issuer == null) {
            System.err.println("   ✗ 无法进行OCSP请求诊断：颁发者证书不可用");
            return;
        }
        
        System.out.println("   开始OCSP请求测试...");
        
        // 使用增强的OCSP工具进行请求
        byte[] ocspResponse = OcspUtil.getOcspResponse(certificate, issuer);
        
        if (ocspResponse != null && ocspResponse.length > 0) {
            System.out.println("   ✓ OCSP请求成功，响应大小: " + ocspResponse.length + " bytes");
        } else {
            System.err.println("   ✗ OCSP请求失败");
            
            // 提供故障排除建议
            System.out.println();
            System.out.println("   故障排除建议:");
            System.out.println("   1. 检查网络连接是否正常");
            System.out.println("   2. 检查防火墙是否阻止了OCSP请求");
            System.out.println("   3. 尝试使用不同的OCSP服务器");
            System.out.println("   4. 检查证书是否已过期或被撤销");
            System.out.println("   5. 联系CA提供商确认OCSP服务状态");
        }
        
        System.out.println();
    }
    
    /**
     * 快速OCSP连接测试
     */
    public static boolean quickOcspConnectivityTest() {
        System.out.println("快速OCSP连接测试...");
        
        String[] testUrls = {
            "http://ocsp.gdca.com.cn/ocsp",
            "http://ocsp2.gdca.com.cn/ocsp",
            "http://ocsp1.cfca.com.cn"
        };
        
        boolean anySuccess = false;
        
        for (String url : testUrls) {
            try {
                URL testUrl = new URL(url);
                HttpURLConnection connection = (HttpURLConnection) testUrl.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(3000);
                connection.setReadTimeout(3000);
                
                int responseCode = connection.getResponseCode();
                if (responseCode == 405 || (responseCode >= 200 && responseCode < 300)) {
                    System.out.println("✓ " + url + " 可达");
                    anySuccess = true;
                } else {
                    System.err.println("✗ " + url + " 响应异常: " + responseCode);
                }
                
            } catch (Exception e) {
                System.err.println("✗ " + url + " 连接失败: " + e.getMessage());
            }
        }
        
        return anySuccess;
    }
}