package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import org.jeecg.modules.lims_core.entity.SysWorkflow;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 流程
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
public interface ISysWorkflowService extends IService<SysWorkflow> {

	/**
	 * 添加一对多
	 *
	 * @param sysWorkflow
	 * @param sysWorkflowStepList
	 */
	public void saveMain(SysWorkflow sysWorkflow,List<SysWorkflowStep> sysWorkflowStepList) ;
	
	/**
	 * 修改一对多
	 *
   * @param sysWorkflow
   * @param sysWorkflowStepList
	 */
	public void updateMain(SysWorkflow sysWorkflow,List<SysWorkflowStep> sysWorkflowStepList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
