package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.lims_core.entity.SysCapability;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 检测能力库
 * @Author: jeecg-boot
 * @Date:   2025-04-10
 * @Version: V1.0
 */
public interface SysCapabilityMapper extends BaseMapper<SysCapability> {
    @Update("${sql}")
    void updateBySql(@Param("sql") String sql);
}
