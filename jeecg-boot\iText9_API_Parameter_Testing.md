# iText 9.2 LTV API参数测试指南

## 问题分析

您提到"是否我们调用官方API传递的参数有问题"，这很可能是正确的！iText 7到9.2的升级中，LTV API的参数要求可能发生了变化。

## 当前测试的参数组合

我已经修改了代码，现在会按顺序测试以下参数组合：

### 策略1: 最基本配置
```java
ltvVerification.addVerification(name, ocspClient, crlClient, 
    LtvVerification.CertificateOption.SIGNING_CERTIFICATE, 
    LtvVerification.Level.OCSP, 
    LtvVerification.CertificateInclusion.YES);
```

### 策略2: 使用CRL替代OCSP
```java
ltvVerification.addVerification(name, ocspClient, crlClient, 
    LtvVerification.CertificateOption.SIGNING_CERTIFICATE, 
    LtvVerification.Level.CRL, 
    LtvVerification.CertificateInclusion.YES);
```

### 策略3: 不包含证书
```java
ltvVerification.addVerification(name, ocspClient, crlClient, 
    LtvVerification.CertificateOption.SIGNING_CERTIFICATE, 
    LtvVerification.Level.OCSP, 
    LtvVerification.CertificateInclusion.NO);
```

## 测试步骤

### 1. 重新编译和运行
```bash
mvn clean compile
# 重新签名PDF
```

### 2. 关注关键日志
查看控制台输出，特别关注：
```
使用iText 9.2官方LTV API（增强配置）...
为签名添加LTV验证: sig
  尝试基本策略: SIGNING_CERTIFICATE + OCSP + YES
  ✓ 基本策略成功
✓ LTV验证数据合并完成
✓ iText 9.2官方LTV API处理成功
```

### 3. 如果所有策略都失败
如果看到：
```
  ✗ 基本策略失败: [错误信息]
  ✗ CRL策略失败: [错误信息]
  ✗ 无证书策略失败: [错误信息]
✗ 签名 'sig' 所有LTV策略都失败
⚠ iText 9.2官方LTV API处理失败
```

## 可能的问题和解决方案

### 1. OCSP客户端问题
如果OCSP相关的策略都失败，可能是OCSP客户端配置问题：

```java
// 当前使用的OCSP客户端
IOcspClient ocspClient = new OcspClientBouncyCastle();

// 可能需要尝试：
IOcspClient ocspClient = new OcspClientBouncyCastle(null); // 明确传递null
```

### 2. CRL客户端问题
如果CRL策略失败，可能是CRL客户端配置问题：

```java
// 当前使用的CRL客户端
ICrlClient crlClient = new CrlClientOnline();

// 可能需要尝试：
ICrlClient crlClient = new CrlClientOnline(null); // 明确传递null
```

### 3. 签名名称问题
可能签名名称获取有问题：

```java
List<String> signatureNames = signatureUtil.getSignatureNames();
// 检查signatureNames是否为空或包含无效名称
```

### 4. PDF文档状态问题
可能PDF文档在LTV处理时状态不正确：

```java
// 确保PDF文档处于正确状态
if (pdfDocument.isClosed()) {
    // PDF已关闭，无法处理
}
```

## 进一步的调试建议

### 1. 添加详细的异常信息
如果测试失败，请提供完整的异常堆栈信息，特别是：
- 异常类型
- 异常消息
- 堆栈跟踪

### 2. 检查iText版本兼容性
确认项目中使用的确实是iText 9.2：

```xml
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>kernel</artifactId>
    <version>9.2.0</version>
</dependency>
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>signatures</artifactId>
    <version>9.2.0</version>
</dependency>
```

### 3. 检查BouncyCastle版本
确保BouncyCastle版本与iText 9.2兼容：

```xml
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcprov-jdk18on</artifactId>
    <version>1.78.1</version>
</dependency>
```

## 替代测试方案

### 1. 简化的LTV测试
如果所有参数组合都失败，可以尝试最简化的版本：

```java
try {
    LtvVerification ltv = new LtvVerification(pdfDocument);
    // 不传递任何客户端，让iText自己处理
    ltv.addVerification(signatureName, null, null, 
        LtvVerification.CertificateOption.SIGNING_CERTIFICATE, 
        LtvVerification.Level.OCSP, 
        LtvVerification.CertificateInclusion.NO);
    ltv.merge();
} catch (Exception e) {
    // 查看具体错误
}
```

### 2. 分步测试
分别测试OCSP和CRL客户端是否工作：

```java
// 测试OCSP客户端
try {
    byte[] ocspResponse = ocspClient.getEncoded(certificate, issuer, null);
    System.out.println("OCSP客户端工作正常");
} catch (Exception e) {
    System.err.println("OCSP客户端失败: " + e.getMessage());
}

// 测试CRL客户端
try {
    Collection<byte[]> crls = crlClient.getEncoded(certificate, null);
    System.out.println("CRL客户端工作正常");
} catch (Exception e) {
    System.err.println("CRL客户端失败: " + e.getMessage());
}
```

## 预期结果

通过这些测试，我们应该能够确定：
1. iText 9.2的LTV API是否能正常工作
2. 哪种参数组合适用于您的环境
3. 是否存在特定的配置问题

请运行测试并提供详细的日志输出，这样我们就能确定具体的问题所在。
