package org.jeecg.modules.lims_order.controller;

import java.util.Arrays;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.lims_order.entity.Payment;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_order.service.IPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 回款
 * @Author: jeecg-boot
 * @Date:   2025-03-31
 * @Version: V1.0
 */
@Tag(name="回款")
@RestController
@RequestMapping("/lims_order/payment")
@Slf4j
public class PaymentController extends JeecgController<Payment, IPaymentService> {
	@Autowired
	private IPaymentService paymentService;
	
	/**
	 * 分页列表查询
	 *
	 * @param payment
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "回款-分页列表查询")
	@Operation(summary="回款-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Payment>> queryPageList(Payment payment,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Payment> queryWrapper = QueryGenerator.initQueryWrapper(payment, req.getParameterMap());
		Page<Payment> page = new Page<Payment>(pageNo, pageSize);
		IPage<Payment> pageList = paymentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param payment
	 * @return
	 */
	@AutoLog(value = "回款-添加")
	@Operation(summary="回款-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:payment:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Payment payment) {
		paymentService.savePayment(payment);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param payment
	 * @return
	 */
	@AutoLog(value = "回款-编辑")
	@Operation(summary="回款-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Payment payment) {
		paymentService.editPayment(payment);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "回款-通过id删除")
	@Operation(summary="回款-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		paymentService.deletePayment(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "回款-批量删除")
	@Operation(summary="回款-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.paymentService.deletePayments(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "回款-通过id查询")
	@Operation(summary="回款-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Payment> queryById(@RequestParam(name="id",required=true) String id) {
		Payment payment = paymentService.getById(id);
		if(payment==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(payment);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param payment
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Payment payment) {
        return super.exportXls(request, payment, Payment.class, "回款");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:payment:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Payment.class);
    }

}
