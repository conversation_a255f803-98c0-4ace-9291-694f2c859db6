package org.jeecg.modules.lims_core.vo;

import cn.hutool.core.date.DateTime;
import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

import java.util.List;

@Data
@TemplateDesigner(value = "Report",drillUp = "sample_id->sample.id")
public class ReportVO  {
    @TemplateField(description = "ID")
    private String id;
    @TemplateField(entityFieldName = "id",func = "getReportNo",description = "报告编号")
    private String reportNo;
    @TemplateField(description = "委托方id",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.customer_id->sys_customer.id")
    private String sysCustomerId;
    @TemplateField(description = "委托方",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.customer_id->sys_customer.id->sys_customer.name")
    private String customerName;
    @TemplateField(description = "委托方地址",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.customer_id->sys_customer.id->sys_customer.detailed_address")
    private String customerAddr;
    @TemplateField(description = "编制")
    private String creatBy;
    @TemplateField(description = "审核")
    private String updateBy;
    @TemplateField(description = "签发")
    private String signBy;
    @TemplateField(description = "测试任务")
    private List<TestTaskVO> testTaskList;
    @TemplateField(description = "结论")
    private String conclusion;
    @TemplateField(description = "附注")
    private String remark;
    @TemplateField(description = "附加说明")
    private String description;
    @TemplateField(description = "合同或检验检测委托书编号",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.contract_no")
    private  String contractNo;
    @TemplateField(description = "联系人ID",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.customer_contact_id->sys_customer_contact.id")
    private  String sysCustomerContactId;
    @TemplateField(description = "联系人",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.customer_contact_id->sys_customer_contact.id->sys_customer_contact.name")
    private  String contactName;
    @TemplateField(description = "联系人电话",entityFieldName = "id",drillChain = "report.order_id->biz_order.id->biz_order.customer_contact_id->sys_customer_contact.id->sys_customer_contact.phone")
    private  String contactPhone;
    @TemplateField(entityFieldName = "id",func = "getContactNameAndPhone",description = "联系人和电话")
    private  String contactNameAndPhone;
    @TemplateField(description ="签发日期" )
    private DateTime signTime;
    @TemplateField(entityFieldName = "id",func = "getDetectionBasis",description = "检验检测依据")
    private String evaluation;
    @TemplateField(entityFieldName = "id",func = "getInspectionItems",description = "检验检测项目")
    private String analytes;
    @TemplateField(description = "修订内容")
    private String reviseMemo;

}