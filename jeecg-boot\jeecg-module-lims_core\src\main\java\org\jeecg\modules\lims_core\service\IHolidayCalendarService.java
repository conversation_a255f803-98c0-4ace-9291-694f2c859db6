package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.HolidayCalendar;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;

/**
 * @Description: 节假日管理表
 * @Author: jeecg-boot
 * @Date:   2025-06-05
 * @Version: V1.0
 */
public interface IHolidayCalendarService extends IService<HolidayCalendar> {
    public Date getWorkDate(Date StartDate, int days);

    public Integer getActualWorkDays(Date startDate, int days);
}
