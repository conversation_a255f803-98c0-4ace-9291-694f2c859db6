# 签名外观文字隐藏设置

## 🎯 问题描述

签章处显示了默认的文字信息：
- "Digitally signed by ..."
- "Reason: ..."
- "Location: ..."
- "Date: ..."

需要隐藏这些文字，只显示签章图片。

## 🔧 解决方案

### 关键设置：RenderingMode

使用`PdfSignatureAppearance.RenderingMode.GRAPHIC`来只显示图片：

```java
// 设置为只显示图片，不显示文字描述
appearance.setRenderingMode(PdfSignatureAppearance.RenderingMode.GRAPHIC);
```

### 完整的签名外观配置

```java
PdfSignatureAppearance appearance = signer.getSignatureAppearance();
signer.setFieldName("sig");

// 设置签名信息（但不在外观中显示）
appearance.setReason("我同意签署该文件，并承认数字签名的法律效力。")
          .setLocation("中国广州")
          .setContact("数字签名系统");

// 设置为只显示图片，不显示文字描述
appearance.setRenderingMode(PdfSignatureAppearance.RenderingMode.GRAPHIC);

signer.setCertificationLevel(PdfSigner.CERTIFIED_NO_CHANGES_ALLOWED);
ImageData imgData = ImageDataFactory.create(new ClassPathResource("pic.png").getInputStream().readAllBytes());
appearance.setImage(imgData);
```

## 💡 RenderingMode选项说明

### 1. GRAPHIC（推荐）
- **效果**：只显示签章图片
- **优点**：外观简洁，无文字干扰
- **适用**：需要纯图片签章的场景

### 2. DESCRIPTION
- **效果**：只显示文字描述
- **内容**：签名者、原因、位置、时间等
- **适用**：不需要图片的文字签名

### 3. GRAPHIC_AND_DESCRIPTION（默认）
- **效果**：同时显示图片和文字
- **布局**：图片 + 文字描述
- **适用**：需要完整信息显示的场景

### 4. NAME_AND_DESCRIPTION
- **效果**：显示签名者姓名和描述
- **内容**：重点突出签名者信息
- **适用**：强调签名者身份的场景

## 🎯 预期效果

### 修改前
```
┌─────────────────────┐
│   [签章图片]        │
│ Digitally signed by │
│ 证书持有者姓名      │
│ Reason: 签名原因    │
│ Location: 中国广州  │
│ Date: 2025.07.15    │
└─────────────────────┘
```

### 修改后
```
┌─────────────────────┐
│                     │
│   [签章图片]        │
│                     │
└─────────────────────┘
```

## 🚀 测试步骤

### 1. 重新编译
```bash
mvn clean compile
```

### 2. 生成签名PDF
运行签名功能，生成新的PDF文件。

### 3. 检查签章外观
打开PDF文件，检查签章区域：
- ✅ 应该只显示签章图片
- ✅ 不应该显示任何文字信息
- ✅ 签章区域应该简洁美观

### 4. 验证签名信息
虽然外观不显示文字，但签名信息仍然存在：
- 右键点击签章 → 验证签名
- 应该能看到完整的签名信息（原因、位置等）

## 🔍 其他外观自定义选项

### 1. 自定义签章位置和大小
```java
// 设置签章位置和大小
Rectangle rect = new Rectangle(100, 100, 200, 100);
appearance.setPageRect(rect);
```

### 2. 自定义签章图片
```java
// 使用不同的图片
ImageData imgData = ImageDataFactory.create("path/to/custom/seal.png");
appearance.setImage(imgData);
```

### 3. 图片缩放模式
```java
// 设置图片如何适应签章区域
appearance.setImageScale(1.0f); // 原始大小
// 或者让图片自动适应区域大小（默认行为）
```

## 💡 注意事项

### 1. 签名信息仍然存在
- 虽然外观不显示文字，但签名的元数据信息仍然完整
- 可以通过PDF阅读器的签名验证功能查看

### 2. 法律效力不受影响
- 隐藏显示文字不影响签名的法律效力
- 所有签名信息都正确嵌入在PDF中

### 3. 兼容性
- 所有主流PDF阅读器都支持这种设置
- Adobe Reader、Foxit Reader等都能正确显示

## 🎯 总结

**签章外观文字隐藏已配置完成**：

1. ✅ **设置RenderingMode为GRAPHIC**：只显示图片
2. ✅ **保留签名信息设置**：确保元数据完整
3. ✅ **保持法律效力**：签名功能不受影响

**现在签章应该只显示图片，不显示任何文字信息！**

如果需要进一步调整签章外观（如位置、大小、图片等），请告诉我具体需求。
