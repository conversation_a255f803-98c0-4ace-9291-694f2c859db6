package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

import java.util.Date;

@Data
@TemplateDesigner(value = "TestStandardMaterial",drillUp = "testId->test.id",entity = "TestStandardMaterial",description = "标品/对照品信息")
public class TestStandardMaterialVO {
    @TemplateField(entityFieldName = "standardMaterialId",dictTable = "standard_material",dictKey = "id",dictText = "name",description = "名称")
    private String name;
    @TemplateField(entityFieldName = "standardMaterialId",drillChain = "standard_material.id->standard_material.supplier_Id->sys_supplier.id->sys_supplier.name", description = "厂家")
    private String supplier;
    @TemplateField(entityFieldName = "standardMaterialId",dictTable = "standard_material",dictKey = "id",dictText = "lot_no",description = "批号")
    private String lotNo;
    @TemplateField(entityFieldName = "standardMaterialId",dictTable = "standard_material",dictKey = "id",dictText = "purity",description = "纯度（含量）")
    private String purity;
    @TemplateField(entityFieldName = "standardMaterialId", drillChain="standard_material.id",calcExpr = "purchase_date.plusDays(effective_length)",  description = "有效期至")
    private Date effectiveDate;
}