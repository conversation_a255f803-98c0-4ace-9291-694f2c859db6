<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.TestTaskMapper">
    <select id="queryPageList1" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        select * from (SELECT
                           t.*,s.sample_no,o.contract_no,o.id as order_id,s.name as sample_name,s.receive_date,
                           (case when t.capability_id is not null then (select dept_id from sys_capability c where c.id = t.capability_id)
                                 when t.method_id is not null then (select dept_id from sys_method m,sys_capability c where m.id = t.method_id and m.cid = c.id)
                                 else ''
                               end) as dept_id,o.customer_id ,o.id as biz_order_id,o.quotation_id,o.biz_type_id,sc.name as customer_name,s.lot_no,s.specification,s.test_remark,s.manufacturer ,rp.rd_no,s.remark ,
                           CASE
                               WHEN ss.id IS NOT NULL AND m.name IS NOT NULL THEN
                                   CASE
                                       WHEN m.name = '/' THEN ss.name
                                       ELSE CONCAT(
                                               COALESCE(m.name, ''),
                                               '：',
                                               COALESCE(
                                                       CASE
                                                           WHEN ss.version IS NOT NULL
                                                               AND ss.version != '/'
                                                           AND ss.name LIKE '%》%'
                                                       THEN CONCAT(
                                                               SUBSTRING(ss.name, 1, LOCATE('》', ss.name)),
                                                               ss.version,
                                                               ' ',
                                                               SUBSTRING(ss.name, LOCATE('》', ss.name) + 1)
                                                            )
                                                       WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%GB%'
                                                       THEN CONCAT(ss.name, '-', ss.version)
                                                       ELSE ss.name
                                                       END,
                                                       ''
                                               )
                                            )
                                       END
                               ELSE COALESCE(m.name, '')
                               END AS standard_method_name
                       FROM
                           test_task t
                               left join sample s on t.sample_id = s.id
                               left join biz_order o on s.order_id = o.id
                               left join sys_customer sc on o.customer_id = sc.id
                               LEFT JOIN rd_project rp on rp.id =t.rd_id
                               LEFT JOIN sys_method m on m.id = t.method_id
                               LEFT JOIN sys_standard ss on m.standard_id = ss.id
                      ) a
            ${ew.customSqlSegment}
    </select>

    <select id="sampleQuery" resultType="org.jeecg.modules.lims_core.vo.SampleQuery">
        select * from (
                          SELECT
                              t.id,
                              q.quotation_no,
                              q.responsible_person,
                              o.contract_no,
                              bt.`name` as biz_type,
                              sc.`name` AS customer_name,
                              s.manufacturer,
                              rp.`name` AS rp_name,
                              rp.rd_no,
                              s.`name` AS sample_name,
                              s.sample_no,
                              c.`name`as capability_name,
                              ( select a.tester_time from test a where a.task_id=t.id LIMIT 1) as tester_time,
                      ( select a.checked_time from test a where a.task_id=t.id LIMIT 1) as checked_time,
                      s.receive_date,
                      ( select create_time from test_task_flow a where a.task_id=t.id and a.step_id='PM确认' LIMIT 1) as pm_time,
                      d.depart_name,
                      t.assign_time,
                      t.assignee,
                      t.cooper,
                      t.checker,
                      ( select create_by from test_task_flow a where a.task_id=t.id and a.step_id='报告编制' LIMIT 1) as make_by,
                      ( select create_time from test_task_flow a where a.task_id=t.id and a.step_id='报告编制' LIMIT 1) as make_time,
                      r.approve_by,
                      r.approve_time,
                      r.sign_by,
                      r.sign_time
            FROM
  test_task t
  left join sys_capability c on t.capability_id=c.id
            LEFT JOIN sample s ON t.sample_id = s.id
            LEFT JOIN quotation q ON q.id = s.quotation_id
            LEFT JOIN biz_order o ON s.order_id = o.id
            LEFT JOIN biz_type bt ON bt.id = s.biz_type_id
            LEFT JOIN sys_customer sc ON q.customer_id = sc.id
            LEFT JOIN rd_project rp ON rp.id = t.rd_id
            LEFT JOIN sys_method m ON m.id = t.method_id
            LEFT JOIN sys_standard ss ON m.standard_id = ss.id
            left join sys_depart d on d.id=t.department_id
            left join report r on r.sample_id=s.id

            ) a
            ${ew.customSqlSegment}
    </select>


    <select id="selectHistoryOperators" resultType="string">
        SELECT
            t.assignee
        FROM
            test_task t
                LEFT JOIN sample s ON t.sample_id = s.id
                LEFT JOIN sys_method m ON t.method_id = m.id
                LEFT JOIN sys_user su ON t.assignee = su.realname COLLATE utf8mb4_unicode_ci
        WHERE
            su.status = '1' COLLATE utf8mb4_unicode_ci
          AND m.cid = #{testCapability} COLLATE utf8mb4_unicode_ci
          AND s.name = #{sampleName} COLLATE utf8mb4_unicode_ci
    </select>

    <select id="myTasklist" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        select * from (
                          select tt.id as `id`,'耗材' as typename, sm.name as methodname, sct.name as `name`   from test_task  tt LEFT JOIN sys_method sm on tt.method_id=sm.id
                                                                                                                                  LEFT JOIN sys_method_consumptive  smc on sm.id=smc.method_id
                                                                                                                                  LEFT JOIN sys_consumptive_type  sct on smc.consumptive_type_id = sct.id
                          UNION ALL
                          select tt.id as `id`,'标品' as typename, sm.name as methodname, ssmt.name as `name` from test_task  tt LEFT JOIN sys_method sm on tt.method_id=sm.id
                                                                                                                                 LEFT JOIN sys_method_std_material  smsm  on sm.id=smsm.method_id
                                                                                                                                 LEFT JOIN sys_standard_material_type  ssmt on smsm.std_materila_type_id = ssmt.id
                      ) a
            ${ew.customSqlSegment}
    </select>

    <select id="selectByMainId" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        select * from (
                          select smc.id as 'id', tt.id as `taskid`,'耗材' as typename, sm.name as methodname, sm.id as methodId, sct.name as `name`   from test_task  tt LEFT JOIN sys_method sm on tt.method_id=sm.id
                                                                                                                                                      LEFT JOIN sys_method_consumptive  smc on sm.id=smc.method_id
                                                                                                                                                      LEFT JOIN sys_consumptive_type  sct on smc.consumptive_type_id = sct.id
                          UNION ALL
                          select smsm.id as 'id', tt.id as `taskid`,'标品' as typename, sm.name as methodname , sm.id as methodId, ssmt.name as `name` from test_task  tt LEFT JOIN sys_method sm on tt.method_id=sm.id
                                                                                                                                                      LEFT JOIN sys_method_std_material  smsm  on sm.id=smsm.method_id
                                                                                                                                                      LEFT JOIN sys_standard_material_type  ssmt on smsm.std_materila_type_id = ssmt.id
                      ) a where a.id is not null and a.taskid = #{Id}
    </select>


    <select id="selectByConsumptiveName" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        select * from (
                          select  c.code ,c.name ,'耗材' as typename ,sct.name  as materialstypename ,ss.name as supplier ,c.purity from  consumptive c
                          left join  sys_consumptive_type sct on c.consumptive_type_id=sct.id left join sys_supplier ss on c.supplier_id=ss.id
                      ) a where a.name = #{Name}
    </select>
    <select id="selectByStandardMaterialName" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        select * from (
                          select  sm.code ,sm.name ,'标品' as typename,ssmt.name as materialstypename,ss.name as supplier ,sm.purity   from  standard_Material  sm
                          LEFT JOIN  sys_standard_material_type ssmt on sm.type_id=ssmt.id LEFT JOIN sys_supplier ss on sm.supplier_id=ss.id
                      ) a where a.name = #{Name}
    </select>

</mapper>