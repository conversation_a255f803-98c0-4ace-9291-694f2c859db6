package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodRepeatType;
import org.jeecg.modules.lims_core.mapper.SysMethodRepeatTypeMapper;
import org.jeecg.modules.lims_core.service.ISysMethodRepeatTypeService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 重复性
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodRepeatTypeServiceImpl extends ServiceImpl<SysMethodRepeatTypeMapper, SysMethodRepeatType> implements ISysMethodRepeatTypeService {
	
	@Autowired
	private SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
	
	@Override
	public List<SysMethodRepeatType> selectByMainId(String mainId) {
		return sysMethodRepeatTypeMapper.selectByMainId(mainId);
	}
}
