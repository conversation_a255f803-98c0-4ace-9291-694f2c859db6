package org.jeecg.modules.holiday;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.lims_core.service.impl.HolidayCalendarServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5 16:51
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
public class test {

    @Autowired
    HolidayCalendarServiceImpl holidayCalendarService;

    @Test
    public void testGetWorkDate() throws ParseException {
        // 测试用例：从2025年6月5日开始，推算10个工作日后的日期
        // 2025年6月5日是一个星期四
        // 假设假期数据已经存在于数据库中
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = sdf.parse("2025-05-30");
        System.out.println(holidayCalendarService.getWorkDate(startDate, 10));
    }
}
