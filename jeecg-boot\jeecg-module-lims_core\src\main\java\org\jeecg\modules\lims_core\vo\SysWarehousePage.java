package org.jeecg.modules.lims_core.vo;

import java.util.List;
import org.jeecg.modules.lims_core.entity.SysWarehouse;
import org.jeecg.modules.lims_core.entity.SysWarehouseBox;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;

/**
 * @Description: 仓库
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Data
@Schema(description="仓库")
public class SysWarehousePage {

	/**主键*/
	@Schema(description = "主键")
    private java.lang.String id;
	/**编号*/
	@Excel(name = "编号", width = 15)
	@Schema(description = "编号")
    private java.lang.String code;
	/**名称*/
	@Excel(name = "名称", width = 15)
	@Schema(description = "名称")
    private java.lang.String name;
	/**位置*/
	@Excel(name = "位置", width = 15)
	@Schema(description = "位置")
    private java.lang.String location;
	/**货架数量*/
	@Excel(name = "货架数量", width = 15)
	@Schema(description = "货架数量")
    private java.lang.Integer shelves;
	/**创建人*/
	@Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

	@ExcelCollection(name="货位")
	@Schema(description = "货位")
	private List<SysWarehouseBox> sysWarehouseBoxList;

}
