package org.jeecg.modules.crm.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 客户联系人
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Schema(description="客户联系人")
@Data
@TableName("sys_customer_contact")
public class SysCustomerContact implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**客户ID*/
    @Schema(description = "客户ID")
    private java.lang.String customerId;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @Schema(description = "姓名")
    private java.lang.String name;
	/**手机*/
	@Excel(name = "手机", width = 15)
    @Schema(description = "手机")
    private java.lang.String phone;
	/**电子邮箱*/
	@Excel(name = "电子邮箱", width = 15)
    @Schema(description = "电子邮箱")
    private java.lang.String email;
	/**传真*/
	@Excel(name = "传真", width = 15)
    @Schema(description = "传真")
    private java.lang.String fax;
	/**座机*/
	@Excel(name = "座机", width = 15)
    @Schema(description = "座机")
    private java.lang.String telephone;
	/**职位*/
	@Excel(name = "职位", width = 15)
    @Schema(description = "职位")
    private java.lang.String title;
	/**性别*/
	@Excel(name = "性别", width = 15)
    @Schema(description = "性别")
    private java.lang.String sex;
	/**生日*/
	@Excel(name = "生日", width = 15)
    @Schema(description = "生日")
    private java.lang.String birthday;
	/**爱好*/
	@Excel(name = "爱好", width = 15)
    @Schema(description = "爱好")
    private java.lang.String hobby;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
