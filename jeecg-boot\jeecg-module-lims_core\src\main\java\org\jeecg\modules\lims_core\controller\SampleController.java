package org.jeecg.modules.lims_core.controller;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.entity.Sample;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_core.service.ISampleService;
import org.jeecg.modules.lims_core.vo.QSampleVo;
import org.jeecg.modules.lims_core.vo.SampleTableEditVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * @Description: 样品
 * @Author: jeecg-boot
 * @Date:   2025-01-13
 * @Version: V1.0
 */
@Tag(name="样品")
@RestController
@RequestMapping("/lims_order/sample")
@Slf4j
public class SampleController extends JeecgController<Sample, ISampleService> {
	@Autowired
	private ISampleService sampleService;
	//@AutoLog(value = "样品-分页列表查询")
	@Operation(summary="样品-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<IPage<Sample>> queryPageList(Sample sample,
											   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
		if(hasQuery != null && "true".equals(hasQuery)){
			QueryWrapper<Sample> queryWrapper =  QueryGenerator.initQueryWrapper(sample, req.getParameterMap());
			List<Sample> list = sampleService.queryTreeListNoPage(queryWrapper);
			IPage<Sample> pageList = new Page<>(1, 10, list.size());
			pageList.setRecords(list);
			pageList.getRecords().stream().forEach(item -> {
				item.setServiceType(sampleService.getServiceType(item.getQuotationId()));
				item.setIsSupplementary(sampleService.getIsSupplementary(item.getQuotationId()));
				item.setWarehouseLocation(sampleService.getWarehouseLocation(item.getSampleNo()));
				item.setPhone(sampleService.getCustomerContactPhone(item.getCustomerContactId()));
				item.setDetailedAddress(sampleService.getCustomerAddress(item.getCustomerId()));
				item.setRdName(sampleService.getRdName(item.getRdId()));
			});
			return Result.OK(pageList);
		}else{
			String parentId = sample.getPid();
			if (oConvertUtils.isEmpty(parentId)) {
				parentId = "0";
			}
			sample.setPid(null);
			String receiveDateStr = null;
            if (sample.getReceiveDate() != null) {
				Date receiveDate = sample.getReceiveDate();
				receiveDateStr = new SimpleDateFormat("yyyy-MM-dd").format(receiveDate);
				sample.setReceiveDate(null);
            }
			QueryWrapper<Sample> queryWrapper = QueryGenerator.initQueryWrapper(sample, req.getParameterMap());

			// 使用 eq 防止模糊查询
			queryWrapper.eq("pid", parentId);
			if(receiveDateStr != null){
				queryWrapper.like("receive_date", receiveDateStr);
			}
			Page<Sample> page = new Page<Sample>(pageNo, pageSize);
			IPage<Sample> pageList = sampleService.page(page, queryWrapper);
			pageList.getRecords().stream().forEach(item -> {
				item.setServiceType(sampleService.getServiceType(item.getQuotationId()));
				item.setIsSupplementary(sampleService.getIsSupplementary(item.getQuotationId()));
				item.setWarehouseLocation(sampleService.getWarehouseLocation(item.getSampleNo()));
				item.setPhone(sampleService.getCustomerContactPhone(item.getCustomerContactId()));
				item.setDetailedAddress(sampleService.getCustomerAddress(item.getCustomerId()));
				item.setRdName(sampleService.getRdName(item.getRdId()));
			});
			return Result.OK(pageList);
		}
	}

	/**
	 * 【vue3专用】加载节点的子数据
	 *
	 * @param pid
	 * @return
	 */
	@RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
	public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
		Result<List<SelectTreeModel>> result = new Result<>();
		try {
			List<SelectTreeModel> ls = sampleService.queryListByPid(pid);
			result.setResult(ls);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	/**
	 * 【vue3专用】加载一级节点/如果是同步 则所有数据
	 *
	 * @param async
	 * @param pcode
	 * @return
	 */
	@RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
	public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
		Result<List<SelectTreeModel>> result = new Result<>();
		try {
			List<SelectTreeModel> ls = sampleService.queryListByCode(pcode);
			if (!async) {
				loadAllChildren(ls);
			}
			result.setResult(ls);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	/**
	 * 【vue3专用】递归求子节点 同步加载用到
	 *
	 * @param ls
	 */
	private void loadAllChildren(List<SelectTreeModel> ls) {
		for (SelectTreeModel tsm : ls) {
			List<SelectTreeModel> temp = sampleService.queryListByPid(tsm.getKey());
			if (temp != null && temp.size() > 0) {
				tsm.setChildren(temp);
				loadAllChildren(temp);
			}
		}
	}

	/**
	 * 获取子数据
	 * @param sample
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "样品-获取子数据")
	@Operation(summary="样品-获取子数据")
	@GetMapping(value = "/childList")
	public Result<IPage<Sample>> queryPageList(Sample sample,HttpServletRequest req) {
		QueryWrapper<Sample> queryWrapper = QueryGenerator.initQueryWrapper(sample, req.getParameterMap());
		List<Sample> list = sampleService.list(queryWrapper);
		IPage<Sample> pageList = new Page<>(1, 10, list.size());
		pageList.setRecords(list);
		return Result.OK(pageList);
	}

	/**
	 * 批量查询子节点
	 * @param parentIds 父ID（多个采用半角逗号分割）
	 * @return 返回 IPage
	 * @param parentIds
	 * @return
	 */
	//@AutoLog(value = "样品-批量获取子数据")
	@Operation(summary="样品-批量获取子数据")
	@GetMapping("/getChildListBatch")
	public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
		try {
			QueryWrapper<Sample> queryWrapper = new QueryWrapper<>();
			List<String> parentIdList = Arrays.asList(parentIds.split(","));
			queryWrapper.in("pid", parentIdList);
			List<Sample> list = sampleService.list(queryWrapper);
			IPage<Sample> pageList = new Page<>(1, 10, list.size());
			pageList.setRecords(list);
			return Result.OK(pageList);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Result.error("批量查询子节点失败：" + e.getMessage());
		}
	}

	/**
	 *   添加
	 *
	 * @param sample
	 * @return
	 */
	@AutoLog(value = "样品-添加")
	@Operation(summary="样品-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Sample sample) {
		sampleService.addSample(sample);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sample
	 * @return
	 */
	@AutoLog(value = "样品-编辑")
	@Operation(summary="样品-编辑")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Sample sample) {
		sampleService.updateSample(sample);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "样品-通过id删除")
	@Operation(summary="样品-通过id删除")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sampleService.deleteSample(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "样品-批量删除")
	@Operation(summary="样品-批量删除")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sampleService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "样品-通过id查询")
	@Operation(summary="样品-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Sample> queryById(@RequestParam(name="id",required=true) String id) {
		Sample sample = sampleService.getById(id);
		if(sample==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sample);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param sample
	 */
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:exportXls')")
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, Sample sample) {
		return super.exportXls(request, sample, Sample.class, "样品");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:importExcel')")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, Sample.class);
	}

	/**
	 *  操作记录
	 *
	 * @param
	 * @param
	 * @return
	 */
	@AutoLog(value = "样品-操作记录")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:add')")
	@RequestMapping(value = "/listDataLog", method = RequestMethod.GET)
	public Result<List<Map>> listDataLog(@RequestParam(name="id",required=true) String id) {
		List<Map> dataLogList = sampleService.listDataLog(id);
		return Result.OK(dataLogList);
	}


	/**
	 * 分页列表查询
	 *
	 * @param sample
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "样品-分页列表查询")
	@Operation(summary="样品-分页列表查询")
	@GetMapping(value = "/listVo")
	public Result<IPage<QSampleVo>> listVo(Sample sample,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												  HttpServletRequest req) throws InvocationTargetException, IllegalAccessException {
		QueryWrapper<Sample> queryWrapper = QueryGenerator.initQueryWrapper(sample, req.getParameterMap());
		Page<Sample> page = new Page<Sample>(pageNo, pageSize);
		IPage<QSampleVo> pageList = sampleService.listVo(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加营销产品
	 *
	 * @param
	 * @return
	 */
	@AutoLog(value = "样品-添加营销产品")
	@Operation(summary="样品-添加营销产品")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:add')")
	@PostMapping(value = "/addProduct/{id}")
	public Result<String> addProduct(@RequestBody List<SysProductSubVo> productSubVos, @PathVariable String id) {
		sampleService.addProduct(productSubVos,id);
		return Result.OK("添加成功！");
	}

	/**
	 *   添加能力
	 *
	 * @param
	 * @return
	 */
	@AutoLog(value = "样品-添加能力")
	@Operation(summary="样品-添加能力")
	@PreAuthorize("@jps.requiresPermissions('lims_order:test_task:add')")
	@PostMapping(value = "/addCapability/{id}")
	public Result<String> addCapability(@RequestBody List<String> capabilityIds, @PathVariable String id) {
		sampleService.addCapability(capabilityIds,id);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param
	 * @return
	 */
	@AutoLog(value = "样品-编辑")
	@Operation(summary="样品-编辑")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:edit')")
	@RequestMapping(value = "/editLevel", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> editLevel(@RequestBody SampleTableEditVo vo) {
		sampleService.editLevel(vo);
		return Result.OK("编辑成功!");
	}




	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "样品-通过id删除")
	@Operation(summary="样品-通过id删除")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:delete')")
	@DeleteMapping(value = "/deleteLevel")
	public Result<String> deleteLevel(@RequestParam(name="id",required=true) String id,@RequestParam(name="level",required=true) Integer level) {
		sampleService.delete(id,level);
		return Result.OK("删除成功!");
	}

	/**
	 * 复制样品
	 */
	@AutoLog(value = "样品-复制样品")
	@Operation(summary="样品-复制样品")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:add')")
	@GetMapping(value = "/copySample")
	public Result<String> copySample(@RequestParam(name="id",required=true) String id) {
		sampleService.copySample(id,null);
		return Result.OK("复制成功！");
	}

	/**
	 * 复制样品
	 */
	@AutoLog(value = "样品-按批号复制")
	@Operation(summary="样品-按批号复制")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sample:add')")
	@GetMapping(value = "/copySampleByLotNo")
	public Result<String> copySampleByLotNo(@RequestParam(name="id",required=true) String id) {
		sampleService.copySampleByLotNo(id);
		return Result.OK("复制成功！");
	}


	/**
	 *  取消样品
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "样品-取消样品")
	@Operation(summary="样品-取消样品")
	@GetMapping(value = "/cancelSample")
	public Result<String> cancelSample(@RequestParam(name="ids",required=true) String ids) {
		this.sampleService.cancelSample(Arrays.asList(ids.split(",")));
		return Result.OK("取消样品成功！");
	}

	/**
	 * 恢复样品
	 */
	@AutoLog(value = "样品-恢复样品")
	@Operation(summary="样品-恢复样品")
	@GetMapping(value = "/resumSample")
	public Result<String> resumSample(@RequestParam(name="ids",required=true) String ids) {
		this.sampleService.resumSample(Arrays.asList(ids.split(",")));
		return Result.OK("恢复样品成功！");


	}

	/**
	 * 复制旧样品
	 */
	@AutoLog(value = "样品-复制旧样品")
	@Operation(summary="样品-复制旧样品")
	@GetMapping(value = "/copyOldSample")
	public Result<String> copyOldSample(@RequestParam(name="quotationId",required=true) String quotationId,@RequestParam(name="sampleIds",required=true) String sampleIds) {
		this.sampleService.copyOldSample(quotationId, Arrays.asList(sampleIds.split(",")));
		return Result.OK("复制旧样品成功！");
	}

	/**
	 * 复制旧样品
	 */
	@AutoLog(value = "样品-复制旧样品")
	@Operation(summary="样品-复制旧样品")
	@GetMapping(value = "/addPmRemark")
	public Result<String> addPmRemark(@RequestParam(name="ids",required=true) String ids,@RequestParam(name="pmRemark",required=true) String pmRemark) {
		this.sampleService.addPmRemark(Arrays.asList(ids.split(",")), pmRemark);
		return Result.OK("添加pm备注成功！");
	}
}
