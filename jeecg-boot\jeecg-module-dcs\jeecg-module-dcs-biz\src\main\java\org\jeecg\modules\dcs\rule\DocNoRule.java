package org.jeecg.modules.dcs.rule;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysDepartModel;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.dcs.entity.SystemDocType;
import org.jeecg.modules.dcs.mapper.SystemDocTypeMapper;

import java.util.Arrays;
import java.util.List;

public class DocNoRule implements IFillRuleHandler {
    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        Object r = null;
        if (formData != null && formData.size() > 0) {
            Object objDocNo = formData.get("docNo");
            Object objVer = formData.get("ver");
            if(objDocNo == null){//新增
                Object objTypeId = formData.get("typeId");
                if (objTypeId != null&& objVer!=null) {
                    SystemDocTypeMapper baseMapper = (SystemDocTypeMapper) SpringContextUtils.getBean("systemDocTypeMapper");
                    SystemDocType docType = baseMapper.selectById(objTypeId.toString());
                    String docNoRule = docType.getDocNoRule();
                    String typeCode = docType.getTypeCode();//{类型编码}
                    String nextSn = String.valueOf(docType.getCurrentSn() + 1);
                    String sn = StrUtil.padPre(nextSn,3,'0');//{XXX}流水号，每个X代表1位
                    String ver = StrUtil.padPre(objVer.toString(),2,'0');//{ZZ}版本号
                    String dcsCatgory = null;//{部门代码}
                    // 获取当前日期，格式为YYYYMMDD
                    String currentDate = cn.hutool.core.date.DateUtil.format(new java.util.Date(), "yyyyMMdd");
                    if(params.containsKey("dcs_category")){
                        dcsCatgory = getDcsCatgory(formData);
                    }
                    // 根据docNoRule生成文档编号
                    if (StrUtil.isNotBlank(docNoRule)) {
                        String result = docNoRule;
                        // 替换变量部分
                        if (dcsCatgory != null) {
                            result = result.replace("{部门代码}", dcsCatgory);
                        }
                        result = result.replace("{YYYYMMDD}", currentDate);
                        result = result.replace("{类型编码}", typeCode);
                        result = result.replace("{XXX}", sn);
                        r = result + "/" + ver;
                    } else {
                        // 使用默认规则
                        if (dcsCatgory != null) {
                            r = typeCode + "-" + dcsCatgory + "-" + sn + "/" + ver;
                        } else {
                            r = typeCode + "-" + sn + "/" + ver;
                        }
                    }
                }
            }else{//修订，升版本即可
                if(objVer!=null){
                    int v = Integer.parseInt(objVer.toString());
                    String ver = StrUtil.padPre(String.valueOf(v),2,'0');
                    r = StrUtil.subBefore(objDocNo.toString(),"/",true) + "/" + ver;
                }
            }
        }
        return r;
    }

    private static String getDcsCatgory(JSONObject formData) {
        String dcsCatgory;
        ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);
        LoginUser sysUser = SecureUtil.currentUser();
        String usrDeptCode = sysUser.getOrgCode();
        String usrDeptId = sysBaseApi.getDepartIdsByOrgCode(usrDeptCode);
        String usrCorpId = sysBaseApi.getCorpIdByDeptId(usrDeptId);
        Object relatedDeptId = formData.get("relatedDeptId");
        SysDepartModel otherCorp = null;
        if(relatedDeptId != null){
            List<String> lstRelatedDeptIds = Arrays.asList(relatedDeptId.toString().split(","));
            for(String relatedDeptIdStr : lstRelatedDeptIds){
                String relatedCorpId = sysBaseApi.getCorpIdByDeptId(relatedDeptIdStr);
                if(!relatedCorpId.equals(usrCorpId) ){
                    otherCorp = sysBaseApi.selectAllById(relatedCorpId);
                    break;
                }
            }
        }
        if(otherCorp == null){
            SysDepartModel usrDepart = sysBaseApi.selectAllById(usrDeptId);
            dcsCatgory = usrDepart.getDcsCategory();
        }else{
            dcsCatgory = otherCorp.getDcsCategory();
        }
        return dcsCatgory;
    }
}