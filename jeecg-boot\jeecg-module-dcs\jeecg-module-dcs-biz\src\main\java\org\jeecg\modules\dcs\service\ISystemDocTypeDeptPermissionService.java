package org.jeecg.modules.dcs.service;

import org.jeecg.modules.dcs.entity.SystemDocTypeDeptPermission;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: system_doc_type_dept_permission
 * @Author: jeecg-boot
 * @Date:   2024-11-29
 * @Version: V1.0
 */
public interface ISystemDocTypeDeptPermissionService extends IService<SystemDocTypeDeptPermission> {
    /**
     * 获得文控权限
     * * @param typeId 编号
     *
     * @return 文控权限
     */
    List<SystemDocTypeDeptPermission> getSystemDocTypeDeptPermission(String typeId);

    String getCurUserDcsPermission(String typeId);
}
