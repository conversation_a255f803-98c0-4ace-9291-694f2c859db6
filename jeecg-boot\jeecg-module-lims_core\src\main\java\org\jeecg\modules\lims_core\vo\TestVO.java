package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

import java.util.List;

@Data
@TemplateDesigner(value = "Test",drillUp = "task_id->test_task.id",drillDown = "test_result.test_id",description = "检测信息",whereSel=" test_type_id='0' ")
public class TestVO {
    @TemplateField(description = "id")
    private String id;
    @TemplateField(entityFieldName = "task_id",drillChain = "test_task.id->test_task.method_id->sys_method.id->sys_method.standard_id->sys_standard.id->sys_standard.name",description = "标准名称")
    private String standard;
    @TemplateField(entityFieldName = "task_id",drillChain = "test_task.id->test_task.method_id->sys_method.id->sys_method.name",description = "方法名称")
    private String method;
    @TemplateField(entityFieldName = "task_id",drillChain = "test_task.id->test_task.method_id->sys_method.id->sys_method.standard_id->sys_standard.id->sys_standard.dcs_no",description = "文控编号")
    private String dcsNo;
    @TemplateField(description = "序列路径")
    private String sequenceUrl;
    @TemplateField(description = "采集方法路径")
    private String acquisitionMethodUrl;
    @TemplateField(description = "采集数据路径")
    private String rawDataUrl;
    @TemplateField(description = "定量方法路径")
    private String quantMethodUrl;
    @TemplateField(description = "原始检测结果路径")
    private String rawResultUrl;
    @TemplateField(description = "所用仪器列表")
    private List<TestInstrumentVO> testInstrumentList;
    @TemplateField(description = "所用试剂耗材列表")
    private List<TestConsumptiveVO> testConsumptiveList;
    @TemplateField(description = "所用标品列表")
    private List<TestStandardMaterialVO> testStandardMaterialList;
    @TemplateField(description = "所用溶液列表")
    private List<TestSolutionVO> testSolutionList;
    @TemplateField(description = "实验过程参数列表")
    private List<TestParaVO> testParaList;
    @TemplateField(description = "检测结果")
    private List<TestResultVO> testResultList;
}