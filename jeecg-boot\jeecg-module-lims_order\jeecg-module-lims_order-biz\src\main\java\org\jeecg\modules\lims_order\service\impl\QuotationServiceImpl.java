package org.jeecg.modules.lims_order.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOaService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.oa.SummaryInfo;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApplyEventRequest;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplate;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplateResult;
import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateContent;
import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateControls;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.IBizTypeService;
import org.jeecg.modules.lims_core.service.ISampleService;
import org.jeecg.modules.lims_core.service.ISysKeyService;
import org.jeecg.modules.lims_core.service.impl.SysCapabilityServiceImpl;
import org.jeecg.modules.lims_order.entity.Opportunity;
import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.mapper.OpportunityMapper;
import org.jeecg.modules.lims_order.mapper.QuotationMapper;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecg.modules.lims_order.vo.QuotationPage;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecg.modules.wx.entity.WecomSp;
import org.jeecg.modules.wx.service.IWecomSpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @Description: 报价单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Service
public class QuotationServiceImpl extends ServiceImpl<QuotationMapper, Quotation> implements IQuotationService {
    @Autowired
    private SampleMapper sampleMapper;
    @Autowired
    private TestTaskMapper testTaskMapper;
    @Autowired
    private SysProductMapper sysProductMapper;
    @Autowired
    private SysMethodMapper sysMethodMapper;
    @Autowired
    private IWecomSpService wecomSpService;
    @Autowired
    private SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private OpportunityMapper opportunityMapper;
    @Autowired
    private TestTaskFlowMapper testTaskFlowMapper;
    @Autowired
    private IBizTypeService iBizTypeService;
    @Autowired
    private ISysKeyService iSysKeyService;
    @Autowired
    private QuotationMapper quotationMapper;

    private static final String SAMPLE_FLOW_STATUS_2 = "已入库";
    private static final String SAMPLE_FLOW_STATUS_3 = "已领用";
    @Autowired
    private SysCapabilityServiceImpl sysCapabilityServiceImpl;

    public void calcPrice(String id){
        Quotation quotation = this.baseMapper.selectById(id);
        QueryWrapper<Sample> queryWrapper = Wrappers.query();
        queryWrapper.eq("quotation_id",id);
        List<Sample> samples = sampleMapper.selectList(queryWrapper);
        BigDecimal applyPrice = BigDecimal.ZERO;
        BigDecimal pmPrice = BigDecimal.ZERO;
        BigDecimal standardPrice = BigDecimal.ZERO;
        for (Sample sample : samples) {
            applyPrice = applyPrice.add(sample.getApplyPrice());
            pmPrice = pmPrice.add(sample.getPmPrice());
            standardPrice = standardPrice.add(sample.getStandardPrice());
        }
        quotation.setApplyPrice(applyPrice);
        quotation.setPmPrice(pmPrice);
        if(pmPrice.compareTo(BigDecimal.ZERO) != 0 && standardPrice.compareTo(BigDecimal.ZERO) != 0)
            quotation.setDiscount(applyPrice.divide(pmPrice, 4, RoundingMode.HALF_UP).doubleValue());
        else
            quotation.setDiscount(1.0);

        updateById(quotation);
    }

    @Override
    public void updateSampleQuotationId(Quotation quotation) {
        if( quotation.getOpportunityId() == null || quotation.getOpportunityId().isEmpty()){
            return;
        }
        sampleMapper.selectList(
               new QueryWrapper<Sample>().eq("opportunity_id", quotation.getOpportunityId())
        ).forEach(sample -> {
            sample.setQuotationId(quotation.getId());
            sampleMapper.updateById(sample);
        }
        );
        Opportunity opportunity = opportunityMapper.selectById(quotation.getOpportunityId());
        opportunity.setWinRate(60);
        opportunity.setOpportunityStage("QUOTATION");
        opportunityMapper.updateById(opportunity);

    }

    @Override
    public void preCheck(String id) {
        Quotation quotation = this.getById(id);
//        if (quotation.getStatusId().equals("5")) {
//            throw new RuntimeException("已经转过合同了!!");
//        }

        sampleMapper.selectByQuotationId(id).forEach(sample -> {
            if(sample.getOrderId() != null){
                throw new RuntimeException("已经转过合同了!!");
            }
            BizType biztype = iBizTypeService.getById(sample.getBizTypeId());
            String samplePrefix = biztype.getSamplePrefix();
            SysKey key = iSysKeyService.getById(biztype.getSamplePrefix());

            testTaskMapper.selectBySampleId(sample.getId()).forEach(task -> {
                SysCapability capa = sysCapabilityServiceImpl.getById(task.getCapabilityId());
                Boolean isbiao = Boolean.FALSE;
                if(capa !=null){
                    isbiao = capa.getIsBiao().equals("Y");
                }
                if(task.getDepartmentId() == null){
                    throw new RuntimeException("任务[" + task.getName() + "]没有指派实验组!!");
                }
                if (task.getMethodId() == null && isbiao && task.getTestControlStatus().equals("正常") ) {
                    throw new RuntimeException("YPT产品,请先指定方法!!");
                }
            });
        });


    }


    @Override
    public void apply(String id, ApplyType applyType) throws WxErrorException {
        Quotation quotation = this.baseMapper.selectById(id);

        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("sysCustomerServiceImpl");
        // 根据 ruleCode 查询出实体
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", quotation.getCustomerId());
       JSONObject entity = JSONObject.parseObject(JSONObject.toJSONString(impl.getOne(queryWrapper)));

        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOaService oaService = cpService.getOaService();
        String templateId = "C4ZVWZXc1GaoXbe2etB2em6Q6eqf86a4fQhFYCTCd";
        WxCpOaApprovalTemplateResult templateDetail = oaService.getTemplateDetail(templateId);
        System.out.println(templateDetail.toJson());
        updateTemplateTest(oaService,templateDetail,templateId,"Tips-1732010198545",quotation.getQuotationNo(),"https://gbjc.cc/lims_order/quotationList?no="+quotation.getQuotationNo(),applyType);

        WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
        // creator_userid	 申请人userid，此审批申请将以此员工身份提交，申请人需在应用可见范围内

        LoginUser curUser = SecureUtil.currentUser();
        request.setCreatorUserId(sysBaseAPI.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
        // template_id	 模板id
        request.setTemplateId(templateId);
        // use_template_approver	 0-通过接口指定审批人、抄送人（此时process参数必填）; 1-使用此模板在管理后台设置的审批流程(需要保证审批流程中没有“申请人自选”节点)，支持条件审批。
        request.setUseTemplateApprover(0);
        // apply_data     审批申请数据
        request.setApplyData(getApplyData(
                quotation.getQuotationNo(),
                quotation.getName(),
                entity.getString("name"),
                quotation.getPmPrice().toString(),
                quotation.getApplyPrice().toString(),
                quotation.getLeadTime().toString()
        ));
        // summary_list    摘要信息
        if(applyType == ApplyType.QUOTATION_UNLOCK){
            request.setSummaryList(getSummaryList("报价单号:"+quotation.getQuotationNo(),"申请解锁",""));

        }
        if(applyType == ApplyType.QUOTATION_APPLY){
            request.setSummaryList(getSummaryList("报价单号:"+quotation.getQuotationNo(),"客户名称:"+entity.getString("name"),"申请金额:"+quotation.getApplyPrice()+",PM金额:"+quotation.getPmPrice()+",折扣:"+quotation.getDiscount()));

        }
        // process	 审批流程信息
        request.setApprovers(getApprovers());
        try {
            String sp_no = oaService.apply(request);
            quotation.setStatusId("1");
            quotation.setIsLocked(1);
            updateById(quotation);

            //插入审批表
            WecomSp wecomSp = new WecomSp();
            wecomSp.setSpNo(sp_no);
            wecomSp.setTargetId(quotation.getId());
            wecomSp.setTargetImpl("quotationServiceImpl");
            wecomSpService.save(wecomSp);


        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Map> listDataLog(String id) {
      QueryWrapper<Object> queryWrapper = Wrappers.query();
        queryWrapper.like("json_data", "\"quotationId\":\"" + id + "\"")
            .or(wrapper -> wrapper.eq("data_id", id).eq("data_table", "quotation"))
                .orderBy(true, false, "create_time");

        IService impl = (IService) SpringContextUtils.getBean("sysDataLogServiceImpl");
        JSONArray objects = JSONArray.parseArray(JSONObject.toJSONString(impl.list(queryWrapper)));

        //1.提取出dataTable为quotation的数据
        JSONArray quotationArray = objects.stream().filter(object -> {
            JSONObject jsonObject = (JSONObject) object;
            return jsonObject.getString("dataTable").equals("quotation");
        }).collect(JSONArray::new, JSONArray::add, JSONArray::addAll);
        //2.提取出dataTable为sample的数据
        JSONArray sampleArray = objects.stream().filter(object -> {
            JSONObject jsonObject = (JSONObject) object;
            return jsonObject.getString("dataTable").equals("sample");
        }).collect(JSONArray::new, JSONArray::add, JSONArray::addAll);
        //3.遍历quotation 和 sample, 如果createtime相差在2秒内，就把sample的dataContent放到quotation的dataContent里,并且删除sample的数据
        JSONArray sampleRemoveArray = new JSONArray();
        for (int i = 0; i < quotationArray.size(); i++) {
            JSONObject quotation = quotationArray.getJSONObject(i);
            for (int j = 0; j < sampleArray.size(); j++) {
                JSONObject sample = sampleArray.getJSONObject(j);
                System.out.println("quotation"+quotation.getDate("createTime").getTime());
                System.out.println("sample"+sample.getDate("createTime").getTime());
                if (Math.abs(quotation.getDate("createTime").getTime() - sample.getDate("createTime").getTime()) < 5000) {
                    quotation.put("dataContent", quotation.getString("dataContent") + "<br>✅" + sample.getString("dataContent"));
                    sampleRemoveArray.add(sample);

                }
            }
        }
        //4.删除sample的数据
        sampleArray.removeAll(sampleRemoveArray);
        //5.合并quotation和sample
        JSONArray resultArray = new JSONArray();
        resultArray.addAll(quotationArray);
        resultArray.addAll(sampleArray);
        //6.排序
        resultArray.sort((o1, o2) -> {
            JSONObject jsonObject1 = (JSONObject) o1;
            JSONObject jsonObject2 = (JSONObject) o2;
            return (jsonObject2.getDate("createTime")).compareTo(jsonObject1.getDate("createTime"));
        });
        //7.返回
        return resultArray.toJavaList(Map.class);
    }

    private List<WxCpOaApplyEventRequest.Approver> getApprovers() {
        ArrayList<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>() {{
            add(new WxCpOaApplyEventRequest.Approver().setAttr(1).setUserIds(new String[]{"xiaoxuebin","wuxianzheng","wangjiujun"}));
        }};
        return approvers;

    }

    private List<SummaryInfo> getSummaryList(String text1,String text2,String text3) {
        return new ArrayList<SummaryInfo>() {{
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text1))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text2))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text3))));
        }};
    }

    private WxCpOaApplyEventRequest.ApplyData getApplyData(
            String no,
            String name,
            String customerName,
            String pmPrice,
            String applyPrice,
            String period
    ) {
        return new WxCpOaApplyEventRequest.ApplyData()
                .setContents(Arrays.asList(
                        new ApplyDataContent().setControl("Text").setId("Text-1744869184796").setValue(new ContentValue().setText(customerName != null ? no : "默认订单号")),
                        new ApplyDataContent().setControl("Textarea").setId("Textarea-1744869213123").setValue(new ContentValue().setText(customerName != null ? name : "默认订单名称")),
                        new ApplyDataContent().setControl("Text").setId("Text-1640339319582").setValue(new ContentValue().setText(customerName != null ? customerName : "默认客户名称")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868258243").setValue(new ContentValue().setText(pmPrice != null ? pmPrice : "默认PM金额")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868242390").setValue(new ContentValue().setText(applyPrice != null ? applyPrice : "默认申请金额")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868261172").setValue(new ContentValue().setText(period != null ? period : "默认工期")),
                        new ApplyDataContent().setControl("Tips").setId("Tips-1732010198545")
                ));
    }


    //    @Test
    public void updateTemplateTest(WxCpOaService oaService,WxCpOaApprovalTemplateResult templateDetail,String templateid,String tipsid,String title,String url,ApplyType applyType) throws WxErrorException {
        System.out.println(templateDetail.toJson());
        List<WxCpOaApprovalTemplateResult.TemplateControls> list = templateDetail.getTemplateContent().getControls().stream().filter(control -> control.getProperty().getId().equals(tipsid)).toList();
        if (list.size()>0){
            WxCpOaApprovalTemplateResult.TemplateControls templateControls = list.get(0);
            templateControls.getConfig().getTips().getTipsContent().get(0).getText().getSubText().get(1).getContent().getLink().setUrl(url);
            templateControls.getConfig().getTips().getTipsContent().get(0).getText().getSubText().get(1).getContent().getLink().setTitle(title);
        }
        TemplateContent templateContent = new TemplateContent();
        List<TemplateControls> templateControlsList = new ArrayList<>();

        templateDetail.getTemplateContent().getControls().forEach(control -> {
            //control转成json字符串,再转成control对象
            String controlJson = JSONObject.toJSONString(control);
            TemplateControls templateControls = JSONObject.parseObject(controlJson, TemplateControls.class);
            templateControlsList.add(templateControls);
        });
        templateContent.setControls(templateControlsList);
        templateDetail.getTemplateNames().get(0).setText(applyType.getType());
        WxCpOaApprovalTemplate wxCpOaApprovalTemplate = new WxCpOaApprovalTemplate().setTemplateId(templateid).setTemplateContent(templateContent).setTemplateName(templateDetail.getTemplateNames());
        oaService.updateOaApprovalTemplate(wxCpOaApprovalTemplate);
    }

    public void wxCallback(WxCpXmlMessage wxCpXmlMessage, String id,String typeId) {
        Quotation quotation = this.baseMapper.selectById(id);
        if (wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.QUOTATION_UNLOCK.getType()) && wxCpXmlMessage.getEvent().equals("sys_approval_change")) {
            if(wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("2")){
                quotation.setIsLocked(0);
                quotation.setStatusId("0");
                updateById(quotation);
            }

        }
        if (wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.QUOTATION_APPLY.getType()) && wxCpXmlMessage.getEvent().equals("sys_approval_change")) {
            // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付

            quotation.setStatusId(wxCpXmlMessage.getApprovalInfo().getSpStatus().toString());
            updateById(quotation);

        }

    }



    @Override
    public IPage<Quotation> queryPageList(Page<Quotation> page, Wrapper<QuotationPage> wrapper) {
        return quotationMapper.queryPageList(page,wrapper);
    }

    @Override
    public void cancelQuotation(String id, Boolean revert) {
        Quotation q = this.getById(id);
        if(!revert){
            if(q.getStatusId().equals("4")){
                throw new RuntimeException("该订单已经取消过!!");
            }
            q.setStatusId("4");
            this.updateById(q);
            sampleMapper.selectByQuotationId(q.getId()).forEach(sample -> {
                //iSampleService.cancelSample(Collections.singletonList(sample.getId()));
                //反射调用
                try {
                    ISampleService iSampleService = (ISampleService) SpringContextUtils.getBean("sampleServiceImpl");
                    iSampleService.cancelSample(Collections.singletonList(sample.getId()));
                } catch (Exception e) {
                    throw new RuntimeException("取消样品失败: " + e.getMessage(), e);
                }
            });

        }else{
            if(!q.getStatusId().equals("4")){
                throw new RuntimeException("该订单未被取消,无需恢复!!");
            }
            if(q.getStatusId().equals("4")){
                q.setStatusId("0");
                this.updateById(q);
                sampleMapper.selectByQuotationId(q.getId()).forEach(sample -> {
                    //iSampleService.resumSample(Collections.singletonList(sample.getId()));
                    //反射调用
                    try {
                        ISampleService iSampleService = (ISampleService) SpringContextUtils.getBean("sampleServiceImpl");
                        iSampleService.resumSample(Collections.singletonList(sample.getId()));
                    } catch (Exception e) {
                        throw new RuntimeException("恢复样品失败: " + e.getMessage(), e);
                    }
                });
            }else{
                throw new RuntimeException("该订单状态不允许恢复!!");
            }
        }

    }

}