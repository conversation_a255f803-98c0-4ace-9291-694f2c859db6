package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.entity.SysAnalyte;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.entity.TestResult;
import org.jeecg.modules.lims_core.service.ISysAnalyteService;
import org.jeecg.modules.lims_core.service.ISysMethodAnalyteService;
import org.jeecg.modules.lims_core.service.ISysMethodService;
import org.jeecg.modules.lims_core.service.ITestResultService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 检测结果
 * @Author: jeecg-boot
 * @Date:   2025-07-03
 * @Version: V1.0
 */
@Tag(name="检测结果")
@RestController
@RequestMapping("/lims_order/testResult")
@Slf4j
public class TestResultController extends JeecgController<TestResult, ITestResultService> {
	@Autowired
	private ITestResultService testResultService;
     @Autowired
     private ISysMethodService iSysMethodService;
     @Autowired
     private ISysMethodAnalyteService iSysMethodAnalyteService;
     @Autowired
     private ISysAnalyteService iSysAnalyteService;

	 /**
	 * 分页列表查询
	 *
	 * @param testResult
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "检测结果-分页列表查询")
	@Operation(summary="检测结果-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestResult>> queryPageList(TestResult testResult,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<TestResult> queryWrapper = QueryGenerator.initQueryWrapper(testResult, req.getParameterMap());
		Page<TestResult> page = new Page<TestResult>(pageNo, pageSize);
		IPage<TestResult> pageList = testResultService.page(page, queryWrapper);
		pageList.getRecords().stream().forEach(item -> {
			SysMethodAnalyte methodAnalyte = iSysMethodAnalyteService.getById(item.getMethodAnalyteId());
			SysAnalyte analyte = iSysAnalyteService.getById(methodAnalyte.getAnalyteId());
			item.setAnalyte(analyte.getName());
		});
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param testResult
	 * @return
	 */
	@AutoLog(value = "检测结果-添加")
	@Operation(summary="检测结果-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:test_result:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestResult testResult) {
		testResultService.save(testResult);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param testResult
	 * @return
	 */
	@AutoLog(value = "检测结果-编辑")
	@Operation(summary="检测结果-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_result:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestResult testResult) {
		testResultService.updateById(testResult);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检测结果-通过id删除")
	@Operation(summary="检测结果-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_result:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testResultService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检测结果-批量删除")
	@Operation(summary="检测结果-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_result:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testResultService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检测结果-通过id查询")
	@Operation(summary="检测结果-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestResult> queryById(@RequestParam(name="id",required=true) String id) {
		TestResult testResult = testResultService.getById(id);
		if(testResult==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testResult);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testResult
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_result:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestResult testResult) {
        return super.exportXls(request, testResult, TestResult.class, "检测结果");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_result:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestResult.class);
    }

}
