package org.jeecg.common.api.dto.message;

import lombok.Data;
import java.util.List;

/**
 * 邮件发送请求DTO
 */
@Data
public class EmailDTO {
    /**
     * 收件人列表
     */
    private List<String> to;
    
    /**
     * 抄送人列表
     */
    private List<String> cc;
    
    /**
     * 密送人列表
     */
    private List<String> bcc;
    
    /**
     * 邮件主题
     */
    private String subject;
    
    /**
     * 邮件内容
     */
    private String body;
    
    /**
     * 附件URL列表
     */
    private List<String> attachments;
}