package org.jeecg.modules.lims_core.rule;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.lims_core.entity.BizType;
import org.jeecg.modules.lims_core.entity.SysKey;
import org.jeecg.modules.lims_core.mapper.BizTypeMapper;
import org.jeecg.modules.lims_core.mapper.SysKeyMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/3 16:20
 */
public class RDNoRule implements IFillRuleHandler {

    @Autowired
    BizTypeMapper bizTypeMapper;
    @Autowired
    SysKeyMapper sysKeyMapper;

    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        String bizTypeId = formData.getString("biz_type_id");
        ServiceImpl bizTypeImpl = (ServiceImpl) SpringContextUtils.getBean("bizTypeServiceImpl");
        Object bizTypeObj = bizTypeImpl.getById(bizTypeId);
        JSONObject bizTypeJson = JSON.parseObject(JSON.toJSONString(bizTypeObj));

        String rdPrefix = bizTypeJson.getString("rdPrefix");
        ServiceImpl sysKeyImpl = (ServiceImpl) SpringContextUtils.getBean("sysKeyServiceImpl");
        Object sysKeyObj = sysKeyImpl.getById(rdPrefix);
        JSONObject sysKeyJson = JSON.parseObject(JSON.toJSONString(sysKeyObj));
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        if(!sysKeyJson.getString("midfix").equals(format.format(new Date()))) {
            sysKeyJson.put("midfix", format.format(new Date()));
            sysKeyJson.put("sn", 1);
            sysKeyImpl.updateById(sysKeyJson);
        }
        sysKeyObj = sysKeyImpl.getById(rdPrefix);
        sysKeyJson = JSON.parseObject(JSON.toJSONString(sysKeyObj));

        int sn = sysKeyJson.getInteger("sn");

        String rdNo = sysKeyJson.getString("prefix") + format.format(new Date()) + StrUtil.padPre(String.valueOf(sn), 6, '0');

        sysKeyJson.put("sn", sn + 1);
        sysKeyImpl.updateById(sysKeyJson);
        return rdNo;

    }
}