package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import org.jeecg.modules.lims_core.vo.enums.StepStatus;

import java.util.Date;




@Data
public class Step {
    private String title;
    private String description;
    private String subTitle;
    private StepStatus status;
    private SysWorkflowStep sysWorkflowStep;
    private Date planDate;
    private Date actualDate;
}
