package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodInstrumentType;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 设备类别
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodInstrumentTypeService extends IService<SysMethodInstrumentType> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysMethodInstrumentType>
	 */
	public List<SysMethodInstrumentType> selectByMainId(String mainId);
}
