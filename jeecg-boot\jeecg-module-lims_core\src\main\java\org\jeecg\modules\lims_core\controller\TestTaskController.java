package org.jeecg.modules.lims_core.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import kotlin.reflect.jvm.internal.impl.descriptors.Visibilities;
import org.apache.commons.lang.ObjectUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysWarehouse;
import org.jeecg.modules.lims_core.entity.TestTask;
import org.jeecg.modules.lims_core.service.IConsumptiveService;
import org.jeecg.modules.lims_core.service.IStandardMaterialService;
import org.jeecg.modules.lims_core.service.ITestTaskService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.SampleQuery;
import org.jeecg.modules.lims_core.vo.TaskVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 测试任务
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
@Tag(name="测试任务")
@RestController
@RequestMapping("/lims_order/testTask")
@Slf4j
public class TestTaskController extends JeecgController<TestTask, ITestTaskService> {
	@Autowired
	private ITestTaskService testTaskService;
	@Autowired
	private IConsumptiveService consumptiveService;
	@Autowired
	private IStandardMaterialService standardMaterialService;
	
	/**
	 * 分页列表查询
	 *
	 * @param testTask
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "测试任务-分页列表查询")
	@Operation(summary="测试任务-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestTask>> queryPageList(TestTask testTask,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<TestTask> queryWrapper = QueryGenerator.initQueryWrapper(testTask, req.getParameterMap());
		queryWrapper.ne("status","-1");
		Page<TestTask> page = new Page<TestTask>(pageNo, pageSize);
		IPage<TestTask> pageList = testTaskService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 @Operation(summary="测试任务-分页列表查询")
	 @GetMapping(value = "/listVo")
	 public Result<IPage<TaskVo>> queryPageList(TaskVo taskVo,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												  HttpServletRequest req) {
		 QueryWrapper<TaskVo> queryWrapper = QueryGenerator.initQueryWrapper(taskVo, req.getParameterMap());
		 if(req.getParameterMap().containsKey("checker")){
			 queryWrapper.and(wrapper ->
					 wrapper.isNull("method_id").or().eq("method_id", "")
			 );
		 }
		 Page<TaskVo> page = new Page<>(pageNo, pageSize);
		 IPage<TaskVo> pageList = testTaskService.pageVo(page, queryWrapper);
		 pageList.getRecords().stream().forEach(item -> {
			 item.setServiceType(testTaskService.getServiceType(item.getSampleId()));
			 item.setIsSupplementary(testTaskService.getIsSupplementary(item.getSampleId()));
		 });
		 return Result.OK(pageList);
	 }

	 @Operation(summary="测试任务-我的任务物料信息")
	 @GetMapping(value = "/myTasklist")
	 public Result<IPage<TaskVo>> myTasklist(TaskVo taskVo,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												  HttpServletRequest req) {
		 QueryWrapper<TaskVo> queryWrapper = QueryGenerator.initQueryWrapper(taskVo, req.getParameterMap());
		 queryWrapper.ne("status","-1");
		 Page<TaskVo> page = new Page<>(pageNo, pageSize);
		 IPage<TaskVo> pageList = testTaskService.myTasklist(page, queryWrapper);
		 return Result.OK(pageList);
	 }


	 @Operation(summary="测试任务-查询统计")
	 @GetMapping(value = "/sampleQuery")
	 public Result<IPage<SampleQuery>> sampleQuery(SampleQuery sampleQuery,
											  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											  HttpServletRequest req) {
		String dateStart = "";
		String dateEnd = "";
		 if(req.getParameterMap().get("receiveDate") != null){
			 dateStart = req.getParameterMap().get("receiveDate")[0].toString().split(",")[0];
			 dateEnd = req.getParameterMap().get("receiveDate")[0].toString().split(",")[1];
		 }
		 Map<String, String[]> paramMap = new HashMap<>(req.getParameterMap());
		 paramMap.remove("receiveDate");
		 sampleQuery.setReceiveDate(null);
		 QueryWrapper<SampleQuery> queryWrapper = QueryGenerator.initQueryWrapper(sampleQuery, paramMap);
		 if(oConvertUtils.isNotEmpty(dateStart) && oConvertUtils.isNotEmpty(dateEnd)){
			 queryWrapper.between("receive_date", dateStart, dateEnd);
		 }
		 Page<SampleQuery> page = new Page<>(pageNo, pageSize);
		 IPage<SampleQuery> pageList = testTaskService.sampleQuery(page, queryWrapper);
		 return Result.OK(pageList);
	 }

	 @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:exportXls')")
	 @RequestMapping(value = "/sampleQueryExportXls")
	 public ModelAndView sampleQueryExportXls(HttpServletRequest request, SampleQuery sampleQuery) {
		 return testTaskService.sampleQueryExportXls(request, sampleQuery, SampleQuery.class, "检品查询");
	 }

	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "物料信息-通过TASKID查询")
	 @Operation(summary="物料信息-通过TASKID查询")
	 @GetMapping(value = "/querymyTasklistByMainId")
	 public Result<IPage<TaskVo>> querymyTasklistByMainId(@RequestParam(name="id",required=true) String id) {
		 List<TaskVo> sysTaskList = testTaskService.selectByMainId(id);
		 IPage <TaskVo> page = new Page<>();
		 page.setRecords(sysTaskList);
		 page.setTotal(sysTaskList.size());
		 return Result.OK(page);
	 }
	
	/**
	 *   添加
	 *
	 * @param testTask
	 * @return
	 */
	@AutoLog(value = "测试任务-添加")
	@Operation(summary="测试任务-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:test_task:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestTask testTask) {
		testTaskService.save(testTask);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param testTask
	 * @return
	 */
	@AutoLog(value = "测试任务-编辑")
	@Operation(summary="测试任务-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestTask testTask) {
		testTaskService.updateById(testTask);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "测试任务-通过id删除")
	@Operation(summary="测试任务-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		TestTask task = testTaskService.getById(id);
		if(!task.getCurStep().equals("业务受理")){
			throw new RuntimeException("当前任务状态为"+task.getCurStep()+"，不能删除！请用取消任务！");
		}
		testTaskService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "测试任务-批量删除")
	@Operation(summary="测试任务-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		//判断是否不在业务受理节点,不在就不能删除
		List<TestTask> tasks = testTaskService.listByIds(Arrays.asList(ids.split(",")));
		List<TestTask> notInStep = tasks.stream().filter(item -> !item.getCurStep().equals("业务受理")).collect(Collectors.toList());
		if(notInStep.size() > 0){
			String steps = notInStep.stream().map(TestTask::getCurStep).collect(Collectors.joining(","));
			throw new RuntimeException("当前任务状态为"+steps+"，不能删除！请用取消任务！");
		}
		this.testTaskService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "测试任务-通过id查询")
	@Operation(summary="测试任务-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestTask> queryById(@RequestParam(name="id",required=true) String id) {
		TestTask testTask = testTaskService.getById(id);
		if(testTask==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testTask);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testTask
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestTask testTask) {
        return super.exportXls(request, testTask, TestTask.class, "测试任务");
    }

	 @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:exportXls')")
	 @RequestMapping(value = "/exportXlsVo")
	 public ModelAndView exportXlsVo(HttpServletRequest request, TaskVo taskVo) {
		 return testTaskService.exportXlsVo(request, taskVo, TaskVo.class, "测试任务");
	 }


	 /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestTask.class);
    }

	 /**
	  *   指派任务
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "测试任务-指派任务")
	 @Operation(summary="测试任务-指派任务")
	 @GetMapping(value = "/assign")
	 public Result<String> assign(@RequestParam(name="id",required=true) String id, @RequestParam(name="userName",required=true) String userName, @RequestParam(name="type",required=true) String type ,@RequestParam(name="experimentNotes",required=true) String experimentNotes) {
		 testTaskService.assign(id,userName,type,experimentNotes);
		 return Result.OK("指派成功!");
	 }



	 /**
	  *  编辑
	  *
	  * @param testTask
	  * @return
	  */
	 @AutoLog(value = "测试任务-编辑")
	 @Operation(summary="测试任务-编辑")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:edit')")
	 @RequestMapping(value = "/changeMethod", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> changeMethod(@RequestBody TestTask testTask) {
		 testTaskService.changeMethod(testTask);
		 return Result.OK("编辑成功!");
	 }

	 /**
	  *  编辑
	  *
	  * @param testTask
	  * @return
	  */
	 @AutoLog(value = "测试任务-编辑")
	 @Operation(summary="测试任务-编辑")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:edit')")
	 @RequestMapping(value = "/changesubtractor", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> changesubtractor(@RequestBody TestTask testTask) {
		 testTaskService.changesubtractor(testTask);
		 return Result.OK("编辑成功!");
	 }


	/**
	 *根据名称查询耗材或者标品的编号
	 *
	 * @param name
	 * @return
	 */
	 @AutoLog(value = "测试任务-根据名称查code")
	 @Operation(summary="测试任务-根据名称查code")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:test_task:add')")
	 @GetMapping(value = "/consumptiveOrstandardmateriallist")
	 public Result<IPage<TaskVo>>  consumptiveOrstandardmateriallist(@RequestParam(name="type",required=true) String type, @RequestParam(name="name",required=true) String name) {
		 List<TaskVo> sysTaskList = new ArrayList<>();
		 if ("耗材".equals(type)){
			 sysTaskList = testTaskService.selectByConsumptiveName(name);
		 }else {//标品
			 sysTaskList = testTaskService.selectByStandardMaterialName(name);
		 }
		 IPage <TaskVo> page = new Page<>();
		 page.setRecords(sysTaskList);
		 page.setTotal(sysTaskList.size());
		 return Result.OK(page);
	 }

	 /**
	  *   流程退回
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "测试任务-流程退回")
	 @Operation(summary="测试任务-流程退回")
	 @GetMapping(value = "/revert")
	 public Result<String> revert(@RequestParam(name="id",required=true) String id, @RequestParam(name="step",required=true) String step, @RequestParam(name="reason",required=true) String reason) {
		 testTaskService.revert(id, step, reason);
		 return Result.OK("流程退回成功!");
	 }

	 /**
	  *   取消任务
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "测试任务-取消任务")
	 @Operation(summary="测试任务-取消任务")
	 @GetMapping(value = "/cancel")
	 public Result<String> cancel(@RequestParam(name="id",required=true) String id, @RequestParam(name="isresum",required=true) Boolean isresum) {
		 testTaskService.cancel(id, isresum);
		 return Result.OK("取消成功!");
	 }

	 /**
	  * 催单
	  */
	 @AutoLog(value = "测试任务-催单")
	 @Operation(summary="测试任务-催单")
	 @GetMapping(value = "/cuidan")
	 public Result<String> cuidan(@RequestParam(name="ids",required=true) String ids) {
		 testTaskService.cuidan(ids.split(","));
		 return Result.OK("催单成功!");
	 }

 }
