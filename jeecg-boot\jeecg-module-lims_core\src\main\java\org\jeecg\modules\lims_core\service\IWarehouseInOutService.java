package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import org.jeecg.modules.lims_core.entity.WarehouseInOut;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;

/**
 * @Description: 出入库记录
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
public interface IWarehouseInOutService extends IService<WarehouseInOut> {
    public IPage<WarehouseInOut> queryPageList(Page<WarehouseInOut> page,
                                              @Param(Constants.WRAPPER) Wrapper<WarehouseInOut> wrapper);
}
