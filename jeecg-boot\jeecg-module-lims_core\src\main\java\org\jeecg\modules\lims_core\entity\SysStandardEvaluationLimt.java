package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 标准指标评定要求
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@Schema(description="标准指标评定要求")
@Data
@TableName("sys_standard_evaluation_limt")
public class SysStandardEvaluationLimt implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**序号*/
    @Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private java.lang.Integer sortNum;
	/**标准ID*/
    @Schema(description = "标准ID")
    private java.lang.String standardId;
	/**指标ID*/
    @Excel(name = "指标ID", width = 15, dictTable = "sys_method_anlyate_selector", dicText = "method_analyte_id", dicCode = "id")
    @Schema(description = "指标ID")
    private java.lang.String methodAnalyteId;
	/**指标*/
	@Excel(name = "指标", width = 15, dictTable = "sys_analyte", dicText = "name", dicCode = "id")
    @Schema(description = "指标")
    private java.lang.String analyteId;
    /**指标报告名称*/
    @Excel(name = "指标报告名称", width = 15)
    @Schema(description = "指标报告名称")
    private java.lang.String reportName;
	/**方法*/
	@Excel(name = "方法", width = 15, dictTable = "sys_method", dicText = "name", dicCode = "id")
    @Schema(description = "方法")
    private java.lang.String methodIds;
	/**决定因素类型*/
	@Excel(name = "决定因素类型", width = 15)
    @Schema(description = "决定因素类型")
    private java.lang.String paraType;
	/**决定因素*/
	@Excel(name = "决定因素", width = 15)
    @Schema(description = "决定因素")
    private java.lang.String paraValue;
	/**评定要求*/
	@Excel(name = "评定要求", width = 15)
    @Schema(description = "评定要求")
    private java.lang.String elimit;
	/**计量单位*/
	@Excel(name = "计量单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "计量单位")
    private java.lang.String unitId;
    /**引用ID*/
    @Schema(description = "引用ID")
    private java.lang.String refId;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**父级ID*/
	@Excel(name = "父级ID", width = 15)
    @Schema(description = "父级ID")
    private java.lang.String parentId;
	/**是否有子节点*/
	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @Schema(description = "是否有子节点")
    private java.lang.String hasChild;
    /**第一组别*/
    @Excel(name = "第一组别", width = 15)
    @Schema(description = "第一组别")
    private java.lang.String groupOne;
    /**第二组别*/
    @Excel(name = "第二组别", width = 15)
    @Schema(description = "第二组别")
    private java.lang.String groupTwo;
    @TableField(exist = false)
    private String analyteName;
    @TableField(exist = false)
    private String methodNames;
    @TableField(exist = false)
    private String standardNames;
    @TableField(exist = false)
    private Integer resultType;
}
