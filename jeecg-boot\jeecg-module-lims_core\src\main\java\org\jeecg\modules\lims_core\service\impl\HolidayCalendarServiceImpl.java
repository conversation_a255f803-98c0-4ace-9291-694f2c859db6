package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.lims_core.entity.HolidayCalendar;
import org.jeecg.modules.lims_core.mapper.HolidayCalendarMapper;
import org.jeecg.modules.lims_core.service.IHolidayCalendarService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 节假日管理表
 * @Author: jeecg-boot
 * @Date:   2025-06-05
 * @Version: V1.0
 */
@Service
public class HolidayCalendarServiceImpl extends ServiceImpl<HolidayCalendarMapper, HolidayCalendar> implements IHolidayCalendarService {

    @Override
    public Date getWorkDate(Date StartDate, int days) {
        if (StartDate == null || days == 0) {
            return StartDate;
        }
        List<HolidayCalendar> holidayCalendars = this.list(new QueryWrapper<HolidayCalendar>().ge("date", StartDate));
        Date currentDate = StartDate;
        int count = 0;
        // 推算工作日
        while (count < days) {
            currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000); // 增加一天
            Date finalCurrentDate = currentDate;
            //当前日期不是节假日且不是周末,周末可能包含补班

            // 检查是否为节假日
            boolean isHoliday = holidayCalendars.stream()
                    .anyMatch(holiday -> isSameDay(holiday.getDate(), finalCurrentDate) && holiday.getIsWorkday().equals("N"));

            // 检查是否为补班日
            boolean isWorkingWeekend = holidayCalendars.stream()
                    .anyMatch(holiday -> isSameDay(holiday.getDate(), finalCurrentDate) && holiday.getIsWorkday().equals("Y"));

            // 检查是否为周末
            boolean isWeekend = isWeekend(finalCurrentDate);

            // 如果是工作日(不是节假日且不是周末)或者是补班日，则计数器加1
            if (((!isHoliday && !isWeekend) || isWorkingWeekend)) {
                count++;
            }

        }

        return currentDate;
    }

    @Override
    public Integer getActualWorkDays(Date startDate, int days) {
        Date workDate = this.getWorkDate(startDate, days);
        if (workDate == null) {
            return 0;
        }
        // 计算天数
        long diffInMillies = workDate.getTime() - startDate.getTime();
        long diffInDays = diffInMillies / (1000 * 60 * 60 * 24);
        return (int) diffInDays;
    }

    /**
     * 判断两个日期是否为同一天
     */
    private boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 判断日期是否为周末
     */
    private boolean isWeekend(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY;
    }
}
