package org.jeecg.modules.dcs.util;

import cn.hutool.core.util.StrUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;

import java.util.ArrayList;
import java.util.List;

public class WeworkUtils {
    public static void SendMsg(String msgType,String toUser,NewArticle newArticle){
        if(StrUtil.isNotEmpty(toUser)){
            WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
            String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
            int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
            WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
            WxCpMessageService messageService = cpService.getMessageService();
            WxCpMessage message = new WxCpMessage();
            message.setAgentId(agentId);
            message.setToUser(toUser);
            message.setMsgType(msgType);
            List<NewArticle> mpAs = new ArrayList<>();
            mpAs.add(newArticle);
            message.setArticles(mpAs);
            try {
                messageService.send(message);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
        }
    }
}