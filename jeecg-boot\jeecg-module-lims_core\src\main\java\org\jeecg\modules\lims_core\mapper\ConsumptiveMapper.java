package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.Consumptive;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.ConsumptiveVO;

/**
 * @Description: 耗材台账
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
public interface ConsumptiveMapper extends BaseMapper<Consumptive> {
    IPage<ConsumptiveVO> queryPageList(Page<Consumptive> page,
                                       @Param(Constants.WRAPPER) Wrapper<Consumptive> wrapper);
}
