package org.jeecg.modules.gitlab.service.impl;

import org.jeecg.modules.gitlab.entity.GitlabSchedule;
import org.jeecg.modules.gitlab.mapper.********************;
import org.jeecg.modules.gitlab.service.**********************;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 存储gitlab issue与企微日程的关系
 * @Author: jeecg-boot
 * @Date:   2025-01-03
 * @Version: V1.0
 */
@Service
public class GitlabScheduleServiceImpl extends ServiceImpl<********************, GitlabSchedule> implements ********************** {

}
