<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.SysProductPackageMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.entity.SysProductPackage">
        select * from (
                          SELECT
                              CASE  WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%》%'
                              THEN CONCAT(
                                SUBSTRING(ss.name, 1, LOCATE('》', ss.name)),
                                ss.version,
                                ' ',
                                SUBSTRING(ss.name, LOCATE('》', ss.name) + 1)
                                )
                               WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%GB%'
                               THEN CONCAT(ss.name, '-', ss.version)
                               ELSE ss.name
                             END AS standard_name,spp.*
                               FROM
                              sys_product_package spp left join sys_standard ss on spp.standard_id =ss.ID
                      ) t
            ${ew.customSqlSegment}
    </select>
</mapper>