package org.jeecg.modules.lims_order.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 报价单
 * @Author: jeecg-boot
 * @Date:   2025-01-13
 * @Version: V1.0
 */
@Data
@TableName("quotation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="报价单")
public class Quotation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "quotation_status")
    @Dict(dicCode = "quotation_status")
    @Schema(description = "状态")
    private java.lang.String statusId;
    /**企微审批单号*/
    @Excel(name = "企微审批单号", width = 15)
    @Schema(description = "企微审批单号")
    private java.lang.String spNo;
    /**报价单编号*/
    @Excel(name = "报价单编号", width = 15)
    @Schema(description = "报价单编号")
    private java.lang.String quotationNo;
    /**项目名称*/
    @Excel(name = "项目名称", width = 15)
    @Schema(description = "项目名称")
    private java.lang.String name;
    /**客户*/
    @Excel(name = "客户", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户")
    private java.lang.String customerId;
    /**客户联系人*/
    @Excel(name = "客户联系人", width = 15, dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Schema(description = "客户联系人")
    private java.lang.String customerContactId;
    /**业务分类*/
    @Excel(name = "业务类别", width = 15, dicCode = "biz_type")
    @Dict(dictTable = "biz_type", dicText = "name", dicCode = "id")
    @Schema(description = "业务类别")
    private java.lang.String bizTypeId;
    /**申请用途*/
    @Excel(name = "申请用途", width = 15, dicCode = "apply_for")
    @Dict(dicCode = "apply_for")
    @Schema(description = "申请用途")
    private java.lang.String applyForId;
    /**是否需要方案*/
    @Excel(name = "是否需要方案", width = 15, dicCode = "logic_code")
    @Dict(dicCode = "logic_code")
    @Schema(description = "是否需要方案")
    private java.lang.String needSolution;
    /**方案状态*/
    @Excel(name = "方案状态", width = 15, dicCode = "solution_status")
    @Dict(dicCode = "solution_status")
    @Schema(description = "方案状态")
    private java.lang.String solutionStatusId;
    /**PM价*/
    @Excel(name = "PM价", width = 15)
    @Schema(description = "PM价")
    private java.math.BigDecimal pmPrice;
    /**销售申请价*/
    @Excel(name = "销售申请价", width = 15)
    @Schema(description = "销售申请价")
    private java.math.BigDecimal applyPrice;
    /**审批折扣*/
    @Excel(name = "审批折扣", width = 15)
    @Schema(description = "审批折扣")
    private java.lang.Double discount;
    /**工期*/
    @Excel(name = "工期", width = 15)
    @Schema(description = "工期")
    private java.lang.Integer leadTime;
    /**方案反馈时间要求*/
    @Excel(name = "方案反馈时间要求", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "方案反馈时间要求")
    private java.util.Date solutionReplyDate;
    /**是否加急项目*/
    @Excel(name = "是否加急项目", width = 15, dicCode = "logic_code")
    @Dict(dicCode = "logic_code")
    @Schema(description = "是否加急项目")
    private java.lang.Integer isUrgent;
    /**加急类别*/
    @Excel(name = "加急类别", width = 15, dicCode = "service_type")
    @Dict(dicCode = "service_type")
    @Schema(description = "加急类别")
    private java.lang.String serviceTypeId;
    /**是否发补项目*/
    @Excel(name = "是否发补项目", width = 15, dicCode = "logic_code")
    @Dict(dicCode = "logic_code")
    @Schema(description = "是否发补项目")
    private java.lang.String isSupplementary;
    /**邮寄单号*/
    @Excel(name = "邮寄单号", width = 15)
    @Schema(description = "邮寄单号")
    private java.lang.String expressNo;
    /**信息调研表*/
    @Excel(name = "信息调研表", width = 15)
    @Schema(description = "信息调研表")
    private java.lang.String researchAttachs;
    /**初稿方案*/
    @Excel(name = "初稿方案", width = 15)
    @Schema(description = "初稿方案")
    private java.lang.String draftSolutionAttach;
    /**已签批方案*/
    @Excel(name = "已签批方案", width = 15)
    @Schema(description = "已签批方案")
    private java.lang.String approvedSolutionAttach;
    /**币种*/
    @Excel(name = "币种", width = 15, dicCode = "currency")
    @Dict(dicCode = "currency")
    @Schema(description = "币种")
    private java.lang.String currencyId;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**样品编号累进值*/
    @Excel(name = "样品编号累进值", width = 15)
    @Schema(description = "样品编号累进值")
    private java.lang.Integer sampleSn;
    /**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    @Excel(name = "更新人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String updateBy;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**已锁定*/
    @Excel(name = "已锁定", width = 15)
    @Schema(description = "已锁定")
    private java.lang.Integer isLocked;

    @Schema(description = "电子报价单")
    private java.lang.String url;

    /**商机*/
    @Excel(name = "商机", width = 15)
    @Schema(description = "商机")
    private java.lang.String opportunityId;

    /**剂型*/
	@Excel(name = "剂型", width = 15)
    @Schema(description = "剂型")
    private java.lang.String dosageForm;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @Schema(description = "规格")
    private java.lang.String specification;
	/**是否需要立项报告*/
	@Excel(name = "是否需要立项报告", width = 15)
    @Schema(description = "是否需要立项报告")
    private java.lang.Integer isProjectInitiationReport;
	/**是否需要报价文件*/
	@Excel(name = "是否需要报价文件", width = 15)
    @Schema(description = "是否需要报价文件")
    private java.lang.Integer isQuotationDocument;
    /**负责人*/
    @Excel(name = "负责人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "负责人")
    private java.lang.String responsiblePerson;
    /**工期类型*/
    @Excel(name = "工期类型", width = 15, dicCode = "day_type")
    @Dict(dicCode = "day_type")
    @Schema(description = "工期类型")
    private java.lang.String dayType;
}