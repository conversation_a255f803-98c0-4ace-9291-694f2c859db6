package org.jeecg.modules.dcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMeetingService;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.oa.meeting.WxCpMeeting;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.modules.dcs.entity.DcsDoc;
import org.jeecg.modules.dcs.entity.Training;
import org.jeecg.modules.dcs.entity.TrainingRecord;
import org.jeecg.modules.dcs.mapper.DcsDocMapper;
import org.jeecg.modules.dcs.mapper.TrainingRecordMapper;
import org.jeecg.modules.dcs.mapper.TrainingMapper;
import org.jeecg.modules.dcs.service.ITrainingService;
import org.jeecg.modules.dcs.vo.TrainingVo;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.*;

/**
 * @Description: 培训
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
@Service
public class TrainingServiceImpl extends ServiceImpl<TrainingMapper, Training> implements ITrainingService {

	@Autowired
	private TrainingMapper trainingMapper;
	@Autowired
	private TrainingRecordMapper trainingRecordMapper;
	@Autowired
	private DcsDocMapper dcsDocMapper;
	@Autowired
	private SysUserMapper sysUserMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(TrainingVo trainingVo, List<TrainingRecord> trainingRecordList) {
		Training training = new Training();
		BeanUtils.copyProperties(trainingVo, training);
		trainingMapper.insert(training);


		if(trainingRecordList!=null && trainingRecordList.size()>0) {
			for(TrainingRecord entity:trainingRecordList) {
				//外键设置
				entity.setTrainingId(training.getId());
				trainingRecordMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(TrainingVo trainingVo,List<TrainingRecord> trainingRecordList) {
		Training training = new Training();
		BeanUtils.copyProperties(trainingVo, training);
		trainingMapper.updateById(training);


		//查询之前的子表记录
		List<TrainingRecord> trainingRecords = trainingRecordMapper.selectByMainId(training.getId());



		//如果trainingRecords不存在于trainingRecordList,则删除
		for(TrainingRecord trainingRecord:trainingRecords) {
			boolean isExist = false;
			for(TrainingRecord entity:trainingRecordList) {
				if(entity.getAttendee()!=null) {
					if(entity.getAttendee().equals(trainingRecord.getAttendee())) {
						isExist = true;
						break;
					}
				}
			}
			if(!isExist) {
				trainingRecordMapper.deleteById(trainingRecord.getId());
			}
		}

		//如果trainingRecordList不存在于trainingRecords,则插入
		for(TrainingRecord trainingRecord:trainingRecordList) {
			boolean isInsert = true;
			for(TrainingRecord entity:trainingRecords) {
				if(entity.getAttendee()!=null) {
					if (entity.getAttendee().equals(trainingRecord.getAttendee())) {
						isInsert = false;
						BeanUtils.copyProperties(trainingRecord,entity);
						trainingRecordMapper.updateById(entity);
						break;
					}
				}
			}
			if(isInsert) {
				//外键设置
				trainingRecord.setTrainingId(training.getId());
				trainingRecordMapper.insert(trainingRecord);
			}
		}


	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {


		trainingRecordMapper.deleteByMainId(id);
		trainingMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {


			trainingRecordMapper.deleteByMainId(id.toString());
			trainingMapper.deleteById(id);
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void confirmBatch(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			TrainingRecord trainingRecord = trainingRecordMapper.selectById(id);
			if(trainingRecord.getCheckinTime()==null) {
				throw new RuntimeException("["+sysUserMapper.selectById(trainingRecord.getAttendee()).getRealname()+"]未签到");
			}
			trainingRecord.setCheckedTime(new Date());
			trainingRecord.setCheckedBy(SecureUtil.currentUser().getId());
			trainingRecordMapper.updateById(trainingRecord);
		}
	}

	@Override
	public IPage<TrainingVo> queryPageList(Page<TrainingVo> page, Wrapper<Training> wrapper) {
		return trainingMapper.queryPageList(page,wrapper);
	}

	@Override
	public TrainingVo getVoById(String id){
		return trainingMapper.getVoById(id);
	}


	@Override
	public void createMeeting(String id) {
		// TODO Auto-generated method stub
		Training training = trainingMapper.selectById(id);
		if(training.getMeetingIds()!=null && !training.getMeetingIds().equals("")) {
			throw new RuntimeException("会议已经发起");
		}


		WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
		String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
		int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
		WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
		WxCpMeetingService meetingService = cpService.getMeetingService();
		ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);
		// 创建会议
		WxCpMeeting wxCpMeeting = new WxCpMeeting();
		wxCpMeeting.setAdminUserId(sysBaseApi.getThirdUserIdByUserId(sysUserMapper.getUserByName(training.getCreateBy()).getId(),"wechat_enterprise"));
		wxCpMeeting.setTitle("{测试}培训会议-"+training.getName());
		wxCpMeeting.setMeetingStart(training.getStartTime().getTime() / 1000);
		wxCpMeeting.setMeetingDuration(training.getDuration());
		WxCpMeeting.Attendees attendee = new WxCpMeeting.Attendees();
		List<String> userids = new ArrayList<>();
		trainingRecordMapper.selectByMainId(id).forEach(trainingRecord -> {
			userids.add(sysBaseApi.getThirdUserIdByUserId(trainingRecord.getAttendee(), "wechat_enterprise"));
		});
		userids.add(sysBaseApi.getThirdUserIdByUserId(sysUserMapper.getUserByName(training.getCreateBy()).getId(),"wechat_enterprise"));
		attendee.setUserId(userids);
		wxCpMeeting.setAttendees(attendee);
		try {
			// {"errcode":0,"errmsg":"ok","meetingid":"hyaw0SDgAAj_JgxDVSbUWHWFkolI396Q","excess_users":[],"meeting_code":"757261256"}
			String jsonStr = meetingService.create(wxCpMeeting);
			JsonObject jsonObject = JsonParser.parseString(jsonStr).getAsJsonObject();
			training.setMeetingIds(jsonObject.get("meetingid").getAsString());
			training.setStatus("1");
			trainingMapper.updateById(training);




		} catch (WxErrorException e) {
			throw new RuntimeException(e);
		}

	}


	@Override
	public void checkin(String id) {
		// TODO Auto-generated method stub
		Training training = trainingMapper.selectById(id);
		if(training.getMeetingIds()==null || training.getMeetingIds().equals("")) {
			throw new RuntimeException("会议未发起");
		}
		LoginUser sysUser = SecureUtil.currentUser();
		QueryWrapper<TrainingRecord> trainingRecordQueryWrapper = QueryGenerator.initQueryWrapper(new TrainingRecord(),new HashMap<String,String[]>());
		trainingRecordQueryWrapper.eq("training_id",training.getId());
		trainingRecordQueryWrapper.eq("attendee",sysUser.getId());
		TrainingRecord trainingRecord = trainingRecordMapper.selectOne(trainingRecordQueryWrapper);
		if(trainingRecord==null) {
			throw new RuntimeException("您不在受训人员名单中");
		}
		if(trainingRecord.getCheckinTime()!=null) {
			throw new RuntimeException("您已经签到");
		}
		trainingRecord.setCheckinTime(new Date());
		trainingRecordMapper.updateById(trainingRecord);
	}

	@Override
	public void raiseCheackIn(String id) {
		// TODO Auto-generated method stub
		Training training = trainingMapper.selectById(id);
		if(training.getMeetingIds()==null || training.getMeetingIds().equals("")) {
			throw new RuntimeException("会议未发起");
		}
		if(training.getStatus().equals("0")) {
			throw new RuntimeException("请先发起会议");
		}
		training.setStatus("2");
		trainingMapper.updateById(training);

		trainingRecordMapper.selectByMainId(id).forEach(trainingRecord -> {
			if(trainingRecord.getCheckinTime()==null) {
				WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
				String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
				int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
				WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
				WxCpMessageService messageService = cpService.getMessageService();

				WxCpMessage message = new WxCpMessage();
				message.setAgentId(agentId);
				ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);
				message.setToUser(sysBaseApi.getThirdUserIdByUserId(trainingRecord.getAttendee(), "wechat_enterprise"));

				message.setMsgType("news");
				List<NewArticle> mpAs = new ArrayList<>();
				NewArticle newArticle = new NewArticle();
				newArticle.setTitle("签到提醒");
				newArticle.setDescription(training.getName()+"-培训签到");
				newArticle.setUrl("https://gbjc.cc/dcs/checkin?id="+training.getId());
				newArticle.setPicUrl("http://nwzimg.wezhan.cn/contents/sitefiles2049/10245330/images/48843853.jpg");
				mpAs.add(newArticle);


				message.setArticles(mpAs);
				try {
					messageService.send(message);
				} catch (WxErrorException e) {
					throw new RuntimeException(e);
				}
			}
		});
	}

	@Override
	public void activateDoc(String id){
		Training training = trainingMapper.selectById(id);

		trainingRecordMapper.selectByMainId(id).forEach(trainingRecord -> {
			if(trainingRecord.getCheckinTime()==null) {
				throw new RuntimeException("["+sysUserMapper.selectById(trainingRecord.getAttendee()).getRealname()+"]未签到");
			}
		});



		List<DcsDoc> dcsDocs = dcsDocMapper.selectBatchIds(Arrays.asList(training.getDocIds().split(",")));
		//清掉之前的train id
		dcsDocs.forEach(dcsDoc -> {
			if (dcsDoc.getEffectiveDate() == null){
				dcsDoc.setEffectiveDate(new Date());
				dcsDocMapper.updateById(dcsDoc);
			}
		});
	}

}
