package org.jeecg.modules.dcs.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.dcs.entity.DcsRefedDoc;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 引用文件
 * @Author: jeecg-boot
 * @Date:   2024-12-26
 * @Version: V1.0
 */
public interface DcsRefedDocMapper extends BaseMapper<DcsRefedDoc> {

        @Select("SELECT * FROM dcs_refed_doc WHERE doc_id = #{docId}")
        List<DcsRefedDoc> selectByDocId(@Param("docId") String docId);

}
