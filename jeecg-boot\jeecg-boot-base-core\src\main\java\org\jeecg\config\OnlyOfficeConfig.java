package org.jeecg.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * onlyOffice配置
 * 这里的配置会从 application.yml或application.properties 中读取
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "only-office")
public class OnlyOfficeConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    private String secret;
    private Config config;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Config implements Serializable {
        private static final long serialVersionUID = 1L;
        private Document document;
        private EditorConfig editorConfig;
        private String type;
        private String token;
        private String documentType;
        private String height = "600";
        private String width = "100%";
        private String status;
        private Object report;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Document implements Serializable {
            private static final long serialVersionUID = 1L;
            private String title;
            private String fileType;
            private String key;
            private String url;
            private Permissions permissions;
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class Permissions implements Serializable {
                private static final long serialVersionUID = 1L;
                private Boolean edit = false;
                private Boolean print = false;
                private Boolean download = false;
                private Boolean comment = false;
                private Boolean fillForms;
                private Boolean review;
            }
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class EditorConfig implements Serializable {
            private static final long serialVersionUID = 1L;
            private String callbackUrl;
            private String lang;
            private CoEditing coEditing;
            private Customization customization;
            private String region;
            private User user;
            private Plugins plugins;
            private String mode;

            public User getUser(){
                return user==null?new User():user;
            }

            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class CoEditing implements Serializable {
                private static final long serialVersionUID = 1L;
                private String mode;
                private Boolean change;
            }
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class Customization implements Serializable {
                private static final long serialVersionUID = 1L;
                private Boolean forcesave;
                private Boolean autosave;
                private Boolean comments;
                private Boolean compactHeader;
                private Boolean compactToolbar;
                private Boolean compatibleFeatures;
                private Close close;
                private Customer customer;
                private Features features;
                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public static class Customer implements Serializable {
                    private static final long serialVersionUID = 1L;
                    private String address;
                    private String info;
                    private String logo;
                    private String logoDark;
                    private String mail;
                    private String name;
                    private String phone;
                    private String www;
                }

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public  static  class Close implements Serializable{
                    private static final long serialVersionUID = 1L;
                    private Boolean visible;
                    private String text;
                }
                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public static class Features implements Serializable {
                    private static final long serialVersionUID = 1L;
                    private Spellcheck spellcheck;
                    @Data
                    @AllArgsConstructor
                    @NoArgsConstructor
                    public static class Spellcheck implements Serializable {
                        private static final long serialVersionUID = 1L;
                        private Boolean mode;
                        private Boolean change;
                    }
                }
            }
            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class User implements Serializable {
                private static final long serialVersionUID = 1L;
                private String id;
                private String name;
                private String image;
                private String group;
            }

            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            public static class Plugins implements Serializable {
                private static final long serialVersionUID = 1L;
                private List<String> autostart;
                private List<String> pluginsData;
                private Options options;

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                public static class Options implements Serializable {
                    private static final long serialVersionUID = 1L;
                    private Map<String, Map<String, String>> asc;
                    private Map<String, String> all;
                }
            }
        }
    }
}