package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysInstrumentTypeSettings;
import org.jeecg.modules.lims_core.service.ISysInstrumentTypeSettingsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 数据采集设置
 * @Author: jeecg-boot
 * @Date:   2025-04-28
 * @Version: V1.0
 */
@Tag(name="数据采集设置")
@RestController
@RequestMapping("/lims_core/sysInstrumentTypeSettings")
@Slf4j
public class SysInstrumentTypeSettingsController extends JeecgController<SysInstrumentTypeSettings, ISysInstrumentTypeSettingsService> {
	@Autowired
	private ISysInstrumentTypeSettingsService sysInstrumentTypeSettingsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysInstrumentTypeSettings
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "数据采集设置-分页列表查询")
	@Operation(summary="数据采集设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysInstrumentTypeSettings>> queryPageList(SysInstrumentTypeSettings sysInstrumentTypeSettings,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysInstrumentTypeSettings> queryWrapper = QueryGenerator.initQueryWrapper(sysInstrumentTypeSettings, req.getParameterMap());
		Page<SysInstrumentTypeSettings> page = new Page<SysInstrumentTypeSettings>(pageNo, pageSize);
		IPage<SysInstrumentTypeSettings> pageList = sysInstrumentTypeSettingsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysInstrumentTypeSettings
	 * @return
	 */
	@AutoLog(value = "数据采集设置-添加")
	@Operation(summary="数据采集设置-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:sys_instrument_type_settings:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysInstrumentTypeSettings sysInstrumentTypeSettings) {
		sysInstrumentTypeSettingsService.save(sysInstrumentTypeSettings);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysInstrumentTypeSettings
	 * @return
	 */
	@AutoLog(value = "数据采集设置-编辑")
	@Operation(summary="数据采集设置-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_instrument_type_settings:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysInstrumentTypeSettings sysInstrumentTypeSettings) {
		sysInstrumentTypeSettingsService.updateById(sysInstrumentTypeSettings);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "数据采集设置-通过id删除")
	@Operation(summary="数据采集设置-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_instrument_type_settings:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysInstrumentTypeSettingsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "数据采集设置-批量删除")
	@Operation(summary="数据采集设置-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_instrument_type_settings:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysInstrumentTypeSettingsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "数据采集设置-通过id查询")
	@Operation(summary="数据采集设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysInstrumentTypeSettings> queryById(@RequestParam(name="id",required=true) String id) {
		SysInstrumentTypeSettings sysInstrumentTypeSettings = sysInstrumentTypeSettingsService.getById(id);
		if(sysInstrumentTypeSettings==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysInstrumentTypeSettings);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysInstrumentTypeSettings
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_instrument_type_settings:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysInstrumentTypeSettings sysInstrumentTypeSettings) {
        return super.exportXls(request, sysInstrumentTypeSettings, SysInstrumentTypeSettings.class, "数据采集设置");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_instrument_type_settings:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysInstrumentTypeSettings.class);
    }

}
