package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import cn.hutool.core.date.DateUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.TestParaMapper;
import org.jeecg.modules.lims_core.service.ISysMethodTestingParaService;
import org.jeecg.modules.lims_core.service.ITestParaService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.ITestService;
import org.jeecg.modules.lims_core.service.ITestTaskService;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 检测过程参数
 * @Author: jeecg-boot
 * @Date:   2025-01-20
 * @Version: V1.0
 */
@Tag(name="检测过程参数")
@RestController
@RequestMapping("/lims_core/testPara")
@Slf4j
public class TestParaController extends JeecgController<TestPara, ITestParaService> {
	@Autowired
	private ITestParaService testParaService;
	@Autowired
	private ITestService testService;
	@Autowired
	private ITestTaskService testTaskService;
	@Autowired
	private ISysMethodTestingParaService sysMethodTestingParaService;
	/**
	 * 分页列表查询
	 *
	 * @param testPara
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "检测过程参数-分页列表查询")
	@Operation(summary="检测过程参数-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestParaVoNew>> queryPageList(TestParaVoNew testPara,
													  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													  HttpServletRequest req) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testPara.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test1 = testService.getOne(qwTest);
		if (test1 != null){
			testPara.setTestId(test1.getId());
		}//如果是taskid 就转换成testid,兼容逻辑
		QueryWrapper<TestParaVoNew> queryWrapper = QueryGenerator.initQueryWrapper(testPara, req.getParameterMap());
		Page<TestParaVoNew> page = new Page<TestParaVoNew>(pageNo, pageSize);
		IPage<TestParaVoNew> pageList = testParaService.queryPageList(page, queryWrapper);
		if ((testPara.getParatypeid() != null && !testPara.getParatypeid().trim().isEmpty()) && pageList.getTotal() == 0) {
			Test test = testService.getById(testPara.getTestId());
			if (test == null) {
				return Result.error("未找到对应的检测记录");
			}
			TestTask testTask = testTaskService.getById(test.getTaskId());
			if (testTask == null) {
				return Result.error("未找到对应的检测任务");
			}
			LoginUser sysUser = SecureUtil.currentUser();
			List<SysMethodTestingPara> sysMethodTestingParaList = sysMethodTestingParaService.selectByMainId(testTask.getMethodId());
			for (SysMethodTestingPara entity : sysMethodTestingParaList) {
				TestPara testParanew = new TestPara();
				testParanew.setParaId(entity.getId());
				testParanew.setParaValue(entity.getReq());
				testParanew.setTestId(testPara.getTestId());
				testParanew.setCreateBy(sysUser.getRealname());
				testParanew.setCreateTime(DateUtil.date());
				testParaService.save(testParanew);
			}
			// 再查询
			pageList = testParaService.queryPageList(page, queryWrapper);
		}
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param testPara
	 * @return
	 */
	@AutoLog(value = "检测过程参数-添加")
	@Operation(summary="检测过程参数-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:test_para:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestPara testPara) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testPara.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test = testService.getOne(qwTest);
		if (test != null){
			testPara.setTestId(test.getId());
		}
		testParaService.save(testPara);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param testPara
	 * @return
	 */
	@AutoLog(value = "检测过程参数-编辑")
	@Operation(summary="检测过程参数-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_para:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestPara testPara) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testPara.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test = testService.getOne(qwTest);
		if (test != null){
			testPara.setTestId(test.getId());
		}
		testParaService.updateById(testPara);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检测过程参数-通过id删除")
	@Operation(summary="检测过程参数-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_para:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testParaService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检测过程参数-批量删除")
	@Operation(summary="检测过程参数-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_para:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testParaService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检测过程参数-通过id查询")
	@Operation(summary="检测过程参数-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestPara> queryById(@RequestParam(name="id",required=true) String id) {
		TestPara testPara = testParaService.getById(id);
		if(testPara==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testPara);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testPara
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_para:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestPara testPara) {
        return super.exportXls(request, testPara, TestPara.class, "检测过程参数");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_para:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestPara.class);
    }

}
