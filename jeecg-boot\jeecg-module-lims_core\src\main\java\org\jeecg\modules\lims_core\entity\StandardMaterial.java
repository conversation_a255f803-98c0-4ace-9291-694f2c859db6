package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 标准物质台账
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
@Data
@TableName("standard_material")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="标准物质台账")
public class StandardMaterial implements Serializable {
    private static final long serialVersionUID = 1L;


    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**编号*/
    @Excel(name = "编号", width = 15)
    @Schema(description = "编号")
    private java.lang.String code;
    /**名称*/
    @Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
    /**类型*/
    @Excel(name = "类型", width = 15, dictTable = "sys_standard_material_type", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_standard_material_type", dicText = "name", dicCode = "id")
    @Schema(description = "类型")
    private java.lang.String typeId;
    /**供应商*/
    @Excel(name = "供应商", width = 15, dictTable = "sys_supplier", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_supplier", dicText = "name", dicCode = "id")
    @Schema(description = "供应商")
    private java.lang.String supplierId;
    /**证书编号*/
    @Excel(name = "证书编号", width = 15)
    @Schema(description = "证书编号")
    private java.lang.String certificateNo;
    /**批号*/
    @Excel(name = "批号", width = 15)
    @Schema(description = "批号")
    private java.lang.String lotNo;
	/**规格数量*/
	@Excel(name = "规格数量", width = 15)
	@Schema(description = "规格数量")
	private java.lang.Double spec;
	/**规格单位*/
	@Excel(name = "规格单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Schema(description = "规格单位")
	private java.lang.String specUnit;
    /**浓或纯度及不确定度*/
    @Excel(name = "浓或纯度及不确定度", width = 15)
    @Schema(description = "浓或纯度及不确定度")
    private java.lang.String purity;
    /**购入日期*/
    @Excel(name = "购入日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "购入日期")
    private java.util.Date purchaseDate;
    /**价格*/
    @Excel(name = "价格", width = 15)
    @Schema(description = "价格")
    private java.math.BigDecimal price;
    /**有效期（天）*/
    @Excel(name = "有效期（天）", width = 15)
    @Schema(description = "有效期（天）")
    private java.lang.Integer effectiveLength;
    /**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "sm_status")
    @Dict(dicCode = "sm_status")
    @Schema(description = "状态")
    private java.lang.String smStatus;
    /**数量*/
    @Excel(name = "数量", width = 15)
    @Schema(description = "数量")
    private java.lang.Integer qty;
    /**单位*/
    @Excel(name = "单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "单位")
    private java.lang.String unit;
    /**存放地点*/
    @Excel(name = "存放地点", width = 15)
    @Schema(description = "存放地点")
    private java.lang.String location;
    /**储藏条件*/
    @Excel(name = "储藏条件", width = 15)
    @Schema(description = "储藏条件")
    private java.lang.String storageCondition;
    /**有效日期*/
    @Excel(name = "有效日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "有效日期")
    private java.util.Date vaildDate;
    /**销毁原因*/
    @Excel(name = "销毁原因", width = 15)
    @Schema(description = "销毁原因")
    private java.lang.String destroyReason;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String comments;
    /**开启人*/
    @Excel(name = "开启人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "开启人")
    private java.lang.String openerId;
    /**开启时间*/
    @Excel(name = "开启时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "开启时间")
    private java.util.Date openDate;
    /**确认效期*/
    @Excel(name = "确认效期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "确认效期")
    private java.util.Date confirmValidDate;
    /**介质*/
    @Excel(name = "介质", width = 15)
    @Schema(description = "介质")
    private java.lang.String mediumInfo;
    /**cas号*/
    @Excel(name = "cas号", width = 15)
    @Schema(description = "cas号")
    private java.lang.String casNo;
    /**样品*/
    @Excel(name = "样品", width = 15, dictTable = "sample", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sample", dicText = "name", dicCode = "id")
    @Schema(description = "样品")
    private java.lang.String sampleId;
    /**客户*/
    @Excel(name = "客户", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户")
    private java.lang.String customerId;
    /**来源或生产商*/
    @Excel(name = "来源或生产商", width = 15)
    @Schema(description = "来源或生产商")
    private java.lang.String manufacturer;
    /**是否退回*/
    @Excel(name = "是否退回", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(description = "是否退回")
    private java.lang.String isReturn;
	/**证书附件*/
	@Excel(name = "证书附件", width = 15)
	@Schema(description = "证书附件")
	private java.lang.String certificateUrl;

    @TableField(exist = false)
    private String InventoryPositive;
}
