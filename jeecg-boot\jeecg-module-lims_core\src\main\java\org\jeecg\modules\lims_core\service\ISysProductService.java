package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.SysProduct;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.SysProductVo;

import java.lang.reflect.InvocationTargetException;

/**
 * @Description: 产品
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface ISysProductService extends IService<SysProduct> {

    IPage<SysProductVo> listVo(Page<SysProduct> page, QueryWrapper<SysProduct> queryWrapper) throws InvocationTargetException, IllegalAccessException;
}
