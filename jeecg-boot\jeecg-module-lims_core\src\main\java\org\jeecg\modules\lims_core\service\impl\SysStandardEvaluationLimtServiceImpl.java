package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.modules.lims_core.entity.SysStandard;
import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
import org.jeecg.modules.lims_core.mapper.SysStandardEvaluationLimtMapper;
import org.jeecg.modules.lims_core.mapper.SysStandardMapper;
import org.jeecg.modules.lims_core.service.ISysStandardEvaluationLimtService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 标准指标评定要求
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@Service
public class SysStandardEvaluationLimtServiceImpl extends ServiceImpl<SysStandardEvaluationLimtMapper, SysStandardEvaluationLimt> implements ISysStandardEvaluationLimtService {

	@Autowired
	private SysStandardEvaluationLimtMapper sysStandardEvaluationLimtMapper;
	@Autowired
	private SysStandardMapper sysStandardMapper;

	@Override
	public List<SysStandardEvaluationLimt> selectByMainId(String mainId) {
		return sysStandardEvaluationLimtMapper.selectByMainId(mainId);
	}
	@Override
	public List<SysStandard> selectByTestId(String Id) {
		return sysStandardEvaluationLimtMapper.selectByTestId(Id);
	}

    @Override
    public List<DictModel> querySysStandardByMethodId(String methodId) {
		List<DictModel> dictModels = new ArrayList<>();
		sysStandardEvaluationLimtMapper.selectList(new QueryWrapper<SysStandardEvaluationLimt>().like("method_ids", methodId)).forEach((item) -> {
			SysStandard sysStandard = sysStandardMapper.selectById(item.getStandardId());
			DictModel dictModel = new DictModel();
			dictModel.setValue(sysStandard.getId());
			dictModel.setText(sysStandard.getName());
			//如果不存在就添加
			if (!dictModels.contains(dictModel)) {
				dictModels.add(dictModel);
			}
		});
		return dictModels;
    }
}
