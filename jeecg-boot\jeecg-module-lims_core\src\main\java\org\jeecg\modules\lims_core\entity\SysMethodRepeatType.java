package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 重复性
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Schema(description="重复性")
@Data
@TableName("sys_method_repeat_type")
public class SysMethodRepeatType implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**方法ID*/
    @Schema(description = "方法ID")
    private java.lang.String methodId;
    /**排序*/
    @Excel(name = "排序", width = 15)
    @Schema(description = "排序")
    private java.lang.String sortNum;
	/**重复类型*/
	@Excel(name = "重复类型", width = 15)
    @Schema(description = "重复类型")
    private java.lang.String repeatType;
    /**重复名称*/
    @Excel(name = "重复名称", width = 15)
    @Schema(description = "重复名称")
    private java.lang.String repeatName;
    /**重复名称*/
    @Excel(name = "重复周期", width = 15)
    @Schema(description = "重复周期")
    private java.lang.String cycleName;
	/**条件要求*/
	@Excel(name = "条件要求", width = 15)
    @Schema(description = "条件要求")
    private java.lang.String conditionReq;
    /**下一个id*/
    @Excel(name = "下一个id", width = 15)
    @Schema(description = "下一个id")
    private java.lang.String nextId;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
