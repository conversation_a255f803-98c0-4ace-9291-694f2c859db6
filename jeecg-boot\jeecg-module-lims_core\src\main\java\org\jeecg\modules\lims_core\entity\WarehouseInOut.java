package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 出入库记录
 * @Author: jeecg-boot
 * @Date:   2025-04-24
 * @Version: V1.0
 */
@Data
@TableName("warehouse_in_out")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="出入库记录")
public class WarehouseInOut implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**仓库*/
	@Excel(name = "仓库", width = 15, dictTable = "sys_warehouse", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_warehouse", dicText = "name", dicCode = "id")
    @Schema(description = "仓库")
    private java.lang.String warehouseId;
	/**物品编号*/
	@Excel(name = "物品编号", width = 15)
    @Schema(description = "物品编号")
    private java.lang.String articleNo;
	/**物品类型*/
	@Excel(name = "物品类型", width = 15, dicCode = "warehouse_goods_type")
	@Dict(dicCode = "warehouse_goods_type")
    @Schema(description = "物品类型")
    private java.lang.String articleTypeId;
	/**数量*/
	@Excel(name = "数量", width = 15)
    @Schema(description = "数量")
    private java.lang.String amount;
	/**单位*/
	@Excel(name = "单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "单位")
    private java.lang.String unitId;
	/**库位*/
	@Excel(name = "库位", width = 15, dictTable = "sys_warehouse_box", dicText = "code", dicCode = "id")
	@Dict(dictTable = "sys_warehouse_box", dicText = "code", dicCode = "id")
    @Schema(description = "库位")
    private java.lang.String boxId;
	/**操作类型*/
	@Excel(name = "操作类型", width = 15, dicCode = "warehouse_operation_type")
	@Dict(dicCode = "warehouse_operation_type")
    @Schema(description = "操作类型")
    private java.lang.String operationTypeId;
	/**操作原因*/
	@Excel(name = "操作原因", width = 15, dicCode = "warehouse_operation_reason")
	@Dict(dicCode = "warehouse_operation_reason")
    @Schema(description = "操作原因")
    private java.lang.String operationReasonId;
	/**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

    /**
     * 部门
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 物品名称
     */
    @TableField(exist = false)
    private String goodsName;
}
