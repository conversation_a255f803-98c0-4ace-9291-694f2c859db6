package org.jeecg.modules.lims_core.rule;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 标准物料编号规则
 */
public class StandardMaterialNoRule implements IFillRuleHandler {
    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        String prefix = "";
        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("sysFillRuleServiceImpl");
        // 根据 ruleCode 查询出实体
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("rule_code", params.get("rule_code"));
        JSONObject entity = JSONObject.parseObject(JSONObject.toJSONString(impl.getOne(queryWrapper)));

        if (params != null) {
            Object obj = params.get("prefix");
            if (obj != null) prefix = obj.toString();
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        if(entity.getString("snDate") != null && entity.getString("snDate").equals(format.format(new Date()))){
            int sn = entity.getInteger("sn") + 1;
            entity.put("sn",sn);
            impl.updateById(entity);
        }else{
            entity.put("sn",1);
            entity.put("snDate",format.format(new Date()));
            impl.updateById(entity);
        }

        String value = prefix + format.format(new Date()) + StrUtil.padPre(String.valueOf(entity.getInteger("sn")),4,'0');
        String name = formData.getString("name");
        if (!StringUtils.isEmpty(name)) {
            value += name;
        }
        return value;
    }
}