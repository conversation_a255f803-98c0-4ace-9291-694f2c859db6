package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysInstrumentType;
import org.jeecg.modules.lims_core.mapper.SysInstrumentTypeMapper;
import org.jeecg.modules.lims_core.service.ISysInstrumentTypeService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 设备类型
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
@Service("sysInstrumentTypeServiceImpl")
public class SysInstrumentTypeServiceImpl extends ServiceImpl<SysInstrumentTypeMapper, SysInstrumentType> implements ISysInstrumentTypeService {

}
