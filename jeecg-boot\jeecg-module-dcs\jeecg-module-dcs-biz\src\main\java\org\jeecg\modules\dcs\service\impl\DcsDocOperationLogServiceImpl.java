package org.jeecg.modules.dcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.dcs.entity.DcsDocOperationLog;
import org.jeecg.modules.dcs.mapper.DcsDocOperationLogMapper;
import org.jeecg.modules.dcs.service.IDcsDocOperationLogService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 文控系统文件操作记录
 * @Author: jeecg-boot
 * @Date:   2024-12-05
 * @Version: V1.0
 */
@Service
public class DcsDocOperationLogServiceImpl extends ServiceImpl<DcsDocOperationLogMapper, DcsDocOperationLog> implements IDcsDocOperationLogService {
}
