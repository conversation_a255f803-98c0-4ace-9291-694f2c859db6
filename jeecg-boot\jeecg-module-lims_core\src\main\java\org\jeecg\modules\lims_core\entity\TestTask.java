package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 测试任务
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
@Data
@TableName("test_task")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="测试任务")
public class TestTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**样品*/
    @Excel(name = "样品", width = 15, dictTable = "sample", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sample", dicText = "name", dicCode = "id")
    @Schema(description = "样品")
    private java.lang.String sampleId;
    /**营销产品*/
    @Excel(name = "营销产品", width = 15, dictTable = "sys_product", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_product", dicText = "name", dicCode = "id")
    @Schema(description = "营销产品")
    private java.lang.String productId;
    /**营销产品详情*/
    @Excel(name = "营销产品详情", width = 15)
    @Schema(description = "营销产品详情")
    private java.lang.String ppdId;
    /**营销产品详情*/
    @Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
    /**能力*/
    @Excel(name = "能力", width = 15, dictTable = "sys_capability", dicText = "name", dicCode = "id")
    @Schema(description = "能力")
    @Dict(dictTable = "sys_capability", dicText = "name", dicCode = "id")
    private java.lang.String capabilityId;
    /**方法*/
    @Excel(name = "方法", width = 15, dictTable = "sys_method", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_method", dicText = "name", dicCode = "id")
    @Schema(description = "方法")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private java.lang.String methodId;
    /**复测序号*/
    @Excel(name = "复测序号", width = 15)
    @Schema(description = "复测序号")
    private java.lang.Integer retestNo;
    /**稳定性*/
    @Excel(name = "稳定性", width = 15, dictTable = "sys_method_repeat_type", dicText = "cycle_name", dicCode = "id")
    @Dict(dictTable = "sys_method_repeat_type", dicText = "cycle_name", dicCode = "id")
    @Schema(description = "稳定性")
    private java.lang.String repeatTypeId;
    /**下次稳定性*/
    @Excel(name = "下次稳定性", width = 15, dictTable = "sys_method_repeat_type", dicText = "cycle_name", dicCode = "id")
    @Dict(dictTable = "sys_method_repeat_type", dicText = "cycle_name", dicCode = "id")
    @Schema(description = "下次稳定性")
    private java.lang.String repeatTypeNextId;
    /**下次稳定性日期*/
    @Excel(name = "下次稳定性日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "下次稳定性日期")
    private java.util.Date nextTestDate;
    /**标准价格*/
    @Excel(name = "标准价格", width = 15)
    @Schema(description = "标准价格")
    private java.math.BigDecimal standardPrice;
    /**申请价格*/
    @Excel(name = "申请价格", width = 15)
    @Schema(description = "申请价格")
    private java.math.BigDecimal applyPrice;
    /**PM价格*/
    @Excel(name = "PM价格", width = 15)
    @Schema(description = "PM价格")
    private java.math.BigDecimal pmPrice;
    /**指派人*/
    @Excel(name = "指派人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "指派人")
    private java.lang.String assigner;
    /**指派时间*/
    @Excel(name = "指派时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "指派时间")
    private java.util.Date assignTime;
    /**被指派人*/
    @Excel(name = "被指派人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "被指派人")
    private java.lang.String assignee;
    /**当前节点*/
    @Excel(name = "当前节点", width = 15, dictTable = "sys_workflow_step", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_workflow_step", dicText = "name", dicCode = "id")
    @Schema(description = "当前节点")
    private java.lang.String curStep;
    /**任务状态*/
    @Excel(name = "任务状态", width = 15, dicCode = "test_task_status")
    @Dict(dicCode = "test_task_status")
    @Schema(description = "任务状态")
    private java.lang.String status;
    /**复测原因*/
    @Excel(name = "复测原因", width = 15)
    @Schema(description = "复测原因")
    private java.lang.String retestReason;
    /**复测申请状态*/
    @Excel(name = "复测申请状态", width = 15)
    @Schema(description = "复测申请状态")
    private java.lang.String retestApplyStatus;
    /**复测任务Id*/
    @Excel(name = "复测任务Id", width = 15)
    @Schema(description = "复测任务Id")
    private java.lang.String retestTaskId;
    /**重复名称*/
    @Excel(name = "重复名称", width = 15)
    @Schema(description = "重复名称")
    private java.lang.String repeatName;
    /**重复周期*/
    @Excel(name = "重复周期", width = 15)
    @Schema(description = "重复周期")
    private java.lang.String repeatCycle;
    /**重复类型*/
    @Excel(name = "重复类型", width = 15)
    @Schema(description = "重复类型")
    private java.lang.String repeatType;
    /**报价用*/
    @Excel(name = "报价用", width = 15)
    @Schema(description = "报价用")
    private java.lang.Integer isForQuotation;
    /**报价用*/
    @Excel(name = "折扣", width = 15)
    @Schema(description = "折扣")
    private java.lang.Double discount;
    /**研发项目编码*/
    @Excel(name = "研发项目编码", width = 15, dictTable = "rd_project", dicText = "rd_no", dicCode = "id")
    @Dict(dictTable = "rd_project", dicText = "rd_no", dicCode = "id")
    @Schema(description = "研发项目编码")
    private java.lang.String rdId;

    /**复核人*/
    @Excel(name = "复核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "复核人")
    private java.lang.String checker;

    /**分包方id*/
    @Excel(name = "分包方id", width = 15, dictTable = "sys_supplier", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_supplier", dicText = "name", dicCode = "id")
    @Schema(description = "分包方id")
    private java.lang.String subtractorId;

    /**测试状态*/
    @Excel(name = "测试状态", width = 15, dicCode = "test_control_status")
    @Dict(dicCode = "test_control_status")
    @Schema(description = "测试状态")
    private java.lang.String testControlStatus;

    /**合作人*/
    @Excel(name = "合作人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "合作人")
    private java.lang.String cooper;

    /**部门*/
    @Excel(name = "部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "部门")
    private java.lang.String departmentId;

    /**PM指派*/
    @Excel(name = "PM指派", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "PM指派")
    private java.lang.String isPmAssign;
    /**PM*/
    @Excel(name = "PM", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "PM")
    private java.lang.String pmName;
    /**PM指派时间*/
    @Excel(name = "PM指派时间", width = 15, format = "yyyy-MM-dd  HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd  HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd  HH:mm:ss")
    @Schema(description = "PM指派时间")
    private java.util.Date pmAssignTime;
    /**指派备注信息*/
    @Excel(name = "指派备注信息", width = 15)
    @Schema(description = "指派备注信息")
    private java.lang.String experimentNotes;
}
