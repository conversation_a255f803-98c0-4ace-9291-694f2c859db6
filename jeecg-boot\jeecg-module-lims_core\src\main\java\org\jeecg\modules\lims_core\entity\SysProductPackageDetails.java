package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 套餐详情表
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Schema(description="套餐详情表")
@Data
@TableName("sys_product_package_details")
public class SysProductPackageDetails implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**套餐*/
    @Schema(description = "套餐")
    private java.lang.String packageId;
	/**能力*/
	@Excel(name = "能力", width = 15, dictTable = "capability", dicText = "name", dicCode = "id")
    @Schema(description = "能力")
    private java.lang.String capabilityId;
	/**指定方法*/
	@Excel(name = "指定方法", width = 15, dictTable = "sys_method", dicText = "name", dicCode = "id")
    @Schema(description = "指定方法")
    private java.lang.String methodId;
	/**指定标准*/
	@Excel(name = "指定标准", width = 15, dictTable = "sys_standard", dicText = "name", dicCode = "id")
    @Schema(description = "指定标准")
    private java.lang.String standardId;
}
