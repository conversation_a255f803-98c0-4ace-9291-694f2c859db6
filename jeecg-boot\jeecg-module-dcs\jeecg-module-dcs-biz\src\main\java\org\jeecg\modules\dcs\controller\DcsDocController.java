package org.jeecg.modules.dcs.controller;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysDepartModel;
import org.jeecg.common.util.*;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.dcs.dto.DcsDocWithPermission;
import org.jeecg.modules.dcs.dto.UpdateRefedDocsRequest;
import org.jeecg.modules.dcs.entity.DcsDoc;
import org.jeecg.modules.dcs.entity.DcsDocOperationLog;
import org.jeecg.modules.dcs.entity.DcsRefedDoc;
import org.jeecg.modules.dcs.entity.SystemDocType;
import org.jeecg.modules.dcs.mapper.SystemDocTypeMapper;
import org.jeecg.modules.dcs.service.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.dcs.vo.RefedDocLinkVO;
import org.jeecg.modules.oo.util.WordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * @Description: dcs_doc
 * @Author: jeecg-boot
 * @Date: 2024-11-25
 * @Version: V1.0
 */
@Tag(name = "dcs_doc")
@RestController
@RequestMapping("/dcs/dcsDoc")
@Slf4j
public class DcsDocController extends JeecgController<DcsDoc, IDcsDocService> {
	@Autowired
	private IDcsDocService dcsDocService;

	@Autowired
	private IDcsRefedDocService dcsRefedDocService;

	@Autowired
	private ISystemDocTypeService systemDocTypeService;

	@Autowired
	private ISysBaseAPI sysBaseAPI;

	@Autowired
	private IDcsDocOperationLogService dcsDocOperationLogService;

	/**
	 * 分页列表查询
	 *
	 * @param dcsDoc
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	// @AutoLog(value = "dcs_doc-分页列表查询")
	@Operation(summary = "dcs_doc-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DcsDocWithPermission>> queryPageList(DcsDoc dcsDoc,
			@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
			HttpServletRequest req) {
		LoginUser user = SecureUtil.currentUser();
		String deptId = sysBaseAPI.getDepartIdsByOrgCode(user.getOrgCode());
		IPage<DcsDocWithPermission> pageList = dcsDocService.queryPageWithPermission(dcsDoc,deptId, req, pageNo, pageSize);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param dcsDoc
	 * @return
	 */
	@AutoLog(value = "dcs_doc-添加")
	@Operation(summary = "dcs_doc-添加")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DcsDoc dcsDoc) {
		SystemDocTypeMapper baseMapper = (SystemDocTypeMapper) SpringContextUtils.getBean("systemDocTypeMapper");
		SystemDocType docType = baseMapper.selectById(dcsDoc.getTypeId());
		String docNoRule = docType.getDocNoRule();
		String sJoson = JSON.toJSONString(dcsDoc);
		JSONObject formData = JSON.parseObject(sJoson);
		Map<String, String> paramValues = getParamValues(dcsDoc, docNoRule);
		Object docNo = FillRuleUtilEx.executeRule("doc_no", paramValues,formData);
		dcsDoc.setDocNo(docNo.toString());
		dcsDocService.save(dcsDoc);
		docType.setCurrentSn(docType.getCurrentSn() + 1);// 增加流水号
		baseMapper.updateById(docType);
		DcsDocOperationLog oLog = new DcsDocOperationLog();
		oLog.setDocId(dcsDoc.getId());
		oLog.setOperationType("新建");
		dcsDocOperationLogService.save(oLog);
		return Result.OK("添加成功！");
	}

	private Map<String, String> getParamValues(DcsDoc dcsDoc, String docNoRule) {
		List<String> params = ReUtil.findAll("\\{([^}]*)\\}", docNoRule, 1);
		Map<String, String> paramValues = new HashMap<>();
		for (String param : params) {
			if (param.equals("部门代码")) {
				LoginUser sysUser = SecureUtil.currentUser();
				String deptCode = sysUser.getOrgCode();
				ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);
				String deptId = sysBaseApi.getDepartIdsByOrgCode(deptCode);
				SysDepartModel sysDepartModel = sysBaseApi.selectAllById(deptId);
				String dcsCatgory = sysDepartModel.getDcsCategory();
				paramValues.put(param, dcsCatgory);
			} else {
				try {
					// 遍历DcsDoc类的所有字段
					Field[] fields = DcsDoc.class.getDeclaredFields();
					Field targetField = null;
					// 查找匹配的字段
					for (Field field : fields) {
						io.swagger.v3.oas.annotations.media.Schema schema = 
							field.getAnnotation(io.swagger.v3.oas.annotations.media.Schema.class);
						if (schema != null && param.equals(schema.description())) {
							targetField = field;
							break;
						}
					}
					if (targetField != null) {
						targetField.setAccessible(true);
						Object value = targetField.get(dcsDoc);
						if (value != null) {
							paramValues.put(param, value.toString());
						}
					} else {
						log.warn("No field found with Schema description: {}", param);
					}
				} catch (IllegalAccessException e) {
					log.error("Error accessing field for description {}", param, e);
				}
			}
		}
		return paramValues;
	}

	/**
	 * 编辑
	 *
	 * @param dcsDoc
	 * @return
	 */
	@AutoLog(value = "dcs_doc-编辑")
	@Operation(summary = "dcs_doc-编辑")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:edit')")
	@RequestMapping(value = "/edit", method = { RequestMethod.PUT, RequestMethod.POST })
	public Result<String> edit(@RequestBody DcsDoc dcsDoc) {
		dcsDocService.updateById(dcsDoc);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "dcs_doc-通过id删除")
	@Operation(summary = "dcs_doc-通过id删除")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
		dcsDocService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "dcs_doc-批量删除")
	@Operation(summary = "dcs_doc-批量删除")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		this.dcsDocService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	// @AutoLog(value = "dcs_doc-通过id查询")
	@Operation(summary = "dcs_doc-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DcsDoc> queryById(@RequestParam(name = "id", required = true) String id) {
		DcsDoc dcsDoc = dcsDocService.getById(id);
		if (dcsDoc == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(dcsDoc);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param dcsDoc
	 */
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:exportXls')")
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, DcsDoc dcsDoc) {
		return super.exportXls(request, dcsDoc, DcsDoc.class, "dcs_doc");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:importExcel')")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, DcsDoc.class);
	}

	@PostMapping("/apply")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:invokeWorkflow')")
	public Result<String> apply(@RequestBody Map<String,String> req) throws Exception {
		String docId = req.get("docId");
		String applyTypeId = req.get("applyTypeId");
		String description = req.get("description");
		dcsDocService.apply(docId, applyTypeId, description);
		return Result.OK("操作成功!");
	}

	/**
	 * 修订
	 * 
	 * @param docId
	 * @return
	 */
	@AutoLog(value = "dcs_doc-修订")
	@Operation(summary = "dcs_doc-修订")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:revise')")
	@PostMapping(value = "/revise/{docId}")
	public Result<String> revise(@PathVariable("docId") String docId) {
		DcsDoc doc = dcsDocService.getById(docId);
		DcsDoc newDoc = new DcsDoc();
		BeanUtil.copyProperties(doc, newDoc);
		newDoc.setId(null);
		newDoc.setVer(newDoc.getVer() + 1);
		newDoc.setProcessInstanceId(null);
		newDoc.setCreateBy(SecureUtil.currentUser().getUsername());
		newDoc.setCreateTime(DateTime.now());
		newDoc.setUpdateBy(null);
		newDoc.setUpdateTime(null);
		String sJoson = JSON.toJSONString(newDoc);
		JSONObject formData = JSON.parseObject(sJoson);
		SystemDocType docType = systemDocTypeService.getById(newDoc.getTypeId());
		Map<String,String> paramsValues = getParamValues(newDoc, docType.getDocNoRule());
		Object docNo = FillRuleUtilEx.executeRule("doc_no",paramsValues, formData);
		newDoc.setDocNo(docNo.toString());
		dcsDocService.save(newDoc);
		// SystemDocTypeMapper baseMapper = (SystemDocTypeMapper)
		// SpringContextUtils.getBean("systemDocTypeMapper");
		// SystemDocType docType = baseMapper.selectById(newDoc.getTypeId());
		// docType.setCurrentSn(docType.getCurrentSn() + 1);//增加流水号
		DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
		dcsDocOperationLog.setDocId(doc.getId());
		dcsDocOperationLog.setOperationType("修订");
		dcsDocOperationLog.setRemark("修订版本号：" + newDoc.getVer().toString());
		dcsDocOperationLogService.save(dcsDocOperationLog);
		return Result.OK(null, newDoc.getId());
	}


	/**
	 * 复审
	 *
	 * @param docId
	 * @return
	 */
	@AutoLog(value = "dcs_doc-复审")
	@Operation(summary = "dcs_doc-复审")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:review')")
	@PostMapping(value = "/review/{docId}")
	public Result<String> review(@PathVariable("docId") String docId) {
		DcsDoc doc = dcsDocService.getById(docId);
		doc.setReviewTime(DateTime.now());
		dcsDocService.updateById(doc);
		DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
		dcsDocOperationLog.setDocId(doc.getId());
		dcsDocOperationLog.setOperationType("复审");
		dcsDocOperationLogService.save(dcsDocOperationLog);
		return Result.OK(null, doc.getId());
	}


	/**
	 * 作废
	 * 
	 * @param docId
	 * @return
	 */
	@AutoLog(value = "dcs_doc-作废")
	@Operation(summary = "dcs_doc-作废")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:abandon')")
	@PostMapping(value = "/abandon/{docId}")
	public Result<String> abandon(@PathVariable("docId") String docId) throws Exception {
		DcsDoc doc = dcsDocService.getById(docId);
		doc.setEffectiveStatus("abandoned");
		dcsDocService.updateById(doc);
		WordUtil.setWaterMark(doc.getUrl(),"作废");
		DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
		dcsDocOperationLog.setDocId(doc.getId());
		dcsDocOperationLog.setOperationType("作废");
		dcsDocOperationLogService.save(dcsDocOperationLog);
		return Result.OK("作废成功！");
	}

	/**
	 * 发布
	 * 
	 * @param docId
	 * @return
	 */
	@AutoLog(value = "dcs_doc-发布")
	@Operation(summary = "dcs_doc-发布")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc:publish')")
	@PostMapping(value = "/publish/{docId}")
	public Result<String> publish(@PathVariable("docId") String docId) {
		DcsDoc doc = dcsDocService.getById(docId);
		doc.setStatus(0);
		dcsDocService.updateById(doc);
		DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
		dcsDocOperationLog.setDocId(doc.getId());
		dcsDocOperationLog.setOperationType("发布");
		dcsDocOperationLogService.save(dcsDocOperationLog);
		return Result.OK("发布成功！");
	}

	@GetMapping("/getRefedDocList")
	public Result<List<RefedDocLinkVO>> getRefedDocList(@RequestParam(required = false) String name) {
		// 使用现有的 DcsDoc 查询方法
		List<DcsDoc> docs;
		if (StrUtil.isEmpty(name)) {
			docs = dcsDocService.getBaseMapper().selectList(null);
		} else {
			LambdaQueryWrapper<DcsDoc> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(DcsDoc::getEffectiveStatus, "effective")
					    .and(wrapper -> wrapper.eq(DcsDoc::getStatus, "0"))
					    .and(wrapper -> wrapper.like(DcsDoc::getDocName, name));
			docs = dcsDocService.getBaseMapper().selectList(queryWrapper);
		}
		return Result.ok(docs.stream()
				.map(RefedDocLinkVO::new)
				.collect(Collectors.toList())) ;
	}

	/**
	 * 更新引用文档
	 *
	 * @param request
	 * @return
	 */
	@AutoLog(value = "dcs_doc-更新引用文档")
	@Operation(summary = "dcs_doc-更新引用文档")
	@PostMapping(value = "/updateRefedDocs")
	public Result<String> updateRefedDocs(@RequestBody UpdateRefedDocsRequest request) {
		String docId = request.getDocId();
		String refedDocs = request.getRefedDocs();
		DcsDoc doc = dcsDocService.getById(docId);
		String[] refedDocIds = refedDocs.split(",");
		List<DcsRefedDoc> refedDocList = new ArrayList<>();
		for (String refedDocId : refedDocIds) {
			DcsRefedDoc refedDoc = new DcsRefedDoc();
			refedDoc.setDocId(doc.getId());
			refedDoc.setRefedDocId(refedDocId);
			refedDocList.add(refedDoc);
		}
		dcsRefedDocService.saveBatch(refedDocList);
		return Result.OK(null,"更新引用成功！");
	}
}