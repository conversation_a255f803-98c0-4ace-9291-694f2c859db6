package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.vo.ReportRequestVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.lims_core.entity.Report;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.TaskVo;

/**
 * @Description: 报告
 * @Author: jeecg-boot
 * @Date:   2025-03-03
 * @Version: V1.0
 */
public interface ReportMapper extends BaseMapper<Report> {

    @Select("select * from report where order_id = #{orderId}")
    List<Report> selectByOrderId(@Param("orderId") String orderId);

    @Select("select * from report where sample_id = #{sampleId}")
    List<Report> selectBySampleId(@Param("sampleId") String sampleId);

    IPage<ReportRequestVO> queryPageList(Page<Report> page,
                                       @Param(Constants.WRAPPER) Wrapper<ReportRequestVO> wrapper);

    List<TaskVo> selectByMainId(String mainId);

    List<TaskVo> getevaluation(String Id);
    List<TaskVo> getanalytes(String Id);
    List<TaskVo> getContactNameAndPhone(String Id);
}
