package org.jeecg.modules.lims_order.service;

import org.jeecg.modules.lims_order.entity.Payment;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 回款
 * @Author: jeecg-boot
 * @Date:   2025-03-31
 * @Version: V1.0
 */
public interface IPaymentService extends IService<Payment> {

    void savePayment(Payment payment);

    void editPayment(Payment payment);

    void deletePayment(String id);

    void deletePayments(List<String> list);
}
