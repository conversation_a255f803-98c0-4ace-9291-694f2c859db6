package org.jeecg.modules;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import org.jeecg.modules.lims_core.mapper.SysWorkflowStepMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
public class flow_calc_time {
    @Autowired
    SysWorkflowStepMapper sysWorkflowStepMapper;

    @Test
    public void test(){
        List<SysWorkflowStep> flowSteps = sysWorkflowStepMapper.selectByMainId("1890214524119957505");
        Integer[] hoursByPmLeadTime = getHoursByPmLeadTime(4, flowSteps);
        for (Integer hour : hoursByPmLeadTime) {
            System.out.println(hour);
        }
    }


    Integer[] getHoursByPmLeadTime(Integer pmLeadTime, List<SysWorkflowStep> flowSteps) {
        String stdTaskTimeExpr = flowSteps.get(0).getStdTaskTimeExpr();
        JSONObject jsonObject = JSONObject.parseObject(stdTaskTimeExpr);

        // pmLeadTime 属于哪个 key 的范围
        String key = null;
        for (String k : jsonObject.keySet()) {
            if (k.startsWith("<")) {
                // 处理 "<5" 的情况
                if (pmLeadTime < Integer.parseInt(k.substring(1))) {
                    key = k;
                    break;
                }
            } else if (k.startsWith("≥")) {
                // 处理 "≥5&&<10" 的情况
                String[] parts = k.substring(1).split("&&");
                boolean inRange = true;
                for (String part : parts) {
                    if (part.contains("<")) {
                        // 处理 "<10"
                        if (pmLeadTime >= Integer.parseInt(part.substring(1))) {
                            inRange = false;
                            break;
                        }
                    } else {
                        // 处理 "≥5"
                        if (pmLeadTime < Integer.parseInt(part)) {
                            inRange = false;
                            break;
                        }
                    }
                }
                if (inRange) {
                    key = k;
                    break;
                }
            } else if (k.startsWith("≥") && !k.contains("&&")) {
                // 处理 "≥20" 的情况
                if (pmLeadTime >= Integer.parseInt(k.substring(1))) {
                    key = k;
                    break;
                }
            }
        }

        if (key == null) {
            throw new IllegalArgumentException("无法匹配到有效的 key 范围");
        }

        Integer[] hours = new Integer[flowSteps.size()];
        int totalHours = 0;
        for (int i = 0; i < flowSteps.size(); i++) {
            SysWorkflowStep sysWorkflowStep = flowSteps.get(i);
            String stdTaskTimeExpr1 = sysWorkflowStep.getStdTaskTimeExpr();
            if (stdTaskTimeExpr1 == null) {
                continue;
            }
            JSONObject jsonObject1 = JSONObject.parseObject(stdTaskTimeExpr1);
            if (jsonObject1.containsKey(key)) {
                hours[i] = (int) Math.round(jsonObject1.getDouble(key) * 24);
                totalHours += hours[i];
            }
        }
        // hours[3] = pmLeadTime - 其他所有 hours 综合
        hours[3] = pmLeadTime * 24 - totalHours;
        return hours;
    }
}
