package org.jeecg.modules.lims_core.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import org.jeecg.modules.lims_core.entity.SysWorkflow;
import org.jeecg.modules.lims_core.vo.SysWorkflowPage;
import org.jeecg.modules.lims_core.service.ISysWorkflowService;
import org.jeecg.modules.lims_core.service.ISysWorkflowStepService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;


 /**
 * @Description: 流程
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Tag(name="流程")
@RestController
@RequestMapping("/lims_core/sysWorkflow")
@Slf4j
public class SysWorkflowController {
	@Autowired
	private ISysWorkflowService sysWorkflowService;
	@Autowired
	private ISysWorkflowStepService sysWorkflowStepService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysWorkflow
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "流程-分页列表查询")
	@Operation(summary="流程-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysWorkflow>> queryPageList(SysWorkflow sysWorkflow,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysWorkflow> queryWrapper = QueryGenerator.initQueryWrapper(sysWorkflow, req.getParameterMap());
		Page<SysWorkflow> page = new Page<SysWorkflow>(pageNo, pageSize);
		IPage<SysWorkflow> pageList = sysWorkflowService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysWorkflowPage
	 * @return
	 */
	@AutoLog(value = "流程-添加")
	@Operation(summary="流程-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_workflow:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysWorkflowPage sysWorkflowPage) {
		SysWorkflow sysWorkflow = new SysWorkflow();
		BeanUtils.copyProperties(sysWorkflowPage, sysWorkflow);
		sysWorkflowService.saveMain(sysWorkflow, sysWorkflowPage.getSysWorkflowStepList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysWorkflowPage
	 * @return
	 */
	@AutoLog(value = "流程-编辑")
	@Operation(summary="流程-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_workflow:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysWorkflowPage sysWorkflowPage) {
		SysWorkflow sysWorkflow = new SysWorkflow();
		BeanUtils.copyProperties(sysWorkflowPage, sysWorkflow);
		SysWorkflow sysWorkflowEntity = sysWorkflowService.getById(sysWorkflow.getId());
		if(sysWorkflowEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysWorkflowService.updateMain(sysWorkflow, sysWorkflowPage.getSysWorkflowStepList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "流程-通过id删除")
	@Operation(summary="流程-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_workflow:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysWorkflowService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "流程-批量删除")
	@Operation(summary="流程-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_workflow:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysWorkflowService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "流程-通过id查询")
	@Operation(summary="流程-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysWorkflow> queryById(@RequestParam(name="id",required=true) String id) {
		SysWorkflow sysWorkflow = sysWorkflowService.getById(id);
		if(sysWorkflow==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysWorkflow);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "流程环节通过主表ID查询")
	@Operation(summary="流程环节-通主表ID查询")
	@GetMapping(value = "/querySysWorkflowStepByMainId")
	public Result<List<SysWorkflowStep>> querySysWorkflowStepListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysWorkflowStep> sysWorkflowStepList = sysWorkflowStepService.selectByMainId(id);
		return Result.OK(sysWorkflowStepList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysWorkflow
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_workflow:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysWorkflow sysWorkflow) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysWorkflow> queryWrapper = QueryGenerator.initQueryWrapper(sysWorkflow, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<SysWorkflow> sysWorkflowList = sysWorkflowService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysWorkflowPage> pageList = new ArrayList<SysWorkflowPage>();
      for (SysWorkflow main : sysWorkflowList) {
          SysWorkflowPage vo = new SysWorkflowPage();
          BeanUtils.copyProperties(main, vo);
          List<SysWorkflowStep> sysWorkflowStepList = sysWorkflowStepService.selectByMainId(main.getId());
          vo.setSysWorkflowStepList(sysWorkflowStepList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "流程列表");
      mv.addObject(NormalExcelConstants.CLASS, SysWorkflowPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("流程数据", "导出人:"+sysUser.getRealname(), "流程"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_workflow:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysWorkflowPage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysWorkflowPage.class, params);
              for (SysWorkflowPage page : list) {
                  SysWorkflow po = new SysWorkflow();
                  BeanUtils.copyProperties(page, po);
                  sysWorkflowService.saveMain(po, page.getSysWorkflowStepList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
