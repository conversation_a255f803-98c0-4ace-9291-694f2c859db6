package org.jeecg.modules.gitlab.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.gitlab.entity.GitlabSchedule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 存储gitlab issue与企微日程的关系
 * @Author: jeecg-boot
 * @Date:   2025-01-03
 * @Version: V1.0
 */
public interface GitlabScheduleMapper extends BaseMapper<GitlabSchedule> {

}
