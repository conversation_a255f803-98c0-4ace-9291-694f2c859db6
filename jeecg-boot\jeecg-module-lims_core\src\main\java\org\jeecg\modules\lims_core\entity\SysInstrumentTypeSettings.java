package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 数据采集设置
 * @Author: jeecg-boot
 * @Date:   2025-04-28
 * @Version: V1.0
 */
@Data
@TableName("sys_instrument_type_settings")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="数据采集设置")
public class SysInstrumentTypeSettings implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**类别*/
	@Excel(name = "类别", width = 15)
    @Schema(description = "类别")
    private java.lang.String instType;
	/**仪器列名*/
	@Excel(name = "仪器列名", width = 15)
    @Schema(description = "仪器列名")
    private java.lang.String columnNames;
	/**隐藏列*/
	@Excel(name = "隐藏列", width = 15)
    @Schema(description = "隐藏列")
    private java.lang.String hiddenColumns;
	/**强制列*/
	@Excel(name = "强制列", width = 15)
    @Schema(description = "强制列")
    private java.lang.String mandatoryColumns;
	/**默认值*/
	@Excel(name = "默认值", width = 15)
    @Schema(description = "默认值")
    private java.lang.String defaultValues;
	/**表达式*/
	@Excel(name = "表达式", width = 15)
    @Schema(description = "表达式")
    private java.lang.String expressions;
	/**导出列头*/
	@Excel(name = "导出列头", width = 15)
    @Schema(description = "导出列头")
    private java.lang.String exportColumnHeader;
	/**示例模板*/
	@Excel(name = "示例模板", width = 15)
    @Schema(description = "示例模板")
    private java.lang.String template;
	/**后缀名*/
	@Excel(name = "后缀名", width = 15)
    @Schema(description = "后缀名")
    private java.lang.String extension;
	/**含BOM头*/
	@Excel(name = "含BOM头", width = 15)
    @Schema(description = "含BOM头")
    private java.lang.String withBom;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
