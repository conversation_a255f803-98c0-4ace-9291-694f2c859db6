package org.jeecg.modules.dcs.job;


import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: 发送季度复审消息
 * @version 1.0
 * @date 2024/12/23 10:08
 */
@Slf4j
public class SendQuotalyReviewListJob implements Job {
    /**
     * 若参数变量名修改 QuartzJobController中也需对应修改
     */
    private String parameter;

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpMessageService messageService = cpService.getMessageService();

        WxCpMessage message = new WxCpMessage();
        message.setAgentId(agentId);
        message.setToUser(this.parameter);

        message.setMsgType("news");
        List<NewArticle> mpAs = new ArrayList<>();
        NewArticle newArticle = new NewArticle();
        newArticle.setTitle("季度评审清单");
        newArticle.setDescription("季度评审清单");
        newArticle.setPicUrl("http://nwzimg.wezhan.cn/contents/sitefiles2049/10245330/images/48843504.jpg");
        newArticle.setUrl("https://gbjc.cc/jeecg-boot/jmreport/shareView/1029917173632159744?shareToken=7b887e4a5d79716f59a86c9c664738ce");
        mpAs.clear();
        mpAs.add(newArticle);

        message.setArticles(mpAs);
        try {
            messageService.send(message);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }
}

