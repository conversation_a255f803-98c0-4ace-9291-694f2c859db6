package org.jeecg.modules.dcs.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.dcs.entity.SystemDocTypeDeptPermission;
import org.jeecg.modules.dcs.mapper.SystemDocTypeDeptPermissionMapper;
import org.jeecg.modules.dcs.mapper.SystemDocTypeMapper;
import org.jeecg.modules.dcs.service.ISystemDocTypeDeptPermissionService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: system_doc_type_dept_permission
 * @Author: jeecg-boot
 * @Date:   2024-11-29
 * @Version: V1.0
 */
@Service
public class SystemDocTypeDeptPermissionServiceImpl extends ServiceImpl<SystemDocTypeDeptPermissionMapper, SystemDocTypeDeptPermission> implements ISystemDocTypeDeptPermissionService {
    /**
     * 获得文控权限
     * * @param typeId 编号
     *
     * @return 文控权限
     */
    public List<SystemDocTypeDeptPermission> getSystemDocTypeDeptPermission(String typeId) {
        QueryWrapper<SystemDocTypeDeptPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_id",typeId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public String getCurUserDcsPermission(String typeId) {
        String r = "";
        LoginUser user = SecureUtil.currentUser();
        ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);
        String deptId =  sysBaseApi.getDepartIdsByOrgCode(user.getOrgCode());
        SystemDocTypeDeptPermissionMapper systemDocTypeDeptPermissionMapper = (SystemDocTypeDeptPermissionMapper) SpringContextUtils.getBean("systemDocTypeDeptPermissionMapper");
        QueryWrapper<SystemDocTypeDeptPermission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_id",typeId)
                    .eq("dept_Id",deptId);
        SystemDocTypeDeptPermission  systemDocTypeDeptPermission = systemDocTypeDeptPermissionMapper.selectOne(queryWrapper);
        if(systemDocTypeDeptPermission!=null)
            r = systemDocTypeDeptPermission.getPermission();
        return r;
    }
}
