package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 研发项目
 * @Author: jeecg-boot
 * @Date:   2025-05-15
 * @Version: V1.0
 */
@Data
@TableName("rd_project")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="研发项目")
public class RdProject implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**研发项目编号*/
	@Excel(name = "研发项目编号", width = 15)
    @Schema(description = "研发项目编号")
    private String rdNo;
	/**订单*/
    @Excel(name = "合同", width = 15, dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
    @Dict(dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
    @Schema(description = "合同")
    private String orderId;
	/**报价*/
    @Schema(description = "报价")
    @Excel(name = "报价申请", width = 15, dictTable = "quotation", dicText = "quotation_no", dicCode = "id")
    @Dict(dictTable = "quotation", dicText = "quotation_no", dicCode = "id")
    private String quotationId;
    /**研发项目名称*/
    @Excel(name = "研发项目名称", width = 15)
    @Schema(description = "研发项目名称")
    private java.lang.String name;
}
