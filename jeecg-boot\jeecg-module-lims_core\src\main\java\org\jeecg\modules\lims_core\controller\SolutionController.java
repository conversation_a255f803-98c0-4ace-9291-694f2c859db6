package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.Solution;
import org.jeecg.modules.lims_core.service.ISolutionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 溶液配置记录
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
@Tag(name="溶液配置记录")
@RestController
@RequestMapping("/lims_core/solution")
@Slf4j
public class SolutionController extends JeecgController<Solution, ISolutionService> {
	@Autowired
	private ISolutionService solutionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param solution
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "溶液配置记录-分页列表查询")
	@Operation(summary="溶液配置记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Solution>> queryPageList(Solution solution,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("solutionTypeId", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<Solution> queryWrapper = QueryGenerator.initQueryWrapper(solution, req.getParameterMap(),customeRuleMap);
		Page<Solution> page = new Page<Solution>(pageNo, pageSize);
		IPage<Solution> pageList = solutionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param solution
	 * @return
	 */
	@AutoLog(value = "溶液配置记录-添加")
	@Operation(summary="溶液配置记录-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:solution:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Solution solution)  throws WxErrorException {
		com.alibaba.fastjson.JSONObject formData = new com.alibaba.fastjson.JSONObject();
		formData.put("solutionTypeId", solution.getSolutionTypeId());
		solution.setCode(FillRuleUtil.executeRule(FillRuleConstant.SOLUTION,formData).toString());
		solutionService.save(solution);
		//发起审批流
		solutionService.apply(solution, ApplyType.SOLUTION_APPLY);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param solution
	 * @return
	 */
	@AutoLog(value = "溶液配置记录-编辑")
	@Operation(summary="溶液配置记录-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:solution:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Solution solution) {
		solutionService.updateById(solution);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "溶液配置记录-通过id删除")
	@Operation(summary="溶液配置记录-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:solution:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		solutionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "溶液配置记录-批量删除")
	@Operation(summary="溶液配置记录-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:solution:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.solutionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "溶液配置记录-通过id查询")
	@Operation(summary="溶液配置记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Solution> queryById(@RequestParam(name="id",required=true) String id) {
		Solution solution = solutionService.getById(id);
		if(solution==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(solution);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param solution
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:solution:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Solution solution) {
        return super.exportXls(request, solution, Solution.class, "溶液配置记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:solution:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Solution.class);
    }

}
