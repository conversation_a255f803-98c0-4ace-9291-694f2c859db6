<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.ReportMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.ReportRequestVO">
        select * from (
                          SELECT
                              r.id,CASE
                                       WHEN r.version IS NOT NULL AND r.version != ''
                                          THEN CONCAT(r.report_no, '-', r.version)
                                       ELSE r.report_no
                                     END AS report_no,
                              r.dead_line,r.create_by,r.create_time,r.update_by,r.update_time,r.sys_org_code,r.test_task_ids,r.template_id,r.order_id,r.sample_id,r.repeat_type_id,r.url
                             ,r.sign_by,r.sign_time,r.remark,r.conclusion,r.description,r.final_url,r.approve_by,r.approve_time,r.is_sent,r.make_time,r.make_by,r.revise_memo,r.version,r.superceded_id,
                              CASE
                                  WHEN r.is_sent = 'Y'
                                      THEN '已发放'
                                  WHEN (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                       ) = (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                             AND t.cur_step = '报告编制'
                                       ) AND (
                                                 SELECT COUNT(*)
                                                 FROM test_task t left join sample s on t.sample_id = s.id
                                                 WHERE  r.sample_id = s.id
                                             ) > 0
                                      THEN '待审核'
                                  WHEN (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                       ) = (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                             AND t.cur_step = '报告审核'
                                       ) AND (
                                                 SELECT COUNT(*)
                                                 FROM test_task t left join sample s on t.sample_id = s.id
                                                 WHERE  r.sample_id = s.id
                                             ) > 0
                                      THEN '待签发'
                                  WHEN (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                       ) = (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                             AND t.cur_step = '报告签发'
                                       ) AND (
                                                 SELECT COUNT(*)
                                                 FROM test_task t left join sample s on t.sample_id = s.id
                                                 WHERE  r.sample_id = s.id
                                             ) > 0
                                      THEN '已签发'
                                  WHEN i.article_no IS NOT NULL THEN '已归档'
                                  WHEN (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                       ) = (
                                           SELECT COUNT(*)
                                           FROM test_task t left join sample s on t.sample_id = s.id
                                           WHERE  r.sample_id = s.id
                                           AND t.cur_step = '结果复核'
                                       ) AND (
                                      SELECT COUNT(*)
                                      FROM test_task t left join sample s on t.sample_id = s.id
                                      WHERE  r.sample_id = s.id
                                  ) > 0
                                      THEN '待编制'
                                  ELSE ''
                                  END AS reportstatus ,c.id as customer_id,c.name as customer_name,cc.name as contact_name,cc.email as contact_email,c.saler_id,s1.name as sample_name
                          FROM Report r left join sample s1 on r.sample_id = s1.id
                          left join sys_customer c on c.id = s1.customer_id
                          left join sys_customer_contact cc on cc.id = s1.customer_contact_id
                          LEFT JOIN inventory i ON CONCAT(r.report_no, '-', r.version) = i.article_no
                          LEFT JOIN warehouse_in_out wio ON i.article_no = wio.article_no AND wio.article_type_id = 'RA'
                      ) t
            ${ew.customSqlSegment}
    </select>

    <select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        SELECT
            t.*, s.sample_no, o.contract_no, o.id as order_id, s.name as sample_name, wf.dept_id, d.depart_name, tr.rep_result, tr.conclusion, sa.name as analyte_name
            ,CASE
             WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%》%'
             THEN CONCAT(
                    SUBSTRING(ss.name, 1, LOCATE('》', ss.name)),
                    ss.version,
                    ' ',
                    SUBSTRING(ss.name, LOCATE('》', ss.name) + 1),
                    ' ',
                    CASE WHEN m.name IS NOT NULL AND m.name != '/' THEN m.name ELSE '' END
                    )
             WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%GB%'
             THEN CONCAT(ss.name, '-', ss.version,
                    ' ',
                    CASE WHEN m.name IS NOT NULL AND m.name != '/' THEN m.name ELSE '' END)
             ELSE ss.name
            END AS standardMethodName
        FROM
            test_task t
                left join  sample s on t.sample_id = s.id
                left join biz_order o on s.order_id = o.id
                left join sys_method m on m. id = t.method_id
                left join sys_method_workflow wf on wf.method_id = m.id
                left join sys_depart d on d.id = wf.dept_id
                left JOIN report r on r.order_id=o.id and r.sample_id=s.id
                left join test te on te.task_id=t.id and te.test_type_id=0
                LEFT JOIN (
                SELECT tr1.*,ROW_NUMBER() OVER (PARTITION BY tr1.test_id ORDER BY tr1.id DESC) AS rn
                FROM test_result tr1
            ) tr ON tr.test_id = te.id AND tr.rn = 1
                left join sys_method_analyte sma on sma.id = tr.method_analyte_id
                left join sys_analyte sa on sa.id = sma.analyte_id
                left join sys_standard ss on ss.id = m.standard_id
             where   r.id = #{mainId} 	</select>


    <select id="getevaluation" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        SELECT
            CASE
                WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%》%'
            THEN CONCAT(
                SUBSTRING(ss.name, 1, LOCATE('》', ss.name)),
                ss.version,
                ' ',
                SUBSTRING(ss.name, LOCATE('》', ss.name) + 1)
                )
            WHEN ss.version IS NOT NULL AND ss.version != '/' AND ss.name LIKE '%GB%'
             THEN CONCAT(ss.name, '-', ss.version)
            ELSE ss.name
        END AS name,st.translation
        FROM
            test_task t
                left join  sample s on t.sample_id = s.id
                left join biz_order o on s.order_id = o.id
                left join sys_method m on m. id = t.method_id
                left JOIN report r on r.order_id=o.id
                left join test te on te.task_id=t.id and te.test_type_id=0
                LEFT JOIN (
                SELECT tr1.*,ROW_NUMBER() OVER (PARTITION BY tr1.test_id ORDER BY tr1.id DESC) AS rn
                FROM test_result tr1
            ) tr ON tr.test_id = te.id AND tr.rn = 1
                left join  sys_method_analyte sma on tr.method_analyte_id=sma.id
                left JOIN  sys_analyte  sn on sma.analyte_id =sn.id
                left join  sys_method sm on  sma.method_id =sm.id
                left join sys_standard_evaluation_limt  ssel on ssel.id=tr.limit_id
                left join  sys_standard ss  on  ssel.standard_id=ss.id
                left join sys_translation st on ss.id=st.source_id  and r.template_id='1925472945282166786'
        where   r.id = #{mainId} 	</select>
    <select id="getanalytes" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        SELECT
            sn.name as code
        FROM
            test_task t
                left join  sample s on t.sample_id = s.id
                left join biz_order o on s.order_id = o.id
                left join sys_method m on m. id = t.method_id
                left JOIN report r on r.order_id=o.id
                left join test te on te.task_id=t.id and te.test_type_id=0
                LEFT JOIN test_result tr  ON tr.test_id = te.id
                left join  sys_method_analyte sma on tr.method_analyte_id=sma.id
                left JOIN  sys_analyte  sn on sma.analyte_id =sn.id
                left join  sys_method sm on  sma.method_id =sm.id
                left join sys_standard_evaluation_limt  ssel on ssel.id=tr.limit_id
                left join  sys_standard ss  on  ssel.standard_id=ss.id
        where   r.id = #{mainId} 	</select>

    <select id="getContactNameAndPhone" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        SELECT scc.name,scc.phone,st.translation from
            sys_customer_contact scc
                left JOIN biz_order bo on scc.id =bo.customer_contact_id
                LEFT JOIN report r on bo.id=r.order_id
                left join sys_translation st on scc.id=st.source_id  and r.template_id='1925472945282166786'
        where r.id= #{mainId} 	</select>
</mapper>