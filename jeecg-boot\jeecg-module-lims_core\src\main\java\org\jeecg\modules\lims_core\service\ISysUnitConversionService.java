package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 单位转换
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface ISysUnitConversionService extends IService<SysUnitConversion> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysUnitConversion>
	 */
	public List<SysUnitConversion> selectByMainId(String mainId);
}
