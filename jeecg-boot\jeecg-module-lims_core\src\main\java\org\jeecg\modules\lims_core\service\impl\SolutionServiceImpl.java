package org.jeecg.modules.lims_core.service.impl;

import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOaService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.oa.SummaryInfo;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApplyEventRequest;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplateResult;
import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.SolutionMapper;
import org.jeecg.modules.lims_core.service.ISolutionService;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.wx.entity.WecomSp;
import org.jeecg.modules.wx.service.IWecomSpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 溶液配置记录
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
@Service("SolutionServiceImpl")
public class SolutionServiceImpl extends ServiceImpl<SolutionMapper, Solution> implements ISolutionService {

    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private ISysDictItemService sysDictItemService;
    @Autowired
    private IWecomSpService wecomSpService;

    @Override
    public void apply(Solution obj, ApplyType applyType) throws WxErrorException {

        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOaService oaService = cpService.getOaService();
        String templateId = "8Tk5o9CdF14fTTLuWbEeUvhYV4dxtUNQeXAXA";
        WxCpOaApprovalTemplateResult templateDetail = oaService.getTemplateDetail(templateId);
        System.out.println(templateDetail.toJson());

        WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
        LoginUser curUser = SecureUtil.currentUser();
        request.setCreatorUserId(sysBaseAPI.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
        // template_id	 模板id
        request.setTemplateId(templateId);
        // use_template_approver	 0-通过接口指定审批人、抄送人（此时process参数必填）; 1-使用此模板在管理后台设置的审批流程(需要保证审批流程中没有“申请人自选”节点)，支持条件审批。
        request.setUseTemplateApprover(0);
        // apply_data     申请数据
        String solution_type_Text = "";
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("solution_type");
        for (SysDictItem item : sysDictItems) {
            if (obj.getSolutionTypeId().equals(item.getItemValue())) {
                solution_type_Text = item.getItemText();
                break;
            }
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = formatter.format(obj.getEffectiveTo());
        request.setApplyData(getApplyData(
                obj.getCode(),
                obj.getRecord(),
                solution_type_Text,
                obj.getConc(),
                obj.getWeighing(),
                obj.getContent(),
                obj.getConstantVolume(),
                obj.getRemoveVolume(),
                obj.getCreateBy(),
                dateStr,
                obj.getStoreCondition()
        ));
        // summary_list    摘要信息
        if (applyType == ApplyType.SOLUTION_APPLY) {
            request.setSummaryList(getSummaryList("溶液编号:" + obj.getCode(), "溶液类型:" + solution_type_Text, "浓度:" + obj.getConc() + ",取样量:" + obj.getWeighing() + ",含量:" + obj.getConc()));
        }
        // process	 审批流程信息
        request.setApprovers(getApprovers());
        try {
            String sp_no = oaService.apply(request);
            obj.setStatus("solutionUnderApproval");
            updateById(obj);

            //插入审批表
            WecomSp wecomSp = new WecomSp();
            wecomSp.setSpNo(sp_no);
            wecomSp.setTargetId(obj.getId());
            wecomSp.setTargetImpl("solutionServiceImpl");
            wecomSpService.save(wecomSp);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }
    private List<SummaryInfo> getSummaryList(String text1, String text2, String text3) {
        return new ArrayList<SummaryInfo>() {{
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text1))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text2))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text3))));
        }};
    }
    private List<WxCpOaApplyEventRequest.Approver> getApprovers() {
        ArrayList<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>() {{
            add(new WxCpOaApplyEventRequest.Approver().setAttr(1).setUserIds(new String[]{"xiaoxuebin", "wuxianzheng", "wangjiujun"}));
        }};
        return approvers;
    }
    private WxCpOaApplyEventRequest.ApplyData getApplyData(
            String Code,
            String Record,
            String solution_type_Text,
            String Conc,
            String Weighing,
            String Content,
            String ConstantVolume,
            String RemoveVolume,
            String CreateBy,
            String EffectiveTo,
            String StoreCondition
    ) {
        return new WxCpOaApplyEventRequest.ApplyData()
                .setContents(Arrays.asList(
                        new ApplyDataContent().setControl("Text").setId("Text-1744869184796").setValue(new ContentValue().setText(Code != null ? Code : "默认默认编号")),
                        new ApplyDataContent().setControl("Textarea").setId("Textarea-1744869213123").setValue(new ContentValue().setText(Record != null ? Record : "默认配制记录")),
                        new ApplyDataContent().setControl("Text").setId("Text-1640339319582").setValue(new ContentValue().setText(solution_type_Text != null ? solution_type_Text : "默认溶液类型")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868258243").setValue(new ContentValue().setText(Conc != null ? Conc : "默认浓度")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868242390").setValue(new ContentValue().setText(Weighing != null ? Weighing : "默认称样量")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868261172").setValue(new ContentValue().setText(Content != null ? Content : "默认含量")),
                        new ApplyDataContent().setControl("Text").setId("Text-1745833086868").setValue(new ContentValue().setText(ConstantVolume != null ? ConstantVolume : "默认定容体积")),
                        new ApplyDataContent().setControl("Text").setId("Text-1745833092380").setValue(new ContentValue().setText(RemoveVolume != null ? RemoveVolume : "默认移取体积")),
                        new ApplyDataContent().setControl("Text").setId("Text-1745833119908").setValue(new ContentValue().setText(CreateBy != null ? CreateBy : "默认配置人")),
                        new ApplyDataContent().setControl("Text").setId("Text-1745833142924").setValue(new ContentValue().setText(EffectiveTo != null ? EffectiveTo : "默认有效期")),
                        new ApplyDataContent().setControl("Text").setId("Text-1745833153420").setValue(new ContentValue().setText(StoreCondition != null ? StoreCondition : "默认保存条件"))
                ));
    }
    public void wxCallback(WxCpXmlMessage wxCpXmlMessage, String id, String typeId) {
        Solution obj = this.baseMapper.selectById(id);
        if(wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.SOLUTION_APPLY.getType())){
            // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
            // 审批中   solutionUnderApproval	已通过			solutionAlreadyPassed	已驳回		solutionRejected	状态异常		solutionAnomaly
            String status = "";
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("1")){
                status = "solutionUnderApproval";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("2")){
                status = "solutionAlreadyPassed";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("3")){
                status = "solutionRejected";
            }
            if (wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("4")){
                status = "solutionAnomaly";
            }
            obj.setStatus(status);
            updateById(obj);
        }
    }
}
