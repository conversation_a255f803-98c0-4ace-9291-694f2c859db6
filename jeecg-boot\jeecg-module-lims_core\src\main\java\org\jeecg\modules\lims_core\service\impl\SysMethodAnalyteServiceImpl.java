package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.mapper.SysMethodAnalyteMapper;
import org.jeecg.modules.lims_core.service.ISysMethodAnalyteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 检测指标
 * @Author: jeecg-boot
 * @Date:   2025-03-27
 * @Version: V1.0
 */
@Service
public class SysMethodAnalyteServiceImpl extends ServiceImpl<SysMethodAnalyteMapper, SysMethodAnalyte> implements ISysMethodAnalyteService {
    @Autowired
    private SysMethodAnalyteMapper sysmethodAnalyteMapper;
    @Override
    public List<SysMethodAnalyte> selectByMainId(String mainId) {
        return sysmethodAnalyteMapper.selectByMainId(mainId);
    }
}
