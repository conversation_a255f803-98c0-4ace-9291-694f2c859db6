package org.jeecg.modules.oo.test;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfDictionary;
import com.itextpdf.kernel.pdf.PdfName;
import com.itextpdf.kernel.pdf.PdfBoolean;
import com.itextpdf.signatures.SignatureUtil;
import com.itextpdf.signatures.PdfPKCS7;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.FileInputStream;
import java.util.List;

/**
 * LTV验证测试工具
 * 用于验证PDF文件是否正确包含LTV信息
 */
public class LtvVerificationTest {
    
    /**
     * 验证PDF文件的LTV状态
     * @param pdfFilePath PDF文件路径
     * @return 验证结果报告
     */
    public static String verifyLtvStatus(String pdfFilePath) {
        StringBuilder report = new StringBuilder();
        report.append("=== LTV验证报告 ===\n");
        report.append("文件: ").append(pdfFilePath).append("\n\n");
        
        try (PdfReader reader = new PdfReader(new FileInputStream(pdfFilePath));
             PdfDocument pdfDoc = new PdfDocument(reader)) {
            
            // 1. 检查PDF扩展
            report.append("1. PDF扩展检查:\n");
            PdfDictionary catalog = pdfDoc.getCatalog().getPdfObject();
            PdfDictionary extensions = catalog.getAsDictionary(PdfName.Extensions);
            if (extensions != null) {
                report.append("   ✓ Extensions字典存在\n");
                
                PdfDictionary adbeExt = extensions.getAsDictionary(new PdfName("ADBE"));
                if (adbeExt != null) {
                    report.append("   ✓ ADBE扩展存在\n");
                    report.append("   扩展级别: ").append(adbeExt.getAsNumber(PdfName.ExtensionLevel)).append("\n");
                } else {
                    report.append("   ✗ ADBE扩展缺失\n");
                }
                
                PdfDictionary esicExt = extensions.getAsDictionary(new PdfName("ESIC"));
                if (esicExt != null) {
                    report.append("   ✓ ESIC扩展存在\n");
                } else {
                    report.append("   ✗ ESIC扩展缺失\n");
                }
            } else {
                report.append("   ✗ Extensions字典缺失\n");
            }
            
            // 2. 检查DSS字典
            report.append("\n2. DSS字典检查:\n");
            PdfDictionary dss = catalog.getAsDictionary(PdfName.DSS);
            if (dss != null) {
                report.append("   ✓ DSS字典存在\n");
                
                if (dss.containsKey(PdfName.OCSPs)) {
                    report.append("   ✓ OCSP响应存在\n");
                    report.append("   OCSP数量: ").append(dss.getAsArray(PdfName.OCSPs).size()).append("\n");
                } else {
                    report.append("   ✗ OCSP响应缺失\n");
                }
                
                if (dss.containsKey(PdfName.CRLs)) {
                    report.append("   ✓ CRL存在\n");
                    report.append("   CRL数量: ").append(dss.getAsArray(PdfName.CRLs).size()).append("\n");
                } else {
                    report.append("   ✗ CRL缺失\n");
                }
                
                if (dss.containsKey(PdfName.VRI)) {
                    report.append("   ✓ VRI字典存在\n");
                    report.append("   VRI条目数量: ").append(dss.getAsDictionary(PdfName.VRI).size()).append("\n");
                } else {
                    report.append("   ✗ VRI字典缺失\n");
                }
            } else {
                report.append("   ✗ DSS字典缺失\n");
            }
            
            // 3. 检查Perms字典
            report.append("\n3. Perms字典检查:\n");
            PdfDictionary perms = catalog.getAsDictionary(PdfName.Perms);
            if (perms != null) {
                report.append("   ✓ Perms字典存在\n");
                
                PdfDictionary ltvDict = perms.getAsDictionary(new PdfName("LTV"));
                if (ltvDict != null) {
                    report.append("   ✓ LTV权限字典存在\n");
                    PdfBoolean enabled = ltvDict.getAsBoolean(new PdfName("Enabled"));
                    if (enabled != null && enabled.getValue()) {
                        report.append("   ✓ LTV已启用\n");
                    } else {
                        report.append("   ✗ LTV未启用\n");
                    }
                } else {
                    report.append("   ✗ LTV权限字典缺失\n");
                }
            } else {
                report.append("   ✗ Perms字典缺失\n");
            }
            
            // 4. 检查签名
            report.append("\n4. 签名检查:\n");
            SignatureUtil signatureUtil = new SignatureUtil(pdfDoc);
            List<String> signatureNames = signatureUtil.getSignatureNames();
            
            if (signatureNames.isEmpty()) {
                report.append("   ✗ 未找到签名\n");
            } else {
                report.append("   ✓ 找到 ").append(signatureNames.size()).append(" 个签名\n");
                
                for (String name : signatureNames) {
                    report.append("   签名: ").append(name).append("\n");
                    
                    try {
                        PdfPKCS7 pdfPKCS7 = signatureUtil.readSignatureData(name, BouncyCastleProvider.PROVIDER_NAME);
                        
                        // 检查时间戳
                        if (pdfPKCS7.getTimeStampDate() != null) {
                            report.append("     ✓ 包含时间戳: ").append(pdfPKCS7.getTimeStampDate()).append("\n");
                        } else {
                            report.append("     ✗ 缺少时间戳\n");
                        }
                        
                        // 检查证书链
                        if (pdfPKCS7.getSignCertificateChain() != null) {
                            report.append("     ✓ 证书链长度: ").append(pdfPKCS7.getSignCertificateChain().length).append("\n");
                        } else {
                            report.append("     ✗ 证书链缺失\n");
                        }
                        
                        // 检查签名字典的LTV标记
                        PdfDictionary sigDict = signatureUtil.getSignature(name).getPdfObject();
                        if (sigDict.containsKey(new PdfName("LTV"))) {
                            report.append("     ✓ 签名包含LTV标记\n");
                        } else {
                            report.append("     ✗ 签名缺少LTV标记\n");
                        }
                        
                    } catch (Exception e) {
                        report.append("     ✗ 签名验证失败: ").append(e.getMessage()).append("\n");
                    }
                }
            }
            
            // 5. 检查验证策略
            report.append("\n5. 验证策略检查:\n");
            PdfDictionary validationPolicy = catalog.getAsDictionary(new PdfName("ValidationPolicy"));
            if (validationPolicy != null) {
                report.append("   ✓ 验证策略存在\n");
                if (validationPolicy.containsKey(new PdfName("LTVEnabled"))) {
                    report.append("   ✓ LTV策略已启用\n");
                } else {
                    report.append("   ✗ LTV策略未启用\n");
                }
            } else {
                report.append("   ✗ 验证策略缺失\n");
            }
            
            // 6. 总结
            report.append("\n=== 验证总结 ===\n");
            boolean hasExtensions = extensions != null;
            boolean hasDss = dss != null;
            boolean hasPerms = perms != null;
            boolean hasSignatures = !signatureNames.isEmpty();
            boolean hasValidationPolicy = validationPolicy != null;
            
            int score = 0;
            if (hasExtensions) score++;
            if (hasDss) score++;
            if (hasPerms) score++;
            if (hasSignatures) score++;
            if (hasValidationPolicy) score++;
            
            report.append("LTV兼容性评分: ").append(score).append("/5\n");
            
            if (score >= 4) {
                report.append("✓ PDF应该被Adobe Reader识别为LTV启用\n");
            } else if (score >= 2) {
                report.append("⚠ PDF可能被部分识别为LTV启用\n");
            } else {
                report.append("✗ PDF不太可能被Adobe Reader识别为LTV启用\n");
            }
            
        } catch (Exception e) {
            report.append("验证过程中发生错误: ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        if (args.length != 1) {
            System.out.println("用法: java LtvVerificationTest <PDF文件路径>");
            return;
        }
        
        String result = verifyLtvStatus(args[0]);
        System.out.println(result);
    }
}
