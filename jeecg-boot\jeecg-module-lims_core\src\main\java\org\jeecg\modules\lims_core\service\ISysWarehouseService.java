package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysWarehouseBox;
import org.jeecg.modules.lims_core.entity.SysWarehouse;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 仓库
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
public interface ISysWarehouseService extends IService<SysWarehouse> {

	/**
	 * 添加一对多
	 *
	 * @param sysWarehouse
	 * @param sysWarehouseBoxList
	 */
	public void saveMain(SysWarehouse sysWarehouse,List<SysWarehouseBox> sysWarehouseBoxList) ;
	
	/**
	 * 修改一对多
	 *
   * @param sysWarehouse
   * @param sysWarehouseBoxList
	 */
	public void updateMain(SysWarehouse sysWarehouse,List<SysWarehouseBox> sysWarehouseBoxList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
