package org.jeecg.modules.lims_order.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_order.entity.Opportunity;
import org.jeecg.modules.lims_order.service.IOpportunityService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 商机
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Tag(name="商机")
@RestController
@RequestMapping("/lims_order/opportunity")
@Slf4j
public class OpportunityController extends JeecgController<Opportunity, IOpportunityService> {
	@Autowired
	private IOpportunityService opportunityService;
	
	/**
	 * 分页列表查询
	 *
	 * @param opportunity
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "商机-分页列表查询")
	@Operation(summary="商机-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Opportunity>> queryPageList(Opportunity opportunity,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("customerId", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("opportunitySource", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("opportunityStage", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<Opportunity> queryWrapper = QueryGenerator.initQueryWrapper(opportunity, req.getParameterMap(),customeRuleMap);
		Page<Opportunity> page = new Page<Opportunity>(pageNo, pageSize);
		IPage<Opportunity> pageList = opportunityService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param opportunity
	 * @return
	 */
	@AutoLog(value = "商机-添加")
	@Operation(summary="商机-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:opportunity:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Opportunity opportunity) {
		opportunity.setOpportunityNo(FillRuleUtil.executeRule(FillRuleConstant.OPPORTUNITY, null).toString());
		opportunityService.save(opportunity);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param opportunity
	 * @return
	 */
	@AutoLog(value = "商机-编辑")
	@Operation(summary="商机-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:opportunity:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Opportunity opportunity) {
		opportunityService.updateById(opportunity);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "商机-通过id删除")
	@Operation(summary="商机-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:opportunity:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		opportunityService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "商机-批量删除")
	@Operation(summary="商机-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:opportunity:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.opportunityService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "商机-通过id查询")
	@Operation(summary="商机-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Opportunity> queryById(@RequestParam(name="id",required=true) String id) {
		Opportunity opportunity = opportunityService.getById(id);
		if(opportunity==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(opportunity);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param opportunity
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:opportunity:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Opportunity opportunity) {
        return super.exportXls(request, opportunity, Opportunity.class, "商机");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:opportunity:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Opportunity.class);
    }

}
