package org.jeecg.modules.translate;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.lims_core.util.TranslateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
public class test {



    @Test
    public void test1() {
        System.out.println(TranslateUtil.translateToEnglish("你好，世界"));
    }
}
