package org.jeecg.modules.crm.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crm.entity.SysCustomerContact;
import org.jeecg.modules.crm.entity.SysCustomer;
import org.jeecg.modules.crm.vo.SysCustomerPage;
import org.jeecg.modules.crm.service.ISysCustomerService;
import org.jeecg.modules.crm.service.ISysCustomerContactService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 客户
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Tag(name="客户")
@RestController
@RequestMapping("/crm/sysCustomer")
@Slf4j
public class SysCustomerController {
	@Autowired
	private ISysCustomerService sysCustomerService;
	@Autowired
	private ISysCustomerContactService sysCustomerContactService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysCustomer
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "客户-分页列表查询")
	@Operation(summary="客户-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysCustomer>> queryPageList(SysCustomer sysCustomer,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
       // 自定义查询规则
       Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
       // 自定义多选的查询规则为：LIKE_WITH_OR
       customeRuleMap.put("type", QueryRuleEnum.LIKE_WITH_OR);
       customeRuleMap.put("grade", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<SysCustomer> queryWrapper = QueryGenerator.initQueryWrapper(sysCustomer, req.getParameterMap(),customeRuleMap);
//		String salerId = sysCustomer.getSalerId();
//		if (salerId == null || "".equals(salerId)) {
//			queryWrapper.and(qw -> qw.isNull("saler_id").or().eq("saler_id", ""));
//		}
		Page<SysCustomer> page = new Page<SysCustomer>(pageNo, pageSize);
		IPage<SysCustomer> pageList = sysCustomerService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysCustomerPage
	 * @return
	 */
	@AutoLog(value = "客户-添加")
	@Operation(summary="客户-添加")
    @PreAuthorize("@jps.requiresPermissions('crm:sys_customer:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysCustomerPage sysCustomerPage) {
		SysCustomer sysCustomer = new SysCustomer();
		sysCustomerPage.setCode(sysCustomerPage.getCode().toUpperCase());
		BeanUtils.copyProperties(sysCustomerPage, sysCustomer);
		sysCustomerService.saveMain(sysCustomer, sysCustomerPage.getSysCustomerContactList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysCustomerPage
	 * @return
	 */
	@AutoLog(value = "客户-编辑")
	@Operation(summary="客户-编辑")
    @PreAuthorize("@jps.requiresPermissions('crm:sys_customer:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysCustomerPage sysCustomerPage) {
		SysCustomer sysCustomer = new SysCustomer();
		sysCustomerPage.setCode(sysCustomerPage.getCode().toUpperCase());
		BeanUtils.copyProperties(sysCustomerPage, sysCustomer);
		SysCustomer sysCustomerEntity = sysCustomerService.getById(sysCustomer.getId());
		if(sysCustomerEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysCustomerService.updateMain(sysCustomer, sysCustomerPage.getSysCustomerContactList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "客户-通过id删除")
	@Operation(summary="客户-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('crm:sys_customer:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysCustomerService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "客户-批量删除")
	@Operation(summary="客户-批量删除")
    @PreAuthorize("@jps.requiresPermissions('crm:sys_customer:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysCustomerService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "客户-通过id查询")
	@Operation(summary="客户-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysCustomer> queryById(@RequestParam(name="id",required=true) String id) {
		SysCustomer sysCustomer = sysCustomerService.getById(id);
		if(sysCustomer==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysCustomer);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "客户联系人-通过主表ID查询")
	@Operation(summary="客户联系人-通过主表ID查询")
	@GetMapping(value = "/querySysCustomerContactByMainId")
	public Result<IPage<SysCustomerContact>> querySysCustomerContactListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysCustomerContact> sysCustomerContactList = sysCustomerContactService.selectByMainId(id);
		IPage <SysCustomerContact> page = new Page<>();
		page.setRecords(sysCustomerContactList);
		page.setTotal(sysCustomerContactList.size());
		return Result.OK(page);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysCustomer
    */
    @PreAuthorize("@jps.requiresPermissions('crm:sys_customer:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysCustomer sysCustomer) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysCustomer> queryWrapper = QueryGenerator.initQueryWrapper(sysCustomer, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<SysCustomer>  sysCustomerList = sysCustomerService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysCustomerPage> pageList = new ArrayList<SysCustomerPage>();
      for (SysCustomer main : sysCustomerList) {
          SysCustomerPage vo = new SysCustomerPage();
          BeanUtils.copyProperties(main, vo);
          List<SysCustomerContact> sysCustomerContactList = sysCustomerContactService.selectByMainId(main.getId());
          vo.setSysCustomerContactList(sysCustomerContactList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "客户列表");
      mv.addObject(NormalExcelConstants.CLASS, SysCustomerPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("客户数据", "导出人:"+sysUser.getRealname(), "客户"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('crm:sys_customer:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysCustomerPage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysCustomerPage.class, params);
              for (SysCustomerPage page : list) {
                  SysCustomer po = new SysCustomer();
                  BeanUtils.copyProperties(page, po);
                  sysCustomerService.saveMain(po, page.getSysCustomerContactList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
