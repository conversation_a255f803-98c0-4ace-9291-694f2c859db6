package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodStdMaterial;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 标品
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodStdMaterialService extends IService<SysMethodStdMaterial> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysMethodStdMaterial>
	 */
	public List<SysMethodStdMaterial> selectByMainId(String mainId);
}
