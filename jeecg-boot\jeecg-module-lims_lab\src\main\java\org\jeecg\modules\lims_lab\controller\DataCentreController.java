package org.jeecg.modules.lims_lab.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.SysMethodWorkflowMapper;
import org.jeecg.modules.lims_core.mapper.SysWorkflowStepMapper;
import org.jeecg.modules.lims_core.mapper.TestTaskFlowMapper;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.util.QianWenUtil;
import org.jeecg.modules.lims_lab.dto.TestResultDTO;
import org.jeecg.modules.lims_lab.dto.TestReworkApplyDTO;
import org.jeecg.modules.lims_lab.vo.QCTestResultVO;
import org.jeecg.modules.lims_lab.vo.TestImporterVO;
import org.jeecg.modules.lims_lab.vo.TestResultVO;
import org.jeecg.modules.lims_lab.vo.TestVO;
import org.jeecg.modules.lims_order.entity.BizOrder;

import org.jeecg.modules.lims_order.service.IBizOrderService;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dataCentre")
@Slf4j
public class DataCentreController {
    @Autowired
    private IBizOrderService bizOrderService;
    @Autowired
    private ISysCapabilityService capabilityService;
    @Autowired
    private ISysStandardService standardService;
    @Autowired
    private IInstrumentService instrumentService;
    @Autowired
    private ITestTaskService testTaskService;
    @Autowired
    private ITestService testService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private ISysMethodService sysMethodService;
    @Autowired
    private ISampleService sampleService;
    @Autowired
    private ITestResultService testResultService;
    @Autowired
    private ISysStandardService sysStandardService;
    @Autowired
    private ISysMethodAnalyteService  sysMethodAnalyteService;
    @Autowired
    private ISysAnalyteService sysAnalyteService;
    @Autowired
    private ISysUnitService  sysUnitService;
    @Autowired
    private ISysUnitConversionService sysUnitConversionService;
    @Autowired
    ISysStandardEvaluationLimtService sysStandardEvaluationLimtService;
    @Autowired
    ISysMethodRepeatTypeService sysMethodRepeatTypeService;
    @Autowired
    private ITestSolutionService testSolutionService;
    @Autowired
    private ISolutionService solutionService;
    @Autowired
    private ITestTaskFlowService testTaskFlowService;
    @Autowired
    private ISysDictItemService sysDictItemService;
    @Autowired
    private SysMethodWorkflowMapper sysMethodWorkflowMapper;
    @Autowired
    private SysWorkflowStepMapper sysWorkflowStepMapper;
    @Autowired
    private TestTaskFlowMapper testTaskFlowMapper;

    private static final String FLOW_STEP_1 = "业务受理";
    private static final String FLOW_STEP_2 = "仓库入库";
    private static final String FLOW_STEP_3 = "PM确认";
    private static final String FLOW_STEP_4 = "任务指派";
    private static final String FLOW_STEP_5 = "结果录入";
    private static final String FLOW_STEP_6 = "结果复核";
    private static final String FLOW_STEP_7 = "报告编制";
    private static final String FLOW_STEP_8 = "报告审核";
    private static final String FLOW_STEP_9 = "报告签发";
    @Autowired
    private ISysCapabilityService iSysCapabilityService;


    @AutoLog
    @GetMapping("/getTests")
    public Result<List<TestVO>> getTests(@RequestParam(name= "loadByType",required=true) int loadByType,@RequestParam(name= "loadBy",required=true) String loadBy){
        List<TestVO> list = new ArrayList<>();
        switch (loadByType){
            case 0->{//by task
                QueryWrapper<Test> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("task_id", loadBy).eq("test_type_id","0");
                List<Test> tests = testService.list(queryWrapper);
                LoginUser loginUser = SecureUtil.currentUser();
                for(Test t : tests){
                    TestVO ttv = new TestVO();
                    ttv.setId(t.getId());
                    TestTask testTask = testTaskService.getById(t.getTaskId());
                    SysMethod sysMethod =sysMethodService.getById(testTask.getMethodId());
                    SysStandard sysStandard = sysStandardService.getById(sysMethod.getStandardId());
                    String modifiedName = sysStandard.getName();
                    if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("》")) {
                        int index = sysStandard.getName().indexOf("》");
                        modifiedName = sysStandard.getName().substring(0, index + 1)
                                + sysStandard.getVersion()
                                + " "
                                + sysStandard.getName().substring(index + 1);
                    } else if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("GB")) {
                        modifiedName = sysStandard.getName() + "-" + sysStandard.getVersion();
                    }
                    ttv.setMethod(sysMethod.getName()  + "\n"+ modifiedName);
                    ttv.setMethodId(sysMethod.getId());
                    ttv.setSysOrgCode(loginUser.getOrgCode());
//                    LoginUser loginUser = SecureUtil.currentUser();
//                    List<String> Roles = StrUtil.split(loginUser.getRoleCode(),",");//缺少结合岗位定义
                    String sampleNo = getSampleNo(testTask,testTask.getRepeatName());
                    ttv.setSampleNo(sampleNo);
                    if(t.getIsSubmitted().equals(1) && t.getCheckedBy() ==null && t.getCheckedTime() == null){
                        ttv.setStatus("1");
                    }else if(t.getCheckedBy()!=null && t.getCheckedTime() != null){
                        ttv.setStatus("2");
                    }else{
                        ttv.setStatus("0");
                    }
                    if(!loginUser.getOrgCode().equals("A01A01A03A01A03")&&!FLOW_STEP_5.equals(testTask.getCurStep()) && !FLOW_STEP_6.equals(testTask.getCurStep())){
                        continue;
                    }
                    list.add(ttv);
                }
            }
            case 1->{//样品
                QueryWrapper<Sample> qws = new QueryWrapper<>();
                qws.eq("sample_no", loadBy);
                Sample sample = sampleService.getOne(qws);
                if(sample!=null){
                    QueryWrapper<TestTask> qwtt = new QueryWrapper<>();
                    qwtt.eq("sample_id", sample.getId());
                    qwtt.eq("test_control_status","正常");
                    List<TestTask> testTasks = testTaskService.list(qwtt);//缺少结合岗位定义
                    list = getTestsByTasks(testTasks,loadByType);
                }
            }
            case 2->{//订单
                QueryWrapper<BizOrder> qwo = new QueryWrapper<>();
                qwo.eq("contract_no", loadBy);
                BizOrder bizOrder = bizOrderService.getOne(qwo);
                if(bizOrder!=null){
                    QueryWrapper<Sample> qws = new QueryWrapper<>();
                    qws.eq("order_id", bizOrder.getId());
                    Sample sample = sampleService.getOne(qws);
                    if(sample!=null){
                        QueryWrapper<TestTask> qwtt = new QueryWrapper<>();
                        qwtt.eq("sample_id", sample.getId());
                        qwtt.eq("test_control_status","正常");
                        List<TestTask> testTasks = testTaskService.list(qwtt);//缺少结合岗位定义
                        list = getTestsByTasks(testTasks, loadByType);
                    }
                }
            }
            case 3->{//未提交任务
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("assignee", loginUser.getUsername());
                queryWrapper.eq("cur_step", FLOW_STEP_5);//结果录入
                queryWrapper.eq("test_control_status","正常");
                List<TestTask> testTasks = testTaskService.list(queryWrapper);
                list = getTestsByTasks(testTasks, loadByType);
            }
            case 4->{//已提交任务
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("assignee", loginUser.getUsername());
                queryWrapper.eq("test_control_status","正常");
                queryWrapper.in("cur_step", FLOW_STEP_6,FLOW_STEP_7,FLOW_STEP_8,FLOW_STEP_9);//结果录入
                List<TestTask> testTasks = testTaskService.list(queryWrapper);
                list = getTestsByTasks(testTasks, loadByType);
            }
            case 5->{//未复核任务
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("checker", loginUser.getUsername());
                queryWrapper.eq("test_control_status","正常");
                queryWrapper.eq("cur_step", FLOW_STEP_5);//结果录入
                List<TestTask> testTasks = testTaskService.list(queryWrapper);
                list = getTestsByTasks(testTasks, loadByType);
            }
            case 6->{//已复核任务
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("checker", loginUser.getUsername());
                queryWrapper.eq("test_control_status","正常");
                queryWrapper.in("cur_step", FLOW_STEP_6,FLOW_STEP_7,FLOW_STEP_8,FLOW_STEP_9);//结果录入
                List<TestTask> testTasks = testTaskService.list(queryWrapper);//缺少结合岗位定义
                list =getTestsByTasks(testTasks, loadByType);
            }
            case 7->{//指派的任务
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("assigner", loginUser.getUsername());
                queryWrapper.eq("test_control_status","正常");
                List<TestTask> testTasks = testTaskService.list(queryWrapper);//缺少结合岗位定义
                list =getTestsByTasks(testTasks, loadByType);
            }
            case 8->{//指派的任务已完成的
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("assigner", loginUser.getUsername());
                queryWrapper.eq("test_control_status","正常");
                List<TestTask> testTasks = testTaskService.list(queryWrapper);//缺少结合岗位定义
                list =getTestsByTasks(testTasks, loadByType);
            }
            case 9->{//指派的任务未完成的
                LoginUser loginUser = SecureUtil.currentUser();
                QueryWrapper<TestTask> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("assigner", loginUser.getUsername());
                queryWrapper.eq("test_control_status","正常");
                List<TestTask> testTasks = testTaskService.list(queryWrapper);//缺少结合岗位定义
                list =getTestsByTasks(testTasks, loadByType);
            }
        }
        return Result.OK(list);
    }

    private List<TestVO> getTestsByTasks(List<TestTask> testTasks, int loadByType) {
        List<TestVO> list = new ArrayList<>();
        LoginUser loginUser = SecureUtil.currentUser();
        for(TestTask testTask : testTasks){
            QueryWrapper<Test> qwt = new QueryWrapper<>();
            qwt.eq("task_id", testTask.getId()).eq("test_type_id","0");
            List<Test> tests = testService.list(qwt);

            for(Test t : tests){
                TestVO ttv = new TestVO();
                ttv.setId(t.getId());
                SysMethod sysMethod =sysMethodService.getById(testTask.getMethodId());
                SysStandard sysStandard = sysStandardService.getById(sysMethod.getStandardId());
                String modifiedName = sysStandard.getName();
                if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("》")) {
                    int index = sysStandard.getName().indexOf("》");
                    modifiedName = sysStandard.getName().substring(0, index + 1)
                            + sysStandard.getVersion()
                            + " "
                            + sysStandard.getName().substring(index + 1);
                } else if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("GB")) {
                    modifiedName = sysStandard.getName() + "-" + sysStandard.getVersion();
                }
                ttv.setMethod(sysMethod.getName()  + "\n"+ modifiedName);
                ttv.setMethodId(sysMethod.getId());
                ttv.setSysOrgCode(loginUser.getOrgCode());
                String sampleNo = getSampleNo(testTask,testTask.getRepeatName());
                ttv.setSampleNo(sampleNo);
                if(t.getIsSubmitted().equals(1) && t.getCheckedBy() ==null && t.getCheckedTime() == null){
                    ttv.setStatus("1");
                }else if(t.getCheckedBy()!=null && t.getCheckedTime() != null){
                    ttv.setStatus("2");
                }else{
                    ttv.setStatus("0");
                }
                if (loadByType == 8 || loadByType == 9){
                    if(loadByType == 8 && t.getCheckedBy()!= null && t.getCheckedTime() != null){
                        list.add(ttv);
                    }
                    if(loadByType == 9 && t.getCheckedBy() == null && t.getCheckedTime() == null){
                        list.add(ttv);
                    }
                }else {
                    list.add(ttv);
                }
            }
        }
        return list;
    }

    private String getSampleNo(TestTask testTask,String repeatName) {
        Sample sample = sampleService.getById(testTask.getSampleId());
        String sampleNo = sample.getSampleNo();
        if(StrUtil.isNotEmpty(testTask.getRepeatTypeId())){
            SysMethodRepeatType sysMethodRepeatType =  sysMethodRepeatTypeService.getById(testTask.getRepeatTypeId());
            DictModel repeatNameDict = sysBaseAPI.queryDictItemsByCode(sysMethodRepeatType.getRepeatType())
                    .stream()
                    // 先过滤出符合条件的数据
                    .filter(dict -> repeatName!=null && repeatName.equals(dict.getValue()))
                    // 再获取第一个匹配项
                    .findFirst()
                    // 转换为Optional<DictModel>或返回null
                    .orElse(null);
            if(repeatNameDict!=null){
                sampleNo += "_S" + sysMethodRepeatType.getCycleName() + "_" + repeatNameDict.getText();
            }else {
                sampleNo += "_S" + sysMethodRepeatType.getCycleName();
            }
        }
        if(testTask.getRetestNo()!=null && StrUtil.isNotEmpty(String.valueOf(testTask.getRetestNo()))){
            sampleNo += "_R" + testTask.getRetestNo();
        }
        return sampleNo;
    }

    @AutoLog
    @GetMapping("/getTestResults")
    public Result<List<TestResultVO>> getTestResults(@RequestParam(name= "testIds",required=true) String testIds){
        List<String> tests = StrUtil.split(testIds,",");
        List<TestResult> testResults = new ArrayList<>();
        for(String testId : tests){
            List<TestResult> testResultList = testResultService.selectByTestId(testId);
            testResults.addAll(testResultList);
            Test t = testService.getById(testId);
            if(t.getTestTypeId().equals("0")){
                QueryWrapper<Test> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("task_id",t.getTaskId())
                        .eq("test_type_id","1");
                List<Test> lstTests = testService.list(queryWrapper);
                if(lstTests!=null && !lstTests.isEmpty()){
                    for(Test test : lstTests){
                        List<TestResult> pTestResultList = testResultService.selectByTestId(test.getId());
                        testResults.addAll(pTestResultList);
                    }
                }
            }
        }
        List<TestResultVO> targetTestResults = BeanUtil.copyToList(testResults, TestResultVO.class);
        for(TestResultVO testResultVO : targetTestResults){
            String TestResultId = testResultVO.getId();
            TestResult testResult = testResults.stream().filter(t -> t.getId().equals(TestResultId)).findFirst().get();
            Test test = testService.getById(testResult.getTestId());
            TestTask testTask = testTaskService.getById(test.getTaskId());
            SysMethod sysMethod=sysMethodService.getById(testTask.getMethodId());
            testResultVO.setMethodId(testTask.getMethodId());
            testResultVO.setMethodName(sysMethod.getName());
            testResultVO.setNatureTypeId(sysMethod.getNatureTypeId());
            testResultVO.setSubmitted(test.getIsSubmitted()==1);
            testResultVO.setSampleNo(getSampleNo(testTask,testTask.getRepeatName()));
            testResultVO.setTrialNo(test.getTrialNo());
            SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteService.getById(testResult.getMethodAnalyteId()) ;
            testResultVO.setSortNum(sysMethodAnalyte.getSortNum());
            testResultVO.setResultType(sysMethodAnalyte.getResultType());
            testResultVO.setLod(sysMethodAnalyte.getLod());
            testResultVO.setLoq(sysMethodAnalyte.getLoq());
            testResultVO.setCalcExpr(sysMethodAnalyte.getCalcExpr());
            testResultVO.setRoundingAlgorithmId(sysMethodAnalyte.getRoundingAlgorithmId());
            testResultVO.setRoundingWayId(sysMethodAnalyte.getRoundingWayId());
            testResultVO.setRoundingPrecision(sysMethodAnalyte.getRoundingPrecision());
            testResultVO.setPrecisionTypeId(sysMethodAnalyte.getPrecisionTypeId());
            testResultVO.setPrecisionReq(sysMethodAnalyte.getPrecisionReq());
            testResultVO.setPreChecker(testTask.getChecker());
            testResultVO.setChecker(test.getCheckedBy());
            testResultVO.setTester(test.getTester());
            testResultVO.setCheckerTime(test.getCheckedTime());
            testResultVO.setTesterTime(test.getTesterTime());
            testResultVO.setTaskId(testTask.getId());
            SysCapability capa = iSysCapabilityService.getById(testTask.getCapabilityId());
            if(capa!=null){
                testResultVO.setDeptId(capa.getDeptId());
            }
            capa = iSysCapabilityService.getById(sysMethod.getCid());
            if(capa!=null){
                testResultVO.setDeptId(capa.getDeptId());
            }
            SysAnalyte sysAnalyte = sysAnalyteService.getById(sysMethodAnalyte.getAnalyteId());
            testResultVO.setAnalyte(sysAnalyte.getName());
            if(sysMethodAnalyte.getResultType()==2){
                SysUnit sysUnit = sysUnitService.getById(sysMethodAnalyte.getUnitId());
//                if(sysUnit == null)
//                    throw new RuntimeException("方法未定义单位!!!");
//                testResultVO.setRawUnit(sysUnit.getUnitName());
                if (ObjectUtil.isNotEmpty(testResult.getLimitId())){
                    SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtService.getById(testResult.getLimitId());
                    testResultVO.setLimit(sysStandardEvaluationLimt.getElimit());
                    sysUnit = sysUnitService.getById(sysStandardEvaluationLimt.getUnitId());
                    if(sysUnit == null)
                        throw new RuntimeException("评价未定义计量单位!!!");
                    testResultVO.setRepUnit(sysUnit.getUnitName());
                    //=======05.31现阶段临时如此;
                    testResultVO.setRawUnit(sysUnit.getUnitName());
                    String calcExpr = sysMethodAnalyte.getCalcExpr();
                    if (calcExpr != null){
                        for (TestResultVO testResultVO2 : targetTestResults) {
                            if (calcExpr.contains("{" + testResultVO2.getAnalyte() + "}")) {
                                testResultVO2.setRawUnit(sysUnit.getUnitName());
                            }
                        }
                    }
                    //===============05.31现阶段临时如此;
                }
                if(testResultVO.getRawUnit()!=null){
                    if(testResultVO.getRawUnit().equals(testResultVO.getRepUnit())){
                        testResultVO.setUCF(new BigDecimal("1.0"));
                    }else{
                        QueryWrapper<SysUnitConversion> qw = new QueryWrapper<>();
                        qw.eq("source_unit_id",sysMethodAnalyte.getUnitId())
                                .eq("target_unit_id",sysMethodAnalyte.getUnitId());
                        Optional<SysUnitConversion> sysUnitConversion = sysUnitConversionService.getOneOpt(qw);
                        if(sysUnitConversion.isPresent()){
                            testResultVO.setUCF(new BigDecimal(sysUnitConversion.get().getFactor()));
                        }else{
                            testResultVO.setUCF(null);
                        }
                    }
                }
            }else{
                if (ObjectUtil.isNotEmpty(testResult.getLimitId())){
                    SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtService.getById(testResult.getLimitId());
                    testResultVO.setLimit(sysStandardEvaluationLimt.getElimit());
                }
            }
            testResultVO.setCheckedBy(test.getCheckedBy());
            testResultVO.setCheckedTime(test.getCheckedTime());
        }
        targetTestResults.sort(Comparator.comparingInt(TestResultVO::getSortNum));
        return Result.OK(targetTestResults);
    }

    @AutoLog
    @GetMapping("/getMethodidByTestid")
    public Result<TestTask> getMethodidByTestid(@RequestParam(name= "testId",required=true) String testId){
        Test test = testService.getById(testId);
        TestTask tt = testTaskService.getById(test.getTaskId());
        return Result.OK(tt);
    }

    @GetMapping("/addDuplicate")
    public Result<String> addDuplicate(@RequestParam(name= "testId",required=true) String testId,@RequestParam(name= "count",required=true) int count){
        if (count < 1) {
            return Result.error("请输入正确的数量");
        }
        Test test = testService.getById(testId);
        QueryWrapper<TestResult> ResultQueryWrapper = new QueryWrapper<>();
        ResultQueryWrapper.eq("test_id", testId);
        List<TestResult> testResults = testResultService.list(ResultQueryWrapper);
        LoginUser sysUser = SecureUtil.currentUser();
        QueryWrapper<Test> testQueryWrapper = new QueryWrapper<>();
        testQueryWrapper.eq("task_id", test.getTaskId());
        List<Test> tests = testService.list(testQueryWrapper);
        try {
            for (int i = 1; i <= count; i++) {
                Test newTest = new Test();
                BeanUtils.copyProperties(test, newTest);
                newTest.setId(null);
                newTest.setCreateTime(new Date());
                newTest.setCreateBy(sysUser.getRealname());
                newTest.setTestTypeId("1");
                newTest.setTrialNo(tests.size() + i);
                testService.save(newTest);
                for (TestResult result : testResults) {
                    TestResult newResult = new TestResult();
                    BeanUtils.copyProperties(result, newResult);
                    newResult.setId(null);
                    newResult.setTestId(newTest.getId());
                    testResultService.save(newResult);
                }
            }
            return Result.OK("添加成功！");
        } catch (Exception e) {
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    @AutoLog
    @GetMapping("/getQCTestResults")
    public Result<List<QCTestResultVO>> getQCTestResults(@RequestParam(name= "testIds",required=true) String testIds,@RequestParam(name= "qcTypes",required=true) String qcTypes){
        List<String> tests = StrUtil.split(testIds,",");
        List<String> lstQcType = StrUtil.split(qcTypes,",");
        List<Test> lstTests = testService.listByIds(tests);
        Set<String> sequenceNos = lstTests.stream()
                .filter(Objects::nonNull)                     // 过滤掉null的Test对象
                .map(Test::getSequenceNo)
                .filter(Objects::nonNull)                     // 过滤掉null的sequenceNo
                .collect(Collectors.toSet());
        QueryWrapper<Test> qw = new QueryWrapper<>();
        qw.in("sequence_no",sequenceNos)
                .in("test_type_id",lstQcType);
        List<Test> qcTests = testService.list(qw);
        if(!qcTests.isEmpty()){
            Set<String> qcTestIds = qcTests.stream()
                    .filter(Objects::nonNull)                     // 过滤掉null的Test对象
                    .map(Test::getId)
                    .filter(Objects::nonNull)                     // 过滤掉null的sequenceNo
                    .collect(Collectors.toSet());
            QueryWrapper<TestResult> qwTestResult = new QueryWrapper<>();
            qwTestResult.in("test_id",qcTestIds);
            List<TestResult> qcTestResults= testResultService.list(qwTestResult);
            List<QCTestResultVO> targetTestResults = BeanUtil.copyToList(qcTestResults, QCTestResultVO.class);
            for(QCTestResultVO qcTestResultVO : targetTestResults){
                String TestResultId = qcTestResultVO.getId();
                TestResult testResult = qcTestResults.stream().filter(t -> t.getId().equals(TestResultId)).findFirst().get();
                Test test = testService.getById(testResult.getTestId());
                Solution solution = solutionService.getById(test.getId());
                qcTestResultVO.setSampleNo(solution.getCode());
                QueryWrapper<Test> qwTest = new QueryWrapper<>();
                qwTest.eq("sequence_no",test.getSequenceNo()).eq(true,"test_type_id",'0');
                Test unkTest = testService.getOne(qwTest);
                TestTask testTask = testTaskService.getById(unkTest.getTaskId());
                SysMethod sysMethod=sysMethodService.getById(testTask.getMethodId());
                qcTestResultVO.setMethodId(testTask.getMethodId());
                qcTestResultVO.setMethodName(sysMethod.getName());
                SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteService.getById(testResult.getMethodAnalyteId()) ;
                SysAnalyte sysAnalyte = sysAnalyteService.getById(sysMethodAnalyte.getAnalyteId());
                qcTestResultVO.setAnalyte(sysAnalyte.getName());
                qcTestResultVO.setCheckedBy(test.getCheckedBy());
                qcTestResultVO.setCheckedTime(test.getCheckedTime());
            }
            targetTestResults.sort(Comparator.comparing(QCTestResultVO::getAnalyte));
            return Result.OK(targetTestResults);
        }else
            return Result.OK("未发现数据！");
    }

    @AutoLog
    @PostMapping("/saveTestResults")
    public Result<String> saveTestResults(@RequestBody List<TestResultDTO> testResults){
        for(TestResultDTO testResultDTO : testResults){
            TestResult tr = testResultService.getById(testResultDTO.getId());
            boolean dirtied = false;
            if(!Objects.equals(tr.getRawResult(),testResultDTO.getRawResult())){
                tr.setRawResult(testResultDTO.getRawResult());
                dirtied = true;
            }

            if(!Objects.equals(tr.getDupResult(),testResultDTO.getDupResult())){
                tr.setDupResult(testResultDTO.getDupResult());
                dirtied = true;
            }
            if(!Objects.equals(tr.getRepResult(),testResultDTO.getRepResult())){
                tr.setRepResult(testResultDTO.getRepResult());
                dirtied = true;
            }
            if(!Objects.equals(tr.getConclusion(),testResultDTO.getConclusion())){
                tr.setConclusion(testResultDTO.getConclusion());
                dirtied = true;
            }
            if(!Objects.equals(tr.getReportable(),testResultDTO.getReportable())){
                tr.setReportable(testResultDTO.getReportable());
                dirtied = true;
            }
            if(!Objects.equals(tr.getRepLimit(),testResultDTO.getRepLimit())){
                tr.setRepLimit(testResultDTO.getRepLimit());
                dirtied = true;
            }
            if(dirtied){
                testResultService.updateById(tr);
            }
        }
        return Result.OK("保存成功！");
    }

    @AutoLog
    @PostMapping("/submitTestResults")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> submitTestResults(@RequestParam(name= "testIds",required=true) String testIds){
        List<String> tests = StrUtil.split(testIds,",");
        List<Test> test2Update = new ArrayList<>();
        LoginUser sysUser = SecureUtil.currentUser();
        for(String testId : tests){
            Test test = testService.getById(testId);
            test.setIsSubmitted(1);
            test.setTester(sysUser.getRealname());
            test.setTesterTime(new Date());
            test2Update.add(test);
        }

        testService.updateBatchById(test2Update);
        return Result.OK("提交成功！");
    }

    @AutoLog
    @PostMapping("/checkTestResults")
    public Result<String> checkTestResults(@RequestParam(name= "testIds",required=true) String testIds){
        List<String> tests = StrUtil.split(testIds,",");
        LoginUser sysUser = SecureUtil.currentUser();
        DateTime checkedTime = DateUtil.date();
        List<Test> test2Update = new ArrayList<>();
        for(String testId : tests){
            Test test = testService.getById(testId);
            test.setCheckedBy(sysUser.getRealname());
            test.setCheckedTime(checkedTime);
            test2Update.add(test);

            TestTask testTask = testTaskService.getById(test.getTaskId());

            //如果当前节点是结果录入，则更新为复核节点
            if(FLOW_STEP_5.equals(testTask.getCurStep())){
                testTask.setCurStep(FLOW_STEP_6);
                testTaskService.updateById(testTask);

                TestTaskFlow testTaskFlow = new TestTaskFlow();
                testTaskFlow.setTaskId(testTask.getId());
                testTaskFlow.setStepId(FLOW_STEP_6);
                testTaskFlowMapper.insert(testTaskFlow);
            }

        }
        testService.updateBatchById(test2Update);
        return Result.OK("复核成功！");
    }

    @AutoLog
    @PostMapping("/unlockTestResults")
    public Result<String> unlockTestResults(@RequestParam(name= "testIds",required=true) String testIds){
        List<String> tests = StrUtil.split(testIds,",");
        List<Test> test2Update = new ArrayList<>();
        for(String testId : tests){
            Test test = testService.getById(testId);
            test.setIsSubmitted(0);
            test.setCheckedBy(null);
            test.setCheckedTime(null);
            test2Update.add(test);
        }
        testService.updateBatchById(test2Update);
        return Result.OK("解锁成功！");
    }

    @AutoLog
    @PostMapping("/applyRework")
    public Result<String> applyRework(@RequestBody @Valid TestReworkApplyDTO reworkApplyDTO) throws WxErrorException {
        List<String> tests = StrUtil.split(reworkApplyDTO.getTestIds(),",");
        for(String testId : tests){
            testService.applyRework(testId,reworkApplyDTO.getReworkReason(),reworkApplyDTO.getReworkBy());
        }
        return Result.OK("申请成功，审批通过后自动分配任务！");
    }

    @AutoLog
    @GetMapping("/getTestsForImporter")
    public Result<List<TestImporterVO>> getTestsForImporter(@RequestParam(name= "taskId",required=true) String taskId, @RequestParam(name= "instrumentId",required=true) String instrumentId){
        List<TestImporterVO> lst = new ArrayList<>();
        TestTask testTask = testTaskService.getById(taskId);
        SysMethod sysMethod = sysMethodService.getById(testTask.getMethodId());
        SysCapability sysCapability = capabilityService.getById(sysMethod.getCid());
        SysStandard sysStandard = standardService.getById(sysMethod.getStandardId()) ;
        QueryWrapper<Test> testQueryWrapper = new QueryWrapper<>();
        testQueryWrapper.eq("task_id", taskId);
        List<Test> lstTest = testService.list(testQueryWrapper);
        if (!lstTest.isEmpty()){
            QueryWrapper<TestSolution> testSolutionQueryWrapper = new QueryWrapper<>();
            testSolutionQueryWrapper.eq("test_task_id", taskId);
            List<TestSolution> lstTestSolutions  = testSolutionService.list(testSolutionQueryWrapper);
            lstTestSolutions.forEach(testSolution -> {
                Solution solution = solutionService.getById(testSolution.getSolutionId());
                if(!solution.getSolutionTypeId().equals("UNK")){
                    Test qcTest = testService.getById(solution.getId());
                    if(qcTest == null){
                        qcTest = new Test();
                        qcTest.setId(solution.getId());
                        qcTest.setTestTypeId(solution.getSolutionTypeId());
                        qcTest.setTrialNo(1);
                        qcTest.setAmount(solution.getWeighing());
                        qcTest.setVolume(solution.getConstantVolume());
                        testService.save(qcTest);
                    }
                    QueryWrapper<TestResult> unkTestResultQueryWrapper = new QueryWrapper<>();
                    unkTestResultQueryWrapper.eq("test_id", lstTest.get(0).getId());
                    List<TestResult> unkTestResults = testResultService.list(unkTestResultQueryWrapper);
                    if(!unkTestResults.isEmpty()) {
                        List<TestResult> qcTestResults = new ArrayList<>();
                        for (TestResult unkTestResult : unkTestResults) {
                            QueryWrapper<TestResult> qcTestResultQueryWrapper = new QueryWrapper<>();
                            qcTestResultQueryWrapper.eq("test_id", qcTest.getId())
                                    .eq("method_analyte_id",unkTestResult.getMethodAnalyteId());
                            TestResult qcTestResult = testResultService.getOne(qcTestResultQueryWrapper);
                            if(qcTestResult== null){
                                qcTestResult = new TestResult();
                                qcTestResult.setTestId(qcTest.getId());
                                qcTestResult.setMethodAnalyteId(unkTestResult.getMethodAnalyteId());
                                qcTestResults.add(qcTestResult);
                            }
                        }
                        testResultService.saveBatch(qcTestResults);
                    }
                    TestImporterVO testImporterVO = new TestImporterVO();
                    testImporterVO.setSortNum(0);
                    testImporterVO.setTestId(qcTest.getId());
                    testImporterVO.setSampleNo(solution.getCode());//溶液编号作为样品编号，只是供仪器显示
                    if(sysCapability!=null)
                        testImporterVO.setCapability(sysCapability.getName());
                    testImporterVO.setMethod(sysStandard.getName() + " " + sysMethod.getName());
                    testImporterVO.setAmount(qcTest.getAmount());
                    testImporterVO.setVolume("50");//////
                    testImporterVO.setDF("1");//////
                    Instrument instrument = instrumentService.getById(instrumentId);
                    testImporterVO.setInstrumentNo(instrument.getCode());
                    lst.add(testImporterVO);
                }
            });
            lstTest.forEach(test -> {
                TestImporterVO testImporterVO = new TestImporterVO();
                testImporterVO.setSortNum(0);
                testImporterVO.setTestId(test.getId());
                Sample sample = sampleService.getById(testTask.getSampleId());
                String sampleNo = sample.getSampleNo();
                if(testTask.getRepeatName()!=null) {
                    List<DictModel> lstRepeatType = sysBaseAPI.getDictItems(testTask.getRepeatType());
                    DictModel repeatType = lstRepeatType.stream()
                            .filter(dictModel -> dictModel.getValue().equals(testTask.getRepeatName()))
                            .findFirst()
                            .orElse(null);
                    sampleNo = sample.getSampleNo() + (repeatType != null ? " " + repeatType.getText() : "");
                }
                testImporterVO.setSampleNo(sampleNo + "_" + test.getTestTypeId() + "_" + test.getTrialNo());
                if(sysCapability!=null)
                    testImporterVO.setCapability(sysCapability.getName());
                testImporterVO.setMethod(sysStandard.getName() + " " + sysMethod.getName());
                testImporterVO.setAmount(test.getAmount());
                testImporterVO.setVolume("50");//////
                testImporterVO.setDF("1");//////
                Instrument instrument = instrumentService.getById(instrumentId);
                testImporterVO.setInstrumentNo(instrument.getCode());
                lst.add(testImporterVO);
            });
        }
        return Result.OK(lst);
    }

    @AutoLog
    @PostMapping("/exportSequence")
    public Result<String> exportSequence(@RequestBody @Valid List<TestImporterVO> testImporterVOs)  {
        List<Test> lstTest = new ArrayList<>();
        for (TestImporterVO testImporterVO : testImporterVOs) {
            Test test = testService.getById(testImporterVO.getTestId());
            test.setVolume(testImporterVO.getVolume());
            test.setSequenceNo(testImporterVO.getSequenceNo());
            lstTest.add(test);
        }
        testService.updateBatchById(lstTest);
        return Result.OK("序列导出成功！");
    }

    @AutoLog
    @PostMapping("/getConclusion")
    public Result<List<TestResultVO>> getConclusion(@RequestBody TestResultVO testResult){
        String conclustion = QianWenUtil.getConclustion(testResult.getRawResult(), testResult.getLimit());
        return Result.OK(conclustion);
    }

    @AutoLog
    @GetMapping("/changeStandard")
    public Result<String> changeStandard(@RequestParam(name= "taskId",required=true) String taskId,@RequestParam(name= "standardId",required=true) String standardId){
        testService.getBaseMapper().selectList(new QueryWrapper<Test>().eq("task_id", taskId)).forEach(test -> {
            testResultService.selectByTestId(test.getId()).forEach(testResult -> {
                SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteService.getById(testResult.getMethodAnalyteId());
                sysStandardEvaluationLimtService.getBaseMapper().selectList(
                        new QueryWrapper<SysStandardEvaluationLimt>().eq("standard_id", standardId)
                                .eq("analyte_id", sysMethodAnalyte.getAnalyteId())
                ).forEach(sysStandardEvaluationLimt -> {
                    testResult.setLimitId(sysStandardEvaluationLimt.getId());
                    testResultService.updateById(testResult);
                });
            });
        });
        return Result.OK("修改成功！");
    }
}
