package org.jeecg.modules.aitest;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.demo.gpt.service.impl.ChatServiceImpl;
import org.jeecg.modules.lims_core.util.QianWenUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
public class test {



    @Test
    public void test1() {
        String test = QianWenUtil.sendChatCompletion("【性状】本品为白色或类白色粉末。 结果为:黑色 请问合格吗?只需回答符合规定/不符合规定");
        //result :{"choices":[{"message":{"role":"assistant","content":"你好！今天过得怎么样？"},"finish_reason":"stop","index":0,"logprobs":null}],"object":"chat.completion","usage":{"prompt_tokens":9,"completion_tokens":6,"total_tokens":15,"prompt_tokens_details":{"cached_tokens":0}},"created":1746608667,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-81cf6324-11f5-92a3-ac9b-05d25468ad57"}
        //获取content
        JSONObject jsonObject = JSONObject.parseObject(test);
        String content = jsonObject.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
        System.out.println(content);


    }
}
