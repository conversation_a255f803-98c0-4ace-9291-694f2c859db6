<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.WarehouseInApplyMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.WarehouseInApplyVo">
        select * from ( SELECT i.*,COALESCE(s.name, c.name, sm.name) AS goodsname,su.realname
                        FROM     warehouse_in_apply i
                                     LEFT JOIN sample s ON i.article_no = s.sample_no
                                     LEFT JOIN consumptive c ON i.article_no = c.code
                                     LEFT JOIN standard_material sm ON i.article_no = sm.code
                                     LEFT JOIN Sys_user su on i.create_by=su.username
                        ) t
            ${ew.customSqlSegment}
    </select>
</mapper>