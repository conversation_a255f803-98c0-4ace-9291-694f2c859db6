package org.jeecg.modules.oo.controller.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import org.jeecg.config.OnlyOfficeConfig;
import org.jeecg.modules.lims_core.vo.ReportRequestVO;

import java.util.List;

@Data
public class DocumentInfo {
    private String id;
    private String fileName;
    private String url;
    private OnlyOfficeConfig.Config.Document.Permissions permissions;
    private List<String> pluginsData = Lists.newArrayList();//默认加载的插件配置路径
    private List<String> autostart = Lists.newArrayList();//默认加载后自动启动的插件ID
    private OnlyOfficeConfig.Config.EditorConfig.Plugins.Options pluginOptions; //传递到插件中的数据
    private String status; //状态
    private ReportRequestVO report;
}