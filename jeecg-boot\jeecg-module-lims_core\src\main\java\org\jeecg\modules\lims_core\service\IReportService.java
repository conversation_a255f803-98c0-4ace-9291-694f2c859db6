package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import org.jeecg.modules.lims_core.vo.ReportRequestVO;
import org.jeecg.modules.lims_core.entity.Report;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.TaskVo;
import java.util.List;

/**
 * @Description: 报告
 * @Author: jeecg-boot
 * @Date:   2025-03-03
 * @Version: V1.0
 */
public interface IReportService extends IService<Report> {
    public IPage<ReportRequestVO> queryPageList(Page<Report> page,
                                              @Param(Constants.WRAPPER) Wrapper<ReportRequestVO> wrapper);

    public List<TaskVo> selectByMainId(String mainId);
}
