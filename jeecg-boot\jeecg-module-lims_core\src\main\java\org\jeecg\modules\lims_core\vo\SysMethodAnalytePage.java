package org.jeecg.modules.lims_core.vo;

import java.util.List;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyteRouding;
import org.jeecg.modules.lims_core.entity.SysMethodAnalytePrecision;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;

/**
 * @Description: 检测指标
 * @Author: jeecg-boot
 * @Date:   2025-03-25
 * @Version: V1.0
 */
@Data
@Schema(description="检测指标")
public class SysMethodAnalytePage {

	/**主键*/
	@Schema(description = "主键")
    private java.lang.String id;
	/**方法ID*/
	@Excel(name = "方法ID", width = 15)
	@Schema(description = "方法ID")
    private java.lang.String methodId;
	/**检测指标*/
	@Excel(name = "检测指标", width = 15, dictTable = "	sys_analyte", dicText = "name", dicCode = "id")
    @Dict(dictTable = "	sys_analyte", dicText = "name", dicCode = "id")
	@Schema(description = "检测指标")
    private java.lang.String analyteId;
	/**结果类型*/
	@Excel(name = "结果类型", width = 15, dicCode = "test_result_type")
    @Dict(dicCode = "test_result_type")
	@Schema(description = "结果类型")
    private java.lang.String resultType;
	/**检测单位*/
	@Excel(name = "检测单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Schema(description = "检测单位")
    private java.lang.String unitId;
	/**计算公式*/
	@Excel(name = "计算公式", width = 15)
	@Schema(description = "计算公式")
    private java.lang.String calcExpr;
	/**检出限*/
	@Excel(name = "检出限", width = 15)
	@Schema(description = "检出限")
    private java.lang.String lod;
	/**定量限*/
	@Excel(name = "定量限", width = 15)
	@Schema(description = "定量限")
    private java.lang.String loq;
	/**创建人*/
	@Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	
	@ExcelCollection(name="修约要求")
	@Schema(description = "修约要求")
	private List<SysMethodAnalyteRouding> sysMethodAnalyteRoudingList;
	@ExcelCollection(name="精密度要求")
	@Schema(description = "精密度要求")
	private List<SysMethodAnalytePrecision> sysMethodAnalytePrecisionList;
	
}
