package org.jeecg.modules.lims_order.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jeecg.modules.lims_core.entity.Sample;
import org.jeecg.modules.lims_order.entity.Quotation;


import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14 15:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QuotationSampleVo {
    private Quotation quotation;
    private List<Sample> samples;
}
