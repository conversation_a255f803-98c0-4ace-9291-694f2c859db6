package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.StandardMaterial;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.StandardMaterialVO;

/**
 * @Description: 标准物质台账
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
public interface StandardMaterialMapper extends BaseMapper<StandardMaterial> {
    IPage<StandardMaterialVO> queryPageList(Page<StandardMaterial> page,
                                            @Param(Constants.WRAPPER) Wrapper<StandardMaterial> wrapper);
}
