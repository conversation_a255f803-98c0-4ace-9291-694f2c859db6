package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 流程环节
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Schema(description="流程环节")
@Data
@TableName("sys_workflow_step")
public class SysWorkflowStep implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
    /**顺序*/
    @Excel(name = "顺序", width = 15)
    @Schema(description = "顺序")
    private java.lang.Integer sortNum;
	/**流程ID*/
    @Schema(description = "流程ID")
    private String workflowId;
    /**环节*/
    @Excel(name = "环节", width = 15, dicCode = "flow_step")
    @Schema(description = "环节")
    private java.lang.String name;
	/**标准工时（%）*/
	@Excel(name = "标准工时（%）", width = 15)
    @Schema(description = "标准工时（%）")
    private java.math.BigDecimal stdTaskTime;
	/**阶段*/
	@Excel(name = "阶段", width = 15, dicCode = "testing_workflow_stage")
    @Schema(description = "阶段")
    private String stage;
	/**流入条件表达式*/
	@Excel(name = "流入条件表达式", width = 15)
    @Schema(description = "流入条件表达式")
    private String expr;
    /**下一个环节*/
    @Excel(name = "下一个环节", width = 15)
    @Schema(description = "下一个环节")
    private java.lang.String nextId;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
    /**标准工时(天)*/
    @Excel(name = "标准工时(天)", width = 15)
    @Schema(description = "标准工时(天)")
    private java.lang.String stdTaskTimeExpr;
}
