package org.jeecg.modules.lims_core;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.lims_core.entity.SysProductPackage;
import org.jeecg.modules.lims_core.entity.SysStandard;
import org.jeecg.modules.lims_core.mapper.SysProductPackageMapper;
import org.jeecg.modules.lims_core.mapper.SysStandardMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/19 10:00
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {JeecgSystemApplication.class})
public class copyStandardToProductPackage {
    @Autowired
    private SysProductPackageMapper sysProductPackageMapper;
    @Autowired
    private SysStandardMapper sysStandardMapper;



    @Test
    public void test() {

        List<SysStandard> sysStandards = sysStandardMapper.selectList(new QueryWrapper<>());

        sysStandards.forEach(sysStandard -> {
            String standardName = sysStandard.getName();
            if(standardName.contains("药典") && !standardName.contains("通则")){
                if(sysProductPackageMapper.selectCount(new QueryWrapper<SysProductPackage>().eq("standard_id",sysStandard.getId())) == 0){
                    SysProductPackage sysProductPackage = new SysProductPackage();
                    sysProductPackage.setName(standardName.split(" ")[standardName.split(" ").length - 1]);
                    sysProductPackage.setStandardId(sysStandard.getId());
                    sysProductPackage.setBizTypeId("1922837690483490818");
                    sysProductPackage.setCategory("STANDARD");
                    sysProductPackage.setStdPrice(0.0);
                    sysProductPackage.setStdTat("0");
                    sysProductPackageMapper.insert(sysProductPackage);
                }
            }
        });

    }
}
