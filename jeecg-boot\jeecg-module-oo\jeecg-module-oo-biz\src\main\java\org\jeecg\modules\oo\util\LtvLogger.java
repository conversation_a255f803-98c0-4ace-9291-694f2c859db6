package org.jeecg.modules.oo.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * LTV处理日志工具类
 * 提供结构化的日志记录功能
 */
public class LtvLogger {
    
    private static final Logger logger = LoggerFactory.getLogger("LTV_PROCESSING");
    
    // MDC键常量
    private static final String SIGNATURE_ID = "signatureId";
    private static final String CERTIFICATE_CN = "certificateCN";
    private static final String OPERATION = "operation";
    
    /**
     * 设置日志上下文
     */
    public static void setContext(String signatureId, String certificateCN, String operation) {
        MDC.put(SIGNATURE_ID, signatureId);
        MDC.put(CERTIFICATE_CN, certificateCN);
        MDC.put(OPERATION, operation);
    }
    
    /**
     * 清除日志上下文
     */
    public static void clearContext() {
        MDC.clear();
    }
    
    /**
     * 记录OCSP处理信息
     */
    public static void logOcspProcessing(String certificateCN, String ocspUrl, int responseSize, boolean success) {
        if (success) {
            logger.info("OCSP处理成功 - 证书: {}, OCSP URL: {}, 响应大小: {} bytes", 
                certificateCN, ocspUrl, responseSize);
        } else {
            logger.warn("OCSP处理失败 - 证书: {}, OCSP URL: {}", certificateCN, ocspUrl);
        }
    }
    
    /**
     * 记录CRL处理信息
     */
    public static void logCrlProcessing(String certificateCN, String crlUrl, int crlCount, boolean success) {
        if (success) {
            logger.info("CRL处理成功 - 证书: {}, CRL URL: {}, CRL数量: {}", 
                certificateCN, crlUrl, crlCount);
        } else {
            logger.warn("CRL处理失败 - 证书: {}, CRL URL: {}", certificateCN, crlUrl);
        }
    }
    
    /**
     * 记录DSS构建信息
     */
    public static void logDssConstruction(int ocspCount, int crlCount, int certCount, int vriCount) {
        logger.info("DSS字典构建完成 - OCSP: {}, CRL: {}, 证书: {}, VRI条目: {}", 
            ocspCount, crlCount, certCount, vriCount);
    }
    
    /**
     * 记录VRI条目创建信息
     */
    public static void logVriCreation(String signatureId, int ocspRefs, int crlRefs, int certRefs) {
        logger.info("VRI条目创建 - 签名ID: {}, OCSP引用: {}, CRL引用: {}, 证书引用: {}", 
            signatureId, ocspRefs, crlRefs, certRefs);
    }
    
    /**
     * 记录时间戳处理信息
     */
    public static void logTimestampProcessing(String tsaUrl, boolean success, String errorMessage) {
        if (success) {
            logger.info("时间戳处理成功 - TSA URL: {}", tsaUrl);
        } else {
            logger.error("时间戳处理失败 - TSA URL: {}, 错误: {}", tsaUrl, errorMessage);
        }
    }
    
    /**
     * 记录LTV验证结果
     */
    public static void logLtvVerificationResult(String signatureId, boolean ltvEnabled, String details) {
        if (ltvEnabled) {
            logger.info("LTV验证启用成功 - 签名ID: {}, 详情: {}", signatureId, details);
        } else {
            logger.warn("LTV验证启用失败 - 签名ID: {}, 详情: {}", signatureId, details);
        }
    }
    
    /**
     * 记录性能指标
     */
    public static void logPerformanceMetrics(String operation, long durationMs, int dataSize) {
        logger.info("性能指标 - 操作: {}, 耗时: {}ms, 数据大小: {} bytes", 
            operation, durationMs, dataSize);
    }
}