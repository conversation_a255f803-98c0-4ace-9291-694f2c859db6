package org.jeecg.modules.oo.controller.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@Builder
@ToString(of = {"voClass"})
@EqualsAndHashCode(of = {"voClass"})
public class TemplateNode {
    private Class<?> voClass;                  // VO类
    private String entityName;                 // 实体名称
    private String parentEntity;               // 父实体名称（来自drillUp）
    private String parentField;                // 父实体关联字段
    private TemplateNode parentNode;
    private Map<String, String> fieldMapping;  // VO字段名到实体字段名的映射
    private Map<String, DictInfo> dictMappings;// 字典表映射
    private Map<String, String> drillChains;   // 字段的钻取链
    private Object instance;                   // 实例对象
    private List<TemplateNode> children;       // 子节点
    private String description;                // 描述
    private String whereSel;                  // 查询条件
    private Map<String, String> funcs;         // 方法
}