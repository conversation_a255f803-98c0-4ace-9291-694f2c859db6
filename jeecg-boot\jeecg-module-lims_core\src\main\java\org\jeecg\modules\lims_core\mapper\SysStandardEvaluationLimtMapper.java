package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.jeecg.modules.lims_core.entity.SysStandard;
import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 标准指标评定要求
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
public interface SysStandardEvaluationLimtMapper extends BaseMapper<SysStandardEvaluationLimt> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<SysStandardEvaluationLimt>
   */
	public List<SysStandardEvaluationLimt> selectByMainId(@Param("mainId") String mainId);

	public List<SysStandard> selectByTestId(@Param("Id") String Id);
}
