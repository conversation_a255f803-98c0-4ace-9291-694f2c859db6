package org.jeecg.modules.lims_order.service.impl;

import org.jeecg.modules.lims_order.entity.Opportunity;
import org.jeecg.modules.lims_order.mapper.OpportunityMapper;
import org.jeecg.modules.lims_order.service.IOpportunityService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 商机
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Service
public class OpportunityServiceImpl extends ServiceImpl<OpportunityMapper, Opportunity> implements IOpportunityService {

}
