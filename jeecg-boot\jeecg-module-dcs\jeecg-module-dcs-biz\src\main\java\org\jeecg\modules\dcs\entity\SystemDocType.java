package org.jeecg.modules.dcs.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: system_doc_type
 * @Author: jeecg-boot
 * @Date:   2024-11-22
 * @Version: V1.0
 */
@Data
@TableName("system_doc_type")
@Schema(description="system_doc_type")
public class SystemDocType implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "ID")
    private java.lang.String id;
	/**类型编码*/
	@Excel(name = "类型编码", width = 15)
    @Schema(description = "类型编码")
    private java.lang.String typeCode;
	/**类型名称*/
	@Excel(name = "类型名称", width = 15)
    @Schema(description = "类型名称")
    private java.lang.String typeName;
	/**审批流*/
    @Excel(name = "审批流", width = 15, dicCode = "qm_template")
    @Dict(dicCode = "qm_template")
    @Schema(description = "审批流")
    private java.lang.String workflowId;
	/**父类型ID*/
	@Excel(name = "父类型ID", width = 15)
    @Schema(description = "父类型ID")
    private java.lang.String parentId;
	/**当前流文件数*/
	@Excel(name = "当前流文件数", width = 15)
    @Schema(description = "当前流文件数")
    private java.lang.Integer currentSn;
    /**编号规则*/
	@Excel(name = "编号规则", width = 15)
    @Schema(description = "编号规则")
    private java.lang.String docNoRule;
	/**创建者*/
    @Schema(description = "创建者")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private java.util.Date createTime;
	/**更新者*/
    @Schema(description = "更新者")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private java.util.Date updateTime;
	/**是否有子节点*/
	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
    @Schema(description = "是否有子节点")
    private java.lang.String hasChild;
    /**复审频次*/
    @Excel(name = "复审频次", width = 15, dicCode = "review_frequency")
    @Dict(dicCode = "review_frequency")
    @Schema(description = "复审频次")
    private java.lang.String reviewFrequencyId;
}