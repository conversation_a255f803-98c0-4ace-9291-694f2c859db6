package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.SysInstrumentTypeSettings;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 数据采集设置
 * @Author: jeecg-boot
 * @Date:   2025-04-28
 * @Version: V1.0
 */
public interface SysInstrumentTypeSettingsMapper extends BaseMapper<SysInstrumentTypeSettings> {

}
