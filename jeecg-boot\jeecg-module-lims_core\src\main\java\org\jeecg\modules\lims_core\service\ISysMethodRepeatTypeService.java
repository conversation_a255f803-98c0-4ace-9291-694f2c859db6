package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodRepeatType;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 重复性
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodRepeatTypeService extends IService<SysMethodRepeatType> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysMethodRepeatType>
	 */
	public List<SysMethodRepeatType> selectByMainId(String mainId);
}
