server:
  port: 48088
  undertow:
    # 平替 tomcat server.tomcat.max-swallow-siz， undertow该值默认为-1
    #    max-http-post-size: 10MB
    threads:
      io: 16 # 4核CPU标准配置
      worker: 256
    buffer-size: 8192 # 以字节为单位，这里设置为8 KB
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /jeecg-boot
    encoding:
      charset: UTF-8
      enabled: true
      force-request: true
      force-response: true
      force: true
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    web:
      exposure:
        include: metrics,httpexchanges,httptrace-new

spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      resolve-lazily: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
    template_update_delay: 0
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
#  autoconfigure:
#    exclude:
#      - com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure
#      - org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration
  #redis 配置
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      password:
#mybatis plus 设置
#jeecg专用配置
#minidao:
#  base-package: org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
jeecg:
  # 平台上线安全配置
  firewall:
    # 数据源安全 (开启后，Online报表和图表的数据源为必填)
    dataSourceSafe: false
    # 低代码模式（dev:开发模式，prod:发布模式——关闭所有在线开发配置能力）
    lowCodeMode: dev
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  #签名拦截接口
  signUrls: /sys/dict/getDictItems/*,/sys/dict/loadDict/*,/sys/dict/loadDictOrderByValue/*,/sys/dict/loadDictItem/*,/sys/dict/loadTreeData,/sys/api/queryTableDictItemsByCode,/sys/api/queryFilterTableDictInfo,/sys/api/queryTableDictByKeys,/sys/api/translateDictFromTable,/sys/api/translateDictFromTableByKeys,/sys/sendChangePwdSms,/sys/user/sendChangePhoneSms,/sys/sms,/desform/api/sendVerifyCode
  # 本地：local、Minio：minio、阿里云：alioss
  uploadType: minio
  # 前端访问地址
  domainUrl:
    pc: http://localhost:3380
    app: http://localhost:8051
  path:
    #文件上传根目录 设置
    upload: /opt/upFiles
    #webapp文件路径
    webapp: /opt/webapp
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: CEgpzwiGCDC2OlJaVeEX
    secretKey: xGyUJmpmuSMuEliIYfnPutO97XrXWMa2VcFwAUq7
    endpoint: https://oss.guobiaotest.com:9000
    bucketName: gbt
  # ElasticSearch 6设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: false
  # 在线预览文件服务器地址配置
  file-view-domain: http://fileview.jeecg.com
  # minio文件上传
  minio:
    minio_url: https://oss.guobiaotest.com:9000
    minio_name: anders
    minio_pass: Gbt@2024
    bucketName: gbt
  #大屏报表参数设置
  jmreport:
    #多租户模式，默认值为空(created:按照创建人隔离、tenant:按照租户隔离) (v1.6.2+ 新增)
    saasMode: 
    # 平台上线安全配置(v1.6.2+ 新增)
    firewall:
      # 数据源安全 (开启后，不允许使用平台数据源、SQL解析加签并且不允许查询数据库)
      dataSourceSafe: false
      # 低代码开发模式（dev:开发模式，prod:发布模式—关闭在线报表设计功能，分配角色admin、lowdeveloper可以放开限制）
      lowCodeMode: dev
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
  # ai-chat
  ai-chat:
    # 是否开启；必须。
    enabled: false
    # openAi接口秘钥，填写自己的apiKey；必须。
    apiKey: "？？？？"
    # openAi域名，有代理就填代理的域名。默认：openAI官方apiHost
    apiHost: "https://api.openai.com"
    # 超时时间单位:s。默认 60s
    timeout: 60
    # 本地代理地址
#    proxy:
#      host: "http://127.0.0.1"
#      port: "7890"
  # 百度开放API配置
  baidu-api:
    app-id: ??
    api-key: ??
    secret-key: ??
#Mybatis输出sql日志
logging:
  level:
    org.flywaydb: debug
    org.jeecg.modules.system.mapper: info
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: false
    username: jeecg
    password: jeecg1314
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ww65e9a335aa4a0f67
      client-secret: pA5mFhjTtC9iXzc4itY60r9cjeCZpB8Sa211bXF_ujw
      redirect-uri: http://gbjc.cc:3379/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: 1000030
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h

security:
  oauth2:
    client:
      ignore-urls:
        - /test/jeecgDemo/demo3
        - /test/jeecgDemo/redisDemo/**
        - /jmreport/bigscreen2/**
# onlyoffice配置
only-office:
  secret: devops_20240521
  config:
    document:
      permissions:
        # 是否可以编辑
        edit: true
        print: false
        download: true
        # 是否可以填写表格，如果将mode参数设置为edit，则填写表单仅对文档编辑器可用。 默认值与edit或review参数的值一致。
        fillForms: false
        # 跟踪变化
        review: true
    editorConfig:
      lang: zh-CN
      coEditing:
        mode: fast,
        change: true
      # 定制化配置
      customization:
        forcesave: true
        autosave: false
        comments: true
        compactHeader: false
        compactToolbar: false
        compatibleFeatures: false
        close:
          visible: true
          text: "关闭"
        customer:
          address: 广州市黄埔区
          info: 国标检验检测文档在线平台
          logo: http://nwzimg.wezhan.cn/sitefiles10245/10245330/%E6%9C%AA%E6%A0%87%E9%A2%98-2.png
          logoDark: http://nwzimg.wezhan.cn/sitefiles10245/10245330/%E6%9C%AA%E6%A0%87%E9%A2%98-2.png
          mail: <EMAIL>
          name: 国标检验检测文档在线平台
          phone: 123456789
          www: www.guobiaotest.com
        features:
          # 是否开启拼写检查
          spellcheck:
            mode: false
            change: false
      region: zh-CN
    type: desktop
wechat:
  cp:
    appConfigs:
      - agentId: 1000030
        corpId: ww65e9a335aa4a0f67
        secret: pA5mFhjTtC9iXzc4itY60r9cjeCZpB8Sa211bXF_ujw
        token: KsMBHPb7WqvbwNOCgd33N
        aesKey: LJpY1l0DtXsSCNtuWBTtQujv9EAq7OP8lfJOXULMoiw
      - agentId: 1000031
        corpId: ww65e9a335aa4a0f67
        secret: emc-hXCKbOoyhCjuh-AArW6eHRUpJ2_R62NEgnY5QH0
        token: KsMBHPb7WqvbwNOCgd33N
        aesKey: LJpY1l0DtXsSCNtuWBTtQujv9EAq7OP8lfJOXULMoiw
gitlab:
  api:
    token: **************************
    url: http://gitlab.guobiaotest.com:9080/api/v4