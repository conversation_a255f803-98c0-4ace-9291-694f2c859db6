package org.jeecg.modules.lims_core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.aspectj.weaver.ast.Var;
import org.jeecg.common.util.SqlInjectionUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.ISysTemplateService;
import org.jeecg.modules.lims_core.service.ISysTranslationService;
import org.jeecg.modules.lims_core.service.ITestResultService;
import org.jeecg.modules.lims_core.util.TranslateUtil;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Objects;

/**
 * @Description: 检测结果
 * @Author: jeecg-boot
 * @Date:   2025-02-08
 * @Version: V1.0
 */
@Service
public class TestResultServiceImpl extends ServiceImpl<TestResultMapper, TestResult> implements ITestResultService {

    @Autowired
    private SysStandardEvaluationLimtMapper sysStandardEvaluationLimtMapper;
    @Autowired
    private SysUnitMapper sysUnitMapper;
    @Autowired
    private SysMethodAnalyteMapper sysMethodAnalyteMapper;
    @Autowired
    private SysAnalyteMapper sysAnalyteMapper;
    @Autowired
    private TestResultMapper testResultMapper;
    @Autowired
    private ISysTranslationService sysTranslationService;
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SysTranslationMapper sysTranslationMapper;
    @Autowired
    private ISysTemplateService sysTemplateService;
    @Autowired
    private TestTaskMapper testTaskMapper;

    @Autowired
    private ISysDictItemService sysDictItemService;

    public void saveTestResult(JSONObject jsonObject) {
        TestResult testResult = new TestResult();
        testResult.setTestId(jsonObject.getString("testId"));
        testResult.setMethodAnalyteId(jsonObject.getString("methodAnalyteId"));
        testResult.setLimitId(jsonObject.getString("limitId"));
        this.save(testResult);
    }


    @Override
    public List<TestResult> selectByTestId(String testId) {
        return baseMapper.selectByTestId(testId);
    }


    public String getRepUnit(String id){
        TestResult tr = this.getById(id);
        if (tr.getRepResult().equals("未检出")){
            return "";
        }
        if(tr.getLimitId() != null){
            SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtMapper.selectById(tr.getLimitId());
            if (sysStandardEvaluationLimt.getUnitId() != null) {
                SysUnit sysUnit = sysUnitMapper.selectById(sysStandardEvaluationLimt.getUnitId());
                return sysUnit.getUnitName().replace("<无>", "");
            }
        }else{
            SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(tr.getMethodAnalyteId());
            if (sysMethodAnalyte.getUnitId() != null) {
                SysUnit sysUnit = sysUnitMapper.selectById(sysMethodAnalyte.getUnitId());
                return sysUnit.getUnitName().replace("<无>", "");
            }
        }

        return "";
    }

    public String getLimit(String id){
        TestResult tr = this.getById(id);
        String limit = "";
        if(tr.getLimitId() != null){
            SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtMapper.selectById(tr.getLimitId());
            if (sysStandardEvaluationLimt.getElimit() != null) {
                String elimit = sysStandardEvaluationLimt.getElimit();
                String unitName = "";
                if(sysStandardEvaluationLimt.getUnitId()!=null && !sysStandardEvaluationLimt.getUnitId().equals("")){
                    SysUnit sysUnit = sysUnitMapper.selectById(sysStandardEvaluationLimt.getUnitId());
                    unitName=sysUnit.getUnitName().replace("<无>", "");
                }
                if(elimit.startsWith("≥")){
                    if(unitName!=null && !unitName.equals("")){
                        limit="不得少于"+ elimit.substring(1)+unitName;
                        if(isNeedTranslation(id)){
                            limit=limit+getanalytetranslation(id,"elimit");
                        }
                        return  limit;
                    }else {
                        limit="不得少于"+ elimit.substring(1);
                        if(isNeedTranslation(id)){
                            limit=limit+getanalytetranslation(id,"elimit");
                        }
                        return  limit;
                    }
                }else if(elimit.startsWith("≤")){
                    if(unitName!=null && !unitName.equals("")){
                        limit="不得过"+ elimit.substring(1)+unitName;
                        if(isNeedTranslation(id)){
                            limit=limit+getanalytetranslation(id,"elimit");
                        }
                        return  limit;
                    }else {
                        limit="不得过"+ elimit.substring(1);
                        if(isNeedTranslation(id)){
                            limit=limit+getanalytetranslation(id,"elimit");
                        }
                        return  limit;
                    }
                }
                if(unitName!=null && !unitName.equals("")){
                    limit=elimit+unitName;
                    if(isNeedTranslation(id)){
                        limit=limit+getanalytetranslation(id,"elimit");
                    }
                    return limit;
                }
                if(isNeedTranslation(id)){
                    elimit=elimit+getanalytetranslation(id,"elimit");
                }
                return elimit;
            }
        }

        return "";
    }

    public String getReportName(String id){
        TestResult tr = this.getById(id);
        String reportName = "";

        SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(tr.getMethodAnalyteId());
        if (sysMethodAnalyte != null) {
            SysAnalyte sysAnalyte = sysAnalyteMapper.selectById(sysMethodAnalyte.getAnalyteId());
            if (sysAnalyte != null) {
                reportName = sysAnalyte.getName() + "：";
            }
        }

        if(tr.getLimitId() != null){
            SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtMapper.selectById(tr.getLimitId());
            if (sysStandardEvaluationLimt.getReportName() != null && !sysStandardEvaluationLimt.getReportName().equals("")) {
                reportName = sysStandardEvaluationLimt.getReportName() + "：";
            }
        }


        if(reportName.contains("性状") || reportName.contains("性狀"))
            return "";
        return reportName;
    }

    public String getAnalytelodloq(String id){
        TestResult testResult = testResultMapper.selectById(id);
        SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(testResult.getMethodAnalyteId());
        SysUnit sysUnit = sysUnitMapper.selectById(sysMethodAnalyte.getUnitId());
        if(sysMethodAnalyte.getLod() != null && sysMethodAnalyte.getLoq() == null){//只有检出限
            if(testResult.getRepResult().equals("未检出") || testResult.getRepResult().equals("未檢出")){
                if (testResult.getRepResult().equals("未检出")){
                    return "检出限："+sysMethodAnalyte.getLod()+sysUnit.getUnitName();
                }
            }
        }else if(sysMethodAnalyte.getLod() == null && sysMethodAnalyte.getLoq() != null){//只有定量限
            if(testResult.getRepResult().equals("未检出") || testResult.getRepResult().equals("未檢出") || testResult.getRepResult().equals("<" + sysMethodAnalyte.getLoq())){
                if (testResult.getRepResult().equals("未检出")){
                    return "定量限："+sysMethodAnalyte.getLoq()+sysUnit.getUnitName();
                }else if (testResult.getRepResult().equals("<" + sysMethodAnalyte.getLoq())){
                    return "定量限："+sysMethodAnalyte.getLoq()+sysUnit.getUnitName();
                }
            }
        }else if(sysMethodAnalyte.getLod() != null && sysMethodAnalyte.getLoq() != null){//既有检出限、又有定量限
            if(testResult.getRepResult().equals("未检出") || testResult.getRepResult().equals("未檢出") || testResult.getRepResult().equals("<" + sysMethodAnalyte.getLoq())){
                if (testResult.getRepResult().equals("未检出")){
                    return "定量限："+sysMethodAnalyte.getLoq()+sysUnit.getUnitName();
                }else if (testResult.getRepResult().equals("<" + sysMethodAnalyte.getLoq())){
                    return "定量限："+sysMethodAnalyte.getLoq()+sysUnit.getUnitName();
                }
            }
        }
        return "/";
    }

    public String getAnalyteName(String id){
        String analyteName = null;
        TestResult tr = this.getById(id);
        SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(tr.getMethodAnalyteId());
        if(tr.getLimitId()== null){
            SysAnalyte sysAnalyte = sysAnalyteMapper.selectById(sysMethodAnalyte.getAnalyteId());
            analyteName = sysAnalyte.getName();
        }else{
            SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtMapper.selectById(tr.getLimitId());
            if(sysStandardEvaluationLimt.getReportName() != null && !sysStandardEvaluationLimt.getReportName().isEmpty()){
                analyteName = sysStandardEvaluationLimt.getReportName();
            }else{
                SysAnalyte sysAnalyte = sysAnalyteMapper.selectById(sysMethodAnalyte.getAnalyteId());
                analyteName = sysAnalyte.getName();
            }
        }
        if(isNeedTranslation(id)){
            analyteName=analyteName+getanalytetranslation(id,"analyte");
        }
        return analyteName;
    }

    public String getRepResult(String id){
        String obj = null;
        TestResult tr = this.getById(id);
        obj=tr.getRepResult();
        if(isNeedTranslation(id)){
            obj=obj+getanalytetranslation(id,"RepResult");
        }
        return obj;
    };
    public String getConclusion(String id){
        String obj = null;
        TestResult tr = this.getById(id);
        obj=tr.getConclusion();
        if(isNeedTranslation(id)){
            obj=obj+getanalytetranslation(id,"Conclusion");
        }
        return obj;
    };

    public boolean isNeedTranslation(String id){
        boolean str=false;
        TestResult tr = this.getById(id);
        Test test = testMapper.selectById(tr.getTestId());
        TestTask testTask = testTaskMapper.selectById(test.getTaskId());
        QueryWrapper<Report> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("test_task_ids", testTask.getId());
        Report report = reportMapper.selectOne(queryWrapper);
        if (report == null) {
            return str;
        }
        SysTemplate sysTemplate = sysTemplateService.getById(report.getTemplateId());
        if (sysTemplate != null) {
            if (sysTemplate.getReportLanguage().equals("zh-en")) {
                str = true;
            }
        }
        return str;
    }
    public String getanalytetranslation(String id,String type){
        String analyteName = "";
        TestResult tr = this.getById(id);
        SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(tr.getMethodAnalyteId());
        SysAnalyte sysAnalyte = sysAnalyteMapper.selectById(sysMethodAnalyte.getAnalyteId());
        SysStandardEvaluationLimt sysStandardEvaluationLimt = sysStandardEvaluationLimtMapper.selectById(tr.getLimitId());
        if(sysStandardEvaluationLimt.getReportName() != null && !sysStandardEvaluationLimt.getReportName().isEmpty()){
            analyteName = sysStandardEvaluationLimt.getReportName();
        }else if(type.equals("elimit")){
            SysTranslation Translation = sysTranslationService.getOne(new QueryWrapper<SysTranslation>()
                    .eq("source_id", sysStandardEvaluationLimt.getId())
                    .eq("context", "sys_standard_evaluation_limt")
                    .eq("field_name", "elimit"));
            if (Translation == null){
                return analyteName;
            }
            analyteName="\n" + Translation.getTranslation();
        }else if(type.equals("analyte")){
            SysTranslation Translation = sysTranslationService.getOne(new QueryWrapper<SysTranslation>()
                    .eq("source_id", sysAnalyte.getId())
                    .eq("context", "sys_analyte")
                    .eq("field_name", "name"));
            if (Translation == null){
                return analyteName;
            }
            analyteName="\n" +Translation.getTranslation();
        }else if(type.equals("RepResult")){
            SysTranslation Translation = sysTranslationService.getOne(new QueryWrapper<SysTranslation>()
                    .eq("source_id", tr.getId())
                    .eq("context", "test_result")
                    .eq("field_name", "rep_result"));
            if (Translation == null){
                return analyteName;
            }
            analyteName="\n" +Translation.getTranslation();
        }else if(type.equals("Conclusion")){
            String source_id = tr.getId();
            List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("conclusion_type");
            for (SysDictItem item : sysDictItems) {
                if (tr.getConclusion().equals(item.getItemValue())) {
                    source_id = item.getId();
                    break;
                }
            }
            SysTranslation Translation = sysTranslationService.getOne(new QueryWrapper<SysTranslation>()
                    .eq("source_id", source_id)
                    .eq("context", "sys_dict_item")
                    .eq("field_name", "item_value"));
            if (Translation == null){
                return analyteName;
            }
            analyteName="\n " +Translation.getTranslation();
        }
        return analyteName;
    }
}
