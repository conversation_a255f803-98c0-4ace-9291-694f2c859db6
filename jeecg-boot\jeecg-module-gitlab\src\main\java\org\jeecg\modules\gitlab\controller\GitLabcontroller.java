package org.jeecg.modules.gitlab.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.gitlab.dto.User;
import org.jeecg.modules.gitlab.dto.issue.IssueHook;
import org.jeecg.modules.gitlab.dto.job.JobHook;
import org.jeecg.modules.gitlab.dto.mergerequest.MergeRequestHook;
import org.jeecg.modules.gitlab.dto.note.NoteHook;
import org.jeecg.modules.gitlab.dto.pipeline.PipelineHook;
import org.jeecg.modules.gitlab.dto.push.PushHook;
import org.jeecg.modules.gitlab.dto.release.ReleaseHook;
import org.jeecg.modules.gitlab.dto.tag.TagPushHook;
import org.jeecg.modules.gitlab.entity.GitlabSchedule;
import org.jeecg.modules.gitlab.mapper.********************;
import org.jeecg.modules.gitlab.message.GitLabWebHookMessage;
import org.jeecg.modules.gitlab.service.**********************;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 15:31
 */
@Tag(name="GitLab相关")
@RestController
@RequestMapping("/gitlab")
@Slf4j
public class GitLabcontroller {
    private static final String PUSH_HOOK = "Push Hook";
    private static final String PIPELINE_HOOK = "Pipeline Hook";
    private static final String MERGE_REQUEST_HOOK = "Merge Request Hook";
    private static final String ISSUE_HOOK = "Issue Hook";
    private static final String RELEASE_HOOK = "Release Hook";
    private static final String NOTE_HOOK = "Note Hook";
    private static final String JOB_HOOK = "Job Hook";
    private static final String TAG_PUSH_HOOK = "Tag Push Hook";
    private static final String ACTION_UPDATE = "update";

    @Autowired
    private ********************** *********************;

    @Value("${gitlab.api.token}")
    private String gitlabApiToken;

    @Value("${gitlab.api.url}")
    private String gitlabApiUrl;

    @PostMapping("/webhook")
    public Result<String> handleWebhook(@RequestBody Map<String, Object> body, HttpServletRequest request) throws WxErrorException, JsonProcessingException {
        String event = request.getHeader("X-Gitlab-Event");
        log.info("Received GitLab event: {}", event);
        log.info("Payload: {}", body);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String toUser = "";
        List<User> issueParticipants = null;
        // 根据不同的事件类型进行处理
        switch (event) {
            case PUSH_HOOK:
                PushHook pushHook = objectMapper.convertValue(body, PushHook.class);
                if (!ObjectUtils.isEmpty(pushHook.getCommits())) {
                    System.out.println(pushHook);
                    GitLabWebHookMessage.sendMsg("xiaoxuebin|wangjiujun|wuxianzheng", GitLabWebHookMessage.pushHookMessage(pushHook));
                }
                break;
            case PIPELINE_HOOK:
                PipelineHook pipelineHook = objectMapper.convertValue(body, PipelineHook.class);
                PipelineHook.ObjectAttributes objectAttributes = pipelineHook.getObjectAttributes();
                if (objectAttributes != null && !"pending".equals(objectAttributes.getStatus())) {
                    System.out.println(pipelineHook);
                    GitLabWebHookMessage.sendMsg("xiaoxuebin|wangjiujun|wuxianzheng", GitLabWebHookMessage.pipelineHookMessage(pipelineHook));
                }
                break;
            case MERGE_REQUEST_HOOK:
                MergeRequestHook mergeRequestHook = objectMapper.convertValue(body, MergeRequestHook.class);
                String action = mergeRequestHook.getObjectAttributes().getAction();
                if (action != null && !ACTION_UPDATE.equals(action)) {
                    System.out.println(mergeRequestHook);
                    GitLabWebHookMessage.sendMsg("xiaoxuebin|wangjiujun|wuxianzheng", GitLabWebHookMessage.mergeRequestHookMessage(mergeRequestHook));
                }
                break;
            case ISSUE_HOOK:
                IssueHook issueHook = objectMapper.convertValue(body, IssueHook.class);
                String issueAction = issueHook.getObjectAttributes().getAction();
                 issueParticipants = getIssueParticipants(issueHook.getProject().getId(), issueHook.getObjectAttributes().getIid());
                for (User user : issueParticipants) {
                    toUser += user.getUsername() + "|";
                }

                GitLabWebHookMessage.sendMsg(toUser, GitLabWebHookMessage.issueHookMessage(issueHook));

                QueryWrapper<GitlabSchedule> queryWrapper = QueryGenerator.initQueryWrapper(new GitlabSchedule(), new HashMap<>());
                queryWrapper.eq("issue_id", issueHook.getObjectAttributes().getIid());
                queryWrapper.eq("project_id", issueHook.getProject().getId());
                Page<GitlabSchedule> page = new Page<GitlabSchedule>(1, 10);
                IPage<GitlabSchedule> pageList = *********************.page(page, queryWrapper);
                if(pageList.getRecords().size()>0 && issueHook.getObjectAttributes().getDueDate() == null){
                    GitlabSchedule gitlabSchedule = pageList.getRecords().get(0);
                    GitLabWebHookMessage.deleteSchedule(gitlabSchedule.getCalId());
                    *********************.removeById(gitlabSchedule.getId());
                }
                // 如果issue的due_date不为空,则将issue的due_date更新到日程表中
                if(issueHook.getObjectAttributes().getDueDate() != null){
                    if(pageList.getRecords().size() == 0){
                        GitlabSchedule gitlabSchedule = new GitlabSchedule();
                        BeanUtils.copyProperties(issueHook.getObjectAttributes(), gitlabSchedule);
                        gitlabSchedule.setIssueId(issueHook.getObjectAttributes().getIid().toString());
                        gitlabSchedule.setProjectId(issueHook.getProject().getId().toString());
                        gitlabSchedule.setDueDate(issueHook.getObjectAttributes().getDueDate());
                        gitlabSchedule.setCalId(GitLabWebHookMessage.raiseschedule(issueHook,issueParticipants));
                        *********************.save(gitlabSchedule);
                    }else{
                        GitlabSchedule gitlabSchedule = pageList.getRecords().get(0);
                        gitlabSchedule.setDueDate(issueHook.getObjectAttributes().getDueDate());
                        GitLabWebHookMessage.updateSchedule(gitlabSchedule.getCalId(),issueHook,issueParticipants);
                        *********************.updateById(gitlabSchedule);
                    }
                }
                break;
            case RELEASE_HOOK:
                ReleaseHook releaseHook = objectMapper.convertValue(body, ReleaseHook.class);
                String releaseAction = releaseHook.getAction();
                if (!ACTION_UPDATE.equals(releaseAction)) {
                    System.out.println(releaseHook);
                    GitLabWebHookMessage.sendMsg("xiaoxuebin|wangjiujun", GitLabWebHookMessage.releaseHookMessage(releaseHook));
                }
                break;
            case NOTE_HOOK:
                NoteHook noteHook = objectMapper.convertValue(body, NoteHook.class);
                issueParticipants = getIssueParticipants(noteHook.getProject().getId(), noteHook.getIssue().getIid());
                // 遍历issueParticipants1,拿到username,拼接"|"隔开

                for (User user : issueParticipants) {
                    toUser += user.getUsername() + "|";
                }
                GitLabWebHookMessage.sendMsg(toUser, GitLabWebHookMessage.noteHookMessage(noteHook));
                break;
            case TAG_PUSH_HOOK:
                TagPushHook tagPushHook = objectMapper.convertValue(body, TagPushHook.class);
                if (Objects.equals(tagPushHook.getObjectKind(), "tag_push")) {
                    System.out.println(tagPushHook);
                    GitLabWebHookMessage.sendMsg("xiaoxuebin|wangjiujun", GitLabWebHookMessage.tagPushHookMessage(tagPushHook));
                }
                break;
            case JOB_HOOK:
                JobHook jobHook = objectMapper.convertValue(body, JobHook.class);
                System.out.println(jobHook);
                GitLabWebHookMessage.sendMsg("xiaoxuebin|wangjiujun", GitLabWebHookMessage.jobHookMessage(jobHook));
                break;
            default:
                return Result.OK("成功！");

        }
        return Result.OK("成功！");
    }

    private List<User> getIssueParticipants(Long projectId, Long issueIid) {
        String url = String.format("%s/projects/%d/issues/%d/participants", gitlabApiUrl, projectId, issueIid);
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("PRIVATE-TOKEN", gitlabApiToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(response.getBody(), new TypeReference<List<User>>() {});
        } catch (Exception e) {
            log.error("Error parsing participants response", e);
            return null;
        }
    }


}
