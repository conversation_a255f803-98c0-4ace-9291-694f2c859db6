package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

import java.util.Date;

@Data
@TemplateDesigner(value = "TestInstrument",drillUp = "testId->test.id",entity = "TestInstrument",description = "仪器设备信息")
public class TestInstrumentVO {
    @TemplateField(entityFieldName = "instrumentId",drillChain="Instrument.id->Instrument.typeId->SysInstrumentType.id->SysInstrumentType.name",description = "类型")
    private String type;
    @TemplateField(entityFieldName = "instrumentId",drillChain="Instrument.id->Instrument.code",description = "编号")
    private String code;
    @TemplateField(entityFieldName = "instrumentId",drillChain="Instrument.id->Instrument.model",description = "型号/规格")
    private String model;
    @TemplateField(entityFieldName = "instrumentId", drillChain="Instrument.id->Instrument.supplierId->SysSupplier.id->SysSupplier.name", description = "厂家")
    private String supplier;
    @TemplateField(entityFieldName = "instrumentId", drillChain="Instrument.id",calcExpr = "last_calibration_date.plusDays(calibration_cycle)",  description = "校准有效期")
    private Date effectiveDate;
    @TemplateField(entityFieldName = "testId",drillChain="test.id->test.method_id->sys_method_instrument_type.method_id->sys_method_instrument_type.remark",description = "备注")
    private String Remark;
}