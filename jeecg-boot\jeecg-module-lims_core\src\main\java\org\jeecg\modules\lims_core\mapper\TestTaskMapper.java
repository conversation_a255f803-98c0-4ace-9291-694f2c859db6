package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.lims_core.entity.TestTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.SampleQuery;
import org.jeecg.modules.lims_core.vo.TaskVo;

/**
 * @Description: 测试任务
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
public interface TestTaskMapper extends BaseMapper<TestTask> {

    @Select("SELECT * FROM test_task WHERE sample_id = #{sampleId}")
    List<TestTask> selectBySampleId(@Param("sampleId") String sampleId);

    @Select("SELECT * FROM test_task WHERE sample_id = #{sampleId} and method_id = #{methodId}")
    List<TestTask> selectBySampleIdAndMethodId(@Param("sampleId") String sampleId,@Param("methodId") String methodId);

    IPage<TaskVo> queryPageList1(Page<TaskVo> page,
                                   @Param(Constants.WRAPPER) Wrapper<TaskVo> wrapper);

    IPage<SampleQuery> sampleQuery(Page<SampleQuery> page,
                                   @Param(Constants.WRAPPER) Wrapper<SampleQuery> wrapper);

    IPage<TaskVo> myTasklist(Page<TaskVo> page,
                                   @Param(Constants.WRAPPER) Wrapper<TaskVo> wrapper);

    List<TaskVo> selectByMainId(@Param("Id") String Id);

    List<TaskVo> selectByConsumptiveName(@Param("Name") String Name);
    List<TaskVo> selectByStandardMaterialName(@Param("Name") String Name);
    List<String> selectHistoryOperators(@Param("sampleName") String sampleName,
                                        @Param("testCapability") String testCapability);
}
