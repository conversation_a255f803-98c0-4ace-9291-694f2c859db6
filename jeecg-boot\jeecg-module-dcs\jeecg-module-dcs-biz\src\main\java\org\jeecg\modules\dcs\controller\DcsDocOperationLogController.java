package org.jeecg.modules.dcs.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.dcs.entity.DcsDocOperationLog;
import org.jeecg.modules.dcs.service.IDcsDocOperationLogService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 文控系统文件操作记录
 * @Author: jeecg-boot
 * @Date:   2024-12-05
 * @Version: V1.0
 */
@Tag(name="文控系统文件操作记录")
@RestController
@RequestMapping("/dcs/dcsDocOperationLog")
@Slf4j
public class DcsDocOperationLogController extends JeecgController<DcsDocOperationLog, IDcsDocOperationLogService> {
	@Autowired
	private IDcsDocOperationLogService dcsDocOperationLogService;
	
	/**
	 * 分页列表查询
	 *
	 * @param dcsDocOperationLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "文控系统文件操作记录-分页列表查询")
	@Operation(summary="文控系统文件操作记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<DcsDocOperationLog>> queryPageList(DcsDocOperationLog dcsDocOperationLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<DcsDocOperationLog> queryWrapper = QueryGenerator.initQueryWrapper(dcsDocOperationLog, req.getParameterMap());
		Page<DcsDocOperationLog> page = new Page<DcsDocOperationLog>(pageNo, pageSize);
		IPage<DcsDocOperationLog> pageList = dcsDocOperationLogService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param dcsDocOperationLog
	 * @return
	 */
	@AutoLog(value = "文控系统文件操作记录-添加")
	@Operation(summary="文控系统文件操作记录-添加")
	@PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc_operation_log:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody DcsDocOperationLog dcsDocOperationLog) {
		dcsDocOperationLogService.save(dcsDocOperationLog);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param dcsDocOperationLog
	 * @return
	 */
	@AutoLog(value = "文控系统文件操作记录-编辑")
	@Operation(summary="文控系统文件操作记录-编辑")
    @PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc_operation_log:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody DcsDocOperationLog dcsDocOperationLog) {
		dcsDocOperationLogService.updateById(dcsDocOperationLog);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "文控系统文件操作记录-通过id删除")
	@Operation(summary="文控系统文件操作记录-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc_operation_log:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		dcsDocOperationLogService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "文控系统文件操作记录-批量删除")
	@Operation(summary="文控系统文件操作记录-批量删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc_operation_log:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.dcsDocOperationLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "文控系统文件操作记录-通过id查询")
	@Operation(summary="文控系统文件操作记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<DcsDocOperationLog> queryById(@RequestParam(name="id",required=true) String id) {
		DcsDocOperationLog dcsDocOperationLog = dcsDocOperationLogService.getById(id);
		if(dcsDocOperationLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(dcsDocOperationLog);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param dcsDocOperationLog
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc_operation_log:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DcsDocOperationLog dcsDocOperationLog) {
        return super.exportXls(request, dcsDocOperationLog, DcsDocOperationLog.class, "文控系统文件操作记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:dcs_doc_operation_log:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DcsDocOperationLog.class);
    }

	 /**
	  * 通过id查询
	  *
	  * @param docId
	  * @return
	  */
	 //@AutoLog(value = "文控系统文件操作记录-通过id查询")
	 @Operation(summary="文控系统文件操作记录-通过id查询")
	 @GetMapping(value = "/queryByDocId")
	 public Result<IPage<DcsDocOperationLog>> queryByDocId(@RequestParam(name="docId",required=true) String docId,
	 								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								       @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		 QueryWrapper<DcsDocOperationLog> wrapper = new QueryWrapper<>();
		 wrapper.eq("doc_id",docId);
		 Page<DcsDocOperationLog> page = new Page<DcsDocOperationLog>(pageNo, pageSize);
		 IPage<DcsDocOperationLog> pageList = dcsDocOperationLogService.page(page, wrapper);
		 return Result.OK(pageList);
	 }
}
