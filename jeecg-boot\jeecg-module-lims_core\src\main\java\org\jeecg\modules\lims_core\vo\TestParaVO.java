package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "TestPara",drillUp = "test_id->test.id",description = "实验过程参数信息")
public class TestParaVO {
    @TemplateField(entityFieldName = "para_id",drillChain = "sys_method_testing_para.id->sys_method_testing_para.type_id",dict = "method_testing_para_type",description = "参数类型")
    private String paraType;
    @TemplateField(entityFieldName = "para_id",dictTable = "sys_method_testing_para",dictKey = "id",dictText = "name",description = "参数名称")
    private String paraName;
    @TemplateField(description = "参数值")
    private String paraValue;
}