package org.jeecg.modules.dcs.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletRequest;
import org.jeecg.modules.dcs.dto.DcsDocWithPermission;
import org.jeecg.modules.dcs.entity.DcsDoc;

/**
 * @Description: dcs_doc
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
public interface IDcsDocService extends IService<DcsDoc> {
    IPage<DcsDocWithPermission> queryPageWithPermission(DcsDoc dcsDoc,String deptId, HttpServletRequest req, int pageNo, int pageSize);

    void apply(String docId, String applyTypeId, String description) throws Exception;
}