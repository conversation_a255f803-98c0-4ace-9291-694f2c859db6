package org.jeecg.modules.lims_core.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysWarehouseBox;
import org.jeecg.modules.lims_core.entity.SysWarehouse;
import org.jeecg.modules.lims_core.vo.SysWarehousePage;
import org.jeecg.modules.lims_core.service.ISysWarehouseService;
import org.jeecg.modules.lims_core.service.ISysWarehouseBoxService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;


 /**
 * @Description: 仓库
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Tag(name="仓库")
@RestController
@RequestMapping("/lims_core/sysWarehouse")
@Slf4j
public class SysWarehouseController {
	@Autowired
	private ISysWarehouseService sysWarehouseService;
	@Autowired
	private ISysWarehouseBoxService sysWarehouseBoxService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysWarehouse
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "仓库-分页列表查询")
	@Operation(summary="仓库-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysWarehouse>> queryPageList(SysWarehouse sysWarehouse,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysWarehouse> queryWrapper = QueryGenerator.initQueryWrapper(sysWarehouse, req.getParameterMap());
		Page<SysWarehouse> page = new Page<SysWarehouse>(pageNo, pageSize);
		IPage<SysWarehouse> pageList = sysWarehouseService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 分页列表查询
	  *
	  * @param sysWarehouseBox
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "仓库-分页列表查询")
	 @Operation(summary="仓库-分页列表查询")
	 @GetMapping(value = "/listboxname")
	 public Result<IPage<SysWarehouseBox>> listboxname(SysWarehouseBox sysWarehouseBox,
													  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													  HttpServletRequest req) {
		 QueryWrapper<SysWarehouseBox> queryWrapper = QueryGenerator.initQueryWrapper(sysWarehouseBox, req.getParameterMap());
		 Page<SysWarehouseBox> page = new Page<SysWarehouseBox>(pageNo, pageSize);
		 IPage<SysWarehouseBox> pageList = sysWarehouseBoxService.page(page, queryWrapper);
		 return Result.OK(pageList);
	 }

	/**
	 *   添加
	 *
	 * @param sysWarehousePage
	 * @return
	 */
	@AutoLog(value = "仓库-添加")
	@Operation(summary="仓库-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_warehouse:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysWarehousePage sysWarehousePage) {
		SysWarehouse sysWarehouse = new SysWarehouse();
		BeanUtils.copyProperties(sysWarehousePage, sysWarehouse);
		sysWarehouseService.saveMain(sysWarehouse, sysWarehousePage.getSysWarehouseBoxList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysWarehousePage
	 * @return
	 */
	@AutoLog(value = "仓库-编辑")
	@Operation(summary="仓库-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_warehouse:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysWarehousePage sysWarehousePage) {
		SysWarehouse sysWarehouse = new SysWarehouse();
		BeanUtils.copyProperties(sysWarehousePage, sysWarehouse);
		SysWarehouse sysWarehouseEntity = sysWarehouseService.getById(sysWarehouse.getId());
		if(sysWarehouseEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysWarehouseService.updateMain(sysWarehouse, sysWarehousePage.getSysWarehouseBoxList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "仓库-通过id删除")
	@Operation(summary="仓库-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_warehouse:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysWarehouseService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "仓库-批量删除")
	@Operation(summary="仓库-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_warehouse:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysWarehouseService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "仓库-通过id查询")
	@Operation(summary="仓库-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysWarehouse> queryById(@RequestParam(name="id",required=true) String id) {
		SysWarehouse sysWarehouse = sysWarehouseService.getById(id);
		if(sysWarehouse==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysWarehouse);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "货位通过主表ID查询")
	@Operation(summary="货位-通主表ID查询")
	@GetMapping(value = "/querySysWarehouseBoxByMainId")
	public Result<List<SysWarehouseBox>> querySysWarehouseBoxListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysWarehouseBox> sysWarehouseBoxList = sysWarehouseBoxService.selectByMainId(id);
		return Result.OK(sysWarehouseBoxList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysWarehouse
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_warehouse:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysWarehouse sysWarehouse) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysWarehouse> queryWrapper = QueryGenerator.initQueryWrapper(sysWarehouse, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<SysWarehouse> sysWarehouseList = sysWarehouseService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysWarehousePage> pageList = new ArrayList<SysWarehousePage>();
      for (SysWarehouse main : sysWarehouseList) {
          SysWarehousePage vo = new SysWarehousePage();
          BeanUtils.copyProperties(main, vo);
          List<SysWarehouseBox> sysWarehouseBoxList = sysWarehouseBoxService.selectByMainId(main.getId());
          vo.setSysWarehouseBoxList(sysWarehouseBoxList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "仓库列表");
      mv.addObject(NormalExcelConstants.CLASS, SysWarehousePage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("仓库数据", "导出人:"+sysUser.getRealname(), "仓库"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_warehouse:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysWarehousePage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysWarehousePage.class, params);
              for (SysWarehousePage page : list) {
                  SysWarehouse po = new SysWarehouse();
                  BeanUtils.copyProperties(page, po);
                  sysWarehouseService.saveMain(po, page.getSysWarehouseBoxList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
