package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

import java.util.Date;

@Data
@TemplateDesigner(value = "TestConsumptive",drillUp = "testId->test.id",entity = "TestConsumptive",description = "试剂耗材信息")
public class TestConsumptiveVO {
    @TemplateField(entityFieldName = "consumptiveId",dictTable = "consumptive",dictKey = "id",dictText = "name",description = "名称")
    private String name;
    @TemplateField(entityFieldName = "consumptiveId",drillChain = "consumptive.supplierId->sys_supplier.id->sys_supplier.name", description = "厂家")
    private String supplier;
    @TemplateField(entityFieldName = "consumptiveId",dictTable = "consumptive",dictKey = "id",dictText = "code",description = "批号")
    private String lotNo;
    @TemplateField(entityFieldName = "consumptiveId",dictTable = "consumptive",dictKey = "id",dictText = "grade",description = "级别")
    private String grade;
    @TemplateField(entityFieldName = "consumptiveId",dictTable = "consumptive",dictKey = "id",dictText = "purity",description = "纯度（含量）")
    private String purity;
    @TemplateField(entityFieldName = "consumptiveId",dictTable = "consumptive",dictKey = "id",dictText = "spec",description = "规格")
    private String spec;
    @TemplateField(entityFieldName = "consumptiveId", drillChain="consumptive.id",calcExpr = "purchase_date.plusDays(effective_length)",  description = "有效期至")
    private Date effectiveDate;
}
