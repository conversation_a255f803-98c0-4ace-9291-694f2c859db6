package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 溶液配置记录
 * @Author: jeecg-boot
 * @Date:   2025-04-28
 * @Version: V1.0
 */
@Data
@TableName("solution")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="溶液配置记录")
public class Solution implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**编号*/
	@Excel(name = "编号", width = 15)
    @Schema(description = "编号")
    private java.lang.String code;
	/**溶液类型*/
	@Excel(name = "溶液类型", width = 15, dicCode = "solution_type")
	@Dict(dicCode = "solution_type")
    @Schema(description = "溶液类型")
    private java.lang.String solutionTypeId;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "solution_status_type")
	@Dict(dicCode = "solution_status_type")
    @Schema(description = "状态")
    private java.lang.String status;
	/**配置记录*/
	@Excel(name = "配置记录", width = 15)
    @Schema(description = "配置记录")
    private java.lang.String record;
	/**溶液浓度*/
	@Excel(name = "溶液浓度", width = 15)
    @Schema(description = "溶液浓度")
    private java.lang.String conc;
	/**称样量*/
	@Excel(name = "称样量", width = 15)
    @Schema(description = "称样量")
    private java.lang.String weighing;
	/**含量*/
	@Excel(name = "含量", width = 15)
    @Schema(description = "含量")
    private java.lang.String content;
	/**定容体积*/
	@Excel(name = "定容体积", width = 15)
    @Schema(description = "定容体积")
    private java.lang.String constantVolume;
	/**移取体积*/
	@Excel(name = "移取体积", width = 15)
    @Schema(description = "移取体积")
    private java.lang.String removeVolume;
	/**配置人*/
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "配置人")
    private java.lang.String createBy;
	/**配置日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "配置日期")
    private java.util.Date createTime;
	/**有效期*/
	@Excel(name = "有效期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "有效期")
    private java.util.Date effectiveTo;
	/**保存条件*/
	@Excel(name = "保存条件", width = 15)
    @Schema(description = "保存条件")
    private java.lang.String storeCondition;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
