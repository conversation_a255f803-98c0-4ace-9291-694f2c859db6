package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 耗材台账
 * @Author: jeecg-boot
 * @Date:   2025-04-29
 * @Version: V1.0
 */
@Data
@TableName("consumptive")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="耗材台账")
public class Consumptive implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**编号（或批号）*/
	@Excel(name = "编号（或批号）", width = 15)
    @Schema(description = "编号（或批号）")
    private java.lang.String code;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
	/**类型*/
	@Excel(name = "类型", width = 15, dictTable = "sys_consumptive_type", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_consumptive_type", dicText = "name", dicCode = "id")
    @Schema(description = "类型")
    private java.lang.String consumptiveTypeId;
	/**级别*/
	@Excel(name = "级别", width = 15)
    @Schema(description = "级别")
    private java.lang.String grade;
	/**纯度（或含量）*/
	@Excel(name = "纯度（或含量）", width = 15)
    @Schema(description = "纯度（或含量）")
    private java.lang.String purity;
	/**规格*/
	@Excel(name = "规格", width = 15)
    @Schema(description = "规格")
    private java.lang.String spec;
	/**供应商*/
	@Excel(name = "供应商", width = 15, dictTable = "sys_supplier", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_supplier", dicText = "name", dicCode = "id")
    @Schema(description = "供应商")
    private java.lang.String supplierId;
	/**购入（配制）日期*/
	@Excel(name = "购入（配制）日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "购入（配制）日期")
    private java.util.Date purchaseDate;
	/**单价*/
	@Excel(name = "单价", width = 15)
    @Schema(description = "单价")
    private java.math.BigDecimal price;
	/**有效期（天）*/
	@Excel(name = "有效期（天）", width = 15)
    @Schema(description = "有效期（天）")
    private java.lang.Integer effectiveLength;
	/**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**填料*/
	@Excel(name = "填料", width = 15)
    @Schema(description = "填料")
    private java.lang.String packingMaterial;
	/**柱长*/
	@Excel(name = "柱长", width = 15)
    @Schema(description = "柱长")
    private java.lang.String columnLength;
	/**粒径*/
	@Excel(name = "粒径", width = 15)
    @Schema(description = "粒径")
    private java.lang.String particleSize;
	/**最高温度*/
	@Excel(name = "最高温度", width = 15)
    @Schema(description = "最高温度")
    private java.lang.String maxTemperature;
	/**品牌*/
	@Excel(name = "品牌", width = 15)
    @Schema(description = "品牌")
    private java.lang.String brand;
	/**厂家*/
	@Excel(name = "厂家", width = 15)
    @Schema(description = "厂家")
    private java.lang.String manufacturer;
	/**出厂编号*/
	@Excel(name = "出厂编号", width = 15)
    @Schema(description = "出厂编号")
    private java.lang.String serialNumber;
	/**型号*/
	@Excel(name = "型号", width = 15)
    @Schema(description = "型号")
    private java.lang.String model;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
	/**样品*/
	@Excel(name = "样品", width = 15, dictTable = "sample", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sample", dicText = "name", dicCode = "id")
    @Schema(description = "样品")
    private java.lang.String sampleId;
	/**客户*/
	@Excel(name = "客户", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户")
    private java.lang.String customerId;
	/**数量*/
	@Excel(name = "数量", width = 15)
    @Schema(description = "数量")
    private java.lang.Integer qty;
	/**单位*/
	@Excel(name = "单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "单位")
    private java.lang.String unit;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "sm_status")
	@Dict(dicCode = "sm_status")
    @Schema(description = "状态")
    private java.lang.String status;
    /**销毁原因*/
    @Excel(name = "销毁原因", width = 15)
    @Schema(description = "销毁原因")
    private java.lang.String destroyReason;
}
