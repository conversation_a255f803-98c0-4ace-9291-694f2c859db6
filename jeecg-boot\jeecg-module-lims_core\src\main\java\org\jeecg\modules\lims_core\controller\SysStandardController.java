package org.jeecg.modules.lims_core.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecg.common.system.vo.DictModel;
import org.jeecg.modules.lims_core.entity.YaoDian;
import org.jeecg.modules.lims_core.vo.LinkageVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
import org.jeecg.modules.lims_core.entity.SysStandard;
import org.jeecg.modules.lims_core.vo.SysStandardPage;
import org.jeecg.modules.lims_core.service.ISysStandardService;
import org.jeecg.modules.lims_core.service.ISysStandardEvaluationLimtService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 标准
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@Tag(name="标准")
@RestController
@RequestMapping("/lims_core/sysStandard")
@Slf4j
public class SysStandardController {
	@Autowired
	private ISysStandardService sysStandardService;
	@Autowired
	private ISysStandardEvaluationLimtService sysStandardEvaluationLimtService;

	/**
	 * 分页列表查询
	 *
	 * @param sysStandard
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "标准-分页列表查询")
	@Operation(summary="标准-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysStandard>> queryPageList(SysStandard sysStandard,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("contentTypeId", QueryRuleEnum.LIKE_WITH_OR);
        customeRuleMap.put("effectiveStatus", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<SysStandard> queryWrapper = QueryGenerator.initQueryWrapper(sysStandard, req.getParameterMap(),customeRuleMap);
		Page<SysStandard> page = new Page<SysStandard>(pageNo, pageSize);
		IPage<SysStandard> pageList = sysStandardService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param sysStandardPage
	 * @return
	 */
	@AutoLog(value = "标准-添加")
	@Operation(summary="标准-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysStandardPage sysStandardPage) {
		SysStandard sysStandard = new SysStandard();
		BeanUtils.copyProperties(sysStandardPage, sysStandard);
		sysStandardService.saveMain(sysStandard, sysStandardPage.getSysStandardEvaluationLimtList());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sysStandardPage
	 * @return
	 */
	@AutoLog(value = "标准-编辑")
	@Operation(summary="标准-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysStandardPage sysStandardPage) {
		SysStandard sysStandard = new SysStandard();
		BeanUtils.copyProperties(sysStandardPage, sysStandard);
		SysStandard sysStandardEntity = sysStandardService.getById(sysStandard.getId());
		if(sysStandardEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysStandardService.updateMain(sysStandard, sysStandardPage.getSysStandardEvaluationLimtList());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "标准-通过id删除")
	@Operation(summary="标准-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysStandardService.delMain(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "标准-批量删除")
	@Operation(summary="标准-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysStandardService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "标准-通过id查询")
	@Operation(summary="标准-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysStandard> queryById(@RequestParam(name="id",required=true) String id) {
		SysStandard sysStandard = sysStandardService.getById(id);
		if(sysStandard==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysStandard);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "标准指标评定要求通过主表ID查询")
	@Operation(summary="标准指标评定要求-通主表ID查询")
	@GetMapping(value = "/querySysStandardEvaluationLimtByMainId")
	public Result<List<SysStandardEvaluationLimt>> querySysStandardEvaluationLimtListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysStandardEvaluationLimt> sysStandardEvaluationLimtList = sysStandardEvaluationLimtService.selectByMainId(id);
		return Result.OK(sysStandardEvaluationLimtList);
	}

	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "标准指标评定要求通过主表ID查询")
	 @Operation(summary="标准指标评定要求-通TestId查询")
	 @GetMapping(value = "/querySysStandardByTestId")
	 public Result<List<SysStandard>> querySysStandardByTestId(@RequestParam(name="id",required=true) String id) {
		 List<SysStandard> sysStandard = sysStandardEvaluationLimtService.selectByTestId(id);
		 return Result.OK(sysStandard);
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param sysStandard
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysStandard sysStandard) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysStandard> queryWrapper = QueryGenerator.initQueryWrapper(sysStandard, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

      //配置选中数据查询条件
       String selections = request.getParameter("selections");
       if(oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id",selectionList);
       }
       //Step.2 获取导出数据
       List<SysStandard> sysStandardList = sysStandardService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysStandardPage> pageList = new ArrayList<SysStandardPage>();
      for (SysStandard main : sysStandardList) {
          SysStandardPage vo = new SysStandardPage();
          BeanUtils.copyProperties(main, vo);
          List<SysStandardEvaluationLimt> sysStandardEvaluationLimtList = sysStandardEvaluationLimtService.selectByMainId(main.getId());
          vo.setSysStandardEvaluationLimtList(sysStandardEvaluationLimtList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "标准列表");
      mv.addObject(NormalExcelConstants.CLASS, SysStandardPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("标准数据", "导出人:"+sysUser.getRealname(), "标准"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysStandardPage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysStandardPage.class, params);
              for (SysStandardPage page : list) {
                  SysStandard po = new SysStandard();
                  BeanUtils.copyProperties(page, po);
                  sysStandardService.saveMain(po, page.getSysStandardEvaluationLimtList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }
	 /**
	  *   级联查询
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "标准-级联查询")
	 @Operation(summary="标准-级联查询")
	 @GetMapping(value = "/fetch")
	 public Result<List<LinkageVo>> fetch(@RequestParam(name="id") String id,@RequestParam(name="version") String version) {
		 List<LinkageVo> fetch = sysStandardService.fetch(id,version);
		 return Result.OK(fetch);
	 }

	 //@AutoLog(value = "通过方法id找标准")
	 @Operation(summary="通过方法id找标准")
	 @GetMapping(value = "/querySysStandardByMethodId")
	 public Result<List<DictModel>> querySysStandardByMethodId(@RequestParam(name="id",required=true) String id) {
		 List<DictModel> standardList = sysStandardEvaluationLimtService.querySysStandardByMethodId(id);
		 return Result.OK(standardList);
	 }

	 /**
	  *   添加药典
	  *
	  * @param sysStandardPage
	  * @return
	  */
	 @AutoLog(value = "标准-添加药典")
	 @Operation(summary="标准-添加药典")
	 @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:add')")
	 @PostMapping(value = "/addYaoDian")
	 public Result<String> addYaoDian(@RequestBody YaoDian yaoDian) {
		 sysStandardService.addYaoDian(yaoDian);
		 return Result.OK("添加成功！");
	 }



	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "标准-通过id查询")
	 @Operation(summary="标准-通过id查询")
	 @GetMapping(value = "/selectOptions")
	 public Result<List<Map<String,String>>> selectOptions() {
		 List<Map<String,String>> sysStandards = sysStandardService.selectOptions();
		 return Result.OK(sysStandards);

	 }


	 /**
	  *   复制方法，通过原方法id,新标准id，新方法名称
	  *
	  * @param sysStandardPage
	  * @return
	  */
	 @AutoLog(value = "标准-复制标准")
	 @Operation(summary="标准-复制标准")
	 @PreAuthorize("@jps.requiresPermissions('lims_core:sys_standard:add')")
	 @PostMapping(value = "/clonestandard")
	 public Result<String> clonestandard(@RequestBody SysStandardPage sysStandardPage) {
		 sysStandardService.saveCopyMain(sysStandardPage);
		 return Result.OK("添加成功！");
	 }

}
