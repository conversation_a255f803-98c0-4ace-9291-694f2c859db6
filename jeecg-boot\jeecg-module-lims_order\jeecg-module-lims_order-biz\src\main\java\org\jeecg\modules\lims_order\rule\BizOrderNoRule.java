package org.jeecg.modules.lims_order.rule;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/3 16:20
 */
public class BizOrderNoRule implements IFillRuleHandler {
    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        String prefix = "";
        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("sysCustomerServiceImpl");
        // 根据 ruleCode 查询出实体
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", formData.get("customer_id"));
        JSONObject entity = JSONObject.parseObject(JSONObject.toJSONString(impl.getOne(queryWrapper)));

        if(entity.getString("currentNo") != null && !entity.getString("currentNo").equals("")){
            return entity.getString("currentNo");
        }
        //订单前缀默认为CN 如果规则参数不为空，则取自定义前缀
        if (params != null) {
            Object obj = params.get("prefix");
            if (obj != null) prefix = obj.toString();
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        if(entity.getString("snDate") != null && entity.getString("snDate").equals(format.format(new Date()))){
            int sn = entity.getInteger("sn") + 1;
            entity.put("sn",sn);
            impl.updateById(entity);
        }else{

            entity.put("sn",1);
            entity.put("snDate",format.format(new Date()));

        }

        String value = prefix+ "-" + entity.getString("code")+ "-" + format.format(new Date()) + "-" + StrUtil.padPre(String.valueOf(entity.getInteger("sn")),3,'0');
        // 根据formData的值的不同，生成不同的订单号
        String name = formData.getString("name");
        if (!StringUtils.isEmpty(name)) {
            value += name;
        }
        entity.put("currentNo",value);
        impl.updateById(entity);
        return value;
    }
}