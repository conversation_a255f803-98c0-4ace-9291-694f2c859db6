package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 营销产品
 * @Author: jeecg-boot
 * @Date:   2025-03-12
 * @Version: V1.0
 */
@Data
@TableName("sys_product")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="营销产品")
public class SysProduct implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private String name;
	/**业务类别*/
    @Excel(name = "业务类别", width = 15, dictTable = "sys_category", dicText = "name", dicCode = "id")
    @Schema(description = "业务类别")
    private String bizTypeId;
	/**标准单价*/
	@Excel(name = "标准单价", width = 15)
    @Schema(description = "标准单价")
    private BigDecimal stdPrice;
	/**标准工时*/
	@Excel(name = "标准工时", width = 15)
    @Schema(description = "标准工时")
    private String stdTat;
	/**产品标准*/
	@Excel(name = "产品标准", width = 15, dictTable = "sys_standard", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_standard", dicText = "name", dicCode = "id")
    @Schema(description = "产品标准")
    private String standardId;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
}
