package org.jeecg.common.constant;

import javax.swing.plaf.PanelUI;

/**
 * 规则值生成 编码常量类
 * @author: taoyan
 * @date: 2020年04月02日
 */
public class FillRuleConstant {

    /**
     * 公文发文编码
     */
    public static final String DOC_SEND = "doc_send_code";

    /**
     * 部门编码
     */
    public static final String DEPART = "org_num_role";

    /**
     * 分类字典编码
     */
    public static final String CATEGORY = "category_code_rule";
    /**
     * 报价单
     */
    public static final String QUOTATION = "quotation_no_rule";
    /**
     * 合同
     */
    public static final String BIZORDER = "biz_order_no_rule";
    /**
     * 样品
     */
    public static final String SAMPLE = "sample_no_rule";
    /**
     * 研发项目
     */
    public static final String RD = "rd_no_rule";

    /**
     * 标准物质
     */
    public static final String STANDARDMATERIAL = "standardmaterial_no_rule";

     /**
     * 溶液
     */
    public static final String SOLUTION = "solution_no_rule";

    /**
     * 试剂耗材
     */
    public static final String CONSUMPTIVE = "consumptive_no_rule";

    /**
     * 仪器
     */
    public static final String INSTRUMENT   ="instrument_no_rule";
    /**
     * 商机
     */
    public static final String OPPORTUNITY = "opportunity_no_rule";
}
