package org.jeecg.modules.dcs.vo;

import java.util.List;

import org.jeecg.modules.dcs.entity.TrainingRecord;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 培训
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
@Data
@Schema(description="培训")
public class TrainingPage {

	/**主键*/
	@Schema(description = "主键")
    private java.lang.String id;
	/**培训名称*/
	@Excel(name = "培训名称", width = 15)
	@Schema(description = "培训名称")
    private java.lang.String name;
	/**课件*/
	@Excel(name = "课件", width = 15)
	@Schema(description = "课件")
    private java.lang.String courseware;
	/**开始时间*/
	@Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "开始时间")
    private java.util.Date startTime;
	/**培训时长(秒)*/
	@Excel(name = "培训时长(秒)", width = 15)
	@Schema(description = "培训时长(秒)")
    private java.lang.Integer duration;
	/**会议id*/
	@Excel(name = "会议id", width = 15)
	@Schema(description = "会议id")
    private java.lang.String meetingIds;
	/**创建人*/
	@Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

	/**文控文件*/
	@Schema(description = "文控文件")
	private String docIds;

	@ExcelCollection(name="培训记录")
	@Schema(description = "培训记录")
	private List<TrainingRecord> trainingRecordList;

}
