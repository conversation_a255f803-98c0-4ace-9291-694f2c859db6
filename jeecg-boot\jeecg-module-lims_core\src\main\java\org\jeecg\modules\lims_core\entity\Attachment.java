package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 附件表
 * @Author: jeecg-boot
 * @Date:   2025-05-12
 * @Version: V1.0
 */
@Data
@TableName("attachment")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="附件表")
public class Attachment implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**源表名*/
	@Excel(name = "源表名", width = 15)
    @Schema(description = "源表名")
    private java.lang.String sourceTable;
	/**源表id*/
	@Excel(name = "源表id", width = 15)
    @Schema(description = "源表id")
    private java.lang.String sourceId;
	/**文件地址*/
	@Excel(name = "文件地址", width = 15)
    @Schema(description = "文件地址")
    private java.lang.String url;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @Schema(description = "类型")
    private java.lang.String type;
    /**父级表id字段*/
    @Excel(name = "父级表id字段", width = 15)
    @Schema(description = "父级表id字段")
    private java.lang.String parentField;
    /**父级id*/
    @Excel(name = "父级id", width = 15)
    @Schema(description = "父级id")
    private java.lang.String parentFieldId;
}
