package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 翻译
 * @Author: jeecg-boot
 * @Date:   2025-05-23
 * @Version: V1.0
 */
@Data
@TableName("sys_translation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="翻译")
public class SysTranslation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**上下文*/
	@Excel(name = "上下文", width = 15)
    @Schema(description = "上下文")
    private java.lang.String context;
	/**字段名*/
	@Excel(name = "字段名", width = 15)
    @Schema(description = "字段名")
    private java.lang.String fieldName;
	/**记录ID*/
	@Excel(name = "记录ID", width = 15)
    @Schema(description = "记录ID")
    private java.lang.String sourceId;
	/**翻译*/
	@Excel(name = "翻译", width = 15)
    @Schema(description = "翻译")
    private java.lang.String translation;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
}
