package org.jeecg.modules.dcs.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.system.query.QueryCondition;
import org.jeecg.modules.dcs.dto.DcsDocWithPermission;
import org.jeecg.modules.dcs.entity.DcsDoc;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: dcs_doc
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
public interface DcsDocMapper extends BaseMapper<DcsDoc> {
//    IPage<DcsDocWithPermission> queryPageList(Page<DcsDocWithPermission> page,
//                                              @Param("deptId") String deptId,
//                                              @Param("queryConditions") List<QueryCondition> queryConditions);

	IPage<DcsDocWithPermission> queryPageList(Page<DcsDocWithPermission> page,
											  @Param(Constants.WRAPPER) Wrapper<DcsDoc> updateWrapper);
}