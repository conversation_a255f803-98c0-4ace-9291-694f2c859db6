package org.jeecg.modules.lims_order.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.impl.TestTaskServiceImpl;
import org.jeecg.modules.lims_core.util.GBDateUtils;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AddRepeatTestJob implements Job {
    /**
     * 若参数变量名修改 QuartzJobController中也需对应修改
     */
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private TestTaskServiceImpl testTaskService;
    @Autowired
    private TestResultMapper testResultMapper;
    @Autowired
    private  SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SysMethodAnalyteMapper sysMethodAnalyteMapper;

    private String parameter;

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        List<TestTask> dataList = testTaskService.getBaseMapper().selectList(new QueryWrapper<TestTask>().eq("next_test_date", new Date()));
        dataList.forEach(testTask->{
            if(testTask.getRepeatTypeNextId() !=null && !"".equals(testTask.getRepeatTypeNextId())){
                SysMethodRepeatType sysMethodRepeatTypeNext = sysMethodRepeatTypeMapper.selectById(testTask.getRepeatTypeNextId());
                if (testTaskService.getBaseMapper().selectList(new QueryWrapper<TestTask>()
                        .eq("sample_id", testTask.getSampleId())
                        .eq("method_id", testTask.getMethodId())
                        .eq("repeat_type_id", testTask.getRepeatTypeNextId())
                ).size() == 0) {
                    if(sysMethodRepeatTypeNext != null){
                        TestTask newTestTask = new TestTask();
                        BeanUtils.copyProperties(testTask, newTestTask);
                        newTestTask.setId(null);

                        newTestTask.setRepeatTypeId(sysMethodRepeatTypeNext.getId());


                        SysMethodRepeatType sysMethodRepeatTypeNextNext = sysMethodRepeatTypeMapper.selectById(sysMethodRepeatTypeNext.getNextId());
                        if(sysMethodRepeatTypeNextNext != null){
                            newTestTask.setRepeatTypeNextId(sysMethodRepeatTypeNext.getNextId());
                            newTestTask.setNextTestDate(GBDateUtils.calcDate(sysMethodRepeatTypeNext.getCycleName(), testTask.getNextTestDate()));
                        }else{
                            newTestTask.setRepeatTypeNextId(null);
                            newTestTask.setNextTestDate(null);
                        }
                        testTaskService.getBaseMapper().insert(newTestTask);

                        //拿到老testtask的test下的testresult的analyteid

                        List< SysProductSubVo> sysProductSubVos = new ArrayList<>();
                        Test test = testMapper.selectList(new QueryWrapper<Test>().eq("task_id", testTask.getId())).get(0);
                        testResultMapper.selectByTestId(test.getId()).forEach(
                                testResult -> {
                                    SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(testResult.getMethodAnalyteId());
                                    SysProductSubVo sysProductSubVo = new SysProductSubVo();
                                    sysProductSubVo.setAnalyteId(sysMethodAnalyte.getAnalyteId());
                                    sysProductSubVo.setEvaluationId(testResult.getLimitId());
                                    sysProductSubVos.add(sysProductSubVo);
                                }
                        );

                        testTaskService.generateTest(newTestTask,sysProductSubVos );


                        List<Report> reports = reportMapper.selectList(new QueryWrapper<Report>().eq("sample_id", testTask.getSampleId()).eq("repeat_type_id", testTask.getRepeatTypeNextId()));
                        if(reports.size()==1){
                            Report report = reports.get(0);
                            report.setTestTaskIds(testTaskService.getBaseMapper().selectList(new QueryWrapper<TestTask>().eq("sample_id", testTask.getSampleId()).eq("repeat_type_id", testTask.getRepeatTypeNextId())).stream().reduce("", (s, t) -> s + t.getId() + ",", String::concat));
                            reportMapper.updateById(report);
                        }
                    }
                }

            }


        });

    }



}
