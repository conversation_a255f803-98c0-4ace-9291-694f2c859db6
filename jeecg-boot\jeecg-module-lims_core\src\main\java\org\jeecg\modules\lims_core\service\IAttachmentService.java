package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.Attachment;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.AttachQueryVo;
import org.jeecg.modules.system.model.TreeModel;

import java.util.List;

/**
 * @Description: 附件表
 * @Author: jeecg-boot
 * @Date:   2025-05-12
 * @Version: V1.0
 */
public interface IAttachmentService extends IService<Attachment> {

    void saveAttach(Attachment attachment);


    List<TreeModel> listSourceOptions(AttachQueryVo attachQueryVo);
}
