<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_order.mapper.QuotationMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_order.vo.QuotationPage">
        select * from ( SELECT
                            q.*,
                            bo.contract_no as contractno,
                            CASE
                                WHEN EXISTS (
                                    SELECT 1
                                    FROM sample s
                                    WHERE s.quotation_id = q.id
                                ) AND NOT EXISTS (
                                    SELECT 1
                                    FROM sample s
                                    WHERE s.quotation_id = q.id
                                      AND s.sample_flow_status != '已入库'
                                ) THEN '已入库'
                                ELSE NULL
                                END as quotationsamplestatus
                        FROM quotation q LEFT JOIN biz_order bo ON q.id = bo.quotation_id) t
            ${ew.customSqlSegment}
    </select>
</mapper>