# LTV（长期验证）功能文档

## 概述

LTV（Long Term Validation）长期验证功能为PDF数字签名提供长期有效性验证，确保签名在证书过期后仍能被验证。

## 功能特性

### ✅ 已实现功能

1. **OCSP响应处理**
   - 自动获取OCSP响应
   - 支持多种OCSP响应格式
   - OCSP响应缓存机制
   - 错误重试机制

2. **CRL处理**
   - 自动下载CRL
   - CRL验证和处理
   - 支持多个CRL源

3. **DSS字典构建**
   - 符合PDF标准的DSS字典
   - VRI条目创建
   - 时间戳信息添加

4. **时间戳支持**
   - TSA时间戳集成
   - 时间戳证书识别
   - 时间戳验证

5. **Adobe Reader兼容性**
   - Adobe扩展支持
   - PDF版本兼容性
   - LTV显示优化

### 🔧 配置选项

```yaml
pdf:
  ltv:
    tsa-keywords: [timestamp, tsa, gdca]  # 时间戳证书关键词
    adobe-extension:
      name: ADBE
      base-version: "1.7"
      extension-level: 8
    ocsp-config:
      max-retries: 3
      timeout-ms: 10000
      enable-caching: true
```

## 使用方法

### 基本用法

```java
@Autowired
private SignService signService;

// 执行带LTV的PDF签名
signService.sign(pdfUrl);
```

### 高级配置

```java
// 自定义LTV配置
LtvConfiguration config = new LtvConfiguration();
config.getOcspConfig().setMaxRetries(5);
config.getOcspConfig().setTimeoutMs(15000);
```

## 技术架构

### 核心组件

1. **SignServiceImpl** - 主要签名服务
2. **LtvConfiguration** - 配置管理
3. **OcspCacheService** - OCSP缓存
4. **LtvLogger** - 结构化日志
5. **LtvProcessingException** - 异常处理

### 处理流程

```
1. PDF签名 → 2. 证书链验证 → 3. OCSP获取 → 4. CRL下载 → 5. DSS构建 → 6. VRI创建 → 7. LTV启用
```

## 故障排除

### 常见问题

1. **OCSP响应获取失败**
   ```
   原因：网络连接问题或OCSP服务器不可用
   解决：检查网络连接，配置重试机制
   ```

2. **Adobe Reader显示"LTV未启用"**
   ```
   原因：DSS字典格式不正确或缺少必要信息
   解决：检查VRI条目和时间戳信息
   ```

3. **证书链验证失败**
   ```
   原因：缺少中间证书或根证书
   解决：确保证书链完整
   ```

### 调试方法

1. **启用详细日志**
   ```yaml
   logging:
     level:
       org.jeecg.modules.oo.service.impl.SignServiceImpl: DEBUG
       LTV_PROCESSING: DEBUG
   ```

2. **检查OCSP缓存**
   ```java
   OcspCacheService.CacheStatistics stats = ocspCacheService.getCacheStatistics();
   logger.info("OCSP缓存统计: {}", stats);
   ```

3. **验证DSS字典**
   - 使用PDF分析工具检查DSS字典结构
   - 确认VRI条目包含正确的OCSP和CRL引用

## 性能优化

### 缓存策略

1. **OCSP响应缓存**
   - 默认缓存1小时
   - 内存缓存 + Spring Cache
   - 自动清理过期条目

2. **证书链缓存**
   - 缓存已验证的证书链
   - 减少重复验证开销

### 监控指标

- OCSP响应时间
- 缓存命中率
- 签名处理时间
- 错误率统计

## 安全考虑

1. **证书验证**
   - 严格的证书链验证
   - 撤销状态检查
   - 时间戳验证

2. **网络安全**
   - HTTPS连接
   - 超时控制
   - 重试限制

## 兼容性

### 支持的PDF版本
- PDF 1.7 及以上
- Adobe Extension Level 8

### 支持的证书类型
- RSA证书
- ECC证书
- 时间戳证书

### 测试环境
- Adobe Reader DC
- Adobe Acrobat Pro
- 福昕阅读器

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础LTV功能实现
- ✅ OCSP和CRL支持
- ✅ Adobe Reader兼容性
- ✅ 配置外部化
- ✅ 异常处理优化
- ✅ 性能监控
- ✅ 单元测试

### 计划功能
- 🔄 批量签名LTV处理
- 🔄 LTV验证API
- 🔄 Web界面管理
- 🔄 更多TSA服务支持

## 联系支持

如有问题或建议，请联系开发团队。