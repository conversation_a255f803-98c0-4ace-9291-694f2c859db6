package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "SysMethod",drillDown = "sys_method_analyte.method_id",description = "方法信息")
public class SysMethodVO {
    @TemplateField(description = "编号")
    private String id;
    @TemplateField(description = "方法名称")
    private String name;

}