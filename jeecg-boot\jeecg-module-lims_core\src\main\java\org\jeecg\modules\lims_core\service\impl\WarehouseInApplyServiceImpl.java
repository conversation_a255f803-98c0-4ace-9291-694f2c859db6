package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.SysUnit;
import org.jeecg.modules.lims_core.entity.WarehouseInApply;
import org.jeecg.modules.lims_core.entity.WarehouseOutApply;
import org.jeecg.modules.lims_core.mapper.InventoryMapper;
import org.jeecg.modules.lims_core.mapper.WarehouseInApplyMapper;
import org.jeecg.modules.lims_core.mapper.WarehouseInOutMapper;
import org.jeecg.modules.lims_core.mapper.WarehouseOutApplyMapper;
import org.jeecg.modules.lims_core.service.ISysUnitService;
import org.jeecg.modules.lims_core.service.ISysWarehouseBoxService;
import org.jeecg.modules.lims_core.service.ISysWarehouseService;
import org.jeecg.modules.lims_core.service.IWarehouseInApplyService;
import org.jeecg.modules.lims_core.vo.WarehouseInApplyVo;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 余物退回申请
 * @Author: jeecg-boot
 * @Date:   2025-05-27
 * @Version: V1.0
 */
@Service
public class WarehouseInApplyServiceImpl extends ServiceImpl<WarehouseInApplyMapper, WarehouseInApply> implements IWarehouseInApplyService {
    @Autowired
    private InventoryMapper inventoryMapper;
    @Autowired
    private InventoryServiceImpl inventoryService;
    @Autowired
    private ISysWarehouseService sysWarehouseService;
    @Autowired
    private ISysWarehouseBoxService sysWarehouseBoxService;
    @Autowired
    private WarehouseInOutMapper warehouseInOutMapper;
    @Autowired
    private WarehouseOutApplyMapper warehouseOutApplyMapper;
    @Autowired
    private WarehouseInApplyMapper warehouseInApplyMapper;
    @Autowired
    private ISysDictItemService sysDictItemService;
    @Autowired
    private ISysUnitService sysUnitService;

    @Override
    public IPage<WarehouseInApplyVo> queryPageList(Page<WarehouseInApplyVo> page, Wrapper<WarehouseInApplyVo> wrapper) {
        return warehouseInApplyMapper.queryPageList(page,wrapper);
    }
    @Override
    public void apply(WarehouseInApply obj, ApplyType applyType) throws WxErrorException {
        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpMessageService messageService = cpService.getMessageService();

        LoginUser curUser = SecureUtil.currentUser();
        WxCpMessage message = new WxCpMessage();
        message.setAgentId(agentId);
        ISysBaseAPI sysBaseApi = SpringContextUtils.getBean(ISysBaseAPI.class);

        List<String> userIds = Arrays.asList("1863417432833499138");
        List<String> wechatUserIds = userIds.stream()
                .map(id -> sysBaseApi.getThirdUserIdByUserId(id, "wechat_enterprise"))
                .filter(id -> id != null && !id.isEmpty())
                .collect(Collectors.toList());
        String toUsers = String.join("|", wechatUserIds);
        message.setToUser(toUsers);
        //message.setToUser(sysBaseApi.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
        message.setMsgType("news");
        String articleTypeId_Text = "";
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("warehouse_goods_type");
        for (SysDictItem item : sysDictItems) {
            if (obj.getArticleTypeId().equals(item.getItemValue())) {
                articleTypeId_Text = item.getItemText();
                break;
            }
        }

        String operationReason_Text = "";
        sysDictItems = sysDictItemService.selectItemsByDictCode("warehouse_operation_reason");
        for (SysDictItem item : sysDictItems) {
            if (obj.getOperationReasonId().equals(item.getItemValue())) {
                operationReason_Text = item.getItemText();
                break;
            }
        }
        SysUnit unit = sysUnitService.getById(obj.getUnitId());
        List<NewArticle> articles = new ArrayList<>();
        NewArticle article = new NewArticle();
        article.setTitle("仓库操作通知");
        article.setDescription(String.format("物品编号: %s\n物品类型: %s\n数量: %s\n单位: %s\n操作类型: %s",
                obj.getArticleNo(), articleTypeId_Text, obj.getAmount(), unit ==null? "" : unit.getUnitName(), operationReason_Text));
        articles.add(article);
        message.setArticles(articles);
        try {
            messageService.send(message);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }
}
