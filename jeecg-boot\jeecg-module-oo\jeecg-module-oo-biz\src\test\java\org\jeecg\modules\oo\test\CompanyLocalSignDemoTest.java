//package org.jeecg.modules.oo.test;
//
//import junit.framework.TestCase;
//import net.qiyuesuo.sdk.SDKClient;
//import net.qiyuesuo.sdk.bean.contract.RelativePosition;
//import net.qiyuesuo.sdk.bean.contract.SealMultipleRequest;
//import net.qiyuesuo.sdk.bean.contract.StamperType;
//import net.qiyuesuo.sdk.bean.sign.local.KeywordMatchRule;
//import net.qiyuesuo.sdk.bean.sign.local.LocalSignFileStream;
//import net.qiyuesuo.sdk.bean.sign.local.LocalSignStamper;
//import net.qiyuesuo.sdk.bean.sign.local.request.CompanyLocalSignRequest;
//import net.qiyuesuo.sdk.bean.sign.local.result.LocalSignResult;
//import net.qiyuesuo.sdk.bean.user.UserInfoRequest;
//import net.qiyuesuo.sdk.impl.local.service.CompanyLocalSignService;
//import org.apache.commons.lang3.BooleanUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.File;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.UUID;
//import java.util.function.Supplier;
//
//
///**
// * CompanyLocalSignDemoTest
// *
// * <AUTHOR>
// */
//public class CompanyLocalSignDemoTest extends TestCase {
//
//    private final Logger logger = LoggerFactory.getLogger(CompanyLocalSignDemoTest.class);
//
//    CompanyLocalSignService companyLocalSignService;
//    SDKClient sdkClient;
//
//    // 输出文件地址
//    private final String dirPath = "/home/<USER>/iTest/jeecg-boot/target/pdf";
//    // 默认值印章ID
//    private Long sealId = 3362975989667455047L;
//
//    @Override
//    public void setUp() {
//        // 初始化开放平台SDK相关参数
//        String url = "http://192.168.1.212:9182/";
//        String accessKey = "jfhjXpbmYy";
//        String accessSecret = "g8sqN7uvlLieETqXCiCuVRkWMEHHbw";
//        sdkClient = new SDKClient(url, accessKey, accessSecret);
//
//        companyLocalSignService = new CompanyLocalSignService(sdkClient);
//    }
//
//    public void testSign() throws Exception {
//
//        // 签署文件
//        LocalSignFileStream pdf = getFileStream("pdf.pdf", "NoSign.pdf");
//
//        // 签署位置
//        List<LocalSignStamper> stampers = getStampers();
//
//        CompanyLocalSignRequest localSignRequest = new CompanyLocalSignRequest("我是本次签署主题", pdf, stampers);
//
//        // 印章
//        setSealRequest(localSignRequest);
//
//        // 操作人
//        setOperator(localSignRequest);
//
//        // 设置耗时统计 默认是关闭
//        localSignRequest.setEnableWatch(true);
//
//        LocalSignResult sign = companyLocalSignService.sign(localSignRequest);
//
////        logger.info("Sdk 本地签署结果：\n{}", sign);
////        if (BooleanUtils.isTrue(localSignRequest.getEnableWatch())) {
////            logger.debug("sdk本地签耗时：\n{}", sign.getPrettyPrint());
////        }
//
//    }
//
//
//    private LocalSignFileStream getFileStream(String fileName, String filePath) throws IOException {
//        return new LocalSignFileStream(fileName,
//                Files.newInputStream(new File(dirPath.concat(filePath)).toPath()),
//                Files.newOutputStream(Paths.get(String.format(dirPath + "signed-pdf/%s-%s.pdf", fileName, UUID.randomUUID()))));
//    }
//
//    private void setOperator(CompanyLocalSignRequest localSignRequest) {
//        UserInfoRequest userInfoRequest = new UserInfoRequest();
//        userInfoRequest.setContact("18607854646");
//        localSignRequest.setOperator(userInfoRequest);
//    }
//
//    private void setSealRequest(CompanyLocalSignRequest localSignRequest) {
//        SealMultipleRequest sealRequest = new SealMultipleRequest();
//        sealRequest.setSealIds(sealId + "");
//        localSignRequest.setSealRequest(sealRequest);
//    }
//
//    private List<LocalSignStamper> getStampers() {
//        // 设置Pdf1 的签署位置
//        return new LinkedList<>(getPdf1Stamper());
//    }
//
//    private List<LocalSignStamper> getPdf1Stamper() {
//
//        List<LocalSignStamper> signStampers = new LinkedList<>();
//        String fileName = "pdf.pdf";
//
//        // 正常的坐标定位
//        Supplier<LocalSignStamper> normalCoordinates = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            List<String> bestKeyword = new ArrayList<>();
//            bestKeyword.add("甲方签名");
//            localSignStamper.setBestKeyWords(bestKeyword);
//            localSignStamper.setOffsetX(0.0D);
//            localSignStamper.setOffsetY(0.0D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        // 正常的坐标定位
//        Supplier<LocalSignStamper> normalCoordinates1 = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.ACROSS_PAGE);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//
//        // 正常的多关键字定位 ALL 模式，全匹配
//
//        Supplier<LocalSignStamper> normalAllKeyWord = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            List<String> bestKeyword = new ArrayList<>();
//            bestKeyword.add("111");
//            bestKeyword.add("222");
//            bestKeyword.add("333");
//            localSignStamper.setBestKeyWords(bestKeyword);
//            localSignStamper.setOffsetX(0.0D);
//            localSignStamper.setOffsetY(0.0D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//
//        // 正常的关键字定位。PRIORITY 模式 ，只能匹配2个
//        Supplier<LocalSignStamper> normalPriorityKeyword = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            List<String> bestKeyword = new ArrayList<>();
//            bestKeyword.add("盖章");
//            bestKeyword.add("签字");
//            localSignStamper.setKeywordMatchRule(KeywordMatchRule.PRIORITY);
//            localSignStamper.setBestKeyWords(bestKeyword);
//            localSignStamper.setRelativePosition(RelativePosition.LOWER_CENTER);
//            localSignStamper.setOffsetX(0.0D);
//            localSignStamper.setOffsetY(0.0D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        // 骑缝章匹配
//        Supplier<LocalSignStamper> normalAcross = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setType(StamperType.ACROSS_PAGE);
//            localSignStamper.setFileName(fileName);
////            localSignStamper.setOffsetY(0.1D);
//            return localSignStamper;
//        };
//
//        signStampers.add(normalCoordinates.get());
//        signStampers.add(normalCoordinates1.get());
////        signStampers.add(normalAllKeyWord.get());
////        signStampers.add(normalPriorityKeyword.get());
////        signStampers.add(normalAcross.get());
//
//        return signStampers;
//
//    }
//
//}