<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.dcs.mapper.DcsDocMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.dcs.dto.DcsDocWithPermission">
        SELECT d.*, p.permission
        FROM dcs_doc d
        LEFT JOIN system_doc_type_dept_permission p
        ON d.type_id = p.type_id
        ${ew.customSqlSegment}
    </select>
</mapper>