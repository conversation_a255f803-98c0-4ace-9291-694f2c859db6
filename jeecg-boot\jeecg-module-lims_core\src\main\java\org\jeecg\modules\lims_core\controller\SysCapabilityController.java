package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysCapability;
import org.jeecg.modules.lims_core.service.ISysCapabilityService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 检测能力库
 * @Author: jeecg-boot
 * @Date:   2025-04-10
 * @Version: V1.0
 */
@Tag(name="检测能力库")
@RestController
@RequestMapping("/lims_order/sysCapability")
@Slf4j
public class SysCapabilityController extends JeecgController<SysCapability, ISysCapabilityService> {
	@Autowired
	private ISysCapabilityService sysCapabilityService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysCapability
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "检测能力库-分页列表查询")
	@Operation(summary="检测能力库-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysCapability>> queryPageList(SysCapability sysCapability,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysCapability> queryWrapper = QueryGenerator.initQueryWrapper(sysCapability, req.getParameterMap());
		Page<SysCapability> page = new Page<SysCapability>(pageNo, pageSize);
		IPage<SysCapability> pageList = sysCapabilityService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysCapability
	 * @return
	 */
	@AutoLog(value = "检测能力库-添加")
	@Operation(summary="检测能力库-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysCapability sysCapability) {
		sysCapabilityService.save(sysCapability);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysCapability
	 * @return
	 */
	@AutoLog(value = "检测能力库-编辑")
	@Operation(summary="检测能力库-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysCapability sysCapability) {
		sysCapabilityService.updateById(sysCapability);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检测能力库-通过id删除")
	@Operation(summary="检测能力库-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysCapabilityService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检测能力库-批量删除")
	@Operation(summary="检测能力库-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysCapabilityService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检测能力库-通过id查询")
	@Operation(summary="检测能力库-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysCapability> queryById(@RequestParam(name="id",required=true) String id) {
		SysCapability sysCapability = sysCapabilityService.getById(id);
		if(sysCapability==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysCapability);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysCapability
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysCapability sysCapability) {
        return super.exportXls(request, sysCapability, SysCapability.class, "检测能力库");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysCapability.class);
    }

	 /**
	  *  通用字段修改
	  *
	  * @param map
	  * @return
	  */
	 @AutoLog(value = "通用字段修改")
	 @Operation(summary="通用字段修改")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:sys_capability:general_field_modification')")
	 @RequestMapping(value = "/batchUpdate", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> batchUpdate(@RequestBody Map<String,Object> map) {
		 sysCapabilityService.batchUpdate(map);
		 return Result.OK("编辑成功!");
	 }
}
