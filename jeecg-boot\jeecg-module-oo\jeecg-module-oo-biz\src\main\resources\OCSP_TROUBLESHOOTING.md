# OCSP问题诊断和解决指南

## 概述

本指南帮助诊断和解决PDF签名中的OCSP（在线证书状态协议）相关问题。OCSP用于验证数字证书的撤销状态，是LTV（长期验证）功能的重要组成部分。

## 常见OCSP问题

### 1. "no OCSP response available" 错误

**症状：**
- 日志显示 "no OCSP response available"
- LTV验证失败
- Adobe Reader无法验证签名的长期有效性

**可能原因：**
1. OCSP服务器暂时不可用
2. 网络连接问题
3. 防火墙阻止OCSP请求
4. 证书已过期或被撤销
5. OCSP服务器配置错误

### 2. OCSP连接超时

**症状：**
- 连接超时错误
- 响应时间过长

**可能原因：**
1. 网络延迟
2. OCSP服务器负载过高
3. DNS解析问题

### 3. OCSP响应格式错误

**症状：**
- 收到响应但格式验证失败
- 响应状态非成功

**可能原因：**
1. 服务器返回错误格式
2. 证书链不完整
3. 时间同步问题

## 诊断步骤

### 1. 使用内置诊断工具

系统提供了自动诊断工具，当OCSP失败时会自动运行：

```java
// 快速连接测试
OcspDiagnosticTool.quickOcspConnectivityTest();

// 完整诊断（可选）
OcspDiagnosticTool.runFullDiagnostic(certificate, issuer);
```

### 2. 手动检查网络连接

```bash
# 测试OCSP服务器连接
curl -I http://ocsp.gdca.com.cn/ocsp
curl -I http://ocsp2.gdca.com.cn/ocsp

# 检查DNS解析
nslookup ocsp.gdca.com.cn
nslookup ocsp2.gdca.com.cn
```

### 3. 检查证书信息

- 验证证书有效期
- 检查证书链完整性
- 确认AIA扩展中的OCSP URL

### 4. 分析日志输出

查看详细的OCSP处理日志：
- 证书信息
- 网络连接状态
- OCSP响应分析
- 错误详情

## 解决方案

### 1. 网络问题解决

**防火墙配置：**
- 允许出站HTTP/HTTPS连接到OCSP服务器
- 端口：80 (HTTP), 443 (HTTPS)

**代理设置：**
```java
// 如果使用代理，配置系统属性
System.setProperty("http.proxyHost", "proxy.company.com");
System.setProperty("http.proxyPort", "8080");
```

### 2. 备用OCSP服务器配置

编辑 `ocsp-servers.properties` 文件：

```properties
# GDCA备用服务器
ca.gdca.backup=http://ocsp.gdca.com.cn/ocsp,http://ocsp1.gdca.com.cn/ocsp,http://ocsp3.gdca.com.cn/ocsp

# CFCA备用服务器
ca.cfca.backup=http://ocsp1.cfca.com.cn,http://ocsp2.cfca.com.cn

# 通用配置
ocsp.connect.timeout=10000
ocsp.read.timeout=10000
ocsp.retry.attempts=3
ocsp.retry.delay=1000
```

### 3. 超时和重试配置

在 `OcspUtil` 中调整超时设置：

```java
private static final int DEFAULT_CONNECT_TIMEOUT = 15000; // 15秒
private static final int DEFAULT_READ_TIMEOUT = 15000; // 15秒
private static final int MAX_RETRY_ATTEMPTS = 5; // 5次重试
```

### 4. 模拟OCSP响应

当所有OCSP服务器都不可用时，系统会自动创建模拟响应：

```java
// 自动创建模拟响应以保持LTV结构完整性
byte[] mockResponse = OcspUtil.createMockOcspResponse(certificate, issuer);
```

**注意：** 模拟响应仅用于保持PDF结构完整性，不提供实际的撤销状态验证。

## 监控和维护

### 1. 日志监控

监控以下关键日志：
- OCSP连接成功/失败率
- 响应时间
- 错误类型分布

### 2. 定期测试

```java
// 定期运行连接测试
@Scheduled(fixedRate = 300000) // 每5分钟
public void testOcspConnectivity() {
    boolean result = OcspDiagnosticTool.quickOcspConnectivityTest();
    if (!result) {
        // 发送告警
        alertService.sendOcspConnectivityAlert();
    }
}
```

### 3. 性能优化

- 启用OCSP响应缓存
- 使用连接池
- 异步处理非关键OCSP请求

## 故障排除清单

### 基本检查
- [ ] 网络连接正常
- [ ] DNS解析正常
- [ ] 防火墙允许OCSP流量
- [ ] 证书未过期
- [ ] 证书链完整

### 配置检查
- [ ] OCSP服务器URL正确
- [ ] 超时设置合理
- [ ] 重试机制启用
- [ ] 备用服务器配置

### 高级诊断
- [ ] 运行完整诊断工具
- [ ] 检查OCSP响应内容
- [ ] 分析网络抓包
- [ ] 联系CA提供商

## 联系支持

如果问题仍然存在，请收集以下信息联系技术支持：

1. 完整的错误日志
2. 证书信息（主体、颁发者、序列号）
3. 网络环境描述
4. 诊断工具输出
5. 问题复现步骤

## 相关文档

- [LTV功能文档](LTV_README.md)
- [OCSP配置指南](ocsp-servers.properties)
- [性能优化指南](performance-optimization.md)
- [安全最佳实践](security-best-practices.md)