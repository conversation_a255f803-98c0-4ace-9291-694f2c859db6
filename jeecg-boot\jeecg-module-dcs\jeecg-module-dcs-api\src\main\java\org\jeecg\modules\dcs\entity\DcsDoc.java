package org.jeecg.modules.dcs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: dcs_doc
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
@Data
@TableName("dcs_doc")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="dcs_doc")
public class DcsDoc implements Serializable {
    private static final long serialVersionUID = 1L;

	/**ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "ID")
    private String id;
	/**编号*/
	@Excel(name = "编号", width = 15)
    @Schema(description = "编号")
    private String docNo;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private String docName;
	/**版本*/
	@Excel(name = "版本", width = 15)
    @Schema(description = "版本")
    private Integer ver;
	/**存储地址*/
	@Excel(name = "存储地址", width = 15)
    @Schema(description = "存储地址")
    private String url;
	/**类型*/
	@Excel(name = "类型", width = 15, dictTable = "system_doc_type", dicText = "type_name", dicCode = "id")
	@Dict(dictTable = "system_doc_type", dicText = "type_name", dicCode = "id")
    @Schema(description = "类型")
    private String typeId;
	/**涉及部门*/
	@Excel(name = "涉及部门", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "涉及部门")
    private String relatedDeptId;
    /**审批结果  申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付； 0 已发布*/
    @Excel(name = "审批结果", width = 15, dicCode = "wework_bpm_status")
    @Dict(dicCode = "wework_bpm_status")
    @Schema(description = "审批结果")
    private Integer status;
	/**流程实例的编号*/
	@Excel(name = "流程实例的编号", width = 15)
    @Schema(description = "流程实例的编号")
    private String processInstanceId;
    /**修订版ID*/
    @Excel(name = "修订版ID", width = 15)
    @Schema(description = "修订版ID")
    private String revisedId;
	/**上传人*/
    @Schema(description = "起草人")
    private String createBy;
	/**上传时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "起草时间")
    private Date createTime;
	/**生效日期*/
	@Excel(name = "生效日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "生效日期")
    private Date effectiveDate;
	/**废止日期*/
	@Excel(name = "废止日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "废止日期")
    private Date abandonDate;
    /**协同起草人*/
    @Excel(name = "协同起草人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "协同起草人")
    private String coDrafters;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "dcs_doc_status")
	@Dict(dicCode = "dcs_doc_status")
    @Schema(description = "状态")
    private String effectiveStatus;
	/**更新者*/
    @Schema(description = "更新者")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**租户编号*/
	@Excel(name = "租户编号", width = 15)
    @Schema(description = "租户编号")
    private Integer tenantId;
    /**审批类型*/
    @Excel(name = "审批类型", width = 15, dicCode = "wework_apply_type")
    @Dict(dicCode = "wework_apply_type")
    @Schema(description = "审批类型")
    private String applyTypeId;

    /**复审时间*/
    @Excel(name = "复审时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "复审时间")
    private Date reviewTime;
    /**培训*/
    @Excel(name = "培训", width = 15)
    @Schema(description = "培训")
    private String trainingId;
    /**引用文件*/
    @Excel(name = "引用文件", width = 15)
    @Schema(description = "引用文件")
    private String refedDocs;
}
