package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 产品套餐
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Schema(description="产品套餐")
@Data
@TableName("sys_product_package")
public class SysProductPackage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
    /**业务类别*/
    @Excel(name = "业务类别", width = 15, dicCode = "biz_type")
    @Dict(dictTable = "biz_type", dicText = "name", dicCode = "id")
    @Schema(description = "业务类别")
    private java.lang.String bizTypeId;
	/**标准单价*/
	@Excel(name = "标准单价", width = 15)
    @Schema(description = "标准单价")
    private java.lang.Double stdPrice;
	/**标准工时*/
	@Excel(name = "标准工时", width = 15)
    @Schema(description = "标准工时")
    private java.lang.String stdTat;
	/**分类*/
	@Excel(name = "分类", width = 15, dicCode = "package_category")
    @Dict(dicCode = "package_category")
    @Schema(description = "分类")
    private java.lang.String category;
	/**标准*/
	@Excel(name = "标准", width = 15, dictTable = "sys_standard", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_standard", dicText = "name", dicCode = "id")
    @Schema(description = "标准")
    private java.lang.String standardId;

    @TableField(exist = false)
    private String standardName;
}
