package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodConsumptive;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 试剂耗材
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodConsumptiveService extends IService<SysMethodConsumptive> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysMethodConsumptive>
	 */
	public List<SysMethodConsumptive> selectByMainId(String mainId);
}
