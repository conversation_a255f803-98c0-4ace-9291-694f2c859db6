package org.jeecg.modules.lims_core.rule;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.lims_core.entity.Solution;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 溶液配制编号规则
 */
public class SolutionNoRule implements IFillRuleHandler {
    @Override
    public Object execute(JSONObject params, JSONObject formData) {
        String prefix = "";
        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("sysFillRuleServiceImpl");
        // 根据 ruleCode 查询出实体
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("rule_code", params.get("rule_code"));
        JSONObject entity = JSONObject.parseObject(JSONObject.toJSONString(impl.getOne(queryWrapper)));

        if (params != null) {
            Object obj = params.get("prefix");
            if (obj != null) prefix = obj.toString();
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        if(entity.getString("snDate") != null && entity.getString("snDate").equals(format.format(new Date()))){
            int sn = entity.getInteger("sn") + 1;
            entity.put("sn",sn);
            impl.updateById(entity);
        }else{
            entity.put("sn",1);
            entity.put("snDate",format.format(new Date()));
            impl.updateById(entity);
        }

        String value = prefix + format.format(new Date());
        // 根据formData的值的不同，生成不同的号
        String solutionTypeId = formData.getString("solutionTypeId");
        if (!StringUtils.isEmpty(solutionTypeId)) {
            value = solutionTypeId+value;
        }
        ServiceImpl implSolu = (ServiceImpl) SpringContextUtils.getBean("SolutionServiceImpl");
        QueryWrapper<Solution> queryWrapperSolu = new QueryWrapper<>();
        queryWrapperSolu.likeRight("code", value).orderByDesc("create_time");
        List<Solution> list = implSolu.list(queryWrapperSolu);
        if (!list.isEmpty()) {
            Solution latest = list.get(0);
            String code = latest.getCode();
            String expectedPrefix = value;
            if (code.startsWith(expectedPrefix) && code.length() >= expectedPrefix.length() + 3) {
                String last = code.substring(code.length() - 3);
                try {
                    int seqNum = Integer.parseInt(last);
                    seqNum += 1;
                    last = StrUtil.padPre(String.valueOf(seqNum), 3, '0');
                    value = expectedPrefix + last;
                } catch (NumberFormatException e) {
                    value = expectedPrefix + "001";
                }
            } else {
                value = expectedPrefix + "001";
            }
        } else {
            value = value + "001";
        }
        return value;
    }
}
