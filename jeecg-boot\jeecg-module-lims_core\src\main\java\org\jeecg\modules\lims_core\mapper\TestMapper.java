package org.jeecg.modules.lims_core.mapper;

import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.lims_core.entity.Test;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Description: 试验标本
 * @Author: jeecg-boot
 * @Date:   2025-01-06
 * @Version: V1.0
 */
public interface TestMapper extends BaseMapper<Test> {

    @Select("select * from test where sample_id = #{sampleId}")
    List<Test> selectBySampleId(String sampleId);

    @Select("select * from test where sample_id = #{sampleId} and method_id = #{methodId}")
    List<Test> selectBySampleIdAndMethodId(String sampleId, String methodId);

    @Select("select * from test where sample_id = #{sampleId} and product_id = #{productId}")
    Test selectBySampleIdAndProductId(String sampleId, String productId);

    @Select("select * from test where task_id = #{taskId}")
    List<Test> selectByTaskId(String taskId);
}
