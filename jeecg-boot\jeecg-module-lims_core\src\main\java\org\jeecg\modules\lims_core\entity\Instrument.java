package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 设备
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Data
@TableName("instrument")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="设备")
public class Instrument implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**编号*/
	@Excel(name = "编号", width = 15)
    @Schema(description = "编号")
    private java.lang.String code;
	/**类型*/
	@Excel(name = "类型", width = 15, dictTable = "sys_instrument_type", dicText = "abbr_name", dicCode = "id")
	@Dict(dictTable = "sys_instrument_type", dicText = "abbr_name", dicCode = "id")
    @Schema(description = "类型")
    private java.lang.String typeId;
	/**供应商*/
	@Excel(name = "供应商", width = 15, dictTable = "sys_supplier", dicText = "abbr_name", dicCode = "id")
	@Dict(dictTable = "sys_supplier", dicText = "abbr_name", dicCode = "id")
    @Schema(description = "供应商")
    private java.lang.String supplierId;
	/**型号规格*/
	@Excel(name = "型号规格", width = 15)
    @Schema(description = "型号规格")
    private java.lang.String model;
	/**购入时间*/
	@Excel(name = "购入时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "购入时间")
    private java.util.Date purchaseDate;
	/**最新校准时间*/
	@Excel(name = "最新校准时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最新校准时间")
    private java.util.Date lastCalibrationDate;
	/**校准周期（天）*/
	@Excel(name = "校准周期（天）", width = 15)
    @Schema(description = "校准周期（天）")
    private java.lang.Integer calibrationCycle;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
