package org.jeecg.modules.dcs.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.dcs.entity.Training;
import org.jeecg.modules.dcs.entity.TrainingRecord;
import org.jeecg.modules.dcs.vo.TrainingVo;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 培训
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
public interface ITrainingService extends IService<Training> {

	/**
	 * 添加一对多
	 *
	 * @param training
	 * @param trainingRecordList
	 */
	public void saveMain(TrainingVo training,List<TrainingRecord> trainingRecordList) ;
	
	/**
	 * 修改一对多
	 *
   * @param training
   * @param trainingRecordList
	 */
	public void updateMain(TrainingVo training,List<TrainingRecord> trainingRecordList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);


	@Transactional(rollbackFor = Exception.class)
	void confirmBatch(Collection<? extends Serializable> idList);

	public IPage<TrainingVo> queryPageList(Page<TrainingVo> page,
										   @Param(Constants.WRAPPER) Wrapper<Training> wrapper);

	public TrainingVo getVoById(String id);

	void createMeeting(String id);

	void checkin(String id);

	void raiseCheackIn(String id);

	void activateDoc(String id);
}
