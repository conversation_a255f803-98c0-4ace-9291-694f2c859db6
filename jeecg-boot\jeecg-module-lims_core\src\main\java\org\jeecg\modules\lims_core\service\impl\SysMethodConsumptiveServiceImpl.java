package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodConsumptive;
import org.jeecg.modules.lims_core.mapper.SysMethodConsumptiveMapper;
import org.jeecg.modules.lims_core.service.ISysMethodConsumptiveService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 试剂耗材
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodConsumptiveServiceImpl extends ServiceImpl<SysMethodConsumptiveMapper, SysMethodConsumptive> implements ISysMethodConsumptiveService {
	
	@Autowired
	private SysMethodConsumptiveMapper sysMethodConsumptiveMapper;
	
	@Override
	public List<SysMethodConsumptive> selectByMainId(String mainId) {
		return sysMethodConsumptiveMapper.selectByMainId(mainId);
	}
}
