package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.util.SqlInjectionUtil;
import org.jeecg.modules.lims_core.entity.SysTranslation;
import org.jeecg.modules.lims_core.mapper.SysTranslationMapper;
import org.jeecg.modules.lims_core.service.ISysTranslationService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Map;

/**
 * @Description: 翻译
 * @Author: jeecg-boot
 * @Date:   2025-05-23
 * @Version: V1.0
 */
@Service
public class SysTranslationServiceImpl extends ServiceImpl<SysTranslationMapper, SysTranslation> implements ISysTranslationService {

    @Override
    public void customSave(Map<String, String> map) {
        if(map.get("baseId")==null||"".equals(map.get("baseId"))){
            throw new RuntimeException("baseId不能为空");
        }
        if(map.get("baseTable")==null||"".equals(map.get("baseTable"))){
            throw new RuntimeException("baseTable不能为空");
        }
        String baseId = map.get("baseId");
        String baseTable = map.get("baseTable");

        map.forEach((k,v)->{
            // 跳过 baseId 和 baseTable 字段
            if ("baseId".equals(k) || "baseTable".equals(k)) {
                return;
            }
            SysTranslation sysTranslation = new SysTranslation()
                    .setSourceId(baseId)
                    .setContext(baseTable)
                    .setFieldName(SqlInjectionUtil.getSqlInjectSortField(k))
                    .setTranslation(v);
            // 检查是否已存在相同的记录
            SysTranslation existingTranslation = this.getOne(new QueryWrapper<SysTranslation>()
                    .eq("source_id", baseId)
                    .eq("context", baseTable)
                    .eq("field_name", SqlInjectionUtil.getSqlInjectSortField(k)));
            if (existingTranslation != null) {
                // 如果存在，更新记录
                existingTranslation.setTranslation(v);
                this.updateById(existingTranslation);
            } else {
                // 如果不存在，插入新记录
                this.save(sysTranslation);
            }

        });
    }
}
