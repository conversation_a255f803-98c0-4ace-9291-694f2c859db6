package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodAnalyteRouding;
import org.jeecg.modules.lims_core.mapper.SysMethodAnalyteRoudingMapper;
import org.jeecg.modules.lims_core.service.ISysMethodAnalyteRoudingService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 修约要求
 * @Author: jeecg-boot
 * @Date:   2025-03-26
 * @Version: V1.0
 */
@Service
public class SysMethodAnalyteRoudingServiceImpl extends ServiceImpl<SysMethodAnalyteRoudingMapper, SysMethodAnalyteRouding> implements ISysMethodAnalyteRoudingService {

}
