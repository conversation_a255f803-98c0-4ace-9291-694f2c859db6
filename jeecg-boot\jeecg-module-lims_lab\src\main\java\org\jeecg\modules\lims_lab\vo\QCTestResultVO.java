package org.jeecg.modules.lims_lab.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class QCTestResultVO {
    private String id;
    /**标本ID*/
    @Schema(description = "标本条码")
    private String testId;
    @Schema(description = "样品编号")
    private String sampleNo;
    @Schema(description = "方法")
    private String methodId;
    @Schema(description = "方法名称")
    private String methodName;
    /**指标*/
    @Schema(description = "指标")
    private String analyte;
    /**检测结果*/
    @Schema(description = "溶液浓度")
    private String rawResult;
    /**峰面积*/
    @Schema(description = "峰面积")
    private String peakArea;
    /**检测人员*/
    @Schema(description = "检测人员")
    private String updateBy;
    /**检测时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检测时间")
    private Date updateTime;
    @Schema(description = "审核人员")
    private String checkedBy;
    /**检测时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审核时间")
    private Date checkedTime;
}
