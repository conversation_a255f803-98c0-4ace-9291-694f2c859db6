package org.jeecg.modules.lims_core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.crm.entity.SysCustomer;
import org.jeecg.modules.crm.entity.SysCustomerContact;
import org.jeecg.modules.crm.service.ISysCustomerContactService;
import org.jeecg.modules.crm.service.ISysCustomerService;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.vo.*;
import org.jeecg.modules.lims_core.util.GBDateUtils;
import org.jeecg.modules.lims_order.entity.BizOrder;
import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.service.IBizOrderService;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description: 样品
 * @Author: jeecg-boot
 * @Date:   2025-01-06
 * @Version: V1.0
 */
@Service
public class SampleServiceImpl extends ServiceImpl<SampleMapper, Sample> implements ISampleService {
    @Autowired
    private IBizOrderService bizOrderServiceImpl;
    @Autowired
    private SysStandardEvaluationLimtServiceImpl sysStandardEvaluationLimtServiceImpl;
    @Autowired
    private ISysCustomerService iSysCustomerService;
    @Autowired
    private ISysCustomerContactService iSysCustomerContactService;
    @Autowired
    private IAttachmentService iAttachmentService;
    @Autowired
    private IStandardMaterialService iStandardMaterialService;
    @Autowired
    private IHolidayCalendarService iHolidayCalendarService;
    @Autowired
    private ISysWorkflowStepService iSysWorkflowStepService;



    @Autowired
    private TestTaskMapper testTaskMapper;
    @Autowired
    private SysMethodMapper sysMethodMapper;
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
    @Autowired
    private TestTaskServiceImpl testTaskServiceImpl;
    @Autowired
    private SysMethodAnalyteMapper sysMethodAnalyteMapper;
    @Autowired
    private SysProductPackageMapper sysProductPackageMapper;
    @Autowired
    private IQuotationService quotationServiceImpl;
    @Autowired
    private TestTaskServiceImpl testTaskService;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private TestResultMapper testResultMapper;

    @Autowired
    private SysCapabilityMapper sysCapabilityMapper;

    @Autowired
    private SysStandardMapper sysStandardMapper;

    @Autowired
    RdProjectMapper rdProjectMapper;

    @Autowired
    private ISysDictItemService sysDictItemService;

    @Autowired
    private IInventoryService inventoryService;

    @Autowired
    private ISysWarehouseBoxService sysWarehouseBox;
    @Autowired
    private ISysCustomerContactService sysCustomerContactService;
    @Autowired
    private ISysCustomerService sysCustomerService;
    @Autowired
    TestTaskFlowMapper testTaskFlowMapper;
    private static final String SAMPLE_FLOW_STATUS_1 = "未入库";
    private static final String SAMPLE_FLOW_STATUS_2 = "已入库";
    private static final String FLOW_STEP_1 = "业务受理";
    private static final String FLOW_STEP_3 = "PM确认";
    private static final String TASK_STATUS_0 = "未指派";
    private static final String TASK_STATUS_1 = "进行中";
    /**
     * @Description: 样品
     * @Author: jeecg-boot
     * @Date:   2025-04-03
     * @Version: V1.0
     */
        @Override
        public void addSample(Sample sample) {
            //新增时设置hasChild为0
            sample.setHasChild(ISampleService.NOCHILD);
            if(oConvertUtils.isEmpty(sample.getPid())){
                sample.setPid(ISampleService.ROOT_PID_VALUE);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("biz_type_id", sample.getBizTypeId());
                if (sample.getSampleNo() == null || !sample.getSampleNo().matches("^[A-Z]{3}\\d{9}$")) {
                    sample.setSampleNo(FillRuleUtil.executeRule(FillRuleConstant.SAMPLE, jsonObject).toString());
                }
                if(sample.getIsGenerateRdNo().equals("1")){
                    if (sample.getRdId() == null || sample.getRdId().equals("")) {
                        RdProject rdProject = new RdProject();
                        rdProject.setRdNo(FillRuleUtil.executeRule(FillRuleConstant.RD, jsonObject).toString());
                        rdProject.setQuotationId(sample.getQuotationId());
                        rdProject.setName(sample.getRdName());
                        rdProjectMapper.insert(rdProject);
                        sample.setRdId(rdProject.getId());

                    }
                }

                bizOrderServiceImpl.list(new QueryWrapper<BizOrder>().eq("quotation_id", sample.getQuotationId())).forEach(bizOrder -> {
                        sample.setOrderId(bizOrder.getId());
                });
            }else{
                //如果当前节点父ID不为空 则设置父节点的hasChildren 为1
                Sample parent = baseMapper.selectById(sample.getPid());
                if(parent!=null && !"1".equals(parent.getHasChild())){
                    parent.setHasChild("1");
                    baseMapper.updateById(parent);
                }
                if(parent.getReceiveDate() != null){
                    sample.setReceiveDate(parent.getReceiveDate());
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("biz_type_id", parent.getBizTypeId());
                    if (sample.getSampleNo() == null || !sample.getSampleNo().matches("^[A-Z]{3}\\d{9}$")) {
                        sample.setSampleNo(FillRuleUtil.executeRule(FillRuleConstant.SAMPLE, jsonObject).toString());
                    }
                }
            }
            sample.setSampleFlowStatus(SAMPLE_FLOW_STATUS_1);
            //---如果已经转了合同,再新建产品,订单状态改为PM未确认
            Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
            if (q.getStatusId().equals("5")) {
                q.setStatusId("11");
                quotationServiceImpl.updateById(q);
            }
            baseMapper.insert(sample);
        }

        @Override
        public void updateSample(Sample sample) {
            Sample entity = this.getById(sample.getId());
            if(entity==null) {
                throw new JeecgBootException("未找到对应实体");
            }
            String old_pid = entity.getPid();
            String new_pid = sample.getPid();
            if(!old_pid.equals(new_pid)) {
                updateOldParentNode(old_pid);
                if(oConvertUtils.isEmpty(new_pid)){
                    sample.setPid(ISampleService.ROOT_PID_VALUE);
                }
                if(!ISampleService.ROOT_PID_VALUE.equals(sample.getPid())) {
                    baseMapper.updateTreeNodeStatus(sample.getPid(), ISampleService.HASCHILD);
                }
            }
            if(sample.getReceiveDate() != null ){
                if(sample.getDayType() != null && sample.getDayType().equals("工作日")){
                    Date workDate = iHolidayCalendarService.getWorkDate(sample.getReceiveDate(), sample.getPmLeadTime());
                    sample.setTestEndDate(workDate);
                }else{
                    sample.setTestEndDate(new Date(sample.getReceiveDate().getTime() + sample.getPmLeadTime() * 24 * 60 * 60 * 1000));
                }
                reportMapper.selectBySampleId(sample.getId()).forEach(report -> {
                    if(sample.getDayType() != null && sample.getDayType().equals("工作日")){
                        Date workDate = iHolidayCalendarService.getWorkDate(sample.getReceiveDate(), sample.getPmLeadTime());
                        report.setDeadLine(workDate);
                    }else{
                        report.setDeadLine(new Date(sample.getReceiveDate().getTime() + sample.getPmLeadTime() * 24 * 60 * 60 * 1000));
                    }
                });
            }
            if((sample.getRdId() != null && entity.getRdId() == null) || (sample.getRdId() !=null && !sample.getRdId().equals(entity.getRdId()))) {
                testTaskMapper.selectBySampleId(sample.getId()).forEach(testTask -> {
                    testTask.setRdId(sample.getRdId());
                    testTaskMapper.updateById(testTask);
                });
            }

            baseMapper.updateById(sample);
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public void deleteSample(String id) throws JeecgBootException {
            //查询选中节点下所有子节点一并删除
            id = this.queryTreeChildIds(id);
            if(id.indexOf(",")>0) {
                StringBuffer sb = new StringBuffer();
                String[] idArr = id.split(",");
                for (String idVal : idArr) {
                    if(idVal != null){
                        Sample sample = this.getById(idVal);
                        String pidVal = sample.getPid();
                        //查询此节点上一级是否还有其他子节点
                        List<Sample> dataList = baseMapper.selectList(new QueryWrapper<Sample>().eq("pid", pidVal).notIn("id",Arrays.asList(idArr)));
                        boolean flag = (dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal) && !sb.toString().contains(pidVal);
                        if(flag){
                            //如果当前节点原本有子节点 现在木有了，更新状态
                            sb.append(pidVal).append(",");
                        }
                    }
                }
                //批量删除节点
                baseMapper.deleteBatchIds(Arrays.asList(idArr));
                //修改已无子节点的标识
                String[] pidArr = sb.toString().split(",");
                for(String pid : pidArr){
                    this.updateOldParentNode(pid);
                }
            }else{
                Sample sample = this.getById(id);
                if(sample==null) {
                    throw new JeecgBootException("未找到对应实体");
                }
                updateOldParentNode(sample.getPid());
                baseMapper.deleteById(id);
            }
        }

        @Override
        public List<Sample> queryTreeListNoPage(QueryWrapper<Sample> queryWrapper) {
            List<Sample> dataList = baseMapper.selectList(queryWrapper);
            List<Sample> mapList = new ArrayList<>();
            for(Sample data : dataList){
                String pidVal = data.getPid();
                //递归查询子节点的根节点
                if(pidVal != null && !ISampleService.NOCHILD.equals(pidVal)){
                    Sample rootVal = this.getTreeRoot(pidVal);
                    if(rootVal != null && !mapList.contains(rootVal)){
                        mapList.add(rootVal);
                    }
                }else{
                    if(!mapList.contains(data)){
                        mapList.add(data);
                    }
                }
            }
            return mapList;
        }

        @Override
        public List<SelectTreeModel> queryListByCode(String parentCode) {
            String pid = ROOT_PID_VALUE;
            if (oConvertUtils.isNotEmpty(parentCode)) {
                LambdaQueryWrapper<Sample> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Sample::getPid, parentCode);
                List<Sample> list = baseMapper.selectList(queryWrapper);
                if (list == null || list.size() == 0) {
                    throw new JeecgBootException("该编码【" + parentCode + "】不存在，请核实!");
                }
                if (list.size() > 1) {
                    throw new JeecgBootException("该编码【" + parentCode + "】存在多个，请核实!");
                }
                pid = list.get(0).getId();
            }
            return baseMapper.queryListByPid(pid, null);
        }

        @Override
        public List<SelectTreeModel> queryListByPid(String pid) {
            if (oConvertUtils.isEmpty(pid)) {
                pid = ROOT_PID_VALUE;
            }
            return baseMapper.queryListByPid(pid, null);
        }

        /**
         * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
         * @param pid
         */
        private void updateOldParentNode(String pid) {
            if(!ISampleService.ROOT_PID_VALUE.equals(pid)) {
                Long count = baseMapper.selectCount(new QueryWrapper<Sample>().eq("pid", pid));
                if(count==null || count<=1) {
                    baseMapper.updateTreeNodeStatus(pid, ISampleService.NOCHILD);
                }
            }
        }

        /**
         * 递归查询节点的根节点
         * @param pidVal
         * @return
         */
        private Sample getTreeRoot(String pidVal){
            Sample data =  baseMapper.selectById(pidVal);
            if(data != null && !ISampleService.ROOT_PID_VALUE.equals(data.getPid())){
                return this.getTreeRoot(data.getPid());
            }else{
                return data;
            }
        }

        /**
         * 根据id查询所有子节点id
         * @param ids
         * @return
         */
        private String queryTreeChildIds(String ids) {
            //获取id数组
            String[] idArr = ids.split(",");
            StringBuffer sb = new StringBuffer();
            for (String pidVal : idArr) {
                if(pidVal != null){
                    if(!sb.toString().contains(pidVal)){
                        if(sb.toString().length() > 0){
                            sb.append(",");
                        }
                        sb.append(pidVal);
                        this.getTreeChildIds(pidVal,sb);
                    }
                }
            }
            return sb.toString();
        }

        /**
         * 递归查询所有子节点
         * @param pidVal
         * @param sb
         * @return
         */
        private StringBuffer getTreeChildIds(String pidVal,StringBuffer sb){
            List<Sample> dataList = baseMapper.selectList(new QueryWrapper<Sample>().eq("pid", pidVal));
            if(dataList != null && dataList.size()>0){
                for(Sample tree : dataList) {
                    if(!sb.toString().contains(tree.getId())){
                        sb.append(",").append(tree.getId());
                    }
                    this.getTreeChildIds(tree.getId(),sb);
                }
            }
            return sb;
        }


    @Override
    public List<Map> listDataLog(String id) {
        QueryWrapper<Object> queryWrapper = Wrappers.query();
        queryWrapper.eq("data_id", id).eq("data_table", "sample")
                .orderBy(true, false, "create_time");

        IService impl = (IService) SpringContextUtils.getBean("sysDataLogServiceImpl");
        JSONArray objects = JSONArray.parseArray(JSONObject.toJSONString(impl.list(queryWrapper)));
        return objects.toJavaList(Map.class);
    }

    @Override
  public IPage<QSampleVo> listVo(Page<Sample> page, QueryWrapper<Sample> queryWrapper) {
        Page<Sample> page1 = this.page(page, queryWrapper);
        Page<QSampleVo> qSampleVoPage = new Page<>();
        BeanUtils.copyProperties(page1, qSampleVoPage);



        List<QSampleVo> qSampleVos = new ArrayList<>();
        for (Sample sample : page1.getRecords()) {
            QSampleVo qSampleVo = new QSampleVo();
            BeanUtils.copyProperties(sample, qSampleVo);
            qSampleVo.setLevel(0);
            SysCustomer customer = iSysCustomerService.getById(qSampleVo.getCustomerId());
            qSampleVo.setDetailedAddress(customer.getDetailedAddress());
            SysCustomerContact cc = iSysCustomerContactService.getById(qSampleVo.getCustomerContactId());
            qSampleVo.setPhone(cc.getPhone());

            int step_sort_num = 100;
            for(TestTask task : testTaskMapper.selectBySampleId(qSampleVo.getId())) {
                String curStep = task.getCurStep();
                if(iSysWorkflowStepService.getSortNumByName(curStep) == null){
                    continue;
                }
                if (iSysWorkflowStepService.getSortNumByName(curStep) < step_sort_num) {
                    step_sort_num = iSysWorkflowStepService.getSortNumByName(curStep);
                }
            }
            qSampleVo.setCurStep(iSysWorkflowStepService.getNameBySortNum(step_sort_num));
            if(qSampleVo.getCurStep() == null){
                qSampleVo.setCurStep("-");
            }


            if (qSampleVo.getRdId() != null) {
                RdProject rdProject = rdProjectMapper.selectById(qSampleVo.getRdId());
                if (rdProject != null) {
                    qSampleVo.setRdName(rdProject.getName());
                }
            }

            qSampleVos.add(qSampleVo);
        }

        qSampleVoPage.setRecords(qSampleVos);
        return qSampleVoPage;
    }

    @Override
    @Transactional(
            rollbackFor = {Exception.class}
    )
    public void addProduct(List<SysProductSubVo> productSubVos, String id) {
        Sample sample = this.getById(id);
        //分组
        Map<String, List<SysProductSubVo>> productSubMap = productSubVos.stream()
                .collect(Collectors.groupingBy(SysProductSubVo::getCategory));
        if(productSubMap.get(SysProductSubVo.CATEGORY_STANDARD) != null && productSubMap.get(SysProductSubVo.CATEGORY_STANDARD).size() > 0){
            //分组
            //去除methodid为空
            productSubMap.get(SysProductSubVo.CATEGORY_STANDARD).removeIf(productSubVo -> productSubVo.getMethodId() == null || productSubVo.getMethodId().equals(""));

            //根据sortNum重排一下
            try {
                productSubMap.get(SysProductSubVo.CATEGORY_STANDARD).sort(Comparator.comparingInt(SysProductSubVo::getSortNum));
            }catch (Exception e){
                // 排不了就算数.
            }


            Map<String, List<SysProductSubVo>> methodMap = productSubMap.get(SysProductSubVo.CATEGORY_STANDARD).stream()
                    .collect(Collectors.groupingBy(SysProductSubVo::getMethodId, LinkedHashMap::new, Collectors.toList()));
            methodMap.forEach((key, value) -> {
                List<TestTask> testTasks = testTaskMapper.selectBySampleIdAndMethodId(id, key);
                if(testTasks.size() > 0){
                    List<Test> tests = testMapper.selectByTaskId(testTasks.get(0).getId());
                    if(tests.size() > 0){
                        List<TestResult> testResults = testResultMapper.selectList(new QueryWrapper<TestResult>().eq("test_id", tests.get(0).getId()));
                        //value中的evaluationId跟testResults中的evaluationId对比,如果testResults中没有,就插入
                        for (SysProductSubVo sysProductSubVo : value) {
                            boolean flag = false;
                            for (TestResult testResult : testResults) {
                                if (testResult.getLimitId().equals(sysProductSubVo.getEvaluationId())) {
                                    flag = true;
                                    break;
                                }
                            }
                            if (!flag) {
                                SysStandardEvaluationLimt el = sysStandardEvaluationLimtServiceImpl.getById(sysProductSubVo.getEvaluationId());
                                SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectList( new QueryWrapper<SysMethodAnalyte>().eq("method_id", key).eq("analyte_id", el.getAnalyteId())).get(0);
                                TestResult testResult = new TestResult();
                                testResult.setLimitId(sysProductSubVo.getEvaluationId());
                                testResult.setMethodAnalyteId(sysMethodAnalyte.getId());
                                testTasks.forEach(task -> {
                                    testMapper.selectByTaskId(task.getId()).forEach(t -> {
                                        testResult.setTestId(t.getId());
                                        testResultMapper.insert(testResult);
                                    });
                                });
                            }
                        }
                    }
                }
                else{
                    SysMethod sysMethod = sysMethodMapper.selectById(key);


                    TestTask testTask = new TestTask();
                    testTask.setMethodId(key);
                    testTask.setSampleId(id);
                    testTask.setRdId(sample.getRdId());
                    testTask.setName(sysMethod.getName());
                    testTask.setPpdId(value.get(0).getPpdId());
                    testTask.setCapabilityId(sysMethod.getCid());
                    testTask.setProductId(value.get(0).getProductPackageId());
                    testTask.setStandardPrice(sysMethod.getStdPrice());
                    testTask.setPmPrice(sysMethod.getStdPrice());
                    testTask.setApplyPrice(sysMethod.getStdPrice());
                    testTask.setIsForQuotation(1);
                    testTask.setCurStep(FLOW_STEP_1);
                    testTask.setStatus(TASK_STATUS_0);

                    //设置部门
                    SysCapability sysCapability = sysCapabilityMapper.selectById(sysMethod.getCid());
                    if(sysCapability != null && sysCapability.getDeptId() !=null && !sysCapability.getDeptId().contains(",")){
                        testTask.setDepartmentId(sysCapability.getDeptId());
                    }

                    List<SysMethodRepeatType> sysMethodRepeatTypes = sysMethodRepeatTypeMapper.selectByMainId(sysMethod.getId());
                    if (sysMethodRepeatTypes.size() > 0) {
                        testTask.setRepeatTypeId(sysMethodRepeatTypes.get(0).getId());
                        testTask.setRepeatTypeNextId(sysMethodRepeatTypes.get(0).getNextId());
                    }
                    testTaskMapper.insert(testTask);

                    //插入flow
                    TestTaskFlow testTaskFlow = new TestTaskFlow();
                    testTaskFlow.setTaskId(testTask.getId());
                    testTaskFlow.setStepId(FLOW_STEP_1);
                    testTaskFlowMapper.insert(testTaskFlow);

                    if(sample.getOrderId() != null && !sample.getOrderId().equals("") && sample.getSampleFlowStatus().equals("已入库")){
                        Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
                        if (q.getStatusId().equals("5")) {
                            testTask.setCurStep(FLOW_STEP_3);
                            testTaskMapper.updateById(testTask);

                            TestTaskFlow testTaskFlow1 = new TestTaskFlow();
                            testTaskFlow1.setTaskId(testTask.getId());
                            testTaskFlow1.setStepId(FLOW_STEP_3);
                            testTaskFlowMapper.insert(testTaskFlow1);
                        }

                    }


                    testTaskServiceImpl.generateTest(testTask,value);

                }
            });
        }
        else if(productSubMap.get(SysProductSubVo.CATEGORY_CAPABILITY).size() > 0){

            //去除capabilityid为空
            productSubMap.get(SysProductSubVo.CATEGORY_CAPABILITY).removeIf(productSubVo -> productSubVo.getCapabilityId() == null || productSubVo.getCapabilityId().equals(""));

            productSubMap.get(SysProductSubVo.CATEGORY_CAPABILITY).forEach(productSubVo -> {
                SysCapability sysCapability = sysCapabilityMapper.selectById(productSubVo.getCapabilityId());
                TestTask testTask = new TestTask();
                testTask.setSampleId(id);
                testTask.setRdId(sample.getRdId());
                testTask.setName(sysCapability.getName());
                testTask.setPpdId(productSubVo.getPpdId());
                testTask.setCapabilityId(productSubVo.getCapabilityId());
                testTask.setProductId(productSubVo.getProductPackageId());
                testTask.setStandardPrice(productSubVo.getStdPrice());
                testTask.setPmPrice(productSubVo.getStdPrice());
                testTask.setApplyPrice(productSubVo.getStdPrice());

                //设置部门
                if(sysCapability != null && sysCapability.getDeptId() !=null && !sysCapability.getDeptId().contains(",")){
                    testTask.setDepartmentId(sysCapability.getDeptId());
                }

                testTask.setCurStep(FLOW_STEP_1);
                testTask.setIsForQuotation(1);
                testTaskMapper.insert(testTask);
                //插入flow
                TestTaskFlow testTaskFlow = new TestTaskFlow();
                testTaskFlow.setTaskId(testTask.getId());
                testTaskFlow.setStepId(FLOW_STEP_1);
                testTaskFlowMapper.insert(testTaskFlow);
                if(productSubVo.getMethodId() !=null){
                    testTask.setMethodId(productSubVo.getMethodId());
                    testTaskMapper.updateById(testTask);
                    testTaskServiceImpl.generateTest(testTask, null);
                }

                if(sample.getOrderId() != null && !sample.getOrderId().equals("") && sample.getSampleFlowStatus().equals("已入库")){
                    testTask.setCurStep(FLOW_STEP_3);
                    testTaskMapper.updateById(testTask);

                    TestTaskFlow testTaskFlow1 = new TestTaskFlow();
                    testTaskFlow1.setTaskId(testTask.getId());
                    testTaskFlow1.setStepId(FLOW_STEP_3);
                    testTaskFlowMapper.insert(testTaskFlow1);
                }


            });

        }

        //计算工期 max leadtime in productsubvo
        productSubVos.forEach(productSubVo -> {
            if(sample.getStandardLeadTime() == null){
                sample.setStandardLeadTime(productSubVo.getLeadTime());
                //sample.setPmLeadTime(productSubVo.getLeadTime());
            }
            if (productSubVo.getLeadTime() != null && productSubVo.getLeadTime() > sample.getStandardLeadTime()) {
                sample.setStandardLeadTime(productSubVo.getLeadTime());
                //sample.setPmLeadTime(productSubVo.getLeadTime());
            }
            updateById(sample);
        });

        calcPrice(id);



        if(sample.getQuotationId() != null && !sample.getQuotationId().equals(""))
            quotationServiceImpl.calcPrice(sample.getQuotationId());
    }



    @Override
    public void delete(String id, Integer level) {

        Sample sample = null;
        if(level == 0) {
            sample = this.getById(id);
            this.removeById(id);
        }else if (level == 1){
            TestTask testTask = testTaskMapper.selectById(id);
            testTaskMapper.deleteById(id);
            calcPrice(testTask.getSampleId());
            sample = this.getById(testTask.getSampleId());
        }
        quotationServiceImpl.calcPrice(sample.getQuotationId());
    }

    @Override
    public void editLevel(SampleTableEditVo vo) {
        String sampleId = "";
        if (vo.getLevel() == 0) {
            sampleId = vo.getId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", vo.getId());
            jsonObject.put(vo.getKey(), vo.getValue());
            Sample sample = JSON.parseObject(jsonObject.toJSONString(), Sample.class);
            this.updateById(sample);
            sample = this.getById(vo.getId());
            if(vo.getKey().equals("pmPrice")){
                BigDecimal price =  sample.getApplyPrice().divide(sample.getPmPrice(), 2, RoundingMode.HALF_UP);
                sample.setDiscount(price.doubleValue());
                this.updateById(sample);
                List<TestTask> tasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", vo.getId()).eq("is_for_quotation",1));
                Sample finalSample = sample;
                tasks.forEach(task -> {
                    task.setPmPrice( finalSample.getPmPrice().divide(finalSample.getStandardPrice(), 2, RoundingMode.HALF_UP).multiply(task.getStandardPrice()));
                    testTaskMapper.updateById(task);
                });
            }
            if(vo.getKey().equals("applyPrice")){
                BigDecimal price =  sample.getApplyPrice().divide(sample.getPmPrice(), 2, RoundingMode.HALF_UP);
                sample.setDiscount(price.doubleValue());
                this.updateById(sample);
                List<TestTask> tasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", vo.getId()).eq("is_for_quotation",1));
                Sample finalSample = sample;
                tasks.forEach(task -> {
                    task.setApplyPrice(finalSample.getApplyPrice().divide(finalSample.getStandardPrice(), 2, RoundingMode.HALF_UP).multiply(task.getStandardPrice()));
                    testTaskMapper.updateById(task);
                });
            }
        }else if (vo.getLevel() == 1) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", vo.getId());
            jsonObject.put(vo.getKey(), vo.getValue());
            TestTask testTask = JSON.parseObject(jsonObject.toJSONString(), TestTask.class);
            testTaskMapper.updateById(testTask);
            TestTask testTask1 = testTaskMapper.selectById(vo.getId());
            sampleId = testTask1.getSampleId();
            calcPrice(testTask1.getSampleId());
        }
        Sample sample = this.getById(sampleId);
        quotationServiceImpl.calcPrice(sample.getQuotationId());

    }



    @Override
    public void copySample(String id, String LotNo) {
        Sample sample = this.getById(id);

        Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
        if (q.getStatusId().equals("5")) {
            q.setStatusId("11");
            quotationServiceImpl.updateById(q);
        }



        //获取附件
        List<Attachment> listAttachment = iAttachmentService.list(new QueryWrapper<Attachment>().like("source_id", id).eq("source_table", "sample"));

        //获取标准品
        List<StandardMaterial> listStandardMaterial = iStandardMaterialService.list(new QueryWrapper<StandardMaterial>().like("sample_id", id));


        List<TestTask> tasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", sample.getId()));
        if (tasks.size() == 0) {
            throw new JeecgBootException("请先添加检验项目");
        }
        sample.setId(null);
        if(LotNo != null)
            sample.setLotNo(LotNo);
        sample.setPmPrice(BigDecimal.valueOf(0));
        sample.setApplyPrice(BigDecimal.valueOf(0));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("biz_type_id", sample.getBizTypeId());
        sample.setSampleNo(FillRuleUtil.executeRule(FillRuleConstant.SAMPLE, jsonObject).toString());
        sample.setSampleFlowStatus(SAMPLE_FLOW_STATUS_1);
        this.baseMapper.insert(sample);

        //处理附件
        listAttachment.forEach(attachment -> {
            attachment.setSourceId(attachment.getSourceId()+","+sample.getId());
            iAttachmentService.updateById(attachment);
        });
        //处理标准品
        listStandardMaterial.forEach(standardMaterial -> {
            standardMaterial.setSampleId(standardMaterial.getSampleId()+","+sample.getId());
            iStandardMaterialService.updateById(standardMaterial);
        });




        copyTask(tasks, sample);


    }

    private void copyTask(List<TestTask> tasks, Sample sample) {
        // 处理任务
        tasks.forEach(task -> {
            List< SysProductSubVo> sysProductSubVos = new ArrayList<>();
            String task_id = task.getId();
            task.setId(null);
            task.setSampleId(sample.getId());
            task.setRdId(sample.getRdId());
            task.setPmPrice(BigDecimal.valueOf(0));
            task.setApplyPrice(BigDecimal.valueOf(0));
            task.setAssigner(null);
            task.setAssignee(null);
            task.setAssignTime(null);
            task.setCooper(null);
            task.setChecker(null);
            task.setCurStep(FLOW_STEP_1);
            task.setStatus(TASK_STATUS_0);
            testTaskMapper.insert(task);

            //插入flow
            TestTaskFlow testTaskFlow = new TestTaskFlow();
            testTaskFlow.setTaskId(task.getId());
            testTaskFlow.setStepId(FLOW_STEP_1);
            testTaskFlowMapper.insert(testTaskFlow);

            if(sample.getOrderId() != null && !sample.getOrderId().equals("") && sample.getSampleFlowStatus().equals("已入库")){
                Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
                if (q.getStatusId().equals("5")) {
                    task.setCurStep(FLOW_STEP_3);
                    testTaskMapper.updateById(task);

                    TestTaskFlow testTaskFlow1 = new TestTaskFlow();
                    testTaskFlow1.setTaskId(task.getId());
                    testTaskFlow1.setStepId(FLOW_STEP_3);
                    testTaskFlowMapper.insert(testTaskFlow1);
                }





            }



            List<Test> tests = testMapper.selectList(new QueryWrapper<Test>().eq("task_id", task_id));


            if (tests.size() > 0) {
                Test test = tests.get(0);
                testResultMapper.selectByTestId(test.getId()).forEach(
                        testResult -> {
                            SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(testResult.getMethodAnalyteId());
                            SysProductSubVo sysProductSubVo = new SysProductSubVo();
                            sysProductSubVo.setAnalyteId(sysMethodAnalyte.getAnalyteId());
                            sysProductSubVo.setEvaluationId(testResult.getLimitId());
                            sysProductSubVos.add(sysProductSubVo);
                        }
                );
                testTaskService.generateTest(task, sysProductSubVos);
            }


        });
    }

    @Override
    public void copySampleByLotNo(String id) {
        Sample sample = this.getById(id);
        if (sample.getLotNo() == null) {
            throw new JeecgBootException("请先填写批号");
        }
        if(sample.getLotNo().split("[,，]").length < 2){
            throw new JeecgBootException("批号中不包含逗号");
        }
        String[] lotNos = sample.getLotNo().split("[,，]");
        for (int i = 0; i < lotNos.length; i++) {
            if(i == 0){
                sample.setLotNo(lotNos[i]);
                this.updateById(sample);
            }else{
                copySample(id, lotNos[i]);
            }

        }
    }

    @Override
    public void addCapability(List<String> capabilityIds, String id) {
            Sample sample = this.getById(id);
        System.out.println(capabilityIds);
        capabilityIds.forEach(
                capabilityId -> {
                    testTaskMapper.selectBySampleId(id).stream().filter(
                            testTask -> testTask.getCapabilityId() != null && testTask.getCapabilityId().equals(capabilityId)
                    ).findFirst().ifPresentOrElse(
                            testTask -> {
                                //如果已经存在,就不插入了
                            },
                            () -> {
                                //如果不存在,就插入
                                SysCapability sysCapability = sysCapabilityMapper.selectById(capabilityId);
                                TestTask testTask = new TestTask();
                                testTask.setSampleId(id);
                                testTask.setRdId(sample.getRdId());
                                testTask.setName(sysCapability.getName());
                                testTask.setCapabilityId(capabilityId);
                                testTask.setStandardPrice(sysCapability.getStdPrice());
                                testTask.setPmPrice(sysCapability.getStdPrice());
                                testTask.setApplyPrice(sysCapability.getStdPrice());
                                testTask.setIsForQuotation(1);

                                //设置部门
                                if(sysCapability != null && sysCapability.getDeptId() !=null && !sysCapability.getDeptId().contains(",")){
                                    testTask.setDepartmentId(sysCapability.getDeptId());
                                }

                                testTask.setCurStep(FLOW_STEP_1);
                                testTaskMapper.insert(testTask);


                                //插入flow
                                TestTaskFlow testTaskFlow = new TestTaskFlow();
                                testTaskFlow.setTaskId(testTask.getId());
                                testTaskFlow.setStepId(FLOW_STEP_1);
                                testTaskFlowMapper.insert(testTaskFlow);


                                if(sample.getOrderId() != null && !sample.getOrderId().equals("") && sample.getSampleFlowStatus().equals("已入库")){
                                    Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
                                    if (q.getStatusId().equals("5")) {
                                        testTask.setCurStep(FLOW_STEP_3);
                                        testTaskMapper.updateById(testTask);

                                        TestTaskFlow testTaskFlow1 = new TestTaskFlow();
                                        testTaskFlow1.setTaskId(testTask.getId());
                                        testTaskFlow1.setStepId(FLOW_STEP_3);
                                        testTaskFlowMapper.insert(testTaskFlow1);
                                    }

                                }


                            }
                    );

                }
        );
    }

    @Override
    public void cancelSample(List<String> list) {
        for(String id : list){
            Sample sample = this.getById(id);
            if(sample.getTestControlStatus() == "取消"){
                //啥也不做
            }
            sample.setTestControlStatus("取消");
            testTaskMapper.selectBySampleId(id).forEach(
                testTask -> {
                    testTask.setTestControlStatus("取消");
                    testTaskMapper.updateById(testTask);
                    testTaskServiceImpl.cancelNotify(testTask, false);
                }
            );
            this.updateById(sample);
        }
    }

    @Override
    public void resumSample(List<String> list) {
        for(String id : list){
            Sample sample = this.getById(id);
            if(sample.getTestControlStatus() == "正常"){
                //啥也不做
            }
            sample.setTestControlStatus("正常");
            testTaskMapper.selectBySampleId(id).forEach(
                testTask -> {
                    testTask.setTestControlStatus("正常");
                    testTaskMapper.updateById(testTask);
                    testTaskServiceImpl.cancelNotify(testTask, true);
                }
            );
            this.updateById(sample);
        }
    }


    private void calcPrice(String id){
        List<TestTask> tasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", id).eq("is_for_quotation",1));
        Sample sample = this.getById(id);
        final BigDecimal[] applyPrice ={BigDecimal.ZERO};
        final BigDecimal[] standardPrice ={BigDecimal.ZERO};
        final BigDecimal[] pmPrice = {BigDecimal.ZERO};
        tasks.forEach(task -> {
            applyPrice[0] = (applyPrice[0].add(task.getApplyPrice()));
            standardPrice[0] = standardPrice[0].add(task.getStandardPrice());
            pmPrice[0] = pmPrice[0].add(task.getPmPrice());
        });
        sample.setApplyPrice(applyPrice[0]);
        sample.setStandardPrice(standardPrice[0]);
        sample.setPmPrice(pmPrice[0]);
        if(pmPrice[0].compareTo(BigDecimal.ZERO) == 0){
            sample.setDiscount(0.00);
        }else if(applyPrice[0].compareTo(BigDecimal.ZERO) == 0){
            sample.setDiscount(0.00);
        }else {
            sample.setDiscount(applyPrice[0].divide(pmPrice[0], 2, RoundingMode.HALF_UP).doubleValue());
        }
        this.updateById(sample);


    }

    public String getStandard(String id){
        Sample sample = this.getById(id);
        HashSet standards = new HashSet();
        testTaskMapper.selectBySampleId(sample.getId()).forEach(testTask -> {
            if(testTask.getProductId()!=null){
                SysProductPackage sysProductPackage = sysProductPackageMapper.selectById(testTask.getProductId());
                if(sysProductPackage != null && sysProductPackage.getStandardId() != null){
                    SysStandard sysStandard = sysStandardMapper.selectById(sysProductPackage.getStandardId());
                    standards.add(sysStandard);
                }
            }
        });
        if(standards.size() > 0){
            StringBuilder sb = new StringBuilder();
            for (Object standard : standards) {
                SysStandard sysStandard = (SysStandard) standard;
                if(sysStandard.getName() != null){
                    sb.append(sysStandard.getName()).append(",");
                }
            }
            return sb.toString().substring(0, sb.length()-1);
        }
        return "";
    }

    public String getTestDate(String id){
        Sample sample = this.getById(id);
        //初始化一个2000年的时间,跟下面的循环比较
        Date initDate = new Date(4102444800000L);
        AtomicReference<Date> testStartDate = new AtomicReference<>(initDate);
        //初始化一个3000年的时间
        initDate = new Date(946684800000L);
        final Date[] testEndDate = {initDate};
        testTaskMapper.selectBySampleId(sample.getId()).forEach(testTask -> {
            if (testTask.getAssignTime() != null) {
                if (testStartDate.get().compareTo(testTask.getAssignTime()) > 0) {
                    testStartDate.set(testTask.getAssignTime());
                }
            }
                testMapper.selectByTaskId(testTask.getId()).forEach(test -> {
                    if(test.getCheckedTime() != null) {
                        if (testEndDate[0].compareTo(test.getCheckedTime()) < 0) {
                            testEndDate[0] = test.getCheckedTime();
                        }
                    }

                });

        });
        // testStartDate ~ testEndDate
        if (testStartDate.get().compareTo(initDate) != 0 && testEndDate[0].compareTo(initDate) != 0) {
            //转化成YYYY-MM-DD,simpledateformat
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String startDate = sdf.format(testStartDate.get());
            String endDate = sdf.format(testEndDate[0]);
            //拼接成字符串
            StringBuilder sb = new StringBuilder();
            sb.append(startDate).append(" ~ ").append(endDate);
            return sb.toString();
        }

        return "";
    }

    public String getTestItem(String id){
        Sample sample = this.getById(id);
        HashSet testitem = new HashSet();
        testTaskMapper.selectBySampleId(sample.getId()).forEach(testTask -> {
           if(testTask.getMethodId()!=null){
               SysMethod sysMethod = sysMethodMapper.selectById(testTask.getMethodId());

               SysCapability sysCapability = sysCapabilityMapper.selectById(sysMethod.getCid());
            if(sysCapability != null && sysCapability.getName() != null){
                testitem.add(sysCapability.getName());
            }
           }
        });
        if(testitem.size() > 0){
            StringBuilder sb = new StringBuilder();
            for (Object item : testitem) {

                    sb.append(item).append("、");

            }
            return sb.toString().substring(0, sb.length()-1);
        }
        return "";
    }
    public String  getServiceType(String id){
        Quotation q = quotationServiceImpl.getById(id);
        if(q!= null){
            List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("service_type");
            for (SysDictItem item : sysDictItems) {
                if (q.getServiceTypeId().equals(item.getItemValue())) {
                    return item.getItemText();
                }
            }
        }
        return "";
    };
    public String getCustomerAddress(String id){
       SysCustomer  sysCustomer = sysCustomerService.getById(id);
        if (sysCustomer!=null){
            return sysCustomer.getDetailedAddress();
        }
        return "";
    };

    public String  getRdName(String id){
        RdProject rdProject = rdProjectMapper.selectById(id);
        if (rdProject!=null){
            return rdProject.getName();
        }
        return "";
    };

    public String getCustomerContactPhone(String id){
        SysCustomerContact sysCustomerContact = sysCustomerContactService.getById(id);
        if (sysCustomerContact!=null){
            return sysCustomerContact.getPhone();
        }
        return "";
    };
    public  String getIsSupplementary(String id){
        Quotation q = quotationServiceImpl.getById(id);
        if(q!= null){
            List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("logic_code");
            for (SysDictItem item : sysDictItems) {
                if (q.getIsSupplementary().equals(item.getItemValue())) {
                    return item.getItemText();
                }
            }
        }
        return "";
    };
    public  String getWarehouseLocation(String code){
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(item->item.eq(Inventory::getArticleNo,code));
        Inventory inve = inventoryService.getOne(queryWrapper);
        if (inve != null){
            SysWarehouseBox box = sysWarehouseBox.getById(inve.getBoxId());
            return box.getCode();
        }
        return "";
    }

    @Override
    public void copyOldSample(String quotationId, List<String> list) {
        Quotation q = quotationServiceImpl.getById(quotationId);
        //如果报价单状态是5,则修改为11
        if (q.getStatusId().equals("5")) {
            q.setStatusId("11");
            quotationServiceImpl.updateById(q);
        }
        LoginUser user = SecureUtil.currentUser();
        for(String id : list){
            Sample sample = this.getById(id);
            List<TestTask> tasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", id));
            sample.setId(null);
            sample.setSampleNo(null);
            sample.setRdId(null);
            sample.setOrderId(null);
            sample.setQuotationId(quotationId);
            sample.setCustomerId(q.getCustomerId());
            sample.setCustomerContactId(q.getCustomerContactId());
            sample.setAcceptor(user.getUsername());
            sample.setCreateTime(new Date());
            sample.setCreateBy(user.getUsername());
            sample.setUpdateTime(new Date());
            sample.setUpdateBy(user.getUsername());
            sample.setSampleFlowStatus(SAMPLE_FLOW_STATUS_1);
            sample.setReceiveDate(new Date());
            sample.setTestEndDate(null);
            sample.setEntrustDate(new Date());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("biz_type_id", sample.getBizTypeId());
            if (sample.getSampleNo() == null || !sample.getSampleNo().matches("^[A-Z]{3}\\d{9}$")) {
                sample.setSampleNo(FillRuleUtil.executeRule(FillRuleConstant.SAMPLE, jsonObject).toString());
            }
            this.baseMapper.insert(sample);


            copyTask(tasks, sample);
        }
    }

    @Override
    public void addPmRemark(List<String> list, String pmRemark) {
        for (String id : list) {
            Sample s = this.getById(id);
            s.setPmRemark(pmRemark);
            this.updateById(s);

        }
    }

    public String getSampleCountAndUnit(String id){
        String str = "";
        List<TaskVo>  list= baseMapper.getSampleCountAndUnit(id);
        if (list != null && !list.isEmpty()) {
            for (TaskVo task : list) {
                str += task.getReceiveCount()+" "+ task.getUnitName();
                if (task.getTranslation() != null && !task.getTranslation().isEmpty()) {
                    str += "\n" + task.getReceiveCount()+" " + task.getTranslation();
                }
            }
        }
        return str;
    }

}