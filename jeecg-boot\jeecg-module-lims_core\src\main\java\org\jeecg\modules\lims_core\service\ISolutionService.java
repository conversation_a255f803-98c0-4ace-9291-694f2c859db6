package org.jeecg.modules.lims_core.service;

import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.modules.lims_core.entity.Solution;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;

/**
 * @Description: 溶液配置记录
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
public interface ISolutionService extends IService<Solution> {
    void apply(Solution obj, ApplyType applyType) throws WxErrorException;
}
