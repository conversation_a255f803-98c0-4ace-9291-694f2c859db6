package org.jeecg.modules.lims_order.vo.enums;

public enum ApplyType {
    // 申请类型
    QUOTATION_UNLOCK("报价单解锁"),
    QUOTATION_APPLY("报价单申请"),
    ORDRER_UNLOCK("合同解锁"),
    ORDRER_APPLY("合同申请"),
    WAREHOUSE_APPLY_IN("入库申请"),
    WAREHOUSE_APPLY_OUT("出库申请"),
    WAREHOUSE_APPLY_DESTROY("报废申请"),
    SOLUTION_APPLY("溶液配制申请");


    private final String type;

    ApplyType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
