package org.jeecg.modules.lims_core.mapper;

import java.util.List;
import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 单位转换
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface SysUnitConversionMapper extends BaseMapper<SysUnitConversion> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<SysUnitConversion>
   */
	public List<SysUnitConversion> selectByMainId(@Param("mainId") String mainId);
}
