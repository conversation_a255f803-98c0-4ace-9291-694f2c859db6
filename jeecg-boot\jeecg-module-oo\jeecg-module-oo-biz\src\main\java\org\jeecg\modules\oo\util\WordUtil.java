package org.jeecg.modules.oo.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aspose.words.*;
import com.aspose.words.Shape;
import com.aspose.words.net.System.Data.DataRow;
import com.aspose.words.net.System.Data.DataTable;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import org.jeecg.common.system.annotation.TemplateAction;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.lims_core.entity.SysTemplate;
import org.jeecg.modules.oo.controller.dto.TableInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.awt.image.WritableRaster;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.*;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static org.jeecg.common.util.oConvertUtils.camelName;

public class WordUtil {
    private static final Logger log = LoggerFactory.getLogger(WordUtil.class);

    /**
     * 调整bufferedimage大小
     * @param source BufferedImage 原始image
     * @param targetW int  目标宽
     * @param targetH int  目标高
     * @param flag boolean 是否同比例调整
     * @return BufferedImage  返回新image
     */
    public static BufferedImage resizeBufferedImage(BufferedImage source, int targetW, int targetH, boolean flag) {
        int type = source.getType();
        BufferedImage target = null;
        double sx = (double) targetW / source.getWidth();
        double sy = (double) targetH / source.getHeight();
        if (flag && sx > sy) {
            sx = sy;
            targetW = (int) (sx * source.getWidth());
        } else if(flag && sx <= sy){
            sy = sx;
            targetH = (int) (sy * source.getHeight());
        }
        if (type == BufferedImage.TYPE_CUSTOM) { // handmade
            ColorModel cm = source.getColorModel();
            WritableRaster raster = cm.createCompatibleWritableRaster(targetW, targetH);
            boolean alphaPremultiplied = cm.isAlphaPremultiplied();
            target = new BufferedImage(cm, raster, alphaPremultiplied, null);
        } else {
            target = new BufferedImage(targetW, targetH, type);
        }
        Graphics2D g = target.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.drawRenderedImage(source, AffineTransform.getScaleInstance(sx, sy));
        g.dispose();
        return target;
    }

    private static Map<String, Object> getDataSource(Object... dataSource) throws IntrospectionException, IllegalAccessException, InvocationTargetException {
        Map<String, Object> data = new HashMap<>();
        for(Object o:dataSource){
            Class<?> aClass = o.getClass();
            if (o instanceof List){
                List<Object> list = (List<Object>) o;
                if(list.size()>0){
                    TemplateDesigner templateDesigner = list.get(0).getClass().getAnnotation(TemplateDesigner.class);
                    String key = null;
                    if(templateDesigner!=null)
                        key = templateDesigner.value();
                    else{
                        TableName tableName =list.get(0).getClass().getAnnotation(TableName.class);
                        if(tableName!=null)
                            key = tableName.value();
                    }
                    if(key!=null)
                        data.put(key, o);
                }
            }else{
                Field[] fields = aClass.getDeclaredFields();
                for (Field field : fields) {
                    if (!Modifier.toString(field.getModifiers()).contains("static ")) {
                        try{
                            PropertyDescriptor pd = org.springframework.beans.BeanUtils.getPropertyDescriptor(aClass,field.getName()); //new PropertyDescriptor(field.getName(), aClass);
                            Method method = pd.getReadMethod();
                            String key = aClass.getAnnotation(org.jeecg.common.system.annotation.TemplateDesigner.class).value()+ "." +  field.getName();
                            Object value = method.invoke(o);
                            if (value != null) {
                                if(value instanceof Date){
                                    value = date2Str((Date) value, "yyyy-MM-dd");
                                }
                                data.put(key, value);
                            }else
                                data.put(key, "/");
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return data;
    }
    public static String date2Str(Date date, String format) {
        if (date == null || format == null || format.isEmpty()) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }
    
    public static String generateReport(Object rootEntity, SysTemplate template, String targetPath, String resourceRoot,Map<String,Object> preDataSource,Boolean ISLogicalTopLevel ) throws Exception {
        String rootVO = rootEntity.getClass().getSimpleName() + "VO";
        String rootId = ReflectUtil.getFieldValue(rootEntity, "id").toString();
        String templateUrl = template.getUrl();
        InputStream is = null;
        ByteArrayOutputStream out = null;
        try {
            String objectName = templateUrl.substring(templateUrl.indexOf(MinioUtil.getBucketName()) + MinioUtil.getBucketName().length() + 1);
            is = MinioUtil.getMinioFile(MinioUtil.getBucketName(), objectName);
            if(is==null)
                throw new RuntimeException("读取不到模板！");
            out = new ByteArrayOutputStream();
            Document doc = new Document(is);
            DocumentBuilder documentBuilder = new DocumentBuilder(doc);
            FieldCollection fields = doc.getRange().getFields();
            Pattern dotPattern = Pattern.compile("MERGEFIELD\\s+([^.]+)\\.");  // 简化正则，只匹配点号前的内容
            Pattern tableStartPattern = Pattern.compile("MERGEFIELD\\s+TableStart:([^\\]]+)");
            Map<String, SysTemplate> subReports = new HashMap<>();
            Map<String, TableInfo> tablesInTemplate = StreamSupport.stream(fields.spliterator(), false)
                    .filter(field -> field.getType() == FieldType.FIELD_MERGE_FIELD)
                    .flatMap(field -> {
                        String fieldCode = field.getFieldCode().trim();
                        List<AbstractMap.SimpleEntry<String, TableInfo>> entries = new ArrayList<>();
                        Matcher tableMatcher = tableStartPattern.matcher(fieldCode);// 尝试匹配 TableStart 格式
                        if (tableMatcher.find()) {
                            String tableName = tableMatcher.group(1);
                            entries.add(new AbstractMap.SimpleEntry<>(tableName, TableInfo.builder().tableName(tableName).isTableStart(true).build()));
                        }
                        Matcher dotMatcher = dotPattern.matcher(fieldCode);// 尝试匹配普通字段格式
                        if (dotMatcher.find()) {
                            String tableName = dotMatcher.group(1);
                            entries.add(new AbstractMap.SimpleEntry<>(tableName, TableInfo.builder().tableName(tableName).isTableStart(false).build()));
                        }
                        if (fieldCode.startsWith("MERGEFIELD SubReport->")) {// 检查 SubReport 字段
                            String subTemplateName = fieldCode.split("->")[1];
                            Object sysTemplateService = SpringContextUtils.getBean("sysTemplateServiceImpl");
                            try {
                                SysTemplate subTemplate = (SysTemplate) sysTemplateService.getClass()
                                        .getMethod("getSubTemplateByName", String.class, String.class)
                                        .invoke(sysTemplateService, template.getId(), subTemplateName);
                                if (!subReports.containsKey(field)) {
                                    subReports.put(fieldCode, subTemplate);
                                }
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException(e);
                            } catch (InvocationTargetException e) {
                                throw new RuntimeException(e);
                            } catch (NoSuchMethodException e) {
                                throw new RuntimeException(e);
                            }
                        }
                        return entries.stream();
                    })
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (existing, replacement) -> {
                                return existing.isTableStart() || replacement.isTableStart()
                                        ? TableInfo.builder().tableName(existing.getTableName()).isTableStart(true).build()
                                        : existing;
                            }
                    ));
            String rootTable = rootEntity.getClass().getSimpleName().replace("VO", "");
            if (!tablesInTemplate.containsKey(rootTable)) {
                tablesInTemplate.put(rootTable, TableInfo.builder().tableName(rootTable).isTableStart(false).build());
            }
            for (SysTemplate subTemplate : subReports.values()) {// 处理 SubReport 字段
                if (StrUtil.isNotEmpty(subTemplate.getLoopPara())) {
                    String[] loopParaInofs = null;
                    loopParaInofs = subTemplate.getLoopPara().split("\\.");
                    String loopTable = loopParaInofs[0];
                    if (!tablesInTemplate.containsKey(loopTable)) {
                        tablesInTemplate.put(loopTable, TableInfo.builder().tableName(loopTable).isTableStart(true).build());
                    }
                }
            }
            TemplateProcessor processor = new TemplateProcessor();
            Map<String, Object> datasource = processor.processTemplateClasses("org.jeecg.modules.lims_core.vo", rootVO, rootId, tablesInTemplate, preDataSource,ISLogicalTopLevel);
            Map<String, Object> data = getDataSource(datasource.values().toArray());
             if (preDataSource != null) {
                for (String key : preDataSource.keySet()) {
                    if (!data.containsKey(key))
                        data.put(key, preDataSource.get(key));
                }
            }
            Map<String, Object> toData = new HashMap<>();
            for (Map.Entry<String, Object> en : data.entrySet()) {
                String key = en.getKey();
                Object value = en.getValue();
                if (key == null || value == null) {
                    continue;
                }
                if (value instanceof List) {//写入表数据
                    //处理语言转换,修改object对象的值
                    for (int i = 0; i < ((List<?>) value).size(); i++) {

                        Object item = ((List<?>) value).get(i);

                        Class<?> clazz = item.getClass();
                        Field[] fields1 = clazz.getDeclaredFields();
                        for (Field field : fields1) {
                            field.setAccessible(true);
                            Object fieldValue = field.get(item);
                            if (fieldValue instanceof String) {
                                if (template.getReportLanguage().equals("zh-cn")) {
                                    fieldValue = ZhConverterUtil.toSimple((String) fieldValue);
                                } else if (template.getReportLanguage().equals("zh-hk")) {
                                    fieldValue = ZhConverterUtil.toTraditional((String) fieldValue);
                                }else if (template.getReportLanguage().equals("zh-en")) {
                                    fieldValue = ZhConverterUtil.toSimple((String) fieldValue);

                                }
                                field.set(item, fieldValue);
                            }
                        }

                    }


                    DataTable dataTable = fillListData((List) value, key);
                    doc.getMailMerge().executeWithRegions(dataTable);
                }
                if(template.getReportLanguage().equals("zh-cn") && en.getValue() instanceof String){
                    en.setValue(ZhConverterUtil.toSimple(en.getValue().toString()));
                }else if (template.getReportLanguage().equals("zh-hk") && en.getValue() instanceof String) {
                    en.setValue(ZhConverterUtil.toTraditional(en.getValue().toString()));
                }else if (template.getReportLanguage().equals("zh-en") && en.getValue() instanceof String && ZhConverterUtil.containsChinese(en.getValue().toString())) {
                        String context = "";
                        String field_name = "";
                        String source_id = "";
                        if (en.getKey().contains(".")) {
                            String[] keyParts = en.getKey().split("\\.");
                            field_name = keyParts[keyParts.length - 1];
                            context = keyParts[0];
                            String sourceIdKey = context + ".id";
                            source_id = data.containsKey(sourceIdKey) ? data.get(sourceIdKey).toString() : "";
                        }
                        String serviceName = "sysTranslationServiceImpl";
                        Object oService = SpringContextUtils.getBean(serviceName);
                        Object entity = null;
                        if (oService != null) {
                            IService service = (IService) oService;
                            QueryWrapper qw = new QueryWrapper<>();
                            qw.eq("context", context);
                            qw.eq("field_name", field_name);
                            qw.eq("source_id", source_id);
                            entity = service.getOne(qw);
                        }
                        if (entity != null) {
                            try {
                                Method getTranslationMethod = entity.getClass().getMethod("getTranslation");
                                String translation = (String) getTranslationMethod.invoke(entity);
                                en.setValue(en.getValue().toString() + "\n" + translation);
                            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                                log.error("Failed to invoke getTranslation for context: {}, field: {}, source_id: {}", context, field_name, source_id, e);
                                en.setValue(en.getValue().toString());
                            }
                        } else {
                            try {
                                String voClassName = "org.jeecg.modules.lims_core.vo." + context + "VO";
                                Class<?> voClass = Class.forName(voClassName);
                                Field voField = voClass.getDeclaredField(field_name);
                                if (voField != null) {
                                    TemplateField templateField = voField.getAnnotation(TemplateField.class);
                                    if (templateField != null && StrUtil.isNotEmpty(templateField.drillChain())) {
                                        String drillChain = templateField.drillChain();
                                        String lastSegment = StrUtil.subAfter(drillChain, "->", true);
                                        if (StrUtil.isNotEmpty(lastSegment)) {
                                            String[] lastSegmentParts = lastSegment.split("\\.");
                                            if (lastSegmentParts.length == 2) {
                                                String drillContext = lastSegmentParts[0];
                                                String drillFieldName = lastSegmentParts[1];
                                                String drillSourceId = "";
                                                String drillid= context+"."+camelName(drillContext) + "Id";
                                                drillSourceId = data.containsKey(drillid) ? data.get(drillid).toString() : "";
                                                Object drillEntity = null;
                                                if (oService != null) {
                                                    IService service = (IService) oService;
                                                    QueryWrapper qw = new QueryWrapper<>();
                                                    qw.eq("context", drillContext);
                                                    qw.eq("field_name", drillFieldName);
                                                    qw.eq("source_id", drillSourceId);
                                                    drillEntity = service.getOne(qw);
                                                }
                                                if (drillEntity != null) {
                                                    try {
                                                        Method getTranslationMethod = drillEntity.getClass().getMethod("getTranslation");
                                                        String translation = (String) getTranslationMethod.invoke(drillEntity);
                                                        en.setValue(en.getValue().toString() + "\n" + translation);
                                                    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                                                        log.error("Failed to invoke getTranslation for drillContext: {}, drillFieldName: {}, source_id: {}",
                                                                drillContext, drillFieldName, drillSourceId, e);
                                                        en.setValue(en.getValue().toString());
                                                    }
                                                } else {
                                                    en.setValue(en.getValue().toString());
                                                }
                                            } else {
                                                en.setValue(en.getValue().toString());
                                            }
                                        } else {
                                            en.setValue(en.getValue().toString());
                                        }
                                    } else {
                                        en.setValue(en.getValue().toString());
                                    }
                                } else {
                                    en.setValue(en.getValue().toString());
                                }
                            } catch (ClassNotFoundException | NoSuchFieldException e) {
                                log.error("Failed to retrieve drillChain for context: {}, field: {}", context, field_name, e);
                                en.setValue(en.getValue().toString());
                            }
                        }
                    }
                toData.put(key, en.getValue());
            }
            MailMerge mm = doc.getMailMerge();
            mm.setUseNonMergeFields(true);
            mm.setPreserveUnusedTags(true);
            mm.setTrimWhitespaces(true);
            mm.setUseWholeParagraphAsRegion(false);
            mm.setCleanupOptions(MailMergeCleanupOptions.REMOVE_EMPTY_TABLE_ROWS
                    | MailMergeCleanupOptions.REMOVE_CONTAINING_FIELDS
                    | MailMergeCleanupOptions.REMOVE_UNUSED_REGIONS
            );
            AtomicBoolean actionProcessed = new AtomicBoolean(false); // 声明在 generateReport 方法内部
            mm.setFieldMergingCallback(new IFieldMergingCallback() {
                @Override
                public void fieldMerging(FieldMergingArgs fieldMergingArgs) throws Exception {
                    String fieldCode = fieldMergingArgs.getField().getFieldCode().trim();
                    if(fieldCode.contains("Action->")){
                        if (!actionProcessed.get()) {
                            processActionFields(doc, data);
                            actionProcessed.set(true); // 只执行一次
                        }
                    } else if(fieldCode.contains("SubReport->")){
                        SysTemplate subTemplate = subReports.get(fieldMergingArgs.getField().getFieldCode().trim());
                        documentBuilder.moveToMergeField(fieldMergingArgs.getField().getFieldName(), true, true);
                        processSubReport(documentBuilder,subTemplate,rootEntity, resourceRoot, datasource,ISLogicalTopLevel);
                    }
                }

                @Override
                public void imageFieldMerging(ImageFieldMergingArgs imageFieldMergingArgs) throws Exception {
                    if (imageFieldMergingArgs.getFieldValue() != null && !StrUtil.isEmpty(imageFieldMergingArgs.getFieldValue().toString())) {
                        File f = new File(resourceRoot + "/" + imageFieldMergingArgs.getFieldValue()); //图片放在应用部署服务器的某个根目录resourceRoot
                        if (f.exists()) {
                            double width = -1;
                            double height = -1;
                            FieldMergeField field = imageFieldMergingArgs.getField();
                            documentBuilder.moveToMergeField(field.getFieldName());
                            CompositeNode cnTable = field.getStart().getAncestor(NodeType.TABLE);
                            if (cnTable instanceof Table) {
                                CompositeNode cnCell = field.getStart().getAncestor(NodeType.CELL);
                                if (cnCell instanceof Cell) {
                                    Cell cell = (Cell) cnCell;
                                    cell.getCellFormat().setLeftPadding(0);
                                    cell.getCellFormat().setRightPadding(0);
                                    cell.getCellFormat().setTopPadding(0);
                                    cell.getCellFormat().setBottomPadding(0);
                                    cell.getCellFormat().setWrapText(false);
                                    cell.getCellFormat().setFitText(true);
                                    width = cell.getCellFormat().getWidth();
                                    height = cell.getParentRow().getRowFormat().getHeight();
                                }
                            }
                            imageFieldMergingArgs.setImageWidth(new MergeFieldImageDimension(width));
                            imageFieldMergingArgs.setImageHeight(new MergeFieldImageDimension(height));
                            //img =resizeBufferedImage(img,400,300,true);
                            if (FileUtil.getSuffix(f).equals("svg")) {
                                byte[] bytes = Files.readAllBytes(Paths.get(resourceRoot, imageFieldMergingArgs.getFieldValue().toString()));
                                imageFieldMergingArgs.setFieldValue(bytes);
                            } else {
                                BufferedImage img = ImageIO.read(f);
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                ImageIO.write(img, "png", baos);
                                baos.flush();
                                byte[] imageBytes = baos.toByteArray();
                                baos.close();
                                imageFieldMergingArgs.setFieldValue(imageBytes);
                            }
                        }
                    } else
                        imageFieldMergingArgs.setFieldValue(null);
                }
            });
            //合并数据
            mm.execute(toData.keySet().toArray(new String[0]), toData.values().toArray());
            int fileType = getSaveFormat(targetPath);

            if (isTemporaryPath(targetPath)) {
                doc.save(targetPath, SaveOptions.createSaveOptions(fileType));
                return targetPath;
            } else {
                doc.save(out, SaveOptions.createSaveOptions(fileType));
                InputStream targetIs = new ByteArrayInputStream(out.toByteArray());
                return MinioUtil.upload(targetIs, targetPath);
            }
        } finally {
            if (is != null)
                is.close();
            if (out != null)
                out.close();
        }
    }

    private static void processSubReport(DocumentBuilder builder,SysTemplate subTemplate,Object rootEntity, String resourceRoot, Map<String, Object> datasource ,boolean ISLogicalTopLevel) throws Exception {
        if (StrUtil.isNotEmpty(subTemplate.getLoopPara())) {
            String[] loopParaInfos = subTemplate.getLoopPara().split("\\.");
            String loopTable = loopParaInfos[0];
            String loopField = loopParaInfos[1];
            Object loopData = datasource.get(loopTable);
            if (loopData instanceof List) {
                List<Object> lst = (List<Object>) loopData;
                List<Object> uniqueLoopBy = lst.stream()
                        .map(o -> ReflectUtil.getFieldValue(o, loopField))
                        .distinct() // 去重
                        .toList(); // 收集到列表中
                for (int s = 0; s < uniqueLoopBy.size(); s++) {
                    Object loopValue = uniqueLoopBy.get(s);
                    if (loopValue != null) {
                        String tempDirPath = System.getProperty("java.io.tmpdir");
                        Path limsTempDir = Paths.get(tempDirPath, "lims");
                        if (!Files.exists(limsTempDir)) {
                            Files.createDirectory(limsTempDir);
                        }
                        String subTemplatePath = Paths.get(limsTempDir.toString(), subTemplate.getName() + "_" + String.valueOf(loopValue) + ".docx").toString();
                        String loopFieldD = loopTable + "." + loopField;
                        Map<String, Object> subReportFilters = new HashMap<>();
                        subReportFilters.put(loopFieldD, loopValue);
                        Object filteredData = lst.stream()
                                .filter(f -> ReflectUtil.getFieldValue(f, loopField).toString().equals(loopValue.toString()))
                                .collect(Collectors.toList());
                        subReportFilters.put(loopTable, filteredData);
                        generateReport(rootEntity, subTemplate, subTemplatePath, resourceRoot, subReportFilters,ISLogicalTopLevel);// 递归调用填充子模板
                        Document subDoc = new Document(subTemplatePath);
                        Paragraph currentParagraph = builder.getCurrentParagraph();
                        builder.moveTo(currentParagraph);
                        builder.insertDocument(subDoc, ImportFormatMode.KEEP_SOURCE_FORMATTING); // 插入子模板
                    }
                }
            }
        }
    }

    public static void GenerateReportV1(String templateUrl, String targetPath, String resourceRoot, Map<String,Object> preDataSource, Object... dataSource) throws Exception {
        Map<String, Object> data = getDataSource(dataSource);
        if(preDataSource!=null){
            for (String key : preDataSource.keySet()) {
                if(!data.containsKey(key))
                    data.put(key,preDataSource.get(key));
            }
        }
        InputStream is = null;
        ByteArrayOutputStream out = null;
        try {
            String objectName = templateUrl.substring(templateUrl.indexOf(MinioUtil.getBucketName()) + MinioUtil.getBucketName().length() + 1);
            is = MinioUtil.getMinioFile(MinioUtil.getBucketName(),objectName);
            out = new ByteArrayOutputStream();
            Document doc = new Document(is);
            //doc.save("d:\\123.docx");
            DocumentBuilder builder = new DocumentBuilder(doc);
            Map<String, Object> toData = new HashMap<>();
            for (Map.Entry<String, Object> en : data.entrySet()) {
                String key = en.getKey();
                Object value = en.getValue();
                if (key == null || value == null) {
                    continue;
                }
                if (value instanceof List) {//写入表数据
                    DataTable dataTable = fillListData((List) value, key);
                    doc.getMailMerge().executeWithRegions(dataTable);
                }
                toData.put(key, en.getValue());
            }
            MailMerge mm = doc.getMailMerge();
            mm.setUseNonMergeFields(true);
            mm.setPreserveUnusedTags(true);
            mm.setTrimWhitespaces(true);
            mm.setUseWholeParagraphAsRegion(false);
            mm.setCleanupOptions(MailMergeCleanupOptions.REMOVE_EMPTY_TABLE_ROWS
                    | MailMergeCleanupOptions.REMOVE_CONTAINING_FIELDS
                    | MailMergeCleanupOptions.REMOVE_UNUSED_REGIONS
                    | MailMergeCleanupOptions.REMOVE_UNUSED_FIELDS);
            mm.setFieldMergingCallback(new IFieldMergingCallback() {
                @Override
                public void fieldMerging(FieldMergingArgs fieldMergingArgs) throws Exception {
                    System.out.println(fieldMergingArgs);
                }

                @Override
                public void imageFieldMerging(ImageFieldMergingArgs imageFieldMergingArgs) throws Exception {
                    if (imageFieldMergingArgs.getFieldValue()!=null && !StrUtil.isEmpty(imageFieldMergingArgs.getFieldValue().toString())){
                        File f = new File(resourceRoot+"/" +imageFieldMergingArgs.getFieldValue()); //图片放在应用部署服务器的某个根目录resourceRoot
                        if(f.exists()){
                            double width =-1;
                            double height =-1;
                            FieldMergeField field = imageFieldMergingArgs.getField();
                            builder.moveToMergeField(field.getFieldName());
                            CompositeNode cnTable = field.getStart().getAncestor(NodeType.TABLE);
                            if(cnTable instanceof Table){
                                CompositeNode cnCell = field.getStart().getAncestor(NodeType.CELL);
                                if(cnCell instanceof  Cell){
                                    Cell cell = (Cell) cnCell;
                                    cell.getCellFormat().setLeftPadding(0);
                                    cell.getCellFormat().setRightPadding(0);
                                    cell.getCellFormat().setTopPadding(0);
                                    cell.getCellFormat().setBottomPadding(0);
                                    cell.getCellFormat().setWrapText(false);
                                    cell.getCellFormat().setFitText(true);
                                    width = cell.getCellFormat().getWidth();
                                    height = cell.getParentRow().getRowFormat().getHeight();
                                }
                            }
                            imageFieldMergingArgs.setImageWidth(new MergeFieldImageDimension(width));
                            imageFieldMergingArgs.setImageHeight(new MergeFieldImageDimension(height));
                            //img =resizeBufferedImage(img,400,300,true);
                            if(FileUtil.getSuffix(f).equals("svg")){
                                byte[] bytes = Files.readAllBytes(Paths.get(resourceRoot , imageFieldMergingArgs.getFieldValue().toString()));
                                imageFieldMergingArgs.setFieldValue(bytes);
                            }else{
                                BufferedImage img = ImageIO.read(f);
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                ImageIO.write(img, "png", baos);
                                baos.flush();
                                byte[] imageBytes = baos.toByteArray();
                                baos.close();
                                imageFieldMergingArgs.setFieldValue(imageBytes);
                            }
                        }
                    }else
                        imageFieldMergingArgs.setFieldValue(null);
                }
            });
            //合并数据
            mm.execute(toData.keySet().toArray(new String[0]), toData.values().toArray());
            int fileType = getSaveFormat(targetPath);

            processActionFields(doc,data);
            if(isTemporaryPath(targetPath)){
                doc.save(targetPath,SaveOptions.createSaveOptions(fileType));
            }else{
                doc.save(out, SaveOptions.createSaveOptions(fileType));
                InputStream targetIs = new ByteArrayInputStream(out.toByteArray());
    //            new FileOutputStream(targetPath).write(out.toByteArray());
                MinioUtil.upload(targetIs,targetPath);
            }
        } finally {
            if(is!=null)
                is.close();
            if(out!=null)
                out.close();
        }
    }

    public static boolean isTemporaryPath(String path) {
        String tempDir = System.getProperty("java.io.tmpdir");
        // 将系统临时目录转换为 Path 对象
        Path tempPath = Paths.get(tempDir);
        // 将传入的路径转换为 Path 对象
        Path inputPath = Paths.get(path);
        // 检查给定路径是否以系统临时目录为前缀
        return inputPath.startsWith(tempPath);
    }

    private static void processActionFields(Document doc, Map<String, Object> data) throws Exception {
        List<com.aspose.words.Field> actionFields = new ArrayList<>();
        // 遍历文档中的所有字段
        for (com.aspose.words.Field field : doc.getRange().getFields()) {
            String fieldCode = field.getFieldCode();
            if (fieldCode.contains("Action")) {
                actionFields.add(field);
            }
        }
        Boolean DoMergeTableCell = false;
        for (com.aspose.words.Field actionField : actionFields) {
            String fieldCode = actionField.getFieldCode();
            String[] fieldcodes = fieldCode.split("->");
            if (fieldcodes.length > 1) {
                if (fieldcodes[1].strip().equals("MergeTableCell")) {
                    if(!DoMergeTableCell) {
                        mergeTableCell(doc, data);
                        DoMergeTableCell=true;
                    }
                } else {
                    boolean canExecute = canExecute(fieldcodes, data);
                    if (canExecute) {
                        if (fieldcodes[1].equals("DeleteTable")){
                            deleteTable(actionField);
                        } else if (fieldcodes[1].equals("DeleteTableRow")){
                            deleteTableRow(actionField);
                        } else  if(fieldcodes[1].equals("DeleteTableCell")) {
                            deleteTableCell(actionField);
                        }
                    }
                }
            }
        }
    }

    private static void deleteTableCell(com.aspose.words.Field actionField) {
        // 获取当前字段所在的单元格
        Cell cell = (Cell) actionField.getStart().getAncestor(NodeType.CELL);
        if (cell != null && cell.getText() == null) {
            // 删除单元格
            cell.remove();
        }
    }

    // 提取的 canExecute 方法
    private static boolean canExecute(String[] fieldcodes, Map<String, Object> data) {
        if (fieldcodes.length == 2) {
            return true; // 如果条件表达式不够，直接返回 false
        }

        String condition = fieldcodes[2].trim(); // 获取条件表达式

        // 使用正则表达式分割条件
        String regex = "\\s*(=|>|<|>=|<=|!=|like|and|or|\\(|\\))\\s*";
        String[] conditions = condition.split(regex);

        // 遍历条件并检查
        for (String cond : conditions) {
            cond = cond.trim();
            if (!cond.isEmpty()) {
                String[] conditionParts = cond.split("\\."); // 分割类名和属性名
                if (conditionParts.length == 2) {
                    String className = conditionParts[0].trim();
                    String propertyName = conditionParts[1].trim();

                    // 从 data 中获取对象
                    Object obj = data.get(className);
                    if (obj != null) {
                        // 判断 obj 是单个对象还是 List
                        if (obj instanceof List) {
                            List<?> objList = (List<?>) obj;
                            // 遍历列表中的每个对象
                            for (Object item : objList) {
                                if (checkCondition(item, propertyName)) {
                                    return true; // 如果找到满足条件的对象，返回 true
                                }
                            }
                        } else {
                            // 对于单个对象，直接检查条件
                            if (checkCondition(obj, propertyName)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false; // 如果没有找到满足条件的对象，返回 false
    }

    // 检查条件的方法
    private static boolean checkCondition(Object obj, String propertyName) {
        try {
            // 使用反射获取属性值
            Field field = obj.getClass().getDeclaredField(propertyName);
            field.setAccessible(true);
            Object value = field.get(obj);
            // 根据需要判断条件，这里假设只要属性值不为 null 即为 true
            return value != null;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return false; // 如果获取属性失败，返回 false
        }
    }

    @TemplateAction(value = "MergeTableCell",description = "合并单元格")
    public static void mergeTableCell(Document doc, Map<String, Object> data) throws Exception {
        // 记录action第一次出现的行号
        Map<String, Integer> mergeFieldMap = new HashMap<>();

        for (int h = 0; h < doc.getSections().getCount(); h++) {
            for (int i = 0; i < doc.getSections().get(h).getBody().getTables().getCount(); i++) {

                Table table = doc.getSections().get(h).getBody().getTables().get(i);
                if (table == null || table.getRows().getCount() == 0) {
                    continue; // 避免空表报错
                }
                for (int j = 0; j < table.getRows().getCount(); j++) {
                    Row row = table.getRows().get(j);
                    if (row == null) {
                        continue;
                    }
                    for (int k = 0; k < row.getCells().getCount(); k++) {
                        Cell cell = row.getCells().get(k);
                        if (cell == null) {
                            continue;
                        }
                        NodeCollection childNodes = cell.getChildNodes(NodeType.FIELD_START, true);
                        for (Object node : childNodes) {
                            FieldStart fieldStart = (FieldStart) node;
                            String fieldCode = fieldStart.getField().getFieldCode();
                            if (fieldCode.contains("Action->MergeTableCellNullLeft")) {
                                if (cell.getCellFormat().getHorizontalMerge() == 0) {
                                    // 记录第一次出现该字段的行号
                                    mergeFieldMap.putIfAbsent(fieldCode, j);

                                    // 解析 fieldCode
                                    String[] fieldParts = fieldCode.replace("MERGEFIELD Action->MergeTableCellNullLeft->", "").split("\\.");
                                    if (fieldParts.length < 2) {
                                        continue;
                                    }
                                    String listName = fieldParts[0].trim();
                                    String[] fieldNames = fieldParts[1].split(",");

                                    // 获取数据
                                    List<Object> objList = (List<Object>) data.get(listName);
                                    if (objList == null || objList.isEmpty()) {
                                        continue;
                                    }
                                    int baseIndex = j - mergeFieldMap.get(fieldCode);
                                    Object o = objList.get(baseIndex);
                                    String sJoson = JSON.toJSONString(o);
                                    JSONObject formData = JSON.parseObject(sJoson);
                                    Object o1 = formData.get(fieldNames[0].trim());
                                    if(o1 == null || o1.toString().isEmpty()){
                                        System.out.println("合并单元格: 行 " + j + " 列 " + k + "，行数 ");
                                        Cell cellStartRange = table.getRows().get(j).getCells().get(k-1);
                                        Cell cellEndRange = table.getRows().get(j).getCells().get(k);
                                        cellStartRange.getFirstParagraph().appendChild(new Run(cellStartRange.getDocument(), cellEndRange.getText().split("MERGEFIELD")[0]));
                                        mergeCells( cellEndRange,cellStartRange);
                                        removeMergeFields(cellStartRange);
                                        removeMergeFields(cellEndRange);
                                    }else {
                                        fieldStart.getField().remove();//特殊处理 有值不管
                                    }
                                }
                            }
                            else if (fieldCode.contains("Action->MergeTableCellLeft")) {
                                if (cell.getCellFormat().getHorizontalMerge() == 0) {
                                    // 记录第一次出现该字段的行号
                                    mergeFieldMap.putIfAbsent(fieldCode, j);

                                    // 解析 fieldCode
                                    String[] fieldParts = fieldCode.replace("MERGEFIELD Action->MergeTableCellLeft->", "").split("\\.");
                                    if (fieldParts.length < 2) {
                                        continue;
                                    }
                                    String listName = fieldParts[0].trim();
                                    String[] fieldNames = fieldParts[1].split(",");

                                    // 获取数据
                                    List<Object> objList = (List<Object>) data.get(listName);
                                    if (objList == null || objList.isEmpty()) {
                                        continue;
                                    }
                                    int baseIndex = j - mergeFieldMap.get(fieldCode);
                                    Object o = objList.get(baseIndex);
                                    String sJoson = JSON.toJSONString(o);
                                    JSONObject formData = JSON.parseObject(sJoson);
                                    Object o1 = formData.get(fieldNames[0].trim());
                                    if(o1 == null || o1.toString().isEmpty()){
                                        System.out.println("合并单元格: 行 " + j + " 列 " + k + "，行数 ");
                                        Cell cellStartRange = table.getRows().get(j).getCells().get(k-1);
                                        Cell cellEndRange = table.getRows().get(j).getCells().get(k);
                                        cellStartRange.getFirstParagraph().appendChild(new Run(cellStartRange.getDocument(), cellEndRange.getText().split("MERGEFIELD")[0]));
                                        mergeCells( cellEndRange,cellStartRange);
                                        fieldStart.getField().remove();
                                    }else {
                                        Cell cellEndRange = table.getRows().get(j).getCells().get(k);
                                        cellEndRange.getFirstParagraph().appendChild(new Run(cellEndRange.getDocument(), cellEndRange.getText().split("MERGEFIELD")[0]));
                                    }
                                }
                            }
                            else if (fieldCode.contains("Action->MergeTableCell")) {
                                if (cell.getCellFormat().getHorizontalMerge() == 0) {

                                    // 记录第一次出现该字段的行号
                                    mergeFieldMap.putIfAbsent(fieldCode, j);
                                    // 解析 fieldCode
                                    String[] fieldParts = fieldCode.replace("MERGEFIELD Action->MergeTableCell->", "").split("\\.");
                                    if (fieldParts.length < 2) {
                                        continue;
                                    }
                                    String listName = fieldParts[0].trim();
                                    String[] fieldNames = fieldParts[1].split(",");

                                    // 获取数据
                                    List<Object> objList = (List<Object>) data.get(listName);
                                    if (objList == null || objList.isEmpty()) {
                                        continue;
                                    }

                                    // 计算相同行数
                                    int count = getRowMergeCount(objList, fieldNames, j - mergeFieldMap.get(fieldCode));
                                    if (count > 1) {
                                        System.out.println("合并单元格: 行 " + j + " 列 " + k + "，行数 " + count);
                                        Cell cellStartRange = table.getRows().get(j).getCells().get(k);
                                        Cell cellEndRange = table.getRows().get(j + count - 1).getCells().get(k);
                                        mergeCells(cellStartRange, cellEndRange);
                                        fieldStart.getField().remove();
                                    }else{
                                        fieldStart.getField().remove();
                                    }
                                }
                            }

                        }
                    }
                }
            }
        }
    }
    private static void removeMergeFields(Cell cell) throws Exception {
        NodeCollection fields = cell.getChildNodes(NodeType.FIELD_START, true);
        for (Object node : fields) {
            FieldStart fieldStart = (FieldStart) node;
            String fieldCode = fieldStart.getField().getFieldCode();
            if (fieldCode.contains("Action->MergeTableCell") || fieldCode.contains("Action->MergeTableCellNullLeft")) {
                fieldStart.getField().remove();
            }
        }
    }
    /**
     * 计算满足条件的相同行数
     */
    private static int getRowMergeCount(List<Object> objList, String[] fieldNames, int baseIndex) throws Exception {
        int count = 0;
        if (baseIndex >= objList.size()) {
            return count;
        }
        Class<?> objClass = objList.get(0).getClass();
        Map<String, Method> methodCache = new HashMap<>();
        for (String fieldName : fieldNames) {
            PropertyDescriptor pd = new PropertyDescriptor(fieldName.trim(), objClass);
            methodCache.put(fieldName.trim(), pd.getReadMethod());
        }
        Object baseObj = objList.get(baseIndex);
        for (int i = baseIndex; i < objList.size(); i++) {
            Object obj = objList.get(i);
            boolean allEqual = true;
            for (String fieldName : fieldNames) {
                Method method = methodCache.get(fieldName.trim());
                Object value1 = method.invoke(baseObj);
                Object value2 = method.invoke(obj);
                if (!Objects.equals(value1, value2)) {
                    allEqual = false;
                    break;
                }
            }
            if (allEqual) {
                count++;
            } else {
                break;
            }
        }
        return count;
    }


    @TemplateAction(value = "DeleteTable",description = "删除表格")
    public static void deleteTable(com.aspose.words.Field actionField){
        CompositeNode cellNode = actionField.getStart().getAncestor(NodeType.CELL);
        if(cellNode!=null){
            Cell cell = (Cell) cellNode;
            Table table = (Table) cell.getAncestor(NodeType.TABLE);
            table.remove();
        }
    }

    @TemplateAction(value = "DeleteTableRow",description = "删除表格行")
    public static void deleteTableRow(com.aspose.words.Field actionField){
        CompositeNode cellNode = actionField.getStart().getAncestor(NodeType.CELL);
        if(cellNode!=null){
            Cell cell = (Cell) cellNode;
            Row parentRow = cell.getParentRow();
            if (parentRow != null) {
                parentRow.remove();
            }
        }
    }

    @TemplateAction(value = "DeleteTableColumn",description = "删除表格列")
    public  static void deleteTableColumn(com.aspose.words.Field actionField){
        CompositeNode cellNode = actionField.getStart().getAncestor(NodeType.CELL);
        if(cellNode!=null){
            Cell cell = (Cell) cellNode;
            Row parentRow = cell.getParentRow();
            if (parentRow != null) {
                int columnIndex = parentRow.getCells().indexOf(cell);
                Table table = (Table) cell.getAncestor(NodeType.TABLE);
                for (Row row : table.getRows()) {
                    if (columnIndex < row.getCells().getCount()) {
                        row.getCells().get(columnIndex).remove(); // 删除该列的单元格
                    }
                }
            }
        }
    }

    @TemplateAction(value = "DeleteParagraph",description = "删除段落")
    public  static void deleteParagraph(com.aspose.words.Field actionField){
        CompositeNode paragraCompositeNode = actionField.getStart().getAncestor(NodeType.PARAGRAPH);
        if(paragraCompositeNode!=null){
            Paragraph paragraph = (Paragraph) paragraCompositeNode;
            paragraph.remove();
        }
    }

    private static void mergeCells(Cell startCell, Cell endCell)
    {
        Table parentTable = startCell.getParentRow().getParentTable();

        // Find the row and cell indices for the start and end cell.
        Point startCellPos = new Point(startCell.getParentRow().indexOf(startCell),
                parentTable.indexOf(startCell.getParentRow()));
        Point endCellPos = new Point(endCell.getParentRow().indexOf(endCell), parentTable.indexOf(endCell.getParentRow()));

        // Create a range of cells to be merged based on these indices.
        Rectangle mergeRange = new Rectangle(Math.min(startCellPos.x, endCellPos.x),
                Math.min(startCellPos.y, endCellPos.y),
                Math.abs(endCellPos.x - startCellPos.x) + 1, Math.abs(endCellPos.y - startCellPos.y) + 1);

        for (Row row : parentTable.getRows()) {
            for (Cell cell : row.getCells()) {
                Point currentPos = new Point(row.indexOf(cell), parentTable.indexOf(row));

                // Check if the current cell is inside our merge range, then merge it.
                if (mergeRange.contains(currentPos)) {
                    System.out.println("11111111111111"+cell.getText());
                    cell.getCellFormat().setHorizontalMerge(currentPos.x == mergeRange.getX() ? CellMerge.FIRST : CellMerge.PREVIOUS);
                    cell.getCellFormat().setVerticalMerge(currentPos.y == mergeRange.getY() ? CellMerge.FIRST : CellMerge.PREVIOUS);
                    System.out.println("22222222222222"+cell.getText());
                }
            }
        }
    }

    /**
     * 封装 list 数据到 word 模板中（word表格）
     *
     * @param list      数据
     * @param tableName 表格列表变量名称
     * @return word表格数据DataTable
     */
    private static DataTable fillListData(List<Object> list, String tableName) throws Exception {
        //创建DataTable,并绑定字段
        DataTable dataTable = new DataTable(tableName);
        if (list == null || list.size() == 0) {
            return dataTable;
        }
        Class<?> objClass = list.get(0).getClass();
        Field[] fields = objClass.getDeclaredFields();
        // 绑定表头字段
        for (int i = 0; i < fields.length; i++) {
//            if(fields[i].getType() == java.util.Date.class){
//                dataTable.getColumns().add(fields[i].getName(),fields[i].getType());
//            }else
                dataTable.getColumns().add(fields[i].getName(),fields[i].getType());
        }
        for (Object obj : list) {
            //创建DataRow，封装该行数据
            DataRow dataRow = dataTable.newRow();
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                if(!field.getName().equals("serialVersionUID")){
                    PropertyDescriptor pd = new PropertyDescriptor(field.getName(), objClass);
                    Method method = pd.getReadMethod();
                    dataRow.set(i, method.invoke(obj));
                }
            }
            dataTable.getRows().add(dataRow);
        }
        return dataTable;
    }

    public static void Sign(String url, String fieldName,String signatureUrl) throws Exception {
        byte[] docBytes = HttpUtil.downloadBytes(url);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(docBytes);
        Document doc = new Document(byteArrayInputStream);
//        processFieldImage(fieldName, signatureUrl, doc);
        processCellKeyword(fieldName, signatureUrl, doc);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int saveFormat = getSaveFormat(url);
        doc.save(byteArrayOutputStream, saveFormat);
        // 上传修改后的文档
        upload2MinIOSvr(doc,url);
    }
    public static String toPDF(String url) throws Exception {
        byte[] docBytes = HttpUtil.downloadBytes(url);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(docBytes);
        Document doc = new Document(byteArrayInputStream);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        doc.save(byteArrayOutputStream, SaveFormat.PDF);
        byte[] modifiedDocBytes = byteArrayOutputStream.toByteArray();
        String fileName = getFileNameFromUrl(url).replace(".docx", ".pdf");
        //暂时去掉签章
//        byte[] bytes = SealUtil.signDocument(modifiedDocBytes, fileName);
        // 上传修改后的文档

        String bizPath = getFileFolderFromUrl(url);
        String contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        MultipartFile multipartFile = new MockMultipartFile(fileName,fileName,contentType, modifiedDocBytes);
        return CommonUtils.upload(multipartFile, bizPath, "minio");
    }


    private static void processFieldImage(String fieldName, String signatureUrl, Document doc) throws Exception {
        DocumentBuilder builder = new DocumentBuilder(doc);

        // 遍历文档中的所有字段
        for (com.aspose.words.Field field : doc.getRange().getFields()) {
            String fieldCode = field.getFieldCode();
            if (fieldCode.contains(fieldName)) {
                // 获取当前字段所在的段落
                Paragraph paragraph = (Paragraph) field.getStart().getAncestor(NodeType.PARAGRAPH);
                if (paragraph != null) {
                    // 在字段位置插入图片
                    builder.moveTo(field.getStart());
                    byte[] imgBytes = HttpUtil.downloadBytes(signatureUrl);
                    builder.insertImage(imgBytes, 100, 50); // 宽 100pt, 高 50pt
                    field.remove(); // 删除原有的字段
                }
            }
            if(fieldName.equals("signer") && fieldCode.contains("signingTime")){
                // 如果字段名是 "signer"，并且字段代码包含 "signingTime"，则插入当前时间
                builder.moveTo(field.getStart());
                DateTime now = DateTime.now();
                builder.write(now.toString("yyyy年MM月dd日")); // 写入当前时间
                field.remove(); // 删除原有的字段
            }
        }
        //保存

    }

    private static void processFieldSigning(Document doc) throws Exception {
        DocumentBuilder builder = new DocumentBuilder(doc);

        // 遍历文档中的所有字段
        for (com.aspose.words.Field field : doc.getRange().getFields()) {
            String fieldCode = field.getFieldCode();
            if( fieldCode.contains("signingTime")){
                // 如果字段名是 "signer"，并且字段代码包含 "signingTime"，则插入当前时间
                builder.moveTo(field.getStart());
                DateTime now = DateTime.now();
                builder.write(now.toString("yyyy年MM月dd日")); // 写入当前时间
                field.remove(); // 删除原有的字段
            }
        }
        //保存

    }



    private static void processCellKeyword(String keyword, String signatureUrl, Document doc) throws Exception {
        DocumentBuilder builder = new DocumentBuilder(doc);

        // 遍历文档中的所有表格
        for (Object table : doc.getChildNodes(NodeType.TABLE, true)) {
            for (Row row : ((Table)table).getRows()) {
                for (int i = 0; i < row.getCells().getCount(); i++) {
                    Cell cell = row.getCells().get(i);
                    // 检查单元格中的文本是否包含关键字
                    if (cell.getText().contains(keyword)) {
                        // 获取下一个单元格
                        if (i + 1 < row.getCells().getCount()) {
                            Cell nextCell = row.getCells().get(i + 1);
                            // 在下一个单元格中插入图片
                            builder.moveTo(nextCell.getFirstParagraph());
                            // 清楚单元格内容
                            nextCell.getFirstParagraph().getRuns().clear();
                            byte[] imgBytes = HttpUtil.downloadBytes(signatureUrl);
                            builder.insertImage(imgBytes, 100, 50); // 宽 100pt, 高 50pt
                        }
                    }
                    if(keyword.equals("签    发") ){
                        processFieldSigning(doc);
                    }
                }
            }
        }
    }


    public static void SignReport(String url, String[] fieldNames,String[] signatureUrls) throws Exception {
        byte[] docBytes = HttpUtil.downloadBytes(url);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(docBytes);
        Document doc = new Document(byteArrayInputStream);
        for (int i = 0; i < fieldNames.length; i++) {
            processFieldImage(fieldNames[i], signatureUrls[i], doc);
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        doc.save(byteArrayOutputStream, SaveFormat.PDF);
        // 上传修改后的文档
        upload2MinIOSvr(doc,url);
    }

    private static void upload2MinIOSvr(Document doc,String url) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int saveFormat = getSaveFormat(url);
        doc.save(byteArrayOutputStream, saveFormat);
        byte[] modifiedDocBytes = byteArrayOutputStream.toByteArray();
        String fileName = getFileNameFromUrl(url);
        String bizPath = getFileFolderFromUrl(url);
        String contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        MultipartFile multipartFile = new MockMultipartFile(fileName,fileName,contentType, modifiedDocBytes);
        CommonUtils.upload(multipartFile, bizPath, "minio");
    }

    /**
     * 给word添加水印，铺满文本
     * @param url word文件路径
     * @param text 水印文字
     * @throws Exception
     */
    public static void setWaterMark(String url, String text) throws Exception {
        Document doc = new Document(url);
        Paragraph watermarkPara = new Paragraph(doc);
        //需要根据文本长度修改每行个数
        for(int i=0; i<4; i++) {
            for(int j=0; j<2; j++) {
                Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
                watermark.getTextPath().setText(text);
                watermark.getTextPath().setFontFamily("simsun");
                watermark.setWidth(200);
                watermark.setHeight(40);
                watermark.setRotation(-20);
                watermark.setFillColor(new Color(255, 0, 0));
                watermark.setStrokeColor(new Color(255, 0, 0));
                watermark.setZOrder(-1);
                watermark.setTop(i * 200);
                watermark.setLeft(j * 260);
                watermarkPara.appendChild(watermark);
            }
        }
        for(Section sect: doc.getSections()) {
            HeaderFooter header = sect.getHeadersFooters().getByHeaderFooterType(HeaderFooterType.HEADER_PRIMARY);
            if(header==null) {
                header = new HeaderFooter(doc, HeaderFooterType.HEADER_PRIMARY);
                sect.getHeadersFooters().add(header);
            }
            Node n = watermarkPara.deepClone(true);
            header.appendChild(n);
        }
        upload2MinIOSvr(doc,url);
    }

    private static int getSaveFormat(String url) {
        String extension = FileUtil.getSuffix(getFileNameFromUrl(url));
        int saveFormat = -1;
        if(extension.equals("docx")) {
            saveFormat = SaveFormat.DOCX;
        }else if(extension.equals("pdf")){
            saveFormat = SaveFormat.PDF;
        }else
            saveFormat = SaveFormat.DOC;
        return saveFormat;
    }

    public static String getFileNameFromUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        // 提取最后一个 '/' 后的内容，并解码
        String fileName = StrUtil.subAfter(url, "/", true);
        return fileName.contains(".") ? URLUtil.decode(fileName) : "";
    }
    public static String getFileFolderFromUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        // 提取最后一个 '/' 后的内容，并解码
        String folderName = StrUtil.subAfter(url, MinioUtil.getMinioUrl()  + MinioUtil.getBucketName() + "/", true);
        folderName = StrUtil.subBefore(folderName, "/", true);
        return folderName;
    }

    public  static List<String> GetHyperLinks(String url) throws Exception {
        List<String> hyperLinks = new ArrayList<>();
        if (StrUtil.isNotEmpty(url)) {
            Document doc = new Document(url);
            hyperLinks = StreamSupport.stream(doc.getRange().getFields().spliterator(), false)
                    .filter(field -> field instanceof FieldHyperlink) // 过滤出 FieldHyperlink 类型
                    .map(field -> (FieldHyperlink) field) // 转换为 FieldHyperlink 类型
                    .map(FieldHyperlink::getResult) // 获取 Address 属性
                    .toList(); // 收集结果到 List<String>
        }
        return hyperLinks;
    }

    /**
     * aspose-words:jdk17:24.6 版本
     */
    public static void registerWord246() {
        // 构造一个注册信息
        try {
            Class<?> zzXgCClass = Class.forName("com.aspose.words.zzVTV");
            Constructor<?> constructors = zzXgCClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            // zzZoV = 1
            java.lang.reflect.Field zzXFN = zzXgCClass.getDeclaredField("zzXAm");
            zzXFN.setAccessible(true);
            zzXFN.set(instance, 1);


            java.lang.reflect.Field zzYXJ = zzXgCClass.getDeclaredField("zzYXJ");
            zzYXJ.setAccessible(true);
            zzYXJ.set(instance, 1);


            // zzW8s = 0
            // zzX6D = "8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7" // SerialNumber
            // zzw9 SubscriptionExpiry
            // zzZq3 LicenseExpiry
            // zzW5k <Data> ...
            // zzYON 签名 Signature
            // zzXjC zzZoT 对象 空
            Class<?> zzWXGClass = Class.forName("com.aspose.words.zzWst");
            constructors.setAccessible(true);
            Object zzWXGInstance = constructors.newInstance(null, null);

            java.lang.reflect.Field zzZzA = zzWXGClass.getDeclaredField("zzZXG");
            zzZzA.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZzA.set(null, zzwPValue);

            // 生成文档会掉这个来判断 zzVSF
            Class<?> zzXQoClass = Class.forName("com.aspose.words.zzYfR");
            java.lang.reflect.Field zzHA = zzXQoClass.getDeclaredField("zzX8G");
            zzHA.setAccessible(true);
            zzHA.set(null, 128);
            java.lang.reflect.Field zzWod = zzXQoClass.getDeclaredField("zz3R");
            zzWod.setAccessible(true);
            zzWod.set(null, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}