package org.jeecg.modules.dcs.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;

import java.util.Date;

@TemplateDesigner(value = "Training",description = "培训")
@Data
public class TrainingInfo {
    @Schema(description = "培训时间")
    private Date startTime;
    @Schema(description = "培训地点")
    private String location;
    @Schema(description = "受训部门")
    private String departs;
    @Schema(description = "受训人数")
    private int attendeeQty;
}