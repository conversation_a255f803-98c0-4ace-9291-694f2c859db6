<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.jeecgframework.boot</groupId>
        <artifactId>jeecg-boot-parent</artifactId>
        <version>3.7.1</version>
    </parent>

    <artifactId>jeecg-module-lims_order</artifactId>
    <packaging>pom</packaging>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <modules>
        <module>jeecg-module-lims_order-api</module>
        <module>jeecg-module-lims_order-biz</module>
        <module>jeecg-module-lims_order-start</module>
    </modules>
<!--    <dependencies>-->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.boot</groupId>-->
<!--            <artifactId>jeecg-boot-base-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.boot</groupId>-->
<!--            <artifactId>jeecg-module-lims_core</artifactId>-->
<!--            <version>${jeecgboot.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.jeecgframework.boot</groupId>-->
<!--            <artifactId>jeecg-module-wx</artifactId>-->
<!--            <version>${jeecgboot.version}</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->
<!--    </dependencies>-->

</project>