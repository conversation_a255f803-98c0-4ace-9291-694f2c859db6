package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.Instrument;
import org.jeecg.modules.lims_core.service.IInstrumentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 设备
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Tag(name="设备")
@RestController
@RequestMapping("/lims_core/instrument")
@Slf4j
public class InstrumentController extends JeecgController<Instrument, IInstrumentService> {
	@Autowired
	private IInstrumentService instrumentService;
	
	/**
	 * 分页列表查询
	 *
	 * @param instrument
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "设备-分页列表查询")
	@Operation(summary="设备-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Instrument>> queryPageList(Instrument instrument,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<Instrument> queryWrapper = QueryGenerator.initQueryWrapper(instrument, req.getParameterMap());
		Page<Instrument> page = new Page<Instrument>(pageNo, pageSize);
		IPage<Instrument> pageList = instrumentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param instrument
	 * @return
	 */
	@AutoLog(value = "设备-添加")
	@Operation(summary="设备-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:instrument:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Instrument instrument) {
		JSONObject formData = new JSONObject();
		formData.put("typeId",instrument.getTypeId());
		instrument.setCode(FillRuleUtil.executeRule(FillRuleConstant.INSTRUMENT,formData).toString());
		instrumentService.save(instrument);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param instrument
	 * @return
	 */
	@AutoLog(value = "设备-编辑")
	@Operation(summary="设备-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:instrument:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Instrument instrument) {
		instrumentService.updateById(instrument);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备-通过id删除")
	@Operation(summary="设备-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:instrument:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		instrumentService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "设备-批量删除")
	@Operation(summary="设备-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:instrument:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.instrumentService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "设备-通过id查询")
	@Operation(summary="设备-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Instrument> queryById(@RequestParam(name="id",required=true) String id) {
		Instrument instrument = instrumentService.getById(id);
		if(instrument==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(instrument);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param instrument
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:instrument:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Instrument instrument) {
        return super.exportXls(request, instrument, Instrument.class, "设备");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:instrument:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Instrument.class);
    }

}
