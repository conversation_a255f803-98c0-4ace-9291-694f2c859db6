package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysConsumptiveType;
import org.jeecg.modules.lims_core.mapper.SysConsumptiveTypeMapper;
import org.jeecg.modules.lims_core.service.ISysConsumptiveTypeService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 耗材类别
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Service
public class SysConsumptiveTypeServiceImpl extends ServiceImpl<SysConsumptiveTypeMapper, SysConsumptiveType> implements ISysConsumptiveTypeService {

}
