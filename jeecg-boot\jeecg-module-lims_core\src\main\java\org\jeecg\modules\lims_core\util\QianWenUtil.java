package org.jeecg.modules.lims_core.util;

import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

public class QianWenUtil {

    static String url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    static String apiKey = "sk-09cc97888cb64d5da380392e2ffb6ee3";
    static String model = "qwen-plus";

    public static String sendChatCompletion(String userContent) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + apiKey);
        headers.set("Content-Type", "application/json");

        // 设置请求体
        String requestBody = String.format(
            "{ \"model\": \"%s\", \"messages\": [  { \"role\": \"user\", \"content\": \"%s\" } ] }",
                model, userContent
        );

        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

        // 发送 POST 请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        JSONObject jsonObject = JSONObject.parseObject(response.getBody());
        String content = jsonObject.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");

        return content;
    }
    public static String getConclustion(String rawResult, String limit) {
        StringBuffer sb = new StringBuffer();
        sb.append("判定标准为:"+limit + ";");
        sb.append("原始结果为:" + rawResult + ";");
        sb.append("请根据判定标准和原始结果进行判断，给出结论。");
        sb.append("只需回答符合规定/不符合规定");
        String result = sendChatCompletion(sb.toString());
        // 处理结果
        return result;
    }
}
