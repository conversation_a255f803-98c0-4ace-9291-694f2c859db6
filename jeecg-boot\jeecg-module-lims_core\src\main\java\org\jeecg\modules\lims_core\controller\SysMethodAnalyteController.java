package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.service.ISysMethodAnalyteService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 检测指标
 * @Author: jeecg-boot
 * @Date:   2025-03-26
 * @Version: V1.0
 */
@Tag(name="检测指标")
@RestController
@RequestMapping("/lims_core/sysMethodAnalyte")
@Slf4j
public class SysMethodAnalyteController extends JeecgController<SysMethodAnalyte, ISysMethodAnalyteService> {
	@Autowired
	private ISysMethodAnalyteService sysMethodAnalyteService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysMethodAnalyte
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "检测指标-分页列表查询")
	@Operation(summary="检测指标-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysMethodAnalyte>> queryPageList(SysMethodAnalyte sysMethodAnalyte,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysMethodAnalyte> queryWrapper = QueryGenerator.initQueryWrapper(sysMethodAnalyte, req.getParameterMap());
		Page<SysMethodAnalyte> page = new Page<SysMethodAnalyte>(pageNo, pageSize);
		IPage<SysMethodAnalyte> pageList = sysMethodAnalyteService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysMethodAnalyte
	 * @return
	 */
	@AutoLog(value = "检测指标-添加")
	@Operation(summary="检测指标-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysMethodAnalyte sysMethodAnalyte) {
        //根据方法id查询已存在多少条数据
		QueryWrapper<SysMethodAnalyte> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("method_id",sysMethodAnalyte.getMethodId());
        List<SysMethodAnalyte> list=sysMethodAnalyteService.list(queryWrapper);
		int num=0;
		num=list.size()+1;
		sysMethodAnalyte.setSortNum(num);
		sysMethodAnalyteService.save(sysMethodAnalyte);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysMethodAnalyte
	 * @return
	 */
	@AutoLog(value = "检测指标-编辑")
	@Operation(summary="检测指标-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysMethodAnalyte sysMethodAnalyte) {
		sysMethodAnalyteService.updateById(sysMethodAnalyte);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "检测指标-通过id删除")
	@Operation(summary="检测指标-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysMethodAnalyteService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "检测指标-批量删除")
	@Operation(summary="检测指标-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysMethodAnalyteService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "检测指标-通过id查询")
	@Operation(summary="检测指标-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysMethodAnalyte> queryById(@RequestParam(name="id",required=true) String id) {
		SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteService.getById(id);
		if(sysMethodAnalyte==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysMethodAnalyte);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysMethodAnalyte
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysMethodAnalyte sysMethodAnalyte) {
        return super.exportXls(request, sysMethodAnalyte, SysMethodAnalyte.class, "检测指标");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysMethodAnalyte.class);
    }

}
