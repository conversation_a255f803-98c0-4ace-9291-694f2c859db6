package org.jeecg.modules.crm.mapper;

import java.util.List;
import org.jeecg.modules.crm.entity.SysCustomerContact;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 客户联系人
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
public interface SysCustomerContactMapper extends BaseMapper<SysCustomerContact> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<SysCustomerContact>
   */
	public List<SysCustomerContact> selectByMainId(@Param("mainId") String mainId);
}
