package org.jeecg.modules.crm.service.impl;

import org.jeecg.modules.crm.entity.SysCustomerContact;
import org.jeecg.modules.crm.mapper.SysCustomerContactMapper;
import org.jeecg.modules.crm.service.ISysCustomerContactService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 客户联系人
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Service
public class SysCustomerContactServiceImpl extends ServiceImpl<SysCustomerContactMapper, SysCustomerContact> implements ISysCustomerContactService {
	
	@Autowired
	private SysCustomerContactMapper sysCustomerContactMapper;
	
	@Override
	public List<SysCustomerContact> selectByMainId(String mainId) {
		return sysCustomerContactMapper.selectByMainId(mainId);
	}
}
