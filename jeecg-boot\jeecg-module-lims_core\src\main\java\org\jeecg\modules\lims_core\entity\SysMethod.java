package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 方法
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Schema(description="方法")
@Data
@TableName("sys_method")
public class SysMethod implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**标准*/
	@Excel(name = "标准", width = 15, dictTable = "sys_standard", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_standard", dicText = "name", dicCode = "id")
    @Schema(description = "标准")
    private java.lang.String standardId;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
    /**标准价格*/
    @Excel(name = "标准价格", width = 15)
    @Schema(description = "标准价格")
    private java.math.BigDecimal stdPrice;
	/**平行数量*/
	@Excel(name = "平行数量", width = 15)
    @Schema(description = "平行数量")
    private java.lang.Integer duplicateQty;
	/**空白数量*/
	@Excel(name = "空白数量", width = 15)
    @Schema(description = "空白数量")
    private java.lang.Integer blkQty;
	/**加标数量*/
	@Excel(name = "加标数量", width = 15)
    @Schema(description = "加标数量")
    private java.lang.Integer stdQty;
	/**产品*/
	@Excel(name = "产品", width = 15, dictTable = "sys_product", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_product", dicText = "name", dicCode = "id")
    @Schema(description = "产品")
    private java.lang.String productId;
	/**实验步骤*/
	@Excel(name = "实验步骤", width = 15)
    @Schema(description = "实验步骤")
    private java.lang.String labSteps;
	/**记录模板*/
	@Excel(name = "记录模板", width = 15, dictTable = "sys_template", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_template", dicText = "name", dicCode = "id")
    @Schema(description = "记录模板")
    private java.lang.String templateId;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "standard_status")
    @Dict(dicCode = "standard_status")
    @Schema(description = "状态")
    private java.lang.String effectiveStatus;
    /**技术性质类别*/
    @Excel(name = "技术性质类别", width = 15, dicCode = "nature_type")
    @Dict(dicCode = "nature_type")
    @Schema(description = "技术性质类别")
    private java.lang.String natureTypeId;
	/**作废日期*/
	@Excel(name = "作废日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "作废日期")
    private java.util.Date abandonedDate;
	/**适用范围*/
	@Excel(name = "适用范围", width = 15)
    @Schema(description = "适用范围")
    private java.lang.String usedScope;
	/**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    @Excel(name = "更新人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**检测能力*/
    @Excel(name = "检测能力", width = 15, dictTable = "sys_capability", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_capability", dicText = "name", dicCode = "id")
    @Schema(description = "检测能力")
    private java.lang.String cid;

    @TableField(exist = false)
    private String StandardName;
}
