package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.Consumptive;
import org.jeecg.modules.lims_core.mapper.ConsumptiveMapper;
import org.jeecg.modules.lims_core.service.IConsumptiveService;
import org.jeecg.modules.lims_core.vo.ConsumptiveVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 耗材台账
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
@Service
public class ConsumptiveServiceImpl extends ServiceImpl<ConsumptiveMapper, Consumptive> implements IConsumptiveService {
    @Autowired
    private ConsumptiveMapper consumptiveMapper;

    @Override
    public IPage<ConsumptiveVO> queryPageList(Page<Consumptive> page, Wrapper<Consumptive> wrapper) {
        return consumptiveMapper.queryPageList(page,wrapper);
    }

}
