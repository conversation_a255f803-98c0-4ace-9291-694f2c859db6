package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 余物退回申请
 * @Author: jeecg-boot
 * @Date:   2025-05-27
 * @Version: V1.0
 */
@Data
@TableName("warehouse_in_apply")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="余物退回申请")
public class WarehouseInApply implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**物品编号*/
	@Excel(name = "物品编号", width = 15)
    @Schema(description = "物品编号")
    private java.lang.String articleNo;
	/**物品类型*/
	@Excel(name = "物品类型", width = 15, dicCode = "warehouse_goods_type")
	@Dict(dicCode = "warehouse_goods_type")
    @Schema(description = "物品类型")
    private java.lang.String articleTypeId;
	/**数量*/
	@Excel(name = "数量", width = 15)
    @Schema(description = "数量")
    private java.lang.String amount;
	/**单位*/
	@Excel(name = "单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "单位")
    private java.lang.String unitId;
    @Excel(name = "使用的数量", width = 15)
    @Schema(description = "使用的数量")
    private java.lang.String usageQuantity;
	/**操作原因*/
	@Excel(name = "操作原因", width = 15, dicCode = "warehouse_operation_reason")
	@Dict(dicCode = "warehouse_operation_reason")
    @Schema(description = "操作原因")
    private java.lang.String operationReasonId;
	/**处理状态*/
	@Excel(name = "处理状态", width = 15, dicCode = "warehouse_out_apply_state")
	@Dict(dicCode = "warehouse_out_apply_state")
    @Schema(description = "处理状态")
    private java.lang.String status;
	/**创建人*/
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

    @TableField(exist = false)
    private String warehouseboxid;
    @TableField(exist = false)
    private String warehousebox;
    /**
     * 物品名称
     */
    @TableField(exist = false)
    private String goodsName;
    /**
     * 开启人
     */
    @TableField(exist = false)
    private String openerId;
    /**
     * 开启日期
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openDate;
    /**
     * 确认效期
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date confirmValidDate;
}
