package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 精密度要求
 * @Author: jeecg-boot
 * @Date:   2025-03-26
 * @Version: V1.0
 */
@Data
@TableName("sys_method_analyte_precision")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="精密度要求")
public class SysMethodAnalytePrecision implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**method_analyte_id*/
	@Excel(name = "method_analyte_id", width = 15)
    @Schema(description = "method_analyte_id")
    private java.lang.String maId;
	/**决定因素类型*/
	@Excel(name = "决定因素类型", width = 15)
    @Schema(description = "决定因素类型")
    private java.lang.String paraTypeId;
	/**决定因素*/
	@Excel(name = "决定因素", width = 15)
    @Schema(description = "决定因素")
    private java.lang.String paraValue;
	/**精密度类型*/
	@Excel(name = "精密度类型", width = 15)
    @Schema(description = "精密度类型")
    private java.lang.String precisionTypeId;
	/**精密度要求*/
	@Excel(name = "精密度要求", width = 15)
    @Schema(description = "精密度要求")
    private java.lang.String precisionReq;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
