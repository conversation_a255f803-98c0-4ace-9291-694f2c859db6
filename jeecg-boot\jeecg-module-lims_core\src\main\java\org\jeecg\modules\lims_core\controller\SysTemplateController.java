package org.jeecg.modules.lims_core.controller;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import cn.hutool.core.util.ClassUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.annotation.TemplateAction;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.lims_core.dto.TreeNode;
import org.jeecg.modules.lims_core.entity.SysTemplate;
import org.jeecg.modules.lims_core.service.ISysTemplateService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

 /**
 * @Description: 模板
 * @Author: jeecg-boot
 * @Date:   2025-02-11
 * @Version: V1.0
 */
@Tag(name="模板")
@RestController
@RequestMapping("/lims_core/sysTemplate")
@Slf4j
public class SysTemplateController extends JeecgController<SysTemplate, ISysTemplateService>{
	@Autowired
	private ISysTemplateService sysTemplateService;

	/**
	 * 分页列表查询
	 *
	 * @param sysTemplate
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "模板-分页列表查询")
	@Operation(summary="模板-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<IPage<SysTemplate>> queryPageList(SysTemplate sysTemplate,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
        if(hasQuery != null && "true".equals(hasQuery)){
            QueryWrapper<SysTemplate> queryWrapper =  QueryGenerator.initQueryWrapper(sysTemplate, req.getParameterMap());
            List<SysTemplate> list = sysTemplateService.queryTreeListNoPage(queryWrapper);
            IPage<SysTemplate> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = sysTemplate.getParentId();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            sysTemplate.setParentId(null);
            QueryWrapper<SysTemplate> queryWrapper = QueryGenerator.initQueryWrapper(sysTemplate, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("parent_id", parentId);
            Page<SysTemplate> page = new Page<SysTemplate>(pageNo, pageSize);
            IPage<SysTemplate> pageList = sysTemplateService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
	}

	 /**
	  * 【vue3专用】加载节点的子数据
	  *
	  * @param pid
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = sysTemplateService.queryListByPid(pid);
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】加载一级节点/如果是同步 则所有数据
	  *
	  * @param async
	  * @param pcode
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = sysTemplateService.queryListByCode(pcode);
			 if (!async) {
				 loadAllChildren(ls);
			 }
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】递归求子节点 同步加载用到
	  *
	  * @param ls
	  */
	 private void loadAllChildren(List<SelectTreeModel> ls) {
		 for (SelectTreeModel tsm : ls) {
			 List<SelectTreeModel> temp = sysTemplateService.queryListByPid(tsm.getKey());
			 if (temp != null && temp.size() > 0) {
				 tsm.setChildren(temp);
				 loadAllChildren(temp);
			 }
		 }
	 }

	 /**
      * 获取子数据
      * @param sysTemplate
      * @param req
      * @return
      */
	//@AutoLog(value = "模板-获取子数据")
	@Operation(summary="模板-获取子数据")
	@GetMapping(value = "/childList")
	public Result<IPage<SysTemplate>> queryPageList(SysTemplate sysTemplate,HttpServletRequest req) {
		QueryWrapper<SysTemplate> queryWrapper = QueryGenerator.initQueryWrapper(sysTemplate, req.getParameterMap());
		List<SysTemplate> list = sysTemplateService.list(queryWrapper);
		IPage<SysTemplate> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
		return Result.OK(pageList);
	}

    /**
      * 批量查询子节点
      * @param parentIds 父ID（多个采用半角逗号分割）
      * @return 返回 IPage
      * @param parentIds
      * @return
      */
	//@AutoLog(value = "模板-批量获取子数据")
    @Operation(summary="模板-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<SysTemplate> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("parent_id", parentIdList);
            List<SysTemplate> list = sysTemplateService.list(queryWrapper);
            IPage<SysTemplate> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }
	
	/**
	 *   添加
	 *
	 * @param sysTemplate
	 * @return
	 */
	@AutoLog(value = "模板-添加")
	@Operation(summary="模板-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_template:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysTemplate sysTemplate) {
		sysTemplateService.addSysTemplate(sysTemplate);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysTemplate
	 * @return
	 */
	@AutoLog(value = "模板-编辑")
	@Operation(summary="模板-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_template:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysTemplate sysTemplate) {
		sysTemplateService.updateSysTemplate(sysTemplate);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "模板-通过id删除")
	@Operation(summary="模板-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_template:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysTemplateService.deleteSysTemplate(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "模板-批量删除")
	@Operation(summary="模板-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_template:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysTemplateService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "模板-通过id查询")
	@Operation(summary="模板-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysTemplate> queryById(@RequestParam(name="id",required=true) String id) {
		SysTemplate sysTemplate = sysTemplateService.getById(id);
		if(sysTemplate==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysTemplate);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysTemplate
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_template:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysTemplate sysTemplate) {
		return super.exportXls(request, sysTemplate, SysTemplate.class, "模板");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_template:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, SysTemplate.class);
    }
	 @GetMapping("/getDataSource")
	 public List<TreeNode> getDataSource(@RequestParam String templateId) {
		 TreeNode tnDataSource = new TreeNode();
		 tnDataSource.setText("数据源");
		 // 扫描带有 TemplateDesigner 注解的类
		 Set<Class<?>> annotatedClasses = ClassUtil.scanPackageByAnnotation("org.jeecg.modules.lims_core.vo", TemplateDesigner.class);
		 List<Class<?>> tables = new ArrayList<>();
		 tables.addAll(annotatedClasses);
		 List<TreeNode> dataSourceChildren = new ArrayList<>();
		 for(int c=0;c<tables.size();c++) {
			 Class<?> clazz = tables.get(c);
			 TemplateDesigner annotation = clazz.getAnnotation(TemplateDesigner.class);// 获取类上的 TemplateDesigner 注解
			 if (annotation != null) {
				 TreeNode classNode = new TreeNode();
				 classNode.setId("c" + String.valueOf(c));
				 // 设置类节点文本格式：类名（注解描述）
				 classNode.setText(clazz.getSimpleName().replace("VO","") + "(" + annotation.description() + ")");
				 // 获取所有字段
				 Field[] fields = clazz.getDeclaredFields();
				 List<TreeNode> fieldNodes = new ArrayList<>();
				 // 遍历字段，找出带有 TemplateField 注解的字段
				 for(int f=0;f<fields.length;f++) {
					 Field field = fields[f];
					 // 排除List类型字段
					 if(!List.class.isAssignableFrom(field.getType())) {
						TemplateField fieldAnnotation = field.getAnnotation(TemplateField.class);
						if (fieldAnnotation != null) {
							TreeNode fieldNode = new TreeNode();
							// 从 TemplateField 注解的 description 属性获取描述
							String description = fieldAnnotation.description();
							fieldNode.setId("f" + String.valueOf(f));
							fieldNode.setText(field.getName() + "(" + description + ")");
							fieldNodes.add(fieldNode);
						}
					 }
				 }
				 // 设置类节点的子节点（字段节点）
				 classNode.setChildren(fieldNodes);
				 dataSourceChildren.add(classNode);
			 }
		 }
		 // 设置数据源节点的子节点
		 tnDataSource.setChildren(dataSourceChildren);

		 List<SelectTreeModel> subReports = sysTemplateService.queryListByPid(templateId);
		 TreeNode tnSubReport = null;
		 if(subReports.size()>0){
			tnSubReport = new TreeNode();
			 tnSubReport.setText("子模板");
			List<TreeNode> subReportChildren = new ArrayList<>();
			for (SelectTreeModel subReport : subReports) {
				TreeNode subNode = new TreeNode();
				subNode.setId(subReport.getKey());
				subNode.setText(subReport.getTitle());
				subReportChildren.add(subNode);
			}
			tnSubReport.setChildren(subReportChildren);
		 }

		 TreeNode tnAction = new TreeNode();
		 tnAction.setText("动作");
		 // 动态获取 TemplateAction 注解的方法
		 Set<Class<?>> classes = ClassUtil.scanPackage("org.jeecg.modules.oo.util");
		 List<TreeNode> actionChildren = new ArrayList<>();
		 for (Class<?> clazz : classes) {
			 Method[] methods = clazz.getDeclaredMethods();
			 for(int m=0;m<methods.length;m++) {
				 Method method = methods[m];
				 // 检查方法是否带有 TemplateAction 注解
				 TemplateAction actionAnnotation = method.getAnnotation(TemplateAction.class);
				 if (actionAnnotation != null) {
					 String actionText = actionAnnotation.value() + "(" + actionAnnotation.description() + ")";
					 TreeNode actionNode = new TreeNode();
					 actionNode.setId("a" + String.valueOf(m));
					 actionNode.setText(actionText);
					 actionChildren.add(actionNode);
				 }
			 }
		 }
		 tnAction.setChildren(actionChildren);

		 if(tnSubReport == null)
		 	return Arrays.asList(tnDataSource, tnAction);
		 else
		 	return Arrays.asList(tnDataSource, tnSubReport, tnAction);
	 }
}
