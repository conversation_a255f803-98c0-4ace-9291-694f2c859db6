package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 模板
 * @Author: jeecg-boot
 * @Date:   2025-04-10
 * @Version: V1.0
 */
@Data
@TableName("sys_template")
@Schema(description="模板")
public class SysTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private String name;
	/**类别*/
	@Excel(name = "类别", width = 15, dicCode = "sys_template_type")
	@Dict(dicCode = "sys_template_type")
    @Schema(description = "类别")
    private String typeId;
    /**语言*/
    @Excel(name = "语言", width = 15, dicCode = "report_language")
    @Dict(dicCode = "report_language")
    @Schema(description = "语言")
    private java.lang.String reportLanguage;
	/**适用的营销产品*/
	@Excel(name = "适用的营销产品", width = 15, dictTable = "sys_product_package", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_product_package", dicText = "name", dicCode = "id")
    @Schema(description = "适用的营销产品")
    private String packageId;
	/**适用的检测能力*/
	@Excel(name = "适用的检测能力", width = 15, dictTable = "capability", dicText = "name", dicCode = "id")
	@Dict(dictTable = "capability", dicText = "name", dicCode = "id")
    @Schema(description = "适用的检测能力")
    private String capabilityId;
	/**电子文件*/
	@Excel(name = "电子文件", width = 15)
    @Schema(description = "电子文件")
    private String url;
	/**循环参数*/
	@Excel(name = "循环参数", width = 15)
    @Schema(description = "循环参数")
    private String loopPara;
	/**可用*/
    @Excel(name = "可用", width = 15,replace = {"是_1","否_0"} )
    @Schema(description = "可用")
    private Integer isEnabled;
	/**父模板ID*/
	@Excel(name = "父模板ID", width = 15)
    @Schema(description = "父模板ID")
    private String parentId;
	/**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**是否有子节点*/
	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
    @Schema(description = "是否有子节点")
    private String hasChild;
}
