package org.jeecg.modules.oo.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.TokenUtils;
import org.jeecg.config.OnlyOfficeConfig;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.crm.entity.SysCustomer;
import org.jeecg.modules.crm.entity.SysCustomerContact;
import org.jeecg.modules.crm.service.ISysCustomerContactService;
import org.jeecg.modules.crm.service.ISysCustomerService;
import org.jeecg.modules.dcs.dto.TrainingDocInfo;
import org.jeecg.modules.dcs.dto.TrainingInfo;
import org.jeecg.modules.dcs.dto.TrainingRecordInfo;
import org.jeecg.modules.dcs.entity.DcsDoc;
import org.jeecg.modules.dcs.entity.Training;
import org.jeecg.modules.dcs.entity.TrainingRecord;
import org.jeecg.modules.dcs.service.IDcsDocService;
import org.jeecg.modules.dcs.service.ITrainingRecordService;
import org.jeecg.modules.dcs.service.ITrainingService;
import org.jeecg.modules.dcs.vo.TrainingVo;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.TestTaskFlowMapper;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.vo.ReportRequestVO;
import org.jeecg.modules.lims_order.entity.*;
import org.jeecg.modules.lims_order.service.*;
import org.jeecg.modules.oo.controller.dto.DocumentInfo;
import org.jeecg.common.api.dto.message.EmailDTO;
import org.jeecg.modules.oo.service.ISignService;
import org.jeecg.modules.oo.util.WordUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@RestController
@RequestMapping("/oo")
@Slf4j
public class OOController {
    @Autowired
    private IDcsDocService dcsDocService;

    @Autowired
    private ISysTemplateService sysTemplateService;

    @Autowired
    private ITrainingService trainingService;

    @Autowired
    private ISysMethodService sysMethodService;

    @Autowired
    private ITrainingRecordService trainingRecordService;

    @Autowired
    private IBizOrderService bizOrderService;

    @Autowired
    private ISysCustomerService sysCustomerService;

    @Autowired
    private ISysCustomerContactService sysCustomerContactService;

    @Autowired
    private ITestService testService;

    @Autowired
    private IQuotationService quotationService;

    @Autowired
    private ISysProductService sysProductService;

    @Autowired
    private ISampleService sampleService;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private ISysStandardService sysStandardService;

    @Autowired
    private IReportService reportService;

    @Autowired
    private ITestTaskService testTaskService;

    @Autowired
    private TestTaskFlowMapper testTaskFlowMapper;
    @Resource
    private OnlyOfficeConfig onlyOfficeConfig;

    @Autowired
    private ISignService signService;

    private static final String FLOW_STEP_1 = "业务受理";
    private static final String FLOW_STEP_2 = "仓库入库";
    private static final String FLOW_STEP_3 = "PM确认";
    private static final String FLOW_STEP_4 = "任务指派";
    private static final String FLOW_STEP_5 = "结果录入";
    private static final String FLOW_STEP_6 = "结果复核";
    private static final String FLOW_STEP_7 = "报告编制";
    private static final String FLOW_STEP_8 = "报告审核";
    private static final String FLOW_STEP_9 = "报告签发";

    private static final HashMap<String, List<String>> extensionMap = new HashMap<>();

    // 初始化扩展名映射
    static {
        extensionMap.put("word", Arrays.asList(
                "doc", "docm", "docx", "docxf", "dot", "dotm", "dotx", "epub", "fb2", "fodt", "htm", "html", "mht",
                "mhtml",
                "odt", "oform", "ott", "rtf", "stw", "sxw", "txt", "wps", "wpt", "xml"));
        extensionMap.put("cell", Arrays.asList(
                "csv", "et", "ett", "fods", "ods", "ots", "sxc", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx",
                "xml"));
        extensionMap.put("slide", Arrays.asList(
                "dps", "dpt", "fodp", "odp", "otp", "pot", "potm", "potx", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx",
                "sxi"));
        extensionMap.put("pdf", Arrays.asList("djvu", "oxps", "pdf", "xps"));
    }

    private String getDocumentType(String fileExtension) {
        AtomicReference<String> r = new AtomicReference<>();
        Optional<Map.Entry<String, List<String>>> foundEntry = extensionMap.entrySet().stream()
                .filter(entry -> entry.getValue().contains(fileExtension))
                .findFirst();
        foundEntry.ifPresent(entry -> r.set(entry.getKey()));
        return r.get();
    }
    /**
     * 获取onlyoffice配置接口
     *  *
     *   * @param biz 业务前缀，可以是表名
     * 	 * @param bizId 业务表ID
     * 	 * @param permission 业务表附件权限配置信息
     */
    @GetMapping("/editor-config")
    public Result<OnlyOfficeConfig.Config> getConfig(@RequestParam String biz, @RequestParam String bizId, @RequestParam(required = false) String permission,HttpServletRequest request) {
        OnlyOfficeConfig.Config configuration = getConfiguration(biz, bizId, permission,null);
        return Result.OK(configuration);
    }

    private OnlyOfficeConfig.Config getConfiguration(String biz, String bizId, String permission,String urlFieldName) {
        DocumentInfo docInfo = getDocumentInfo(biz, bizId, permission,urlFieldName);
        OnlyOfficeConfig.Config config = onlyOfficeConfig.getConfig();
        OnlyOfficeConfig.Config configuration = new OnlyOfficeConfig.Config();
        OnlyOfficeConfig.Config.Document documentConfig = new OnlyOfficeConfig.Config.Document();
        String timestamp = String.valueOf(System.nanoTime()) ;
        documentConfig.setKey(biz + bizId + "_" + timestamp );
        documentConfig.setTitle(docInfo.getFileName());
        documentConfig.setFileType(FileUtil.getSuffix(docInfo.getUrl()));
        documentConfig.setPermissions(docInfo.getPermissions());
        documentConfig.setUrl(docInfo.getUrl());
        //documentConfig.setUrl(docInfo.getUrl() + "?t=" + timestamp);
        LoginUser sysUser = SecureUtil.currentUser();
        OnlyOfficeConfig.Config.EditorConfig editorConfig = config.getEditorConfig();
        if(documentConfig.getPermissions()!=null && documentConfig.getPermissions().getEdit())
            editorConfig.setMode("edit");
        else
            editorConfig.setMode("view");
        editorConfig.setCallbackUrl("https://gbjc.cc/jeecg-boot/oo/callback");
        editorConfig.setLang("zh-CN");
        OnlyOfficeConfig.Config.EditorConfig.Customization.Close close = new OnlyOfficeConfig.Config.EditorConfig.Customization.Close();
        close.setVisible(true);
        OnlyOfficeConfig.Config.EditorConfig.User user = editorConfig.getUser();
        user.setId(String.valueOf(sysUser.getId()));
        user.setName(sysUser.getRealname());
        user.setImage(sysUser.getAvatar());
        editorConfig.setUser(user);
        OnlyOfficeConfig.Config.EditorConfig.Plugins plugins = new OnlyOfficeConfig.Config.EditorConfig.Plugins();
        plugins.setAutostart(docInfo.getAutostart());
        plugins.setPluginsData(docInfo.getPluginsData());
        plugins.setOptions(docInfo.getPluginOptions());
        editorConfig.setPlugins(plugins);
        configuration.setEditorConfig(editorConfig);
        configuration.setDocumentType(this.getDocumentType(FileUtil.getSuffix(docInfo.getUrl())));
        configuration.setDocument(documentConfig);
        configuration.setType(config.getType());
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(onlyOfficeConfig.getSecret());
        SecretKeySpec keySpec = new SecretKeySpec(apiKeySecretBytes, "hs256");
        JWTSigner signer = JWTSignerUtil.createSigner("hs256", keySpec);
        HashMap<String, Object> claims = new HashMap<>();
        claims.put("document", documentConfig);
        claims.put("editorConfig", editorConfig);
        claims.put("documentType", configuration.getDocumentType());
        claims.put("type", configuration.getType());
        JWT jwt = new JWT();
        for (String key : claims.keySet()) {
            jwt.setPayload(key, claims.get(key));
        }
        String token = jwt.sign(signer);
        configuration.setToken(token);
        configuration.setStatus(docInfo.getStatus());
        configuration.setReport(docInfo.getReport());
        return configuration;
    }

    @NotNull
    private DocumentInfo getDocumentInfo(String biz, String docId, String permission,String urlFieldName) {
        DocumentInfo docInfo = new DocumentInfo();
        switch (biz) {
            case "dcs" -> {
                docInfo.setId(docId);
                DcsDoc docDo = dcsDocService.getById(docId);
                docInfo.setFileName(docDo.getDocName());
                docInfo.setUrl(docDo.getUrl());
                if (permission != null)
                    docInfo.setPermissions(getDcsPermission(permission, docDo));
                List<String> pluginsData = new ArrayList<>();
                pluginsData.add("/ooplugins/dcs/config.json");
                docInfo.setPluginsData(pluginsData);
            }
            case "sys_template" -> {
                docInfo.setId(docId);
                SysTemplate template = sysTemplateService.getById(docId);
                docInfo.setFileName(FileUtil.getName(template.getUrl()));
                docInfo.setUrl(template.getUrl());
                OnlyOfficeConfig.Config.Document.Permissions permissions = new OnlyOfficeConfig.Config.Document.Permissions();
                permissions.setEdit(true);
                permissions.setReview(false);
                permissions.setPrint(true);
                permissions.setDownload(true);
                docInfo.setPermissions(permissions);
                List<String> autostart = new ArrayList<>();
                autostart.add("asc.{11700c35-1fdb-4e37-9edb-b31637139602}");
                docInfo.setAutostart(autostart);
                List<String> pluginsData = new ArrayList<>();
                pluginsData.add("/ooplugins/template_designer/config.json");
                docInfo.setPluginsData(pluginsData);
            }
            case "training" -> {
                Training training = trainingService.getById(docId);
                docInfo.setId(docId);
                docInfo.setUrl(training.getRecordUrl());
                docInfo.setFileName(FileUtil.getName(training.getRecordUrl()));
            }
            case "sys_method" -> {
                SysMethod sysMethod = sysMethodService.getById(docId);
                docInfo.setId(docId);
                String url = MinioUtil.getMinioUrl() + MinioUtil.getBucketName() + "/temp/" + sysMethod.getName() + ".docx";
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
            }
            case "test" -> {
                Test test = testService.getById(docId);
                TestTask testTask = testTaskService.getById(test.getTaskId());
                SysMethod sysMethod = sysMethodService.getById(testTask.getMethodId());
                Sample sample = sampleService.getById(testTask.getSampleId());
                docInfo.setId(docId);
                String url = MinioUtil.getMinioUrl() + MinioUtil.getBucketName() + "/lims/原始记录/" + sysMethod.getName() + "/" + sample.getSampleNo() + ".docx";
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
            }
            case "sys_standard_test" -> {
                Test test = testService.getById(docId);
                TestTask testTask = testTaskService.getById(test.getTaskId());
                SysMethod sysMethod = sysMethodService.getById(testTask.getMethodId());
                SysStandard sysStandard = sysStandardService.getById(sysMethod.getStandardId());
                docInfo.setId(sysStandard.getId());
                String url = sysStandard.getUrl();
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
            }
            case "task" -> {
                TestTask testTask = testTaskService.getById(docId);
                SysMethod sysMethod = sysMethodService.getById(testTask.getMethodId());
                SysStandard sysStandard = sysStandardService.getById(sysMethod.getStandardId());
                docInfo.setId(sysStandard.getId());
                String url = sysStandard.getUrl();
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
            }
            case "test_chart" -> {
                Test test = testService.getById(docId);
                docInfo.setId(docId);
                String url = test.getRawResultUrl();////?rawDataUrl
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
            }
            case "report" -> {
                ReportRequestVO report = reportService.queryPageList(new Page<>(0,10), new QueryWrapper<ReportRequestVO>().eq("id", docId)).getRecords().get(0);

                docInfo.setId(docId);
                String url = report.getUrl();
                if(urlFieldName!=null)
                    url = report.getFinalUrl();
                docInfo.setUrl(url);
                docInfo.setStatus(report.getReportstatus());
                docInfo.setReport(report);
                docInfo.setFileName(FileUtil.getName(url));
                OnlyOfficeConfig.Config.Document.Permissions permissions = new OnlyOfficeConfig.Config.Document.Permissions();
                if(urlFieldName==null){
                    permissions.setEdit(true);
                    permissions.setReview(false);
                    permissions.setPrint(true);
                    permissions.setDownload(true);
                    docInfo.setPermissions(permissions);
                }
                //BizOrder bizOrder = bizOrderService.getById(report.getOrderId());
                //String subject = "【" + bizOrder.getName() + " " + report.getReportNo() + "】报告";
                //String body="报告详见附件,请在2各工作日内回复,多谢！";
                //addMailPlugin(docInfo,subject,body, url, bizOrder.getCustomerId(),bizOrder.getCustomerContactId());
            }
            case "quotation" -> {
                Quotation quotation = quotationService.getById(docId);
                docInfo.setId(docId);
                String url = quotation.getUrl();
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
                //String subject = "【" + quotation.getName() + "】报价事宜";
                //String body="报价详见附件,请在2个工作日内回复,多谢！";
                //ddMailPlugin(docInfo,subject,body, url, quotation.getCustomerId(),quotation.getCustomerContactId());
            }
            case "signreport" -> {
                Report report = reportService.getById(docId);
                docInfo.setId(docId);
                String url = report.getFinalUrl();
                docInfo.setUrl(url);
                docInfo.setFileName(FileUtil.getName(url));
            }
            case "sys_standard" -> {
                SysStandard sysStandard = sysStandardService.getById(docId);
                docInfo.setId(docId);
                docInfo.setUrl(sysStandard.getUrl());
                docInfo.setFileName(sysStandard.getName());
            }
        }
        OnlyOfficeConfig.Config.EditorConfig.Plugins.Options options = docInfo.getPluginOptions();
        if (options == null) {
            options = new OnlyOfficeConfig.Config.EditorConfig.Plugins.Options();
        }
        Map<String, String> all = options.getAll();
        if(all==null) {
            all = new HashMap<>();
        }
        all.put("docId", docId);//传递到插件中的数据
        all.put("token", TokenUtils.getTokenByRequest() );//传递到插件中的api token，以便插件中可以调用后台API
        options.setAll(all);
        docInfo.setPluginOptions(options);
        return docInfo;
    }

    private void addMailPlugin(DocumentInfo docInfo,String subject,String body,String url, String customerId, String customerContactId) {
        List<String> pluginsData = new ArrayList<>();
        pluginsData.add("/ooplugins/mail/config.json");
        docInfo.setPluginsData(pluginsData);
        OnlyOfficeConfig.Config.EditorConfig.Plugins.Options options = new OnlyOfficeConfig.Config.EditorConfig.Plugins.Options();
        Map<String, String> all = new HashMap<>();
        all.put("url", url);//生成邮件附件
        all.put("fileName", FileUtil.getName(url));
        all.put("subject", subject);
        if (customerContactId != null && StrUtil.isNotBlank(customerContactId)) {
            SysCustomerContact sysCustomerContact = sysCustomerContactService.getById(customerContactId);
            if (sysCustomerContact!=null && sysCustomerContact.getEmail() != null && StrUtil.isNotBlank(sysCustomerContact.getEmail())) {
                all.put("to", sysCustomerContact.getEmail());
            }
        }
        if (customerId != null && StrUtil.isNotBlank(customerId)){
            SysCustomer sysCustomer = sysCustomerService.getById(customerId);
            if(sysCustomer.getSalerId()!=null && StrUtil.isNotBlank(sysCustomer.getSalerId())) {
                List<String> salerIds = Arrays.asList(sysCustomer.getSalerId().split(","));
                String cc ="";
                for (String salerId : salerIds) {
                    LoginUser user = sysBaseAPI.getUserByName(salerId.trim());
                    if (user!=null && user.getEmail() != null && StrUtil.isNotBlank(user.getEmail())){
                        cc+=user.getEmail()+";";
                    }
                }
                if (StrUtil.isNotBlank(cc)){
                    cc = cc.substring(0, cc.lastIndexOf(";"));
                    all.put("cc", cc);
                }
            }
        }
        all.put("body", body);
        options.setAll(all);
        docInfo.setPluginOptions(options);
    }

    private static OnlyOfficeConfig.Config.Document.Permissions getDcsPermission(String permission, DcsDoc docDo) {
        String[] arrPerms = permission.split(",");
        OnlyOfficeConfig.Config.Document.Permissions permissions = new OnlyOfficeConfig.Config.Document.Permissions();
        if (!StrUtil.isEmpty(docDo.getProcessInstanceId())) {// 已发起审批流不允许修改
            permissions.setEdit(false);
        } else {
            LoginUser curUser = SecureUtil.currentUser();
            if(curUser.getUsername().equals(docDo.getCreateBy())){
                permissions.setEdit(true);
            } else if(docDo.getCoDrafters() != null && docDo.getCoDrafters().contains(curUser.getUsername())){
                permissions.setEdit(true);
            } else {
                permissions.setEdit(false);
            }
        }
        permissions.setReview(false);
        if (arrPerms.length > 0) {
            for (String perm : arrPerms) {
                if (perm.equals("print")) {
                    permissions.setPrint(true);
                } else if (perm.equals("download")) {
                    permissions.setDownload(true);
                }else if (perm.equals("comment")) {
                    permissions.setComment(true);
                }
            }
        }
        return permissions;
    }

    @PermitAll
    @PostMapping("/callback")
    public String callback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Scanner scanner = new Scanner(request.getInputStream()).useDelimiter("\\A");
        String body = scanner.hasNext() ? scanner.next() : "";
        JSONObject jsonObj = JSONObject.parseObject(body);
        //System.out.println(jsonObj.get("status"));
        int eStatus = (int) jsonObj.get("status");
//		0 - no document with the key identifier could be found,
//		1 - document is being edited,
//		2 - document is ready for saving,
//		3 - document saving error has occurred,
//		4 - document is closed with no changes,
//		6 - document is being edited, but the current document state is saved,
//		7 - error has occurred while force saving the document
        if ( eStatus == 2 || eStatus == 6) {
            String downloadUri = (String) jsonObj.get("url");
            URL url = new URL(downloadUri);
            String key = jsonObj.get("key").toString();
            String relativePath = null;
            if(key.startsWith("dcs")){
                String id =key.replace("dcs","").split("_")[0];
                DcsDoc doc = dcsDocService.getById(id);
                String docUrl = doc.getUrl();
                relativePath = docUrl.replace(MinioUtil.getMinioUrl(),"");
                relativePath = relativePath.replace(MinioUtil.getBucketName(),"");
            }else if(key.startsWith("sys_template")){
                String id =key.replace("sys_template","").split("_")[0];
                SysTemplate sysTemplate = sysTemplateService.getById(id);
                String docUrl = sysTemplate.getUrl();
                relativePath = docUrl.replace(MinioUtil.getMinioUrl(),"");
                relativePath = relativePath.replace(MinioUtil.getBucketName(),"");
            } else if(key.startsWith("test")){
                String id =key.replace("test","").split("_")[0];
                Test test = testService.getById(id);
                String docUrl = test.getRecordUrl();
                relativePath = docUrl.replace(MinioUtil.getMinioUrl(),"");
                relativePath = relativePath.replace(MinioUtil.getBucketName(),"");
            }else if(key.startsWith("report")){
                String id =key.replace("report","").split("_")[0];
                Report report = reportService.getById(id);
                String docUrl = report.getUrl();
                relativePath = docUrl.replace(MinioUtil.getMinioUrl(),"");
                relativePath = relativePath.replace(MinioUtil.getBucketName(),"");
            }else
                relativePath = "temp";
            String sourceObjectName = downloadUri.replace(MinioUtil.getMinioUrl(),"");
            if(sourceObjectName.startsWith(MinioUtil.getBucketName())){
                sourceObjectName = sourceObjectName.substring(MinioUtil.getBucketName().length());
            }
            int index = sourceObjectName.indexOf("?");
            if(index != -1) {
                sourceObjectName = sourceObjectName.substring(0, index);
            }
            MinioUtil.copy(MinioUtil.getBucketName(),sourceObjectName,MinioUtil.getBucketName(),relativePath);
        }
        return "{\"error\":0}";
    }

    /**
     *  生成培训记录
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "培训-批量生成培训记录")
    @Operation(summary="培训-批量生成培训记录")
    @PreAuthorize("@jps.requiresPermissions('dcs:training:generateTrainingRecord')")
    @PostMapping(value = "/generateTrainingRecords")
    public Result<String> generateTrainingRecords(@RequestParam(name="ids",required=true) String ids) throws Exception {
        String[] trainingIds = ids.split(",");
        for (String trainingId : trainingIds) {
            generateTrainingRecordById(trainingId);
        }
        return Result.OK("生成成功！","success");
    }

    /**
     *  生成培训记录
     *
     * @param id
     * @return
     */
    @AutoLog(value = "培训-生成培训记录")
    @Operation(summary="培训-生成培训记录")
    @PreAuthorize("@jps.requiresPermissions('dcs:training:generateTrainingRecord')")
    @PostMapping(value = "/generateTrainingRecord")
    public Result<String> generateTrainingRecord(@RequestParam(name="id",required=true) String id) throws Exception {
        generateTrainingRecordById(id);
        return Result.OK("生成成功！","success");
    }

    private void generateTrainingRecordById(String id) throws Exception {
        TrainingInfo trainingInfo = new TrainingInfo();
        TrainingVo trainingVo = trainingService.getVoById(id);
        trainingInfo.setStartTime(trainingVo.getStartTime());
        String location = sysBaseAPI.translateDict("meeting_room",trainingVo.getLocation());
        trainingInfo.setLocation(location);
        List<String> attendeeDeparts = trainingRecordService.getAttendeeDeparts(id);
        List<TrainingRecord> trainingRecordList = trainingRecordService.selectByMainId(id);
        trainingInfo.setAttendeeQty(trainingRecordList.size());
        List<TrainingRecordInfo> trainingRecordInfoList = new ArrayList<>();
        for (TrainingRecord trainingRecord : trainingRecordList) {
            TrainingRecordInfo trainingRecordInfo = new TrainingRecordInfo();
            String attendee = sysBaseAPI.translateDictFromTable("sys_user","realname","id",trainingRecord.getAttendee());
            trainingRecordInfo.setAttendee(attendee);
            trainingRecordInfoList.add(trainingRecordInfo);
        }
        trainingInfo.setDeparts(String.join(",", attendeeDeparts));
        List<TrainingDocInfo> trainingDocInfoList = new ArrayList<>();
        if(StrUtil.isNotEmpty(trainingVo.getDocIds()) ){
            String[] docIds = trainingVo.getDocIds().split(",");
            for (String docId : docIds) {
                DcsDoc dcsDoc = dcsDocService.getById(docId);
                TrainingDocInfo trainingDocInfo = new TrainingDocInfo();
                trainingDocInfo.setTrainingId(id);
                BeanUtils.copyProperties(dcsDoc, trainingDocInfo);
                trainingDocInfo.setName(trainingVo.getName());
                trainingDocInfo.setDuration(trainingVo.getDuration()/3600);
                trainingDocInfo.setStartTime(trainingVo.getStartTime());
                trainingDocInfo.setTeacher(trainingVo.getCreateBy());
                trainingDocInfoList.add(trainingDocInfo);
            }
        }
        SysTemplate sysTemplate =  sysTemplateService.getById("1876111026873856001");
        String targetPath = "/dcs/Training/" +trainingVo.getName() + ".pdf";
        WordUtil.GenerateReportV1(sysTemplate.getUrl(),targetPath,"",null,trainingInfo, trainingDocInfoList,trainingRecordInfoList);
        trainingVo.setRecordUrl(MinioUtil.getMinioUrl()  + MinioUtil.getBucketName()  + targetPath);
        trainingService.updateById(trainingVo);
    }

    /**
     *  预览原始记录
     *
     * @param methodId 方法id
     * @return
     */
    @AutoLog(value = "方法-预览原始记录")
    @Operation(summary="方法-预览原始记录")
    @PostMapping(value = "/generateTestingRecord")
    public Result<String> generateTestingRecord(@RequestParam(name="methodId",required=true) String methodId) throws Exception {
        generateTestingRecordByMethodId(methodId);
        return Result.OK("生成成功！","success");
    }

    private void generateTestingRecordByMethodId(String methodId) throws Exception {
        SysMethod sysMethod = sysMethodService.getById(methodId);
        SysTemplate sysTemplate = sysTemplateService.getById(sysMethod.getTemplateId());
        Test test = testService.getById("1899422173470420994");
        WordUtil.generateReport(test,sysTemplate,"temp/" + sysMethod.getName() + ".docx","",null,false);
    }

    /**
     *  生成原始记录
     *
     * @param testIds 标本ID以，隔开
     * @return
     */
    @AutoLog(value = "生成原始记录")
    @Operation(summary="生成原始记录")
    @GetMapping(value = "/generateTestRecord")
    public Result<List<String>> generateTestRecord(@RequestParam(name="testIds",required=true) String testIds) throws Exception {
        List<String> resultList = new ArrayList<>();
        String[] testIdArray = testIds.split(",");
        for (String testId : testIdArray) {
            Test test = testService.getById(testId);
            TestTask testTask = testTaskService.getById(test.getTaskId());
            SysMethod sysMethod = sysMethodService.getById(testTask.getMethodId());
            Sample sample = sampleService.getById(testTask.getSampleId());
            SysTemplate sysTemplate = sysTemplateService.getById(sysMethod.getTemplateId());
            if(sysTemplate == null)
                throw new RuntimeException("方法未设置记录模板!");
            String targetPath = "lims/原始记录/" + sysMethod.getName()  + "/" + sample.getSampleNo() + ".docx";
            String recordUrl = WordUtil.generateReport(test,sysTemplate,targetPath,"",null,false);
            test.setRecordUrl(recordUrl);
            testService.updateById(test);
            resultList.add(recordUrl);
        }
        return Result.OK("生成成功！",resultList);
    }

    @AutoLog(value = "报告-批量生成报告")
    @Operation(summary="报告-批量生成报告")
    @GetMapping(value = "/report/generate")
    public Result<String> generateReport(@RequestParam(name="id",required=true) String id,@RequestParam(name="isremake",required=true) Boolean isremake) throws Exception {
        internalGenerateReport(id,isremake);
        if(isremake) {
            Report report = reportService.getById(id);
            report.setSignBy(null);
            report.setSignTime(null);
            report.setApproveBy(null);
            report.setApproveTime(null);
            report.setFinalUrl(null);
            for (String sampleid : report.getSampleId().split(",")) {
                testTaskService.list(new QueryWrapper<TestTask>().eq("sample_id", sampleid))
                        .forEach(testTask -> {
                            if(!testTask.getCurStep().equals(FLOW_STEP_6))
                                testTaskService.revert(testTask.getId(), FLOW_STEP_6, "重新生成报告");
                        });
            }
            reportService.updateById(report);
        }
        return Result.OK("生成成功！");
    }

    @AutoLog(value = "报告-批量生成报告")
    @Operation(summary="报告-批量生成报告")
    @PostMapping(value = "/report/batchGenerate")
    public Result<String> batchGenerateReport(@RequestParam(name="ids",required=true) String ids) throws Exception {
        String[] reportIdArray = ids.split(",");
        for (String reportId : reportIdArray) {
            internalGenerateReport(reportId,false);
        }
        return Result.OK("生成成功！");
    }

    @Transactional(rollbackFor = Exception.class)
    public void internalGenerateReport(String reportId,Boolean isRemake) throws Exception {
        Report report = reportService.getById(reportId);
        if(report.getUrl() !=null && !report.getUrl().isEmpty() && !isRemake) {
            throw new RuntimeException("已经生成过报告了!如需重新生成,请点击重新生成报告按钮");
        }
        BizOrder bizOrder = bizOrderService.getById(report.getOrderId());
        SysCustomer sysCustomer = sysCustomerService.getById(bizOrder.getCustomerId());
        Sample sample = null;
        if(!StrUtil.isEmpty(report.getSampleId())){
            sample = sampleService.getById(report.getSampleId());
        }else if(StrUtil.isEmpty(report.getTestTaskIds())){
            String[] testTaskIds = report.getTestTaskIds().split(",");
            TestTask testtask = testTaskService.getById(testTaskIds[0]);
            sample = sampleService.getById(testtask.getSampleId());
        }
        if(sample != null) {
            SysTemplate sysTemplate = sysTemplateService.getById(report.getTemplateId());
            String month = DateUtil.thisMonth() < 10 ? "0" + DateUtil.thisMonth() : String.valueOf(DateUtil.thisMonth());
            String day = DateUtil.thisDayOfMonth() < 10 ? "0" + DateUtil.thisDayOfMonth() : String.valueOf(DateUtil.thisDayOfMonth());
            String targetPath = DateUtil.thisYear() + "/" + month + "/" + day + "/lims/报告/" + report.getReportNo() + "_" + sample.getName() + "_" + sysCustomer.getName().replace(" ","_") + ".docx";
            String reportUrl = WordUtil.generateReport(report, sysTemplate, targetPath, "", null, false);
            report.setUrl(reportUrl);
            reportService.updateById(report);


            LoginUser loginUser = SecureUtil.currentUser();
            if (loginUser.getSignature() != null && !loginUser.getSignature().isEmpty()) {
                WordUtil.Sign(reportUrl, "编    制", loginUser.getSignature());
            }





        }
    }

    /**
     * 报价单-批量生成报价单
     * @param ids
     * @return
     * @throws Exception
     */
    @AutoLog(value = "报价单-批量生成报价单")
    @Operation(summary="报价单-批量生成报价单")
    @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:batchGenerateQuotations')")
    @PostMapping(value = "/batchGenerateQuotations")
    public Result<String> batchGenerateQuotations(@RequestParam(name="ids",required=true) String ids) throws Exception {
        String[] quotationIds = ids.split(",");
        for (String quotationId : quotationIds) {
            generateQuotationById(quotationId);
        }
        return Result.OK("生成成功！");
    }

    /**
     * 报价单-生成报价单
     * @param id
     * @return
     * @throws Exception
     */
    @AutoLog(value = "报价单-生成报价单")
    @Operation(summary="报价单-生成报价单")
    @PreAuthorize("@jps.requiresPermissions('lims_order:quotation:generateQuotation')")
    @PostMapping(value = "/generateQuotation")
    public Result<String> generateQuotation(@RequestParam(name="id",required=true) String id) throws Exception {
        generateQuotationById(id);
        return  Result.OK("生成成功！");
    }

    private void generateQuotationById(String id) throws Exception {
        Quotation quotation = quotationService.getById(id);
        SysTemplate sysTemplate =  sysTemplateService.getById("1899335625290465281");
        String targetPath = "/lims_order/Quotation/" +quotation.getName() + ".pdf";
        WordUtil.generateReport(quotation,sysTemplate,targetPath,"",null,true);
        quotation.setUrl(MinioUtil.getMinioUrl()  + MinioUtil.getBucketName()  + targetPath);
        quotationService.updateById(quotation);
    }

    /**
     * 发送邮件（支持附件）
     *
     * @param emailDTO 邮件请求参数
     * @return 发送结果
     */
    @AutoLog(value = "邮件-发送带附件邮件")
    @Operation(summary = "邮件-发送带附件邮件")
    @PostMapping("/sendEmail")
    public Result<?> sendEmail(@RequestBody EmailDTO emailDTO){
        if (emailDTO.getTo() == null || emailDTO.getTo().isEmpty()) {
            return Result.error("收件人不能为空");
        }
        if (StrUtil.isBlank(emailDTO.getSubject())) {
            return Result.error("邮件主题不能为空");
        }
        if (StrUtil.isBlank(emailDTO.getBody())) {
            return Result.error("邮件内容不能为空");
        }
        try{
            sysBaseAPI.sendEmail(emailDTO);
            return Result.OK("发送邮件成功!");
        }catch (Exception e){
            return Result.error("发送邮件失败：" + e.getMessage());
        }
    }

    /**
     *   审核报告
     *
     * @param ids: 报告Id,逗号分割
     * @return
     */
    @AutoLog(value = "报告-签发")
    @Operation(summary="报告-签发")
//    @PreAuthorize("@jps.requiresPermissions('lims_lab:report:sign')")
    @PostMapping(value = "/sign")
    public Result<List<OnlyOfficeConfig.Config>> sign(@RequestParam(name="ids",required=true) String ids,@RequestParam(name="status",required=true) String status) throws Exception {
        String[] reportIds = ids.split(",");
        List<OnlyOfficeConfig.Config> finals = new ArrayList<>();
        for(String reportId:reportIds) {
            Report report = reportService.getById(reportId);
            String[] fieldNames = new String[0];
            String[] signatureUrls = new String[0];
            WordUtil.SignReport(report.getUrl(), fieldNames, signatureUrls);
            if(status.equals("submit")) {
                LoginUser loginUser = SecureUtil.currentUser();
                report.setMakeBy(loginUser.getUsername());
                report.setMakeTime(DateUtil.date());
                reportService.updateById(report);
                //更新测试任务状态
                testTaskService.getBaseMapper().selectList(new QueryWrapper<TestTask>().eq("sample_id", report.getSampleId())).forEach(testTask -> {
                    if (testTask.getCurStep().equals(FLOW_STEP_6)){
                        testTask.setCurStep(FLOW_STEP_7);
                        testTaskService.updateById(testTask);
                        //插入flow
                        TestTaskFlow testTaskFlow = new TestTaskFlow();
                        testTaskFlow.setTaskId(testTask.getId());
                        testTaskFlow.setStepId(FLOW_STEP_7);
                        testTaskFlowMapper.insert(testTaskFlow);
                    }else if (testTask.getCurStep().equals(FLOW_STEP_7)){
                        // 等于重新生成报告
                    }
                    else {
//                    throw new RuntimeException("测试任务状态异常，当前状态为：" + testTask.getCurStep() + "，无法生成报告！");
                    }
                });
            }
            /**
             * TODO,添加电子签系统接口调用
             * */
            if(status.equals("approve")) {
                LoginUser loginUser = SecureUtil.currentUser();
                report.setApproveBy(loginUser.getUsername());
                report.setApproveTime(DateUtil.date());
                //签名

                WordUtil.Sign(report.getUrl(), "审    核", loginUser.getSignature());
                reportService.updateById(report);

                testTaskService.list(new QueryWrapper<TestTask>().eq("sample_id", report.getSampleId())).forEach(testTask -> {
                    if (testTask.getCurStep().equals(FLOW_STEP_7)) {
                        testTask.setCurStep(FLOW_STEP_8);
                        testTaskService.updateById(testTask);
                        //插入flow
                        TestTaskFlow testTaskFlow = new TestTaskFlow();
                        testTaskFlow.setTaskId(testTask.getId());
                        testTaskFlow.setStepId(FLOW_STEP_8);
                        testTaskFlowMapper.insert(testTaskFlow);
                    } else {
//                        throw new RuntimeException("测试任务状态异常，当前状态为：" + testTask.getCurStep() + "，无法审核报告！");
                    }
                });
            }
            if(status.equals("sign")) {
                LoginUser loginUser = SecureUtil.currentUser();
                report.setSignBy(loginUser.getUsername());
                report.setSignTime(DateUtil.date());
                WordUtil.Sign(report.getUrl(), "签    发", loginUser.getSignature());
                String url = WordUtil.toPDF(report.getUrl());
                signService.sign(url);//调用电子签系统接口
                report.setFinalUrl(url);
                reportService.updateById(report);

//                testTaskService.list(new QueryWrapper<TestTask>().eq("sample_id", report.getSampleId())).forEach(testTask -> {
//                    if (testTask.getCurStep().equals(FLOW_STEP_8)) {
//                        testTask.setCurStep(FLOW_STEP_9);
//                        testTask.setStatus("已完成");
//                        testTaskService.updateById(testTask);
//                        //插入flow
//                        TestTaskFlow testTaskFlow = new TestTaskFlow();
//                        testTaskFlow.setTaskId(testTask.getId());
//                        testTaskFlow.setStepId(FLOW_STEP_9);
//                        testTaskFlowMapper.insert(testTaskFlow);
//                    } else {
////                        throw new RuntimeException("测试任务状态异常，当前状态为：" + testTask.getCurStep() + "，无法签发报告！");
//                    }
//                });
            }
            OnlyOfficeConfig.Config config = getConfiguration("report", report.getId(), null,"final_url");
            finals.add(config);
        }
        return Result.OK("操作成功!",finals);
    }
}