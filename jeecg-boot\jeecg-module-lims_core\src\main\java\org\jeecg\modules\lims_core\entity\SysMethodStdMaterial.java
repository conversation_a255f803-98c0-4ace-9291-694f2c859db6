package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 标品
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Schema(description="标品")
@Data
@TableName("sys_method_std_material")
public class SysMethodStdMaterial implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**方法ID*/
    @Schema(description = "方法ID")
    private java.lang.String methodId;
	/**标品类型*/
	@Excel(name = "标品类型", width = 15, dictTable = "sys_standard_material_type", dicText = "name", dicCode = "id")
    @Schema(description = "标品类型")
    private java.lang.String stdMaterilaTypeId;
	/**?需要配制*/
    @Excel(name = "?需要配制", width = 15,replace = {"是_1","否_0"} )
    @Schema(description = "?需要配制")
    private java.lang.Integer needMakeup;
	/**配制溶液类型*/
	@Excel(name = "配制溶液类型", width = 15, dicCode = "solution_type")
    @Schema(description = "配制溶液类型")
    private java.lang.String solutionTypeId;
	/**配制要求*/
	@Excel(name = "配制要求", width = 15)
    @Schema(description = "配制要求")
    private java.lang.String makeupReq;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
