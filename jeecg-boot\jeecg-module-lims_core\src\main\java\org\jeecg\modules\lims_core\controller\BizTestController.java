package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.lims_core.entity.Test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_core.service.ITestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * @Description: 试验标本
 * @Author: jeecg-boot
 * @Date:   2025-01-07
 * @Version: V1.0
 */
@Tag(name="试验标本")
@RestController
@RequestMapping("/lims_order/test")
@Slf4j
public class BizTestController extends JeecgController<Test, ITestService> {
	@Autowired
	private ITestService testService;

	/**
	 * 分页列表查询
	 *
	 * @param test
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "试验标本-分页列表查询")
	@Operation(summary="试验标本-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Test>> queryPageList(Test test,
											 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											 HttpServletRequest req) {
		QueryWrapper<Test> queryWrapper = QueryGenerator.initQueryWrapper(test, req.getParameterMap());
		Page<Test> page = new Page<Test>(pageNo, pageSize);
		IPage<Test> pageList = testService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param test
	 * @return
	 */
	@AutoLog(value = "试验标本-添加")
	@Operation(summary="试验标本-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Test test) {
		testService.save(test);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param test
	 * @return
	 */
	@AutoLog(value = "试验标本-编辑")
	@Operation(summary="试验标本-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Test test) {
		testService.updateById(test);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "试验标本-通过id删除")
	@Operation(summary="试验标本-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "试验标本-批量删除")
	@Operation(summary="试验标本-批量删除")
	@PreAuthorize("@jps.requiresPermissions('lims_order:test:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "试验标本-通过id查询")
	@Operation(summary="试验标本-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Test> queryById(@RequestParam(name="id",required=true) String id) {
		Test test = testService.getById(id);
		if(test==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(test);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param test
	 */
	@PreAuthorize("@jps.requiresPermissions('lims_order:test:exportXls')")
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, Test test) {
		return super.exportXls(request, test, Test.class, "试验标本");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@PreAuthorize("@jps.requiresPermissions('lims_order:test:importExcel')")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, Test.class);
	}

	/**
	 * 通过task来查询testid
	 *
	 * @param taskId
	 * @param testTypeId 测试类型id 0只查普通样
	 * @return
	 */
	@Operation(summary="测试-通过task来查询testid")
	@GetMapping(value = "/queryTaskByTestId")
	public Result<List<Test>> queryTaskByTestId(@RequestParam(name="taskId",required=true) String taskId,
												@RequestParam(name="testTypeId",required=true) String testTypeId) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", taskId);
		if (!"".equals(testTypeId)){
			qwTest.eq("test_type_id", testTypeId);
		}
		List<Test> List = testService.list(qwTest);
		return Result.OK(List);
	}

}
