package org.jeecg.modules.dcs.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletRequest;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.cp.api.WxCpMediaService;
import me.chanjar.weixin.cp.api.WxCpOaService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpXmlApprovalInfo;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.oa.SummaryInfo;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApplyEventRequest;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplateResult;
import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysDepartModel;
import org.jeecg.common.util.FillRuleUtilEx;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.dcs.dto.DcsDocWithPermission;
import org.jeecg.modules.dcs.entity.DcsDoc;
import org.jeecg.modules.dcs.entity.DcsDocOperationLog;
import org.jeecg.modules.dcs.entity.DcsRefedDoc;
import org.jeecg.modules.dcs.entity.SystemDocType;
import org.jeecg.modules.dcs.mapper.DcsDocMapper;
import org.jeecg.modules.dcs.mapper.DcsRefedDocMapper;
import org.jeecg.modules.dcs.service.IDcsDocOperationLogService;
import org.jeecg.modules.dcs.service.IDcsDocService;
import org.jeecg.modules.dcs.service.IDcsRefedDocService;
import org.jeecg.modules.dcs.service.ISystemDocTypeService;
import org.jeecg.modules.dcs.util.JsonFileReader;
import org.jeecg.modules.dcs.util.WeworkUtils;
import org.jeecg.modules.oo.util.WordUtil;
import org.jeecg.modules.system.entity.SysDict;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.wx.entity.WecomSp;
import org.jeecg.modules.wx.service.IWecomSpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.web.bind.annotation.PathVariable;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: dcs_doc
 * @Author: jeecg-boot
 * @Date:   2024-11-25
 * @Version: V1.0
 */
@Service
public class DcsDocServiceImpl extends ServiceImpl<DcsDocMapper, DcsDoc> implements IDcsDocService {

    @Autowired
    private DcsRefedDocMapper dcsRefedDocMapper;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private IDcsRefedDocService dcsRefedDocService;

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private ISysDictItemService sysDictItemService;

    @Autowired
    private ISystemDocTypeService systemDocTypeService;

    @Autowired
    private IDcsDocOperationLogService dcsDocOperationLogService;

    @Autowired
    private IWecomSpService wecomSpService;


    @Override
    public IPage<DcsDocWithPermission> queryPageWithPermission(DcsDoc dcsDoc,String deptId, HttpServletRequest req, int pageNo, int pageSize) {
        // 查询条件
        QueryWrapper<DcsDoc> queryWrapper =  QueryGenerator.initQueryWrapper(dcsDoc, req.getParameterMap());
        queryWrapper.eq("dept_id", deptId);
        // 分页对象
        Page<DcsDocWithPermission> page = new Page<>(pageNo, pageSize);
        page.setOptimizeCountSql(false);
        // 调用 Mapper 查询
        IPage<DcsDocWithPermission> dcsDocWithPermissionIPage = this.baseMapper.queryPageList(page, queryWrapper);
        List<DcsDocWithPermission> records = dcsDocWithPermissionIPage.getRecords();
        records.forEach(dcsDocWithPermission -> {
            List<DcsDocWithPermission> dcsDocWithPermissions = new ArrayList<>();
            String id = dcsDocWithPermission.getId();
            List<DcsRefedDoc> dcsRefedDocs = dcsRefedDocMapper.selectByDocId(id);
            if(dcsRefedDocs.size() == 0){
                return;
            }
            List<DcsDoc> dcsDocs = this.baseMapper.selectBatchIds(List.of(dcsRefedDocs.stream().filter(dcsRefedDoc -> dcsRefedDoc.getRefedDocId() != null).map(DcsRefedDoc::getRefedDocId).toArray(String[]::new)));
            dcsDocs.forEach(dcsDoc1 -> {
                DcsDocWithPermission dcsDocWithPermission1 = new DcsDocWithPermission();
                BeanUtils.copyProperties(dcsDoc1, dcsDocWithPermission1);
                dcsDocWithPermission1.setPermission("read");
                dcsDocWithPermission1.setIsChild(true);
                dcsDocWithPermissions.add(dcsDocWithPermission1);
            });
            if(dcsDocWithPermissions.size() > 0){
                dcsDocWithPermission.setChildren(dcsDocWithPermissions);
            }
        });
        return dcsDocWithPermissionIPage;
    }

    @Override
    public void apply(String docId, String applyTypeId, String description) throws Exception {
        DcsDoc doc = getById(docId);
        if (StrUtil.isEmpty(doc.getUrl())) {
            throw new RuntimeException("请先上传文件!");
        } else {
            List<String> abandonedDocs = getAbandonedRefDocs(doc);
            if(abandonedDocs.size() > 0){
                throw new RuntimeException("下列文件已经作废，不允许发审！\n" + String.join("\n",abandonedDocs));
            }else{
                WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
                LoginUser curUser = SecureUtil.currentUser();
                request.setCreatorUserId(sysBaseAPI.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
                SummaryInfo.SummaryInfoData summaryInfoData = new SummaryInfo.SummaryInfoData();
                Page<SysDict> page = sysDictService.page(new Page<>(0, 10), new QueryWrapper<>() {{
                    eq("dict_code", "wework_apply_type");
                }});
                SysDictItem sysDictItem1 = null;
                if (page.getRecords().size() == 1) {
                    SysDict sysDict = page.getRecords().get(0);
                    String dict_id = sysDict.getId();
                    List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByMainId(dict_id);
                    sysDictItem1 = sysDictItems.stream().filter(sysDictItem -> sysDictItem.getItemValue().equals(applyTypeId))
                            .findFirst().get();
                }
                summaryInfoData.setText(sysDictItem1.getItemText()+":===文控系统优化调试===" + doc.getDocName());
                summaryInfoData.setLang("zh_CN");
                List<SummaryInfo.SummaryInfoData> lstSummaryInfoData = new ArrayList<>();
                lstSummaryInfoData.add(summaryInfoData);
                SummaryInfo summaryInfo = new SummaryInfo();
                summaryInfo.setSummaryInfoData(lstSummaryInfoData);
                List<SummaryInfo> summaryList = new ArrayList<>();
                summaryList.add(0, summaryInfo);
                request.setSummaryList(summaryList);
                SystemDocType systemDocType = systemDocTypeService.getById(doc.getTypeId());
                String templateId = systemDocType.getWorkflowId();
                request.setTemplateId(templateId);
                request.setUseTemplateApprover(1);// 调试环境0，正式环境要为1；
                if (request.getUseTemplateApprover() == 0) {// 调试环境
                    List<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>();
                    WxCpOaApplyEventRequest.Approver approver = new WxCpOaApplyEventRequest.Approver();
                    approver.setAttr(1);
                    String[] UserIds = new String[] { request.getCreatorUserId() };
                    approver.setUserIds(UserIds);
                    approvers.add(approver);
                    request.setApprovers(approvers);
                }
                WxCpOaApplyEventRequest.ApplyData applyData = new WxCpOaApplyEventRequest.ApplyData();
                JSONObject jsonObject = JsonFileReader.readJsonFileAsJSONObject("qm_template.json");// 读取配置文件
                WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
                String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
                int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
                WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
                WxCpOaService wxCpOaService = cpService.getOaService();
                WxCpOaApprovalTemplateResult templateDetail = wxCpOaService.getTemplateDetail(templateId);// 获取template
                templateDetail.getTemplateContent().getControls().forEach(control -> {// 遍历jsonObject,如果control相同,则替换jsonObject的id
                    jsonObject.getJSONArray("contents").forEach(content -> {
                        JSONObject contentObj = (JSONObject) content;
                        String controlType = contentObj.getString("control");
                        if (controlType.equals(control.getProperty().getControl())) {
                            contentObj.put("id", control.getProperty().getId());
                        }
                    });
                });
                JSONArray contentsArray = jsonObject.getJSONArray("contents");// 获取 contents 数组
                List<ApplyDataContent> applyDataContents = new ArrayList<>();
                if (contentsArray.size() > 0) {
                    for (int i = 0; i < contentsArray.size(); i++) {// 遍历每个 content 对象
                        JSONObject content = contentsArray.getJSONObject(i);
                        ApplyDataContent applyDataContent = new ApplyDataContent();
                        String controlType = content.getString("control");
                        applyDataContent.setControl(controlType);
                        applyDataContent.setId(content.getString("id"));
                        JSONObject value = content.getJSONObject("value");// 解析 value
                        ContentValue contentValue = new ContentValue();
                        if (value.containsKey("text")) {
                            contentValue.setText(sysDictItem1.getDescription());
                        }
                        if ("Textarea".equals(controlType)) {// 根据不同的 control 类型处理
                            contentValue.setText(
                                    "文档编号:"+doc.getDocNo()+
                                            "\n文档名称:"+doc.getDocName()+
                                            "\n起草时间"+doc.getCreateTime()+
                                            "\n备注内容:"+description);
                        } else if ("Contact".equals(controlType)) {
                            List<ContentValue.Member> members = new ArrayList<>();
                            if (StrUtil.isEmpty(doc.getRelatedDeptId()))
                                throw new RuntimeException("请先配置相关部门!");
                            List<String> relatedDeptLeaders = sysBaseAPI.getDeptLeadersByIds(doc.getRelatedDeptId());
                            if(relatedDeptLeaders.size() == 0) {
                                throw new RuntimeException("相关部门的负责人未配置,请先配置后再发审!");
                            }
                            if (request.getUseTemplateApprover() == 0) {// 调试环境
                                relatedDeptLeaders.clear();
                                relatedDeptLeaders.add(request.getCreatorUserId());
                            }
                            for (String relatedDeptLeader : relatedDeptLeaders) {
                                ContentValue.Member member = new ContentValue.Member();
                                member.setUserId(relatedDeptLeader);
                                members.add(member);
                            }
                            contentValue.setMembers(members);
                        } else if ("File".equals(controlType)) {
                            List<ContentValue.File> files = new ArrayList<>();
                            WxCpMediaService mediaService = cpService.getMediaService();
                            String fileName = WordUtil.getFileNameFromUrl(doc.getUrl());
//						WordUtil.Sign(doc.getUrl(), "PreparedBy", curUser.getSignature());
                            WxMediaUploadResult upload = mediaService.upload(WxConsts.MediaFileType.FILE, fileName, doc.getUrl());
                            String mediaId = upload.getMediaId();
                            ContentValue.File file = new ContentValue.File();
                            file.setFileId(mediaId);
                            files.add(file);
                            contentValue.setFiles(files);
                        }
                        applyDataContent.setValue(contentValue);
                        applyDataContents.add(applyDataContent);
                    }
                    applyData.setContents(applyDataContents);
                    request.setApplyData(applyData);
                    String procInstanceId = wxCpOaService.apply(request);
                    doc.setStatus(1);// 审批中
                    doc.setApplyTypeId(applyTypeId);
                    doc.setProcessInstanceId(procInstanceId);
                    updateById(doc);
                    DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
                    dcsDocOperationLog.setDocId(doc.getId());
                    dcsDocOperationLog.setOperationType(sysDictItem1.getItemText());
                    dcsDocOperationLog.setRemark(description);
                    dcsDocOperationLogService.save(dcsDocOperationLog);

                    //插入审批表
                    WecomSp wecomSp = new WecomSp();
                    wecomSp.setSpNo(procInstanceId);
                    wecomSp.setTargetId(docId);
                    wecomSp.setTargetImpl("dcsDocServiceImpl");
                    wecomSp.setTargetTypeId(applyTypeId);
                    wecomSpService.save(wecomSp);

                } else {
                    throw new RuntimeException("发审表单模板配置错误!");
                }
            }
        }
    }
    private List<String> getAbandonedRefDocs(DcsDoc doc) throws Exception {
        List<String> refedUrls =  WordUtil.GetHyperLinks(doc.getUrl());
        List<String> abandonedDocs = new ArrayList<>();
        for (String refedUrl : refedUrls) {
            String[] decodedUrl = refedUrl.split(" ");
            if(decodedUrl.length > 0) {
                QueryWrapper<DcsDoc> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("doc_no", decodedUrl[0].trim());
                List<DcsDoc> lstDcsDoc = list(queryWrapper);
                for (DcsDoc dcsDoc : lstDcsDoc) {
                    if (!dcsDoc.getEffectiveStatus().equals("effective"))
                        abandonedDocs.add(dcsDoc.getDocNo() + " 《" + dcsDoc.getDocName() + "》");
                }
            }
        }
        return abandonedDocs;
    }


    /**
     * 微信审批回调
     * @param inMessage
     */
    public void wxCallback(WxCpXmlMessage inMessage, String id,String typeId) throws Exception {
        if (inMessage.getEvent().equals("sys_approval_change")) {
            List<WxCpXmlApprovalInfo.SpRecord>  spRecords =  inMessage.getApprovalInfo().getSpRecords();
            String SpStatus = String.valueOf(inMessage.getApprovalInfo().getSpStatus());
            for(int i=0;i<spRecords.size();i++){
                List<WxCpXmlApprovalInfo.Detail> approvalDetails = spRecords.get(i).getDetails();
                for(WxCpXmlApprovalInfo.Detail approvalDetail:approvalDetails){
                    String approver = approvalDetail.getApprover().getUserId();
                    String userId = sysBaseAPI.getUserIdByThirdUserId(approver,"wechat_enterprise");
                    LoginUser sysUser = sysBaseAPI.getUserById(userId);
                    DcsDoc dcsDoc = getById(id);
                    String signField = i==spRecords.size()-1?"ApprovedBy": "ReviewedBy" + String.valueOf(i +1);
                    WordUtil.Sign(dcsDoc.getUrl(),signField,sysUser.getSignature());
                    DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
                    dcsDocOperationLog.setDocId(dcsDoc.getId());
                    String applyType = sysBaseAPI.queryDictTextByKey("wework_apply_type",dcsDoc.getApplyTypeId());
                    dcsDocOperationLog.setOperationType(applyType+"审批");
                    dcsDoc.setStatus(Integer.parseInt(SpStatus));
                    if("2".equals(SpStatus) && dcsDoc.getVer()>1){
                        QueryWrapper<DcsRefedDoc> queryWrapperRef = new QueryWrapper<>();
                        queryWrapperRef.eq("refed_doc_id",dcsDoc.getId());
                        List<DcsRefedDoc> refedDocList = dcsRefedDocService.list(queryWrapperRef);
                        String refedDocsInfo ="";
                        for (DcsRefedDoc refedDoc : refedDocList) {
                            DcsDoc refedDcsDoc = getById(refedDoc.getDocId());
                            refedDocsInfo += refedDcsDoc.getDocNo() + ",";
                        }
                        if(StrUtil.isNotEmpty(refedDocsInfo)){
                            refedDocsInfo = refedDocsInfo.substring(0,refedDocsInfo.length()-1);
                            String toUser = sysBaseAPI.findJobParameterByJobClassName("org.jeecg.modules.dcs.job.SendQuotalyReviewListJob");
                            NewArticle newArticle = new NewArticle();
                            newArticle.setTitle("关联提醒");
                            newArticle.setDescription("关联文件发生了修订下列文件需要排查" +"\n" + refedDocsInfo);
                            newArticle.setUrl("http://gbjc.cc/dcs/dcsDocList");
                            newArticle.setPicUrl("http://nwzimg.wezhan.cn/contents/sitefiles2049/10245330/images/48843504.jpg");
                            WeworkUtils.SendMsg("news",toUser,newArticle);
                        }
                    }
                    String status = sysBaseAPI.queryDictTextByKey("wework_bpm_status",dcsDoc.getStatus().toString());
                    dcsDocOperationLog.setRemark(applyType+"审批状态：" +  status);
                    dcsDocOperationLog.setCreateBy(sysUser.getUsername());
                    updateById(dcsDoc);
                    dcsDocOperationLogService.save(dcsDocOperationLog);
                    if(applyType.equals("修订") && SpStatus.equals("2")){
                        revise(dcsDoc.getId(),sysUser.getUsername());
                    }
                }
            }

        }

    }

    public void revise(@PathVariable("docId") String docId, String username) {
        DcsDoc doc = getById(docId);
        DcsDoc newDoc = new DcsDoc();
        BeanUtil.copyProperties(doc, newDoc);
        newDoc.setId(null);
        newDoc.setVer(newDoc.getVer() + 1);
        newDoc.setProcessInstanceId(null);
        newDoc.setCreateBy(username);
        newDoc.setCreateTime(DateTime.now());
        newDoc.setUpdateBy(null);
        newDoc.setUpdateTime(null);
        String sJoson = JSON.toJSONString(newDoc);
        JSONObject formData = JSON.parseObject(sJoson);
        SystemDocType docType = systemDocTypeService.getById(newDoc.getTypeId());
        Map<String,String> paramsValues = getParamValues(newDoc, docType.getDocNoRule());
        Object docNo = FillRuleUtilEx.executeRule("doc_no",paramsValues, formData);
        newDoc.setDocNo(docNo.toString());
        save(newDoc);
        // SystemDocTypeMapper baseMapper = (SystemDocTypeMapper)
        // SpringContextUtils.getBean("systemDocTypeMapper");
        // SystemDocType docType = baseMapper.selectById(newDoc.getTypeId());
        // docType.setCurrentSn(docType.getCurrentSn() + 1);//增加流水号
        DcsDocOperationLog dcsDocOperationLog = new DcsDocOperationLog();
        dcsDocOperationLog.setDocId(newDoc.getId());
        dcsDocOperationLog.setOperationType("修订");
        dcsDocOperationLog.setCreateBy(username);
        dcsDocOperationLog.setRemark("修订版本号：" + newDoc.getVer().toString());
        dcsDocOperationLogService.save(dcsDocOperationLog);
    }

    private Map<String, String> getParamValues(DcsDoc dcsDoc, String docNoRule) {
        List<String> params = ReUtil.findAll("\\{([^}]*)\\}", docNoRule, 1);
        Map<String, String> paramValues = new HashMap<>();
        for (String param : params) {
            if (param.equals("部门代码")) {
                LoginUser sysUser = SecureUtil.currentUser();
                String deptCode = sysUser.getOrgCode();
                String deptId = sysBaseAPI.getDepartIdsByOrgCode(deptCode);
                SysDepartModel sysDepartModel = sysBaseAPI.selectAllById(deptId);
                String dcsCatgory = sysDepartModel.getDcsCategory();
                paramValues.put(param, dcsCatgory);
            } else {
                try {
                    // 遍历DcsDoc类的所有字段
                    Field[] fields = DcsDoc.class.getDeclaredFields();
                    Field targetField = null;
                    // 查找匹配的字段
                    for (Field field : fields) {
                        io.swagger.v3.oas.annotations.media.Schema schema =
                                field.getAnnotation(io.swagger.v3.oas.annotations.media.Schema.class);
                        if (schema != null && param.equals(schema.description())) {
                            targetField = field;
                            break;
                        }
                    }
                    if (targetField != null) {
                        targetField.setAccessible(true);
                        Object value = targetField.get(dcsDoc);
                        if (value != null) {
                            paramValues.put(param, value.toString());
                        }
                    } else {
                        log.warn("No field found with Schema description: ${param}");
                    }
                } catch (IllegalAccessException e) {
                    log.error("Error accessing field for description {}");
                }
            }
        }
        return paramValues;
    }

}