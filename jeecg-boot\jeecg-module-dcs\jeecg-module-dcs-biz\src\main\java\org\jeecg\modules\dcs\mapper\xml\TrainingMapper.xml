<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.dcs.mapper.TrainingMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.dcs.vo.TrainingVo">
        SELECT
            t.*,
            cast((SELECT GROUP_CONCAT(dd.id)
             FROM dcs_doc dd
             WHERE dd.training_id = t.id) as char) AS doc_ids
        FROM
            training t
            ${ew.customSqlSegment}
    </select>

    <select id="getVoById" resultType="org.jeecg.modules.dcs.vo.TrainingVo">
        SELECT
            t.*,
            cast((SELECT GROUP_CONCAT(dd.id)
                  FROM dcs_doc dd
                  WHERE dd.training_id = t.id) as char) AS doc_ids
        FROM
            training t
            where id = ${id}
    </select>
</mapper>