package org.jeecg.modules.lims_core.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.jeecg.modules.lims_core.vo.SysProductPackageVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysProductPackageDetails;
import org.jeecg.modules.lims_core.entity.SysProductPackage;
import org.jeecg.modules.lims_core.vo.SysProductPackagePage;
import org.jeecg.modules.lims_core.service.ISysProductPackageService;
import org.jeecg.modules.lims_core.service.ISysProductPackageDetailsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;


 /**
 * @Description: 产品套餐
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Tag(name="产品套餐")
@RestController
@RequestMapping("/lims_order/sysProductPackage")
@Slf4j
public class SysProductPackageController {
	@Autowired
	private ISysProductPackageService sysProductPackageService;
	@Autowired
	private ISysProductPackageDetailsService sysProductPackageDetailsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysProductPackage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "产品套餐-分页列表查询")
	@Operation(summary="产品套餐-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysProductPackage>> queryPageList(SysProductPackage sysProductPackage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysProductPackage> queryWrapper = QueryGenerator.initQueryWrapper(sysProductPackage, req.getParameterMap());
		Page<SysProductPackage> page = new Page<SysProductPackage>(pageNo, pageSize);
		IPage<SysProductPackage> pageList = sysProductPackageService.queryPageList(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 套餐选择器
	  *
	  * @param sysProductPackage
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 @Operation(summary="产品套餐-套餐选择器")
	 @GetMapping(value = "/listVo")
	 public Result<IPage<SysProductPackageVo>> listVo(SysProductPackage sysProductPackage,
													  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													  HttpServletRequest req) {
		 QueryWrapper<SysProductPackage> queryWrapper = QueryGenerator.initQueryWrapper(sysProductPackage, req.getParameterMap());
		 Page<SysProductPackage> page = new Page<SysProductPackage>(pageNo, pageSize);
		 IPage<SysProductPackageVo> pageList = sysProductPackageService.listVo(page, queryWrapper);
		 return Result.OK(pageList);
	 }
	
	/**
	 *   添加
	 *
	 * @param sysProductPackagePage
	 * @return
	 */
	@AutoLog(value = "产品套餐-添加")
	@Operation(summary="产品套餐-添加")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_product_package:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysProductPackagePage sysProductPackagePage) {
		SysProductPackage sysProductPackage = new SysProductPackage();
		BeanUtils.copyProperties(sysProductPackagePage, sysProductPackage);
		sysProductPackageService.saveMain(sysProductPackage, sysProductPackagePage.getSysProductPackageDetailsList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysProductPackagePage
	 * @return
	 */
	@AutoLog(value = "产品套餐-编辑")
	@Operation(summary="产品套餐-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_product_package:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysProductPackagePage sysProductPackagePage) {
		SysProductPackage sysProductPackage = new SysProductPackage();
		BeanUtils.copyProperties(sysProductPackagePage, sysProductPackage);
		SysProductPackage sysProductPackageEntity = sysProductPackageService.getById(sysProductPackage.getId());
		if(sysProductPackageEntity==null) {
			return Result.error("未找到对应数据");
		}
		sysProductPackageService.updateMain(sysProductPackage, sysProductPackagePage.getSysProductPackageDetailsList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "产品套餐-通过id删除")
	@Operation(summary="产品套餐-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_product_package:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysProductPackageService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "产品套餐-批量删除")
	@Operation(summary="产品套餐-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_product_package:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysProductPackageService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "产品套餐-通过id查询")
	@Operation(summary="产品套餐-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysProductPackage> queryById(@RequestParam(name="id",required=true) String id) {
		SysProductPackage sysProductPackage = sysProductPackageService.getById(id);
		if(sysProductPackage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysProductPackage);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "套餐详情表通过主表ID查询")
	@Operation(summary="套餐详情表-通主表ID查询")
	@GetMapping(value = "/querySysProductPackageDetailsByMainId")
	public Result<List<SysProductPackageDetails>> querySysProductPackageDetailsListByMainId(@RequestParam(name="id",required=true) String id) {
		List<SysProductPackageDetails> sysProductPackageDetailsList = sysProductPackageDetailsService.selectByMainId(id);
		return Result.OK(sysProductPackageDetailsList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysProductPackage
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_product_package:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysProductPackage sysProductPackage) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<SysProductPackage> queryWrapper = QueryGenerator.initQueryWrapper(sysProductPackage, request.getParameterMap());
      LoginUser sysUser = SecureUtil.currentUser();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<SysProductPackage> sysProductPackageList = sysProductPackageService.list(queryWrapper);

      // Step.3 组装pageList
      List<SysProductPackagePage> pageList = new ArrayList<SysProductPackagePage>();
      for (SysProductPackage main : sysProductPackageList) {
          SysProductPackagePage vo = new SysProductPackagePage();
          BeanUtils.copyProperties(main, vo);
          List<SysProductPackageDetails> sysProductPackageDetailsList = sysProductPackageDetailsService.selectByMainId(main.getId());
          vo.setSysProductPackageDetailsList(sysProductPackageDetailsList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "产品套餐列表");
      mv.addObject(NormalExcelConstants.CLASS, SysProductPackagePage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("产品套餐数据", "导出人:"+sysUser.getRealname(), "产品套餐"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:sys_product_package:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<SysProductPackagePage> list = ExcelImportUtil.importExcel(file.getInputStream(), SysProductPackagePage.class, params);
              for (SysProductPackagePage page : list) {
                  SysProductPackage po = new SysProductPackage();
                  BeanUtils.copyProperties(page, po);
                  sysProductPackageService.saveMain(po, page.getSysProductPackageDetailsList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
