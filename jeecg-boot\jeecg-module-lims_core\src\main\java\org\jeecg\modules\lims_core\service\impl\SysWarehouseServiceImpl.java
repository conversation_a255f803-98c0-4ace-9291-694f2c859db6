package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysWarehouse;
import org.jeecg.modules.lims_core.entity.SysWarehouseBox;
import org.jeecg.modules.lims_core.mapper.SysWarehouseBoxMapper;
import org.jeecg.modules.lims_core.mapper.SysWarehouseMapper;
import org.jeecg.modules.lims_core.service.ISysWarehouseService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 仓库
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Service
public class SysWarehouseServiceImpl extends ServiceImpl<SysWarehouseMapper, SysWarehouse> implements ISysWarehouseService {

	@Autowired
	private SysWarehouseMapper sysWarehouseMapper;
	@Autowired
	private SysWarehouseBoxMapper sysWarehouseBoxMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysWarehouse sysWarehouse, List<SysWarehouseBox> sysWarehouseBoxList) {
		sysWarehouseMapper.insert(sysWarehouse);
		if(sysWarehouseBoxList!=null && sysWarehouseBoxList.size()>0) {
			for(SysWarehouseBox entity:sysWarehouseBoxList) {
				//外键设置
				entity.setWarehouseId(sysWarehouse.getId());
				sysWarehouseBoxMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysWarehouse sysWarehouse,List<SysWarehouseBox> sysWarehouseBoxList) {
		sysWarehouseMapper.updateById(sysWarehouse);
		
		//1.先删除子表数据
		sysWarehouseBoxMapper.deleteByMainId(sysWarehouse.getId());
		
		//2.子表数据重新插入
		if(sysWarehouseBoxList!=null && sysWarehouseBoxList.size()>0) {
			for(SysWarehouseBox entity:sysWarehouseBoxList) {
				//外键设置
				entity.setWarehouseId(sysWarehouse.getId());
				sysWarehouseBoxMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysWarehouseBoxMapper.deleteByMainId(id);
		sysWarehouseMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysWarehouseBoxMapper.deleteByMainId(id.toString());
			sysWarehouseMapper.deleteById(id);
		}
	}
	
}
