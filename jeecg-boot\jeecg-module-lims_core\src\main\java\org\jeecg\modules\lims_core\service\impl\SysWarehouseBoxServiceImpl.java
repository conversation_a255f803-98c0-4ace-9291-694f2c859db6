package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysWarehouseBox;
import org.jeecg.modules.lims_core.mapper.SysWarehouseBoxMapper;
import org.jeecg.modules.lims_core.service.ISysWarehouseBoxService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 货位
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Service
public class SysWarehouseBoxServiceImpl extends ServiceImpl<SysWarehouseBoxMapper, SysWarehouseBox> implements ISysWarehouseBoxService {
	
	@Autowired
	private SysWarehouseBoxMapper sysWarehouseBoxMapper;
	
	@Override
	public List<SysWarehouseBox> selectByMainId(String mainId) {
		return sysWarehouseBoxMapper.selectByMainId(mainId);
	}
}
