package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysMethodAnalytePrecision;
import org.jeecg.modules.lims_core.service.ISysMethodAnalytePrecisionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 精密度要求
 * @Author: jeecg-boot
 * @Date:   2025-03-26
 * @Version: V1.0
 */
@Tag(name="精密度要求")
@RestController
@RequestMapping("/lims_core/sysMethodAnalytePrecision")
@Slf4j
public class SysMethodAnalytePrecisionController extends JeecgController<SysMethodAnalytePrecision, ISysMethodAnalytePrecisionService> {
	@Autowired
	private ISysMethodAnalytePrecisionService sysMethodAnalytePrecisionService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysMethodAnalytePrecision
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "精密度要求-分页列表查询")
	@Operation(summary="精密度要求-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysMethodAnalytePrecision>> queryPageList(SysMethodAnalytePrecision sysMethodAnalytePrecision,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysMethodAnalytePrecision> queryWrapper = QueryGenerator.initQueryWrapper(sysMethodAnalytePrecision, req.getParameterMap());
		Page<SysMethodAnalytePrecision> page = new Page<SysMethodAnalytePrecision>(pageNo, pageSize);
		IPage<SysMethodAnalytePrecision> pageList = sysMethodAnalytePrecisionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysMethodAnalytePrecision
	 * @return
	 */
	@AutoLog(value = "精密度要求-添加")
	@Operation(summary="精密度要求-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysMethodAnalytePrecision sysMethodAnalytePrecision) {
		sysMethodAnalytePrecisionService.save(sysMethodAnalytePrecision);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysMethodAnalytePrecision
	 * @return
	 */
	@AutoLog(value = "精密度要求-编辑")
	@Operation(summary="精密度要求-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysMethodAnalytePrecision sysMethodAnalytePrecision) {
		sysMethodAnalytePrecisionService.updateById(sysMethodAnalytePrecision);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "精密度要求-通过id删除")
	@Operation(summary="精密度要求-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysMethodAnalytePrecisionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "精密度要求-批量删除")
	@Operation(summary="精密度要求-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method_analyte_precision:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysMethodAnalytePrecisionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "精密度要求-通过id查询")
	@Operation(summary="精密度要求-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysMethodAnalytePrecision> queryById(@RequestParam(name="id",required=true) String id) {
		SysMethodAnalytePrecision sysMethodAnalytePrecision = sysMethodAnalytePrecisionService.getById(id);
		if(sysMethodAnalytePrecision==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysMethodAnalytePrecision);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysMethodAnalytePrecision
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method_analyte_precision:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysMethodAnalytePrecision sysMethodAnalytePrecision) {
        return super.exportXls(request, sysMethodAnalytePrecision, SysMethodAnalytePrecision.class, "精密度要求");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method_analyte_precision:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysMethodAnalytePrecision.class);
    }

}
