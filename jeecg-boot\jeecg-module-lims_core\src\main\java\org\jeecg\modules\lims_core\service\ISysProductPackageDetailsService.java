package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysProductPackageDetails;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 套餐详情表
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
public interface ISysProductPackageDetailsService extends IService<SysProductPackageDetails> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysProductPackageDetails>
	 */
	public List<SysProductPackageDetails> selectByMainId(String mainId);
}
