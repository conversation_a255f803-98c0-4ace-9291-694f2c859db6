package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import org.jeecg.modules.lims_core.mapper.SysWorkflowStepMapper;
import org.jeecg.modules.lims_core.service.ISysWorkflowStepService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 流程环节
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Service
public class SysWorkflowStepServiceImpl extends ServiceImpl<SysWorkflowStepMapper, SysWorkflowStep> implements ISysWorkflowStepService {
	
	@Autowired
	private SysWorkflowStepMapper sysWorkflowStepMapper;
	
	@Override
	public List<SysWorkflowStep> selectByMainId(String mainId) {
		return sysWorkflowStepMapper.selectByMainId(mainId);
	}

    @Override
    public Integer getSortNumByName(String name) {
		List<SysWorkflowStep> sysWorkflowSteps = this.list(new QueryWrapper<SysWorkflowStep>().eq("name", name));
		if (sysWorkflowSteps != null && !sysWorkflowSteps.isEmpty()) {
			return sysWorkflowSteps.get(0).getSortNum();
		}else{
			return null;
		}
	}

	@Override
	public String getNextStepName(String name) {
		Integer sortNumByName = this.getSortNumByName(name);
		if (sortNumByName != null) {
			String nameBySortNum = getNameBySortNum(sortNumByName + 1);
			if (nameBySortNum != null) {
				return nameBySortNum;
			} else {
				return null; // No next step found
			}
		}
		return null;
	}

	@Override
	public String getNameBySortNum(Integer sortNum) {
		List<SysWorkflowStep> steps = this.list(new QueryWrapper<SysWorkflowStep>().eq("sort_num", sortNum));
		if (steps != null && !steps.isEmpty()) {
			return steps.get(0).getName();
		} else {
			return null;
		}
	}


}
