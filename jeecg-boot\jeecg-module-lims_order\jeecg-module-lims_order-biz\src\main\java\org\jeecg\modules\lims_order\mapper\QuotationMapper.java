package org.jeecg.modules.lims_order.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;
import org.jeecg.modules.lims_order.entity.Quotation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_order.vo.QuotationPage;

/**
 * @Description: 报价单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
public interface QuotationMapper extends BaseMapper<Quotation> {
    IPage<Quotation> queryPageList(Page<Quotation> page,
                                       @Param(Constants.WRAPPER) Wrapper<QuotationPage> wrapper);
}
