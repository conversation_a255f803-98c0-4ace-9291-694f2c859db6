package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.lims_core.entity.TestTaskFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 测试任务流转
 * @Author: jeecg-boot
 * @Date:   2025-03-10
 * @Version: V1.0
 */
public interface TestTaskFlowMapper extends BaseMapper<TestTaskFlow> {

    @Select("select * from test_task_flow where task_id = #{id}")
    List<TestTaskFlow> selectByTaskId(String id);
}
