package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysAnalyte;
import org.jeecg.modules.lims_core.service.ISysAnalyteService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 待测物
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Tag(name="待测物")
@RestController
@RequestMapping("/lims_core/sysAnalyte")
@Slf4j
public class SysAnalyteController extends JeecgController<SysAnalyte, ISysAnalyteService> {
	@Autowired
	private ISysAnalyteService sysAnalyteService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysAnalyte
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "待测物-分页列表查询")
	@Operation(summary="待测物-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysAnalyte>> queryPageList(SysAnalyte sysAnalyte,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysAnalyte> queryWrapper = QueryGenerator.initQueryWrapper(sysAnalyte, req.getParameterMap());
		Page<SysAnalyte> page = new Page<SysAnalyte>(pageNo, pageSize);
		IPage<SysAnalyte> pageList = sysAnalyteService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysAnalyte
	 * @return
	 */
	@AutoLog(value = "待测物-添加")
	@Operation(summary="待测物-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:sys_analyte:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysAnalyte sysAnalyte) {
		sysAnalyteService.save(sysAnalyte);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysAnalyte
	 * @return
	 */
	@AutoLog(value = "待测物-编辑")
	@Operation(summary="待测物-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_analyte:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysAnalyte sysAnalyte) {
		sysAnalyteService.updateById(sysAnalyte);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "待测物-通过id删除")
	@Operation(summary="待测物-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_analyte:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysAnalyteService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "待测物-批量删除")
	@Operation(summary="待测物-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_analyte:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysAnalyteService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "待测物-通过id查询")
	@Operation(summary="待测物-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysAnalyte> queryById(@RequestParam(name="id",required=true) String id) {
		SysAnalyte sysAnalyte = sysAnalyteService.getById(id);
		if(sysAnalyte==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysAnalyte);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysAnalyte
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_analyte:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysAnalyte sysAnalyte) {
        return super.exportXls(request, sysAnalyte, SysAnalyte.class, "待测物");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_analyte:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysAnalyte.class);
    }




}
