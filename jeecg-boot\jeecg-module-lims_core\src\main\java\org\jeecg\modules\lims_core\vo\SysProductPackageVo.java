package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.entity.SysProductPackage;

import java.util.List;

@Data
public class SysProductPackageVo extends SysProductPackage {
    private List<SysProductSubVo> children;
    private java.lang.String hasChild;
    private String method;
    private String methodId;
    private List<SysMethod> methods;
    private String productPackageId;
    private String capabilityId;
    private String ppdId;
    private String standardName;
}
