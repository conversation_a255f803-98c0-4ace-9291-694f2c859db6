package org.jeecg.modules.oo.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * LTV（长期验证）配置类
 * 将LTV相关配置外部化，提高可维护性
 */
@Component
@ConfigurationProperties(prefix = "pdf.ltv")
public class LtvConfiguration {
    
    /**
     * 时间戳证书识别关键词
     */
    private List<String> tsaKeywords = Arrays.asList(
        "timestamp", "tsa", "time-stamp", "timestamping", 
        "时间戳", "时戳", "tss", "timeserver", "chronos", "gdca"
    );
    
    /**
     * Adobe扩展配置
     */
    private AdobeExtension adobeExtension = new AdobeExtension();
    
    /**
     * 时间戳配置
     */
    private TimestampConfig timestampConfig = new TimestampConfig();
    
    /**
     * OCSP配置
     */
    private OcspConfig ocspConfig = new OcspConfig();
    
    // Getters and Setters
    public List<String> getTsaKeywords() {
        return tsaKeywords;
    }
    
    public void setTsaKeywords(List<String> tsaKeywords) {
        this.tsaKeywords = tsaKeywords;
    }
    
    public AdobeExtension getAdobeExtension() {
        return adobeExtension;
    }
    
    public void setAdobeExtension(AdobeExtension adobeExtension) {
        this.adobeExtension = adobeExtension;
    }
    
    public TimestampConfig getTimestampConfig() {
        return timestampConfig;
    }
    
    public void setTimestampConfig(TimestampConfig timestampConfig) {
        this.timestampConfig = timestampConfig;
    }
    
    public OcspConfig getOcspConfig() {
        return ocspConfig;
    }
    
    public void setOcspConfig(OcspConfig ocspConfig) {
        this.ocspConfig = ocspConfig;
    }
    
    /**
     * Adobe扩展配置
     */
    public static class AdobeExtension {
        private String name = "ADBE";
        private String baseVersion = "1.7";
        private int extensionLevel = 8;
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getBaseVersion() { return baseVersion; }
        public void setBaseVersion(String baseVersion) { this.baseVersion = baseVersion; }
        public int getExtensionLevel() { return extensionLevel; }
        public void setExtensionLevel(int extensionLevel) { this.extensionLevel = extensionLevel; }
    }
    
    /**
     * 时间戳配置
     */
    public static class TimestampConfig {
        private String format = "yyyy-MM-dd'T'HH:mm:ss'Z'";
        private String timezone = "UTC";
        private String timeStampingOid = "1.3.6.1.5.5.7.3.8";
        
        // Getters and Setters
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        public String getTimezone() { return timezone; }
        public void setTimezone(String timezone) { this.timezone = timezone; }
        public String getTimeStampingOid() { return timeStampingOid; }
        public void setTimeStampingOid(String timeStampingOid) { this.timeStampingOid = timeStampingOid; }
    }
    
    /**
     * OCSP配置
     */
    public static class OcspConfig {
        private int maxRetries = 3;
        private int timeoutMs = 10000;
        private boolean enableCaching = true;
        private int cacheExpirationMinutes = 60;
        
        // Getters and Setters
        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = maxRetries; }
        public int getTimeoutMs() { return timeoutMs; }
        public void setTimeoutMs(int timeoutMs) { this.timeoutMs = timeoutMs; }
        public boolean isEnableCaching() { return enableCaching; }
        public void setEnableCaching(boolean enableCaching) { this.enableCaching = enableCaching; }
        public int getCacheExpirationMinutes() { return cacheExpirationMinutes; }
        public void setCacheExpirationMinutes(int cacheExpirationMinutes) { this.cacheExpirationMinutes = cacheExpirationMinutes; }
    }
}