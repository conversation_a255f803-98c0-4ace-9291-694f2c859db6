package org.jeecg.modules.dcs.vo;

import lombok.Data;
import org.jeecg.modules.dcs.entity.DcsDoc;


@Data
public class RefedDocLinkVO {
    private String id;
    private String name;    // 对应 DcsDoc 的 docName
    private String url;     // 对应 DcsDoc 的 url

    // 从 DcsDoc 转换为 LinkVO 的构造方法
    public RefedDocLinkVO(DcsDoc dcsDoc) {
        this.id = dcsDoc.getId();
        this.name = dcsDoc.getDocNo() + " 《" +  dcsDoc.getDocName() + "》";
        this.url = dcsDoc.getUrl();
    }
}
