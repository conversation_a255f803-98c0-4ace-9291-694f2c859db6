package org.jeecg.modules.lims_order.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 订单
 * @Author: jeecg-boot
 * @Date:   2025-01-20
 * @Version: V1.0
 */
@Data
@TableName("biz_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="订单")
public class BizOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "quotation_status")
    @Dict(dicCode = "quotation_status")
    @Schema(description = "状态")
    private java.lang.String statusId;
    /**订单编号*/
    @Excel(name = "订单编号", width = 15)
    @Schema(description = "订单编号")
    private java.lang.String orderNo;
    @Excel(name = "补充协议", width = 15)
    @Schema(description = "补充协议")
    private java.lang.String supplemental;
    /**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
    /**创建日期*/
    @Excel(name = "创建日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    @Excel(name = "更新人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**合同名称*/
    @Excel(name = "合同名称", width = 15)
    @Schema(description = "合同名称")
    private java.lang.String name;
    /**客户名称*/
    @Excel(name = "客户名称", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户名称")
    private java.lang.String customerId;
    /**合同类型*/
    @Excel(name = "合同类型", width = 15, dicCode = "contract_type")
    @Dict(dicCode = "contract_type")
    @Schema(description = "合同类型")
    private java.lang.String contractTypeId;
    /**业务分类*/
    @Excel(name = "业务类别", width = 15, dicCode = "biz_type")
    @Dict(dictTable = "biz_type", dicText = "name", dicCode = "id")
    @Schema(description = "业务类别")
    private java.lang.String bizTypeId;
    /**合同编号*/
    @Excel(name = "合同编号", width = 15)
    @Schema(description = "合同编号")
    private java.lang.String contractNo;
    /**签订日期*/
    @Excel(name = "签订日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "签订日期")
    private java.util.Date signingDate;
    /**负责人*/
    @Excel(name = "负责人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "负责人")
    private java.lang.String responsiblePerson;
    /**项目状态*/
    @Excel(name = "项目状态", width = 15, dicCode = "project_type")
    @Dict(dicCode = "project_type")
    @Schema(description = "项目状态")
    private java.lang.String projectStatus;
    /**协同人*/
    @Excel(name = "协同人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "协同人")
    private java.lang.String coordinator;
    /**报价申请*/
    @Excel(name = "报价申请", width = 15, dictTable = "quotation", dicText = "quotation_no", dicCode = "id")
    @Dict(dictTable = "quotation", dicText = "quotation_no", dicCode = "id")
    @Schema(description = "报价申请")
    private java.lang.String quotationId;
    /**客户联系人*/
    @Excel(name = "客户联系人", width = 15, dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Schema(description = "客户联系人")
    private java.lang.String customerContactId;
    /**客户联系人姓名*/
    @Excel(name = "客户联系人姓名", width = 15)
    @Schema(description = "客户联系人姓名")
    private java.lang.String customerContactName;
    /**客户联系人电话*/
    @Excel(name = "客户联系人电话", width = 15)
    @Schema(description = "客户联系人电话")
    private java.lang.String customerContactPhone;
    /**客户联系人邮箱*/
    @Excel(name = "客户联系人邮箱", width = 15)
    @Schema(description = "客户联系人邮箱")
    private java.lang.String customerContactEmail;
    /**签约主体*/
    @Excel(name = "签约主体", width = 15, dicCode = "signing_entity")
    @Dict(dicCode = "signing_entity")
    @Schema(description = "签约主体")
    private java.lang.String signingEntityId;
    /**加急类别*/
    @Excel(name = "加急类别", width = 15, dicCode = "service_type")
    @Dict(dicCode = "service_type")
    @Schema(description = "加急类别")
    private java.lang.String serviceTypeId;
    /**工期*/
    @Excel(name = "工期", width = 15)
    @Schema(description = "工期")
    private java.lang.String leadTime;
    /**支付方式*/
    @Excel(name = "支付方式", width = 15)
    @Schema(description = "支付方式")
    private java.lang.String payMethod;
    /**扫描件*/
    @Excel(name = "扫描件", width = 15)
    @Schema(description = "扫描件")
    private java.lang.String scanAttach;
    /**标准价*/
    @Excel(name = "标准价", width = 15)
    @Schema(description = "标准价")
    private java.lang.Double standardPrice;
    /**PM价*/
    @Excel(name = "PM价", width = 15)
    @Schema(description = "PM价")
    private java.lang.Double pmPrice;
    /**合同状态*/
    @Excel(name = "合同状态", width = 15, dicCode = "contract_status")
    @Dict(dicCode = "contract_status")
    @Schema(description = "合同状态")
    private java.lang.String contractStatusId;
    /**合同金额*/
    @Excel(name = "合同金额", width = 15)
    @Schema(description = "合同金额")
    private java.lang.Double contractAmount;
    /**合同类别(对接)*/
    @Excel(name = "合同类别(对接)", width = 15, dicCode = "contract_integration_type")
    @Dict(dicCode = "contract_integration_type")
    @Schema(description = "合同类别(对接)")
    private java.lang.String contractIntegrationTypeId;
    /**合同金额(人民币)*/
    @Excel(name = "合同金额(人民币)", width = 15)
    @Schema(description = "合同金额(人民币)")
    private java.lang.Double contractAmountRmb;
    /**计划回款金额*/
    @Excel(name = "计划回款金额", width = 15)
    @Schema(description = "计划回款金额")
    private java.lang.Double plannedPaymentAmount;
    /**发票已作废金额*/
    @Excel(name = "发票已作废金额", width = 15)
    @Schema(description = "发票已作废金额")
    private java.lang.Double invoiceCancelAmount;
    /**实际开票金额*/
    @Excel(name = "实际开票金额", width = 15)
    @Schema(description = "实际开票金额")
    private java.lang.Double actualInvoicedAmount;
    /**待开票金额*/
    @Excel(name = "待开票金额", width = 15)
    @Schema(description = "待开票金额")
    private java.lang.Double pendingInvoicedAmount;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
    /**结算币别*/
    @Excel(name = "结算币别", width = 15, dicCode = "currency")
    @Dict(dicCode = "currency")
    @Schema(description = "结算币别")
    private java.lang.String currencyId;
    /**汇率*/
    @Excel(name = "汇率", width = 15)
    @Schema(description = "汇率")
    private java.lang.Double exchangeRate;
    /**未下单原因*/
    @Excel(name = "未下单原因", width = 15)
    @Schema(description = "未下单原因")
    private java.lang.String notOrderingReason;
    /**预计到样日期*/
    @Excel(name = "预计到样日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "预计到样日期")
    private java.util.Date etaDate;
    /**确认生成回款计划*/
    @Excel(name = "确认生成回款计划", width = 15, dicCode = "logic_code")
    @Dict(dicCode = "logic_code")
    @Schema(description = "确认生成回款计划")
    private java.lang.String isGeneratePaymentPlan;
    /**已送检批次*/
    @Excel(name = "已送检批次", width = 15)
    @Schema(description = "已送检批次")
    private java.lang.String lotNo;
    /**签收单*/
    @Excel(name = "签收单", width = 15)
    @Schema(description = "签收单")
    private java.lang.String receiptAttach;
    /**出报告客户*/
    @Excel(name = "出报告客户", width = 15)
    @Schema(description = "出报告客户")
    private java.lang.String reportClientName;
    /**实际回款金额*/
    @Excel(name = "实际回款金额", width = 15)
    @Schema(description = "实际回款金额")
    private java.lang.Double actualPaymentAmount;
    /**退款金额*/
    @Excel(name = "退款金额", width = 15)
    @Schema(description = "退款金额")
    private java.lang.Double refundAmount;
    /**已锁定*/
    @Excel(name = "已锁定", width = 15)
    @Schema(description = "已锁定")
    private java.lang.Integer isLocked;
    /**工期类型*/
    @Excel(name = "工期类型", width = 15, dicCode = "day_type")
    @Dict(dicCode = "day_type")
    @Schema(description = "工期类型")
    private java.lang.String dayType;
}
