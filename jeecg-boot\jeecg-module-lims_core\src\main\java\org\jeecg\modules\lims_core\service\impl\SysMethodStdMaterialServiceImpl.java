package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodStdMaterial;
import org.jeecg.modules.lims_core.mapper.SysMethodStdMaterialMapper;
import org.jeecg.modules.lims_core.service.ISysMethodStdMaterialService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 标品
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodStdMaterialServiceImpl extends ServiceImpl<SysMethodStdMaterialMapper, SysMethodStdMaterial> implements ISysMethodStdMaterialService {
	
	@Autowired
	private SysMethodStdMaterialMapper sysMethodStdMaterialMapper;
	
	@Override
	public List<SysMethodStdMaterial> selectByMainId(String mainId) {
		return sysMethodStdMaterialMapper.selectByMainId(mainId);
	}
}
