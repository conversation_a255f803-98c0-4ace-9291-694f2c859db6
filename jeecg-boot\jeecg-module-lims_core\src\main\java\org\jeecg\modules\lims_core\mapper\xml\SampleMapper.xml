<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.SampleMapper">
    <update id="updateTreeNodeStatus" parameterType="java.lang.String">
        update sample set has_child = #{status} where id = #{id}
    </update>

    <!-- 【vue3专用】 -->
    <select id="queryListByPid" parameterType="java.lang.Object" resultType="org.jeecg.common.system.vo.SelectTreeModel">
        select
        id as "key",
        name as "title",
        (case when has_child = '1' then 0 else 1 end) as isLeaf,
        pid as parentId
        from sample
        where pid = #{pid}
        <if test="query != null">
            <foreach collection="query.entrySet()" item="value" index="key">
                and ${key} = #{value}
            </foreach>
        </if>
    </select>

    <select id="getSampleCountAndUnit" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.vo.TaskVo">
        SELECT
            s.receive_count,su.unit_name,st.translation
        FROM
            test_task t
                left join  sample s on t.sample_id = s.id
                left join  sys_unit su on s.receive_count_unit=su.id
                left join biz_order o on s.order_id = o.id
                left join sys_method m on m. id = t.method_id
                left JOIN report r on r.order_id=o.id
                left join test te on te.task_id=t.id and te.test_type_id=0
                LEFT JOIN (
                SELECT tr1.*,ROW_NUMBER() OVER (PARTITION BY tr1.test_id ORDER BY tr1.id DESC) AS rn
                FROM test_result tr1
            ) tr ON tr.test_id = te.id AND tr.rn = 1
                left join  sys_method_analyte sma on tr.method_analyte_id=sma.id
                left JOIN  sys_analyte  sn on sma.analyte_id =sn.id
                left join  sys_method sm on  sma.method_id =sm.id
                left join sys_standard_evaluation_limt  ssel on ssel.id=tr.limit_id
                left join  sys_standard ss  on  ssel.standard_id=ss.id
                left join sys_translation st on su.id=st.source_id  and r.template_id='1925472945282166786'
        WHERE
            s.id = #{mainId} 	</select>

</mapper>