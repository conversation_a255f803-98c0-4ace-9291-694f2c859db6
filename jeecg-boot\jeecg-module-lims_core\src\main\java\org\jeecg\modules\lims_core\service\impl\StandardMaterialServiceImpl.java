package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.StandardMaterial;
import org.jeecg.modules.lims_core.mapper.StandardMaterialMapper;
import org.jeecg.modules.lims_core.service.IStandardMaterialService;
import org.jeecg.modules.lims_core.vo.StandardMaterialVO;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 标准物质台账
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Service
public class StandardMaterialServiceImpl extends ServiceImpl<StandardMaterialMapper, StandardMaterial> implements IStandardMaterialService {
    @Autowired
    private  StandardMaterialMapper standardMaterialMapper;

    @Override
    public IPage<StandardMaterialVO> queryPageList(Page<StandardMaterial> page, Wrapper<StandardMaterial> wrapper) {
        return standardMaterialMapper.queryPageList(page,wrapper);
    }
}
