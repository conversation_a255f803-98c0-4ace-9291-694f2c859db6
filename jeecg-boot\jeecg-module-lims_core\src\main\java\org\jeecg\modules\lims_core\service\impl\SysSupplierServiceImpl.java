package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysSupplier;
import org.jeecg.modules.lims_core.mapper.SysSupplierMapper;
import org.jeecg.modules.lims_core.service.ISysSupplierService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 供应商
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Service
public class SysSupplierServiceImpl extends ServiceImpl<SysSupplierMapper, SysSupplier> implements ISysSupplierService {

}
