package org.jeecg.modules.dcs.service.impl;

import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.dcs.entity.TrainingRecord;
import org.jeecg.modules.dcs.mapper.TrainingRecordMapper;
import org.jeecg.modules.dcs.service.ITrainingRecordService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 培训记录
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
@Service
public class TrainingRecordServiceImpl extends ServiceImpl<TrainingRecordMapper, TrainingRecord> implements ITrainingRecordService {
	
	@Autowired
	private TrainingRecordMapper trainingRecordMapper;

	@Autowired
	private ISysBaseAPI sysBaseAPI;
	
	@Override
	public List<TrainingRecord> selectByMainId(String mainId) {
		return trainingRecordMapper.selectByMainId(mainId);
	}

	@Override
	public List<String> getAttendeeDeparts(String mainId) {
		List<String> attendeeDeparts = trainingRecordMapper.selectByMainId(mainId).stream()
				.map(trainingRecord -> sysBaseAPI.getUserById(trainingRecord.getAttendee()))
				.map(user -> sysBaseAPI.getDepartNamesByUsername(user.getUsername()))
				.filter(departs -> departs != null && !departs.isEmpty())
				.map(departs -> departs.get(0))
				.distinct()
				.collect(Collectors.toList());
		return attendeeDeparts;
	}
}