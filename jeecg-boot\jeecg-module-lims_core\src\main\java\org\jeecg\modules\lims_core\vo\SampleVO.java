package org.jeecg.modules.lims_core.vo;

import cn.hutool.core.date.DateTime;
import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "Sample",drillUp = "quotation_id->quotation.id",drillDown = "test_task.sample_id",description = "样品信息")
public class SampleVO {
    @TemplateField(description = "ID")
    private String id;
    @TemplateField(description = "编号")
    private String sampleNo;
    @TemplateField(description = "样品名称")
    private String name;
    @TemplateField(description = "批号")
    private String lotNo;
    @TemplateField(description = "规格")
    private String specification;
    @TemplateField(description ="价格" )
    private String standardPrice;
    @TemplateField(description ="生产厂家" )
    private String manufacturer;
    @TemplateField(description ="产地" )
    private String origin;
    @TemplateField(description ="检品数量" )
    private String receiveCount;
    @TemplateField(description ="包装规格" )
    private String packMedicineType;
    @TemplateField(description ="生产日期" )
    private String productDate;
    @TemplateField(description ="接收日期" )
    private DateTime receiveDate;
    @TemplateField(description ="有效期" )
    private String expiry;
    @TemplateField(description ="贮存" )
    private String storage;
    @TemplateField(description ="注册编号" )
    private String certificateNo  ;
    @TemplateField(description ="批准文号" )
    private String authorizeNo  ;
    @TemplateField(description = "检品数量单位id",entityFieldName = "id",drillChain = "sample.receive_count_unit->sys_unit.id")
    private String sysUnitId;
    @TemplateField(description = "检品数量单位",entityFieldName = "id",drillChain = "sample.receive_count_unit->sys_unit.id->sys_unit.unit_name")
    private String receiveCountUnit;
    @TemplateField(description ="备注" )
    private String remark;

    @TemplateField(description ="剂型", entityFieldName="dosageForm" ,dict = "dosage_form"  )
    private String dosageForm;

    @TemplateField(entityFieldName = "id", func = "getStandard",description ="标准" )
    private String standard ;

    @TemplateField(entityFieldName = "id", func = "getTestDate",description ="测试日期" )
    private String testDate;

    @TemplateField(entityFieldName = "id", func = "getTestItem",description ="测试项目" )
    private String testItem;

    @TemplateField(description ="包装材料" )
    private String packMaterial;

    @TemplateField(entityFieldName = "id",func = "getSampleCountAndUnit",description = "检品数量和单位")
    private String receiveCountAndUnit;


}