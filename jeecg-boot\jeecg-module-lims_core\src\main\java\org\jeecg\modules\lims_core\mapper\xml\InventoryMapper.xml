<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.InventoryMapper">
    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.InventoryVo">
        select * from ( SELECT i.*,COALESCE(s.name, c.name, sm.name) AS goodsname, s.rd_id as rdid
                        FROM     inventory i
                                     LEFT JOIN sample s ON i.article_no = s.sample_no
                                     LEFT JOIN consumptive c ON i.article_no = c.code
                                     LEFT JOIN standard_material sm ON i.article_no = sm.code ) t
            ${ew.customSqlSegment}
    </select>
</mapper>