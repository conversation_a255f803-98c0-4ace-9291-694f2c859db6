package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.dto.MethodNeedMakeupModel;
import org.jeecg.modules.lims_core.entity.TestSolution;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 溶液使用记录
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
public interface ITestSolutionService extends IService<TestSolution> {
    /**
     * 通过id查询
     *
     * @param testId 测试id
     * @return List<SysMethodAnalyte>
     */
    List<MethodNeedMakeupModel> selectMethodNeedMakeupByTestId(String testId);
}
