package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodAnalytePrecision;
import org.jeecg.modules.lims_core.mapper.SysMethodAnalytePrecisionMapper;
import org.jeecg.modules.lims_core.service.ISysMethodAnalytePrecisionService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 精密度要求
 * @Author: jeecg-boot
 * @Date:   2025-03-26
 * @Version: V1.0
 */
@Service
public class SysMethodAnalytePrecisionServiceImpl extends ServiceImpl<SysMethodAnalytePrecisionMapper, SysMethodAnalytePrecision> implements ISysMethodAnalytePrecisionService {

}
