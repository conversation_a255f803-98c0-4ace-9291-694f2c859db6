package org.jeecg.modules.lims_order.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.lettuce.core.dynamic.annotation.Param;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.vo.QuotationPage;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;

import java.util.List;
import java.util.Map;

/**
 * @Description: 报价单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
public interface IQuotationService extends IService<Quotation> {


    void apply(String id, ApplyType applyType) throws WxErrorException;

    List<Map> listDataLog(String id);

    void calcPrice(String quotationId);

    void updateSampleQuotationId(Quotation quotation);

    void preCheck(String id);

    public IPage<Quotation> queryPageList(Page<Quotation> page,
                                              @Param(Constants.WRAPPER) Wrapper<QuotationPage> wrapper);

    void cancelQuotation(String id, Boolean revert);
}
