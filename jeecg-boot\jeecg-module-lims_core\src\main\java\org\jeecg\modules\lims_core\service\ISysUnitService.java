package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import org.jeecg.modules.lims_core.entity.SysUnit;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 计量单位
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
public interface ISysUnitService extends IService<SysUnit> {

	/**
	 * 添加一对多
	 *
	 * @param sysUnit
	 * @param sysUnitConversionList
	 */
	public void saveMain(SysUnit sysUnit,List<SysUnitConversion> sysUnitConversionList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param sysUnit
	 * @param sysUnitConversionList
	 */
	public void updateMain(SysUnit sysUnit,List<SysUnitConversion> sysUnitConversionList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
