package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 耗材使用记录
 * @Author: jeecg-boot
 * @Date:   2025-02-13
 * @Version: V1.0
 */
@Data
@TableName("test_consumptive")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="耗材使用记录")
public class TestConsumptive implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**耗材*/
	@Excel(name = "耗材", width = 15, dictTable = "consumptive", dicText = "name", dicCode = "id")
	@Dict(dictTable = "consumptive", dicText = "name", dicCode = "id")
    @Schema(description = "耗材")
    private java.lang.String consumptiveId;
	/**耗材使用量*/
	@Excel(name = "耗材使用量", width = 15)
    @Schema(description = "耗材使用量")
    private java.lang.String usageAmount;
	/**耗材使用单位*/
	@Excel(name = "耗材使用单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "耗材使用单位")
    private java.lang.String unitId;
	/**测试id*/
	@Excel(name = "测试id", width = 15)
    @Schema(description = "测试id")
    private java.lang.String testId;
	/**记录状态id*/
	@Excel(name = "记录状态id", width = 15, dicCode = "status")
	@Dict(dicCode = "status")
    @Schema(description = "记录状态id")
    private java.lang.String statusId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
}
