package org.jeecg.modules.oo.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.bouncycastle.cert.ocsp.BasicOCSPResp;
import org.bouncycastle.cert.ocsp.OCSPResp;

import java.security.cert.CertificateParsingException;
import java.security.cert.X509Certificate;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SignServiceImpl LTV功能单元测试
 */
@ExtendWith(MockitoExtension.class)
class SignServiceImplLtvTest {
    
    @InjectMocks
    private SignServiceImpl signService;
    
    @Mock
    private X509Certificate mockCertificate;
    
    @Mock
    private OCSPResp mockOcspResp;
    
    @Mock
    private BasicOCSPResp mockBasicOcspResp;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }
    
    @Test
    void testCreateTimestamp() {
        // 测试时间戳创建
        Date testDate = new Date(1609459200000L); // 2021-01-01 00:00:00 UTC
        String timestamp = SignServiceImpl.createTimestamp(testDate);
        
        assertNotNull(timestamp);
        assertEquals("2021-01-01T00:00:00Z", timestamp);
    }
    
    @Test
    void testCreateTimestampWithNull() {
        // 测试空日期的时间戳创建
        String timestamp = SignServiceImpl.createTimestamp(null);
        
        assertNotNull(timestamp);
        assertTrue(timestamp.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z"));
    }
    
    @Test
    void testIsTimeStampAuthorityCertificate() {
        // 模拟时间戳证书
        when(mockCertificate.getSubjectX500Principal().getName())
            .thenReturn("CN=GDCA TrustAUTH TSA CA, O=GDCA");
        when(mockCertificate.getIssuerX500Principal().getName())
            .thenReturn("CN=GDCA TrustAUTH R5 ROOT, O=GDCA");
        
        boolean result = SignServiceImpl.isTimeStampAuthorityCertificate(mockCertificate);
        
        assertTrue(result, "应该识别为时间戳证书");
    }
    
    @Test
    void testIsTimeStampAuthorityCertificateWithNormalCert() throws CertificateParsingException {
        // 模拟普通证书
        when(mockCertificate.getSubjectX500Principal().getName())
            .thenReturn("CN=Test User, O=Test Company");
        when(mockCertificate.getIssuerX500Principal().getName())
            .thenReturn("CN=Test CA, O=Test Company");
        when(mockCertificate.getKeyUsage()).thenReturn(new boolean[9]);
        when(mockCertificate.getExtendedKeyUsage()).thenReturn(null);
        
        boolean result = SignServiceImpl.isTimeStampAuthorityCertificate(mockCertificate);
        
        assertFalse(result, "不应该识别为时间戳证书");
    }
    
    @Test
    void testGetOcspHashKeyWithValidResponse() throws Exception {
        // 测试有效OCSP响应的哈希计算
        byte[] mockOcspBytes = new byte[]{0x30, (byte) 0x82, 0x01, 0x00}; // 模拟OCSP响应
        
        // 这里需要更详细的模拟，实际测试中应该使用真实的OCSP响应数据
        assertDoesNotThrow(() -> {
            SignServiceImpl.getOcspHashKey(mockOcspBytes);
        });
    }
    
    @Test
    void testValidationDataCreation() {
        // 测试ValidationData创建
        SignServiceImpl.ValidationData validationData = new SignServiceImpl.ValidationData();
        
        assertNotNull(validationData.crls);
        assertNotNull(validationData.ocsps);
        assertNotNull(validationData.certs);
        assertTrue(validationData.crls.isEmpty());
        assertTrue(validationData.ocsps.isEmpty());
        assertTrue(validationData.certs.isEmpty());
    }
    
    @Test
    void testConvertToHex() {
        // 测试字节数组转十六进制
        byte[] testBytes = {0x01, 0x23, 0x45, 0x67, (byte)0x89, (byte)0xAB, (byte)0xCD, (byte)0xEF};
        String hexString = SignServiceImpl.convertToHex(testBytes);
        
        assertEquals("0123456789ABCDEF", hexString);
    }
    
    @Test
    void testHashBytesSha1() throws Exception {
        // 测试SHA1哈希计算
        byte[] testData = "Hello, World!".getBytes();
        byte[] hash = SignServiceImpl.hashBytesSha1(testData);
        
        assertNotNull(hash);
        assertEquals(20, hash.length); // SHA1哈希长度为20字节
    }
}