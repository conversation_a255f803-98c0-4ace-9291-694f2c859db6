package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.jeecg.modules.lims_core.entity.SysAnalyte;
import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.entity.SysProduct;
import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
import org.jeecg.modules.lims_core.mapper.SysAnalyteMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodMapper;
import org.jeecg.modules.lims_core.mapper.SysProductMapper;
import org.jeecg.modules.lims_core.mapper.SysStandardEvaluationLimtMapper;
import org.jeecg.modules.lims_core.service.ISysProductService;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.vo.SysProductVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 产品
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Service
public class SysProductServiceImpl extends ServiceImpl<SysProductMapper, SysProduct> implements ISysProductService {

    @Autowired
    private SysMethodMapper sysMethodMapper;
    @Autowired
    private SysStandardEvaluationLimtServiceImpl standardEvaluationLimtService;
    @Autowired
    private SysAnalyteMapper analyteMapper;

    @Override
    public IPage<SysProductVo> listVo(Page<SysProduct> page, QueryWrapper<SysProduct> queryWrapper)  {
        Page<SysProduct> pageProduct = this.page(page, queryWrapper);
        Page<SysProductVo> pageVoProduct = new Page<>();
        BeanUtils.copyProperties(pageProduct,pageVoProduct);
        List<SysProductVo> sysProductVos = new ArrayList<>();
        pageProduct.getRecords().forEach(p -> {

            SysProductVo sysProductVo = new SysProductVo();
            BeanUtils.copyProperties( p,sysProductVo);
            List<SysProductSubVo> children = new ArrayList<>();
            if(p.getStandardId() != null && !p.getStandardId().equals("")){
                // 1.第一版 根据standardEvaluationLimt拿
                standardEvaluationLimtService.list(new QueryWrapper<SysStandardEvaluationLimt>().eq("standard_id", p.getStandardId()).and(wrapper -> wrapper.isNull("parent_id").or().eq("parent_id", ""))).forEach(s -> {
                    sysProductVo.setHasChild("1");
                    SysAnalyte sysAnalyte = analyteMapper.selectById(s.getAnalyteId());
                    SysProductSubVo sysProductSubVo = new SysProductSubVo();
                    sysProductSubVo.setId(s.getId());
                    if(s.getMethodIds().indexOf(',') > 0){
                        String[] methodIds = s.getMethodIds().split(",");
                        sysProductSubVo.setMethods(sysMethodMapper.selectBatchIds(Arrays.asList(methodIds)));
                    }else{
                        SysMethod sysMethod = sysMethodMapper.selectById(s.getMethodIds());
                        sysProductSubVo.setMethodId(s.getMethodIds());
                        sysProductSubVo.setMethod(sysMethod.getName());
                        sysProductSubVo.setStdPrice(sysMethod.getStdPrice());

                    }

                    sysProductSubVo.setProductPackageId(p.getId());
                    sysProductSubVo.setName(sysAnalyte.getName());
                    sysProductSubVo.setEvaluationId(s.getId());
                    sysProductSubVo.setAnalyteId(s.getAnalyteId());
                    children.add(sysProductSubVo);
                });

                //2.第二版 根据method拿
//                sysMethodMapper.selectList(new QueryWrapper<SysMethod>().eq("standard_id", p.getStandardId())).forEach(s -> {
//                    sysProductVo.setHasChild("1");
//                    SysProductSubVo sysProductSubVo = new SysProductSubVo();
//                    sysProductSubVo.setId(s.getId());
//                        sysProductSubVo.setMethodId(s.getId());
//                        sysProductSubVo.setMethod(s.getName());
//                        sysProductSubVo.setStdPrice(s.getStdPrice());
//
//
//
//                    sysProductSubVo.setProductId(p.getId());
//                    sysProductSubVo.setName(s.getName());
////                    sysProductSubVo.setEvaluationId(s.getId());
//                    children.add(sysProductSubVo);
//                });
            }else{
                List<SysMethod> sysMethods = sysMethodMapper.selectByProductId(p.getId());
                if(sysMethods.size()>1){
                    sysProductVo.setMethods(sysMethods);
                }
                if(sysMethods.size()==1){
                    sysProductVo.setMethodId(sysMethods.get(0).getId());
                    sysProductVo.setMethod(sysMethods.get(0).getName());
                    sysProductVo.setStdPrice(sysMethods.get(0).getStdPrice());
                }

            }
            if(children.size() > 0){
                sysProductVo.setChildren(children);
            }
            sysProductVo.setProductId(p.getId());
            sysProductVos.add(sysProductVo);
        });
        pageVoProduct.setRecords(sysProductVos);

        return pageVoProduct;
    }
}
