package org.jeecg.modules.sys;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.system.service.impl.SysBaseApiImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,classes = JeecgSystemApplication.class)
public class test {

    @Autowired
    private SysBaseApiImpl sysBaseApi;

    @Test
    public void test1() {
        List<Map<String, String>> maps = sysBaseApi.listForAutoComplete( "sysAnalyte", "groupOne","123");
        for (Map<String, String> map : maps) {
            System.out.println(map.get("value"));
        }
    }
}
