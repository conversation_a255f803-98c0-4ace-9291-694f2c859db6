package org.jeecg.modules.dcs.controller;

import java.util.Arrays;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.dcs.entity.SystemDocType;
import org.jeecg.modules.dcs.service.ISystemDocTypeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

 /**
 * @Description: system_doc_type
 * @Author: jeecg-boot
 * @Date:   2024-11-22
 * @Version: V1.0
 */
@Tag(name="system_doc_type")
@RestController
@RequestMapping("/dcs/systemDocType")
@Slf4j
public class SystemDocTypeController extends JeecgController<SystemDocType, ISystemDocTypeService>{
	@Autowired
	private ISystemDocTypeService systemDocTypeService;

	/**
	 * 分页列表查询
	 *
	 * @param systemDocType
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "system_doc_type-分页列表查询")
	@Operation(summary="system_doc_type-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<IPage<SystemDocType>> queryPageList(SystemDocType systemDocType,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
        if(hasQuery != null && "true".equals(hasQuery)){
            QueryWrapper<SystemDocType> queryWrapper =  QueryGenerator.initQueryWrapper(systemDocType, req.getParameterMap());
            List<SystemDocType> list = systemDocTypeService.queryTreeListNoPage(queryWrapper);
            IPage<SystemDocType> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = systemDocType.getParentId();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            systemDocType.setParentId(null);
            QueryWrapper<SystemDocType> queryWrapper = QueryGenerator.initQueryWrapper(systemDocType, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("parent_id", parentId);
            Page<SystemDocType> page = new Page<SystemDocType>(pageNo, pageSize);
            IPage<SystemDocType> pageList = systemDocTypeService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
	}

	 /**
	  * 【vue3专用】加载节点的子数据
	  *
	  * @param pid
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = systemDocTypeService.queryListByPid(pid);
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】加载一级节点/如果是同步 则所有数据
	  *
	  * @param async
	  * @param pcode
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = systemDocTypeService.queryListByCode(pcode);
			 if (!async) {
				 loadAllChildren(ls);
			 }
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】递归求子节点 同步加载用到
	  *
	  * @param ls
	  */
	 private void loadAllChildren(List<SelectTreeModel> ls) {
		 for (SelectTreeModel tsm : ls) {
			 List<SelectTreeModel> temp = systemDocTypeService.queryListByPid(tsm.getKey());
			 if (temp != null && temp.size() > 0) {
				 tsm.setChildren(temp);
				 loadAllChildren(temp);
			 }
		 }
	 }

	 /**
      * 获取子数据
      * @param systemDocType
      * @param req
      * @return
      */
	//@AutoLog(value = "system_doc_type-获取子数据")
	@Operation(summary="system_doc_type-获取子数据")
	@GetMapping(value = "/childList")
	public Result<IPage<SystemDocType>> queryPageList(SystemDocType systemDocType,HttpServletRequest req) {
		QueryWrapper<SystemDocType> queryWrapper = QueryGenerator.initQueryWrapper(systemDocType, req.getParameterMap());
		List<SystemDocType> list = systemDocTypeService.list(queryWrapper);
		IPage<SystemDocType> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
		return Result.OK(pageList);
	}

    /**
      * 批量查询子节点
      * @param parentIds 父ID（多个采用半角逗号分割）
      * @return 返回 IPage
      * @param parentIds
      * @return
      */
	//@AutoLog(value = "system_doc_type-批量获取子数据")
    @Operation(summary="system_doc_type-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<SystemDocType> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("parent_id", parentIdList);
            List<SystemDocType> list = systemDocTypeService.list(queryWrapper);
            IPage<SystemDocType> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }
	
	/**
	 *   添加
	 *
	 * @param systemDocType
	 * @return
	 */
	@AutoLog(value = "system_doc_type-添加")
	@Operation(summary="system_doc_type-添加")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SystemDocType systemDocType) {
		systemDocTypeService.addSystemDocType(systemDocType);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param systemDocType
	 * @return
	 */
	@AutoLog(value = "system_doc_type-编辑")
	@Operation(summary="system_doc_type-编辑")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SystemDocType systemDocType) {
		systemDocTypeService.updateSystemDocType(systemDocType);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "system_doc_type-通过id删除")
	@Operation(summary="system_doc_type-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		systemDocTypeService.deleteSystemDocType(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "system_doc_type-批量删除")
	@Operation(summary="system_doc_type-批量删除")
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.systemDocTypeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "system_doc_type-通过id查询")
	@Operation(summary="system_doc_type-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SystemDocType> queryById(@RequestParam(name="id",required=true) String id) {
		SystemDocType systemDocType = systemDocTypeService.getById(id);
		if(systemDocType==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(systemDocType);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param systemDocType
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SystemDocType systemDocType) {
		return super.exportXls(request, systemDocType, SystemDocType.class, "system_doc_type");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('dcs:system_doc_type:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, SystemDocType.class);
    }
}
