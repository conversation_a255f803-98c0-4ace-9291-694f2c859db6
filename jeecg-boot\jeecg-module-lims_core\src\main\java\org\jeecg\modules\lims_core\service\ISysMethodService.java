package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodConsumptive;
import org.jeecg.modules.lims_core.entity.SysMethodStdMaterial;
import org.jeecg.modules.lims_core.entity.SysMethodInstrumentType;
import org.jeecg.modules.lims_core.entity.SysMethodTestingPara;
import org.jeecg.modules.lims_core.entity.SysMethodRepeatType;
import org.jeecg.modules.lims_core.entity.SysMethodWorkflow;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.entity.SysMethod;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 方法
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodService extends IService<SysMethod> {

	/**
	 * 添加一对多
	 *
	 * @param sysMethod
	 * @param sysMethodConsumptiveList
	 * @param sysMethodStdMaterialList
	 * @param sysMethodInstrumentTypeList
	 * @param sysMethodTestingParaList
	 * @param sysMethodRepeatTypeList
	 * @param sysMethodWorkflowList
	 * @param sysMethodAnalyteList
	 */
	public void saveMain(SysMethod sysMethod,List<SysMethodConsumptive> sysMethodConsumptiveList,List<SysMethodStdMaterial> sysMethodStdMaterialList,List<SysMethodInstrumentType> sysMethodInstrumentTypeList,List<SysMethodTestingPara> sysMethodTestingParaList,List<SysMethodRepeatType> sysMethodRepeatTypeList,List<SysMethodWorkflow> sysMethodWorkflowList,List<SysMethodAnalyte> sysMethodAnalyteList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param sysMethod
	 * @param sysMethodConsumptiveList
	 * @param sysMethodStdMaterialList
	 * @param sysMethodInstrumentTypeList
	 * @param sysMethodTestingParaList
	 * @param sysMethodRepeatTypeList
	 * @param sysMethodWorkflowList
	 * @param sysMethodAnalyteList
	 */
	public void updateMain(SysMethod sysMethod,List<SysMethodConsumptive> sysMethodConsumptiveList,List<SysMethodStdMaterial> sysMethodStdMaterialList,List<SysMethodInstrumentType> sysMethodInstrumentTypeList,List<SysMethodTestingPara> sysMethodTestingParaList,List<SysMethodRepeatType> sysMethodRepeatTypeList,List<SysMethodWorkflow> sysMethodWorkflowList,List<SysMethodAnalyte> sysMethodAnalyteList);

	/**
	 * 添加一对多
	 *
	 * @param sysMethod
	 * @param oldmethodId
	 * @param sysMethodConsumptiveList
	 * @param sysMethodStdMaterialList
	 * @param sysMethodInstrumentTypeList
	 * @param sysMethodTestingParaList
	 * @param sysMethodRepeatTypeList
	 * @param sysMethodWorkflowList
	 * @param sysMethodAnalyteList
	 */
	public void saveCopyMain(SysMethod sysMethod,String oldmethodId,List<SysMethodConsumptive> sysMethodConsumptiveList,List<SysMethodStdMaterial> sysMethodStdMaterialList,List<SysMethodInstrumentType> sysMethodInstrumentTypeList,List<SysMethodTestingPara> sysMethodTestingParaList,List<SysMethodRepeatType> sysMethodRepeatTypeList,List<SysMethodWorkflow> sysMethodWorkflowList,List<SysMethodAnalyte> sysMethodAnalyteList) ;


	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
