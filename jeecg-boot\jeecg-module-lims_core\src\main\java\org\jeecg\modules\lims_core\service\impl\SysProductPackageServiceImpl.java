package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.impl.SysStandardEvaluationLimtServiceImpl;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.service.ISysProductPackageService;
import org.jeecg.modules.lims_core.vo.SysProductPackageVo;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 产品套餐
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Service
public class SysProductPackageServiceImpl extends ServiceImpl<SysProductPackageMapper, SysProductPackage> implements ISysProductPackageService {
	@Autowired
	private SysMethodMapper sysMethodMapper;
	@Autowired
	private SysStandardEvaluationLimtServiceImpl standardEvaluationLimtService;
	@Autowired
	private SysAnalyteMapper analyteMapper;

	@Autowired
	private SysProductPackageMapper sysProductPackageMapper;
	@Autowired
	private SysProductPackageDetailsMapper sysProductPackageDetailsMapper;
	@Autowired
	private SysCapabilityMapper sysCapabilityMapper;
	@Autowired
	private SysStandardMapper sysStandardMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysProductPackage sysProductPackage, List<SysProductPackageDetails> sysProductPackageDetailsList) {
		sysProductPackageMapper.insert(sysProductPackage);
		if(sysProductPackageDetailsList!=null && sysProductPackageDetailsList.size()>0) {
			for(SysProductPackageDetails entity:sysProductPackageDetailsList) {
				//外键设置
				entity.setPackageId(sysProductPackage.getId());
				sysProductPackageDetailsMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysProductPackage sysProductPackage,List<SysProductPackageDetails> sysProductPackageDetailsList) {
		sysProductPackageMapper.updateById(sysProductPackage);
		
		//1.先删除子表数据
		sysProductPackageDetailsMapper.deleteByMainId(sysProductPackage.getId());
		
		//2.子表数据重新插入
		if(sysProductPackageDetailsList!=null && sysProductPackageDetailsList.size()>0) {
			for(SysProductPackageDetails entity:sysProductPackageDetailsList) {
				//外键设置
				entity.setPackageId(sysProductPackage.getId());
				sysProductPackageDetailsMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysProductPackageDetailsMapper.deleteByMainId(id);
		sysProductPackageMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysProductPackageDetailsMapper.deleteByMainId(id.toString());
			sysProductPackageMapper.deleteById(id);
		}
	}

	@Override
	public IPage<SysProductPackageVo> listVo(Page<SysProductPackage> page, QueryWrapper<SysProductPackage> queryWrapper)  {
		Page<SysProductPackage> pageProduct = this.page(page, queryWrapper);
		Page<SysProductPackageVo> pageVoProduct = new Page<>();
		BeanUtils.copyProperties(pageProduct,pageVoProduct);
		List<SysProductPackageVo> sysProductVos = new ArrayList<>();
		pageProduct.getRecords().forEach(p -> {

			SysProductPackageVo sysProductVo = new SysProductPackageVo();
			BeanUtils.copyProperties( p,sysProductVo);
			List<SysProductSubVo> children = new ArrayList<>();
			if(p.getCategory().equals("STANDARD")){

				//根据标准拿
				if(p.getStandardId() != null && !p.getStandardId().equals("")){
					// 1.第一版 根据standardEvaluationLimt拿
					standardEvaluationLimtService.list(new QueryWrapper<SysStandardEvaluationLimt>().eq("standard_id", p.getStandardId()).and(wrapper -> wrapper.isNull("parent_id").or().eq("parent_id", "")).orderByAsc("sort_num")).forEach(s -> {
						sysProductVo.setHasChild("1");
						SysAnalyte sysAnalyte = analyteMapper.selectById(s.getAnalyteId());
						SysProductSubVo sysProductSubVo = new SysProductSubVo();
						sysProductSubVo.setId(s.getId());
						sysProductSubVo.setLeadTime(Integer.valueOf(sysProductVo.getStdTat()));
						if(s.getMethodIds().indexOf(',') > 0){
							String[] methodIds = s.getMethodIds().split(",");
							sysProductSubVo.setMethods(sysMethodMapper.selectBatchIds(Arrays.asList(methodIds)));
							sysProductSubVo.setCategory(p.getCategory());
						}else{
							SysMethod sysMethod = sysMethodMapper.selectById(s.getMethodIds());
							SysCapability sysCapability = sysCapabilityMapper.selectById(sysMethod.getCid());
							sysProductSubVo.setMethodId(s.getMethodIds());
							if(sysCapability != null)
								sysProductSubVo.setLeadTime(sysCapability.getLeadTime());
							sysProductSubVo.setMethod(sysMethod.getName());
							sysProductSubVo.setCategory(p.getCategory());
							sysProductSubVo.setStdPrice(sysMethod.getStdPrice());

						}
						sysProductSubVo.setSortNum(s.getSortNum());
						sysProductSubVo.setAnalyteId(s.getAnalyteId());
						sysProductSubVo.setProductPackageId(p.getId());
						sysProductSubVo.setName(sysAnalyte.getName());
						sysProductSubVo.setEvaluationId(s.getId());
						children.add(sysProductSubVo);
					});
				}
			}
			else{
				// 根据明细表拿
				sysProductPackageDetailsMapper.selectByMainId(p.getId()).forEach(s -> {
					sysProductVo.setHasChild("1");
					SysCapability sysCapability = sysCapabilityMapper.selectById(s.getCapabilityId());
					SysProductSubVo sysProductSubVo = new SysProductSubVo();
					sysProductSubVo.setLeadTime(Integer.valueOf(sysProductVo.getStdTat()));
					sysProductSubVo.setId(s.getId());
					if(s.getMethodId() != null && !s.getMethodId().equals("")){
						SysMethod sysMethod = sysMethodMapper.selectById(s.getMethodId());
						sysProductSubVo.setMethodId(s.getMethodId());
						sysProductSubVo.setMethod(sysMethod.getName());
					}
					sysProductSubVo.setPpdId(s.getId());
					sysProductSubVo.setCategory(p.getCategory());
					sysProductSubVo.setCapabilityId(s.getCapabilityId());
					sysProductSubVo.setStdPrice((sysCapability.getStdPrice()));
					sysProductSubVo.setProductPackageId(p.getId());
					sysProductSubVo.setName(sysCapability.getName());
					sysProductSubVo.setLeadTime(sysCapability.getLeadTime());
					sysProductSubVo.setEvaluationId(s.getId());
					children.add(sysProductSubVo);
				});

			}
			if(children.size() > 0){
				sysProductVo.setChildren(children);
			}
			if(p.getStandardId()!= null){
				SysStandard sysStandard = sysStandardMapper.selectById(p.getStandardId());
				if(sysStandard != null){
					String modifiedName = sysStandard.getName();
					if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("》")) {
						int index = sysStandard.getName().indexOf("》");
						modifiedName = sysStandard.getName().substring(0, index + 1)
								+ sysStandard.getVersion()
								+ " "
								+ sysStandard.getName().substring(index + 1);
					} else if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("GB")) {
						modifiedName = sysStandard.getName() + "-" + sysStandard.getVersion();
					}
					sysProductVo.setStandardName(modifiedName);
				}
			}

			sysProductVo.setProductPackageId(p.getId());
			sysProductVos.add(sysProductVo);
		});
		pageVoProduct.setRecords(sysProductVos);

		return pageVoProduct;
	}

	@Override
	public IPage<SysProductPackage> queryPageList(Page<SysProductPackage> page, Wrapper<SysProductPackage> wrapper) {
		return sysProductPackageMapper.queryPageList(page,wrapper);
	}
}
