package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;

@Data
public class SampleQuery {
    private String id;
    @Excel( name = "报价单号", width = 15)
    private String quotationNo;
    @Excel(name = "BD销售人员", width = 15)
    private String responsiblePerson;
    @Excel(name = "合同号", width = 15)
    private String contractNo;
    @Excel(name = "业务类型", width = 15)
    private String bizType;
    @Excel(name = "委托单位", width = 15)
    private String customerName;
    @Excel(name = "生产单位", width = 15)
    private String manufacturer;
    @Excel(name = "研发项目名称", width = 15)
    private String rpName;
    @Excel(name = "研发项目编号", width = 15)
    private String rdNo;
    @Excel(name = "检品名称", width = 15)
    private String sampleName;
    @Excel(name = "检品编号", width = 15)
    private String sampleNo;
    @Excel(name = "检验项目", width = 15)
    private String capabilityName;
    @Excel(name = "指派检验组时间", width = 15)
    private String pmTime;
    @Excel(name = "结果录入日期", width = 15)
    private String testerTime;
    @Excel(name = "复核日期", width = 15)
    private String checkedTime;
    @Excel(name = "受理时间", width = 15)
    private String receiveDate;
    @Excel(name = "检验组", width = 15)
    private String departName;
    @Excel(name = "指派时间", width = 15)
    private String assignTime;
    @Excel(name = "检验人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String assignee;
    @Excel(name = "合作人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String cooper;
    @Excel(name = "复核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String checker;
    @Excel(name = "报告书编制人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String makeBy;
    @Excel(name = "报告书编制日期", width = 15)
    private String makeTime;
    @Excel(name = "报告书审核人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String approveBy;
    @Excel(name = "报告书审核日期", width = 15)
    private String approveTime;
    @Excel(name = "报告书签发人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private String signBy;
    @Excel(name = "报告书签发日期", width = 15)
    private String signTime;

}
