package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysUnit;
import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import org.jeecg.modules.lims_core.mapper.SysUnitConversionMapper;
import org.jeecg.modules.lims_core.mapper.SysUnitMapper;
import org.jeecg.modules.lims_core.service.ISysUnitService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 计量单位
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Service
public class SysUnitServiceImpl extends ServiceImpl<SysUnitMapper, SysUnit> implements ISysUnitService {

	@Autowired
	private SysUnitMapper sysUnitMapper;
	@Autowired
	private SysUnitConversionMapper sysUnitConversionMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysUnit sysUnit, List<SysUnitConversion> sysUnitConversionList) {
		sysUnitMapper.insert(sysUnit);
		if(sysUnitConversionList!=null && sysUnitConversionList.size()>0) {
			for(SysUnitConversion entity:sysUnitConversionList) {
				//外键设置
				entity.setSourceUnitId(sysUnit.getId());
				sysUnitConversionMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysUnit sysUnit,List<SysUnitConversion> sysUnitConversionList) {
		sysUnitMapper.updateById(sysUnit);
		
		//1.先删除子表数据
		sysUnitConversionMapper.deleteByMainId(sysUnit.getId());
		
		//2.子表数据重新插入
		if(sysUnitConversionList!=null && sysUnitConversionList.size()>0) {
			for(SysUnitConversion entity:sysUnitConversionList) {
				//外键设置
				entity.setSourceUnitId(sysUnit.getId());
				sysUnitConversionMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysUnitConversionMapper.deleteByMainId(id);
		sysUnitMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysUnitConversionMapper.deleteByMainId(id.toString());
			sysUnitMapper.deleteById(id);
		}
	}
	
}
