package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodTestingPara;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 实验过程参数
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodTestingParaService extends IService<SysMethodTestingPara> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysMethodTestingPara>
	 */
	public List<SysMethodTestingPara> selectByMainId(String mainId);
}
