package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.Consumptive;
import org.jeecg.modules.lims_core.entity.Inventory;
import org.jeecg.modules.lims_core.entity.StandardMaterial;
import org.jeecg.modules.lims_core.service.IConsumptiveService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.IStandardMaterialService;
import org.jeecg.modules.lims_core.vo.ConsumptiveVO;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_core.vo.StandardMaterialVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 耗材台账
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
@Tag(name="耗材台账")
@RestController
@RequestMapping("/lims_core/consumptive")
@Slf4j
public class ConsumptiveController extends JeecgController<Consumptive, IConsumptiveService> {
	@Autowired
	private IConsumptiveService consumptiveService;
	 @Autowired
	 private IStandardMaterialService standardMaterialService;
	/**
	 * 分页列表查询
	 *
	 * @param consumptive
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "耗材台账-分页列表查询")
	@Operation(summary="耗材台账-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ConsumptiveVO>> queryPageList(Consumptive consumptive,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String sampleId = req.getParameter("sampleId");
		if (StringUtils.isNotBlank(sampleId)) {
			QueryWrapper<Consumptive> queryWrapper = new QueryWrapper<>();
			List<String> sampleIdList = Arrays.asList(sampleId.split(","));
			String inCondition = sampleIdList.stream()
					.map(id -> "FIND_IN_SET('" + id + "', sample_id)")
					.collect(Collectors.joining(" OR "));
			queryWrapper.apply(inCondition)
					.and(wrapper -> wrapper.notLike("destroy_reason", "销毁").or().isNull("destroy_reason"));
			Page<Consumptive> page = new Page<>(pageNo, pageSize);
			IPage<ConsumptiveVO> pageList = consumptiveService.queryPageList(page, queryWrapper);
			return Result.OK(pageList);
		}else {
			QueryWrapper<Consumptive> queryWrapper = QueryGenerator.initQueryWrapper(consumptive, req.getParameterMap());
			Page<Consumptive> page = new Page<Consumptive>(pageNo, pageSize);
			IPage<ConsumptiveVO> pageList = consumptiveService.queryPageList(page, queryWrapper);
			return Result.OK(pageList);
		}
	}

	 /**
	  *   是否存在标准品和色谱柱
	  *
	  * @param consumptive
	  * @return
	  */
	 @AutoLog(value = "是否存在标准品和色谱柱")
	 @Operation(summary="是否存在标准品和色谱柱")
	 @PostMapping(value = "/existForeignGoods")
	 public Result<String> existForeignGoods(@RequestBody Consumptive consumptive) {
		 String sampleId = consumptive.getSampleId();
		 String msg="";
		 if (StringUtils.isNotBlank(sampleId)) {
			 QueryWrapper<Consumptive> queryWrapper = new QueryWrapper<>();
			 List<String> sampleIdList = Arrays.asList(sampleId.split(","));
			 String inCondition = sampleIdList.stream()
					 .map(id -> "FIND_IN_SET('" + id + "', sample_id)")
					 .collect(Collectors.joining(" OR "));
			 queryWrapper.apply(inCondition);
			 Page<Consumptive> page = new Page<>(1, 10);
			 IPage<ConsumptiveVO> pageList = consumptiveService.queryPageList(page, queryWrapper);

			 QueryWrapper<StandardMaterial> queryWrapper1 = new QueryWrapper<>();
			 List<String> sampleIdList1 = Arrays.asList(sampleId.split(","));
			 String inCondition1 = sampleIdList1.stream()
					 .map(id -> "FIND_IN_SET('" + id + "', sample_id)")
					 .collect(Collectors.joining(" OR "));
			 queryWrapper1.apply(inCondition1);
			 Page<StandardMaterial> page1 = new Page<>(1, 10);
			 IPage<StandardMaterialVO> pageList1 = standardMaterialService.queryPageList(page1, queryWrapper1);
			 boolean hasChromatographicColumn = pageList != null && pageList.getRecords() != null && !pageList.getRecords().isEmpty();
			 boolean hasStandardMaterial = pageList1 != null && pageList1.getRecords() != null && !pageList1.getRecords().isEmpty();
			 if (hasStandardMaterial && hasChromatographicColumn) {
				 msg = "存在外来标准品和色谱柱，注意领用";
			 } else if (hasStandardMaterial) {
				 msg = "存在外来标准品，注意领用";
			 } else if (hasChromatographicColumn) {
				 msg = "存在外来色谱柱，注意领用";
			 }
		 }
           return Result.OK(msg);
	 }

	
	/**
	 *   添加
	 *
	 * @param consumptive
	 * @return
	 */
	@AutoLog(value = "耗材台账-添加")
	@Operation(summary="耗材台账-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:consumptive:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Consumptive consumptive) {
		consumptive.setCode(FillRuleUtil.executeRule(FillRuleConstant.CONSUMPTIVE,null).toString());
		consumptiveService.save(consumptive);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param consumptive
	 * @return
	 */
	@AutoLog(value = "耗材台账-编辑")
	@Operation(summary="耗材台账-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:consumptive:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Consumptive consumptive) {
		consumptiveService.updateById(consumptive);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "耗材台账-通过id删除")
	@Operation(summary="耗材台账-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:consumptive:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		consumptiveService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "耗材台账-批量删除")
	@Operation(summary="耗材台账-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:consumptive:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.consumptiveService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "耗材台账-通过id查询")
	@Operation(summary="耗材台账-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Consumptive> queryById(@RequestParam(name="id",required=true) String id) {
		Consumptive consumptive = consumptiveService.getById(id);
		if(consumptive==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(consumptive);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param consumptive
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:consumptive:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Consumptive consumptive) {
        return super.exportXls(request, consumptive, Consumptive.class, "耗材台账");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:consumptive:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Consumptive.class);
    }

}
