package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.Inventory;
import org.jeecg.modules.lims_core.mapper.InventoryMapper;
import org.jeecg.modules.lims_core.service.IInventoryService;
import org.jeecg.modules.lims_core.vo.InventoryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2025-04-23
 * @Version: V1.0
 */
@Service
public class InventoryServiceImpl extends ServiceImpl<InventoryMapper, Inventory> implements IInventoryService {
    @Autowired
    private InventoryMapper inventoryMapper;

    @Override
    public IPage<InventoryVo> queryPageList(Page<InventoryVo> page, Wrapper<InventoryVo> wrapper) {
        return inventoryMapper.queryPageList(page,wrapper);
    }
}
