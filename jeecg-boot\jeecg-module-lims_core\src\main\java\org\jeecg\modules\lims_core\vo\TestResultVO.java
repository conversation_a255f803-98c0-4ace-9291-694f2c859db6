package org.jeecg.modules.lims_core.vo;

import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "TestResult",drillUp = "test_id->test.id",description = "检测指标信息")
public class TestResultVO {
    @TemplateField(description = "id")
    private String id;
    @TemplateField(entityFieldName = "id",func = "getAnalyteName",description = "指标名称")
    private String analyte;
    @TemplateField(description = "检测结果")
    private String rawResult;
    @TemplateField(entityFieldName = "id",func = "getRepResult",description = "报告结果")
    private String repResult;
    @TemplateField(description = "方法参数关联id")
    private String methodAnalyteId;
    @TemplateField(entityFieldName = "method_analyte_id",drillChain = "sys_method_analyte.id->sys_method_analyte.method_id->sys_method.id->sys_method.name",description = "方法名称")
    private String methodName;
    @TemplateField(entityFieldName = "method_analyte_id",drillChain = "sys_method_analyte.id->sys_method_analyte.analyte_id->sys_analyte.id->sys_analyte.name",description = "参数名称")
    private String analyteName;
    @TemplateField(entityFieldName = "id",drillChain = "test_result.test_id->test.id->test.task_id->test_task.id->test_task.sample_id->sample.id->sample.name",description = "样品名称")
    private String sampleName;
    @TemplateField(entityFieldName = "method_analyte_id",drillChain = "sys_method_analyte.id->sys_method_analyte.method_id->sys_method.id->sys_method.std_price",description = "方法价格")
    private String standardPrice;
    @TemplateField(entityFieldName = "method_analyte_id",drillChain = "sys_method_analyte.id->sys_method_analyte.method_id->sys_method.id->sys_method.standard_id->sys_standard.id->sys_standard.name",description = "标准名称")
    private String standardName;
    @TemplateField(entityFieldName = "id",func = "getLimit",description = "评价标准")
    private String limit;
    @TemplateField(entityFieldName = "id",func = "getReportName",description = "报告名称")
    private String reportName;
    @TemplateField(entityFieldName = "id",func = "getConclusion",description = "结论")
    private String conclusion;
    @TemplateField(entityFieldName = "method_analyte_id",drillChain = "sys_method_analyte.id->sys_method_analyte.method_id->sys_method.id->sys_method.cid->sys_capability.id->sys_capability.name",description = "报告名称")
    private String capability;
    @TemplateField(description = "报告单位",entityFieldName = "id",func="getRepUnit")
    private String repUnit;
    @TemplateField(entityFieldName = "limit_id",drillChain = "sys_standard_evaluation_limt.id->sys_standard_evaluation_limt.group_one",description = "第一组别")
    private String groupOne;
    @TemplateField(entityFieldName = "limit_id",drillChain = "sys_standard_evaluation_limt.id->sys_standard_evaluation_limt.group_two",description = "第二组别")
    private String groupTwo;
//   未检出要出检出限或<=定量限的备注 举例： 检出限0.5 ,定量限0.8，  限值 <=1.0
//    0.2    未检出   备注:检测限： 0.5 ppm
//    0.7   <=0.8     备注: 定量限：0.8ppm
//    0.9
//    1.1
    @TemplateField(entityFieldName = "id",func = "getAnalytelodloq",description = "备注")
    private String remark;

}