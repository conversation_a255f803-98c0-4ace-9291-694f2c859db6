package org.jeecg.modules.crm.service;

import org.jeecg.modules.crm.entity.SysCustomerContact;
import org.jeecg.modules.crm.entity.SysCustomer;
import com.baomidou.mybatisplus.extension.service.IService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 客户
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
public interface ISysCustomerService extends IService<SysCustomer> {

	/**
	 * 添加一对多
	 *
	 * @param sysCustomer
	 * @param sysCustomerContactList
	 */
	public void saveMain(SysCustomer sysCustomer,List<SysCustomerContact> sysCustomerContactList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param sysCustomer
	 * @param sysCustomerContactList
	 */
	public void updateMain(SysCustomer sysCustomer,List<SysCustomerContact> sysCustomerContactList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
