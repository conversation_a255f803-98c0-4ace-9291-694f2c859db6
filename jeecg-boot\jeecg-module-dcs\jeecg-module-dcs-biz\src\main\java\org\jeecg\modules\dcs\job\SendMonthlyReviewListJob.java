package org.jeecg.modules.dcs.job;

import me.chanjar.weixin.cp.bean.article.NewArticle;
import org.jeecg.modules.dcs.util.WeworkUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @title: 发送月度复审消息
 * @version 1.0
 * @date 2024/12/23 10:08
 */
@Slf4j
public class SendMonthlyReviewListJob implements Job {
    /**
     * 若参数变量名修改 QuartzJobController中也需对应修改
     */
    private String parameter;

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        NewArticle newArticle = new NewArticle();
        newArticle.setTitle("月度评审清单");
        newArticle.setDescription("月度评审清单");
        newArticle.setUrl("https://gbjc.cc/jeecg-boot/jmreport/shareView/1029930079551053824?shareToken=c2de75577e3a80270af20b6a4018011c");
        newArticle.setPicUrl("http://nwzimg.wezhan.cn/contents/sitefiles2049/10245330/images/48843504.jpg");
        WeworkUtils.SendMsg("news",this.parameter,newArticle);
    }
}