package org.jeecg.modules.oo.util;

import org.bouncycastle.asn1.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ASN1TaggedObject处理测试
 */
public class Asn1TaggedObjectTest {

    @Test
    @DisplayName("测试隐式标记的ASN1TaggedObject处理")
    void testImplicitTaggedObject() {
        try {
            // 创建一个隐式标记的对象来模拟实际场景
            String testUrl = "http://ocsp2.gdca.com.cn/ocsp";
            byte[] urlBytes = testUrl.getBytes("UTF-8");
            
            // 创建隐式标记的对象 (标签6，隐式)
            ASN1TaggedObject implicitTagged = new DERTaggedObject(false, 6, new DEROctetString(urlBytes));
            
            // 测试我们的解析方法是否能处理隐式标记
            String result = OcspUtil.getStringFromGeneralName(implicitTagged);
            
            assertNotNull(result, "应该能够解析隐式标记的对象");
            assertTrue(result.contains("ocsp"), "解析结果应该包含OCSP相关内容");
            
            System.out.println("隐式标记测试成功: " + result);
            
        } catch (Exception e) {
            fail("隐式标记测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试显式标记的ASN1TaggedObject处理")
    void testExplicitTaggedObject() {
        try {
            // 创建一个显式标记的对象
            String testUrl = "http://ocsp1.gdca.com.cn/ocsp";
            DERIA5String urlString = new DERIA5String(testUrl);
            
            // 创建显式标记的对象 (标签6，显式)
            ASN1TaggedObject explicitTagged = new DERTaggedObject(true, 6, urlString);
            
            // 测试我们的解析方法是否能处理显式标记
            String result = OcspUtil.getStringFromGeneralName(explicitTagged);
            
            assertNotNull(result, "应该能够解析显式标记的对象");
            assertEquals(testUrl, result, "解析结果应该与原始URL相同");
            
            System.out.println("显式标记测试成功: " + result);
            
        } catch (Exception e) {
            fail("显式标记测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试十六进制编码的URL解析")
    void testHexEncodedUrl() {
        try {
            // 测试十六进制编码的URL
            String hexUrl = "687474703a2f2f6f637370322e676463612e636f6d2e636e2f6f637370";
            String expectedUrl = "http://ocsp2.gdca.com.cn/ocsp";
            
            // 创建包含十六进制数据的对象
            byte[] hexBytes = new byte[hexUrl.length() / 2];
            for (int i = 0; i < hexUrl.length(); i += 2) {
                hexBytes[i / 2] = (byte) Integer.parseInt(hexUrl.substring(i, i + 2), 16);
            }
            
            ASN1TaggedObject hexTagged = new DERTaggedObject(false, 6, new DEROctetString(hexBytes));
            
            String result = OcspUtil.getStringFromGeneralName(hexTagged);
            
            assertNotNull(result, "应该能够解析十六进制编码的URL");
            assertEquals(expectedUrl, result, "解析结果应该与期望的URL相同");
            
            System.out.println("十六进制编码测试成功: " + result);
            
        } catch (Exception e) {
            fail("十六进制编码测试失败: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        Asn1TaggedObjectTest test = new Asn1TaggedObjectTest();
        
        System.out.println("开始ASN1TaggedObject处理测试...");
        
        try {
            test.testImplicitTaggedObject();
            test.testExplicitTaggedObject();
            test.testHexEncodedUrl();
            System.out.println("所有测试通过！");
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}