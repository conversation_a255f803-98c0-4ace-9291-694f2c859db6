package org.jeecg.modules.crm.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crm.entity.MarketingActivity;
import org.jeecg.modules.crm.service.IMarketingActivityService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 市场活动
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Tag(name="市场活动")
@RestController
@RequestMapping("/crm/marketingActivity")
@Slf4j
public class MarketingActivityController extends JeecgController<MarketingActivity, IMarketingActivityService> {
	@Autowired
	private IMarketingActivityService marketingActivityService;
	
	/**
	 * 分页列表查询
	 *
	 * @param marketingActivity
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "市场活动-分页列表查询")
	@Operation(summary="市场活动-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<MarketingActivity>> queryPageList(MarketingActivity marketingActivity,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<MarketingActivity> queryWrapper = QueryGenerator.initQueryWrapper(marketingActivity, req.getParameterMap());
		Page<MarketingActivity> page = new Page<MarketingActivity>(pageNo, pageSize);
		IPage<MarketingActivity> pageList = marketingActivityService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param marketingActivity
	 * @return
	 */
	@AutoLog(value = "市场活动-添加")
	@Operation(summary="市场活动-添加")
	@PreAuthorize("@jps.requiresPermissions('crm:marketing_activity:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody MarketingActivity marketingActivity) {
		marketingActivityService.save(marketingActivity);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param marketingActivity
	 * @return
	 */
	@AutoLog(value = "市场活动-编辑")
	@Operation(summary="市场活动-编辑")
    @PreAuthorize("@jps.requiresPermissions('crm:marketing_activity:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody MarketingActivity marketingActivity) {
		marketingActivityService.updateById(marketingActivity);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "市场活动-通过id删除")
	@Operation(summary="市场活动-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('crm:marketing_activity:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		marketingActivityService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "市场活动-批量删除")
	@Operation(summary="市场活动-批量删除")
    @PreAuthorize("@jps.requiresPermissions('crm:marketing_activity:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.marketingActivityService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "市场活动-通过id查询")
	@Operation(summary="市场活动-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<MarketingActivity> queryById(@RequestParam(name="id",required=true) String id) {
		MarketingActivity marketingActivity = marketingActivityService.getById(id);
		if(marketingActivity==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(marketingActivity);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param marketingActivity
    */
    @PreAuthorize("@jps.requiresPermissions('crm:marketing_activity:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MarketingActivity marketingActivity) {
        return super.exportXls(request, marketingActivity, MarketingActivity.class, "市场活动");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('crm:marketing_activity:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, MarketingActivity.class);
    }

}
