package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.WarehouseInApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.entity.WarehouseOutApply;
import org.jeecg.modules.lims_core.vo.WarehouseInApplyVo;

/**
 * @Description: 余物退回申请
 * @Author: jeecg-boot
 * @Date:   2025-05-27
 * @Version: V1.0
 */
public interface WarehouseInApplyMapper extends BaseMapper<WarehouseInApply> {
    IPage<WarehouseInApplyVo> queryPageList(Page<WarehouseInApplyVo> page,
                                           @Param(Constants.WRAPPER) Wrapper<WarehouseInApplyVo> wrapper);
}
