package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.WarehouseOutApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.WarehouseOutApplyVo;

/**
 * @Description: 出库申请
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
public interface WarehouseOutApplyMapper extends BaseMapper<WarehouseOutApply> {
    IPage<WarehouseOutApplyVo> queryPageList(Page<WarehouseOutApplyVo> page,
                                             @Param(Constants.WRAPPER) Wrapper<WarehouseOutApplyVo> wrapper);
}
