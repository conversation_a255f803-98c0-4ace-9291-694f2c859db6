package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import org.jeecg.modules.lims_core.entity.Inventory;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.InventoryVo;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2025-04-23
 * @Version: V1.0
 */
public interface IInventoryService extends IService<Inventory> {

     public IPage<InventoryVo> queryPageList(Page<InventoryVo> page,
                                             @Param(Constants.WRAPPER) Wrapper<InventoryVo> wrapper);
}
