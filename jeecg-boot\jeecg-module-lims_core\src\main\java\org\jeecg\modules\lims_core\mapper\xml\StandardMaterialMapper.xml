<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.StandardMaterialMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.StandardMaterialVO">
        select * from ( select sm.*,i.amount as inventoryquantity ,wsb.code as warehousebox  from  standard_material sm
            left join  inventory i on sm.CODE=i.article_no
            LEFT JOIN sys_warehouse_box wsb on i.box_id=wsb.id ) t
            ${ew.customSqlSegment}
    </select>

</mapper>