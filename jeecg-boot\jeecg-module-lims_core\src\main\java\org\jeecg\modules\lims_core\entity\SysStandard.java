package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 标准
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
@Schema(description="标准")
@Data
@TableName("sys_standard")
public class SysStandard implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
	/**版本*/
	@Excel(name = "版本", width = 15)
    @Schema(description = "版本")
    private java.lang.String version;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @Schema(description = "描述")
    private java.lang.String description;
	/**等级类别*/
    @Excel(name = "等级类别", width = 15, dictTable = "sys_category", dicText = "name", dicCode = "id")
    @Schema(description = "等级类别")
    private java.lang.String gradeTypeId;
	/**客户*/
	@Excel(name = "客户", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户")
    private java.lang.String customerId;
	/**内容类别*/
	@Excel(name = "内容类别", width = 15, dicCode = "standard_content_type")
    @Dict(dicCode = "standard_content_type")
    @Schema(description = "内容类别")
    private java.lang.String contentTypeId;
	/**备案日期*/
	@Excel(name = "备案日期", width = 15)
    @Schema(description = "备案日期")
    private java.lang.String registeredDate;
	/**发布日期*/
	@Excel(name = "发布日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "发布日期")
    private java.util.Date publishDate;
	/**生效日期*/
	@Excel(name = "生效日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "生效日期")
    private java.util.Date effectiveDate;
	/**作废日期*/
	@Excel(name = "作废日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "作废日期")
    private java.util.Date abandonedDate;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "standard_status")
    @Dict(dicCode = "standard_status")
    @Schema(description = "状态")
    private java.lang.String effectiveStatus;
	/**文件地址*/
	@Excel(name = "文件地址", width = 15)
    @Schema(description = "文件地址")
    private java.lang.String url;
	/**受控号*/
	@Excel(name = "受控号", width = 15)
    @Schema(description = "受控号")
    private java.lang.String dcsNo;
	/**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;

    @TableField(exist = false)
    private String isSubmitted;

}
