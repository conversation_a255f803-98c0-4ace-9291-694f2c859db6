package org.jeecg.modules.lims_core.vo;
import lombok.Data;
import org.jeecg.modules.lims_core.entity.Report;

@Data
public class ReportRequestVO extends Report {
    private String reportstatus;
    private String sampleName;
    //c.id as customer_id,c.name as customer_name,cc.name as contact_name,cc.email
    private String customerId;
    private String customerName;
    private String contactName;
    private String contactEmail;
    private String salerEmail;
    private String salerId;
}
