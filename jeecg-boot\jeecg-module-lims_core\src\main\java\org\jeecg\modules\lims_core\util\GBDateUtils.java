package org.jeecg.modules.lims_core.util;

import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;

public class GBDateUtils {
    public static Date calcDate(String textForMatch, Date startDate) {
        //写正则匹配，根据年，个月，日 匹配，然后从现在开始算完后多长时间
        Date nextTestTime = null;
        String nextCycle = textForMatch;
        if(nextCycle.matches(".*年.*")) {
            Matcher matcher = java.util.regex.Pattern.compile("(\\d+)年").matcher(nextCycle);
            if (matcher.find()) {
                int year = Integer.parseInt(matcher.group(1));
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(Calendar.YEAR, year);
                Date date = calendar.getTime();
                nextTestTime = date;
            }
        }
        if(nextCycle.matches(".*个月.*")) {
            Matcher matcher = java.util.regex.Pattern.compile("(\\d+)个月").matcher(nextCycle);
            if (matcher.find()) {
                int month = Integer.parseInt(matcher.group(1));
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(Calendar.MONTH, month);
                Date date = calendar.getTime();
                nextTestTime = date;
            }
        }
        if(nextCycle.matches(".*天.*")) {
            Matcher matcher = java.util.regex.Pattern.compile("(\\d+)天").matcher(nextCycle);
            if (matcher.find()) {
                int day = Integer.parseInt(matcher.group(1));
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(Calendar.DATE, day);
                Date date = calendar.getTime();
                nextTestTime = date;
            }
        }
        return nextTestTime;
    }

}
