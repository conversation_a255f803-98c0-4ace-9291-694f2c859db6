package org.jeecg.modules.role_add;

import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.system.service.ISysRolePermissionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19 15:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
public class test {
    @Autowired
    private ISysRolePermissionService iSysRolePermissionService;

    @Test
    public void test() {
        iSysRolePermissionService.saveRolePermission("1935596694003638273","2024122009595050100,1899386909049667586,2024122009595050101,2024122009595050102,2024122009595060105,2024122009595060106,1876082174709538818,2024121907294880380,2024121907294880381,2024121907294880382,2024121907294880385,2024121907294880386,2025030604505130050,1897832776228704258,2025030604505130051,2025030604505130052,2025030604505130053,2025030604505130054,2025030604505130055,2025030604505130056,1859917970957905922,2024112309053780240,1860977590400962562,1860977862586126338,1861303418754146306,1863551735403896834,1864640370182148097,1864877470609444866,1864883156219301889,1870029246559195138,2024112309053780241,2024112309053780242,2024112309053790243,2024112309053790244,2024112309053790245,2024112309053790246,1869707636515008514,1869284954765991937,2025040809262590151,2025040809262590152,2025051207165870531,2025051207165870532,2025051207165870533,2025051207165870534,2025030710245520400,2025030710245520401,2025030710245520402,2025030710245520403,2025030710245520404,2025030710245520405,2025030710245520406,2025021209011160590,2025040809262590150,2025021209011170592,1588513553652436993,2025040909024690090,2025040909024690092,1592114772665790465");
        iSysRolePermissionService.saveRolePermission("1935596752761643009","2024122009595050100,1899386909049667586,2024122009595050101,2024122009595050102,2024122009595060105,2024122009595060106,1876082174709538818,2024121907294880380,2024121907294880381,2024121907294880382,2024121907294880385,2024121907294880386,2025030604505130050,1897832776228704258,2025030604505130051,2025030604505130052,2025030604505130053,2025030604505130054,2025030604505130055,2025030604505130056,1859917970957905922,2024112309053780240,1860977590400962562,1860977862586126338,1861303418754146306,1863551735403896834,1864640370182148097,1864877470609444866,1864883156219301889,1870029246559195138,2024112309053780241,2024112309053780242,2024112309053790243,2024112309053790244,2024112309053790245,2024112309053790246,1869707636515008514,1869284954765991937,2025040809262590151,2025040809262590152,2025051207165870531,2025051207165870532,2025051207165870533,2025051207165870534,2025030710245520400,2025030710245520401,2025030710245520402,2025030710245520403,2025030710245520404,2025030710245520405,2025030710245520406,2025021209011160590,2025040809262590150,2025021209011170592,1588513553652436993,2025040909024690090,2025040909024690092,1592114772665790465");
    }
}
