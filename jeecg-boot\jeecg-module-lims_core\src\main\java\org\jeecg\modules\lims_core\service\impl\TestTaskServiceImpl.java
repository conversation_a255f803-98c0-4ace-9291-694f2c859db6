package org.jeecg.modules.lims_core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.JeecgBaseConfig;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.entity.Test;
import org.jeecg.modules.lims_core.entity.TestTask;
import org.jeecg.modules.lims_core.entity.TestTaskFlow;
import org.jeecg.modules.lims_core.mapper.TestMapper;
import org.jeecg.modules.lims_core.mapper.TestTaskFlowMapper;
import org.jeecg.modules.lims_core.mapper.TestTaskMapper;
import org.jeecg.modules.lims_core.vo.SampleQuery;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.vo.TaskVo;
import org.jeecg.modules.lims_order.entity.Quotation;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysDictItem;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysDictItemService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 测试任务
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
@Service
public class TestTaskServiceImpl extends ServiceImpl<TestTaskMapper, TestTask> implements ITestTaskService {
    @Autowired
    private TestTaskMapper testTaskMapper;
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
    @Autowired
    private SysMethodMapper sysMethodMapper;
    @Autowired
    private ISysMethodAnalyteService sysMethodAnalyteService;
    @Autowired
    private SysMethodWorkflowMapper sysMethodWorkflowMapper;
    @Autowired
    private SysWorkflowStepMapper sysWorkflowStepMapper;
    @Autowired
    private TestTaskFlowMapper testTaskFlowMapper;
    @Autowired
    private TestResultMapper testResultMapper;
    @Autowired
    private ISysAnalyteService sysAnalyteService;
    @Autowired
    private IQuotationService quotationServiceImpl;
    @Autowired
    private ISysDictItemService sysDictItemService;

    private static final String FLOW_STEP_1 = "业务受理";
    private static final String FLOW_STEP_2 = "仓库入库";
    private static final String FLOW_STEP_3 = "PM确认";
    private static final String FLOW_STEP_4 = "任务指派";
    private static final String TASK_STATUS_0 = "未指派";
    private static final String TASK_STATUS_1 = "进行中";
    @Autowired
    private SysCapabilityServiceImpl sysCapabilityServiceImpl;
    @Autowired
    private SampleMapper sampleMapper;
    @Autowired
    private IQuotationService iQuotationService;
    @Autowired
    private ISysCapabilityService iSysCapabilityService;
    @Autowired
    private ISysDepartService iSysDepartService;
    @Autowired
    private IHolidayCalendarService iHolidayCalendarService;
    @Autowired
    private IRdProjectService iRdProjectService;
    @Autowired
    private SysStandardMapper sysStandardMapper ;
    @Autowired
    private ISysWorkflowStepService iSysWorkflowStepService;
    @Autowired
    private IReportService iReportService;
    @Resource
    private JeecgBaseConfig jeecgBaseConfig;
    @Autowired
    private ISysBaseAPI iSysBaseAPI;

    public void generateTest(TestTask testTask, List<SysProductSubVo> sysProductSubVos) {

        if(testMapper.selectByTaskId(testTask.getId()).size() > 0){
            return;
        }

        SysMethod sysMethod = sysMethodMapper.selectById(testTask.getMethodId());
        QueryWrapper<SysMethodAnalyte> qwMethodAnalyte = new QueryWrapper<>();
        qwMethodAnalyte.eq("method_id", testTask.getMethodId());
        List<SysMethodAnalyte> sysMethodAnalytes = sysMethodAnalyteService.list(qwMethodAnalyte);

        //生成第一个test
        Test test = new Test();
        test.setTaskId(testTask.getId());
        test.setTrialNo(1);
        test.setTestTypeId("0");
        testMapper.insert(test);

        //生成平行样
        if (sysMethod.getDuplicateQty() > 1) {
            for (int i = 1; i < sysMethod.getDuplicateQty(); i++) {
                Test dupTest = new Test();
                BeanUtils.copyProperties(test, dupTest);
                dupTest.setId(null);
                dupTest.setTaskId(testTask.getId());
                dupTest.setTestTypeId("1");
                dupTest.setTrialNo(testMapper.selectByTaskId(testTask.getId()).size() + 1);
                testMapper.insert(dupTest);
            }
        }
        //生成空白样
        if(sysMethod.getBlkQty() > 0){
            for (int i = 0; i < sysMethod.getBlkQty(); i++) {
                Test blkTest = new Test();
                BeanUtils.copyProperties(test, blkTest);
                blkTest.setId(null);
                blkTest.setTestTypeId("2");
                blkTest.setTaskId(testTask.getId());
                blkTest.setTrialNo(testMapper.selectByTaskId(testTask.getId()).size() + 1);
                testMapper.insert(blkTest);
            }
        }
        //生成加标样
        if(sysMethod.getStdQty() > 0) {
            for (int i = 0; i < sysMethod.getStdQty(); i++) {
                Test stdTest = new Test();
                BeanUtils.copyProperties(test, stdTest);
                stdTest.setId(null);
                stdTest.setTestTypeId("3");
                stdTest.setTaskId(testTask.getId());
                stdTest.setTrialNo(testMapper.selectByTaskId(testTask.getId()).size() + 1);
                testMapper.insert(stdTest);
            }
        }

        //生成测试结果
        sysMethodAnalytes.forEach(sysMethodAnalyte -> {
            for (Test testToAdd : testMapper.selectByTaskId(testTask.getId())) {
                TestResult testResult = new TestResult();
                testResult.setTestId(testToAdd.getId());
                testResult.setMethodAnalyteId(sysMethodAnalyte.getId());
                if(sysProductSubVos != null && sysProductSubVos.size() > 0){
                    List<SysProductSubVo> collect = sysProductSubVos.stream().filter(sysProductSubVo -> sysProductSubVo.getAnalyteId().equals(sysMethodAnalyte.getAnalyteId())).collect(Collectors.toList());
                    if(collect.size() > 0){
                        SysProductSubVo sysProductSubVo = collect.get(0);
                        testResult.setLimitId(sysProductSubVo.getEvaluationId());
                        testResult.setReportable("是");
                        testResultMapper.insert(testResult);
                    } else {
                        // 获取当前 sysMethodAnalyte 对应的 SysAnalyte
                        SysAnalyte currentAnalyte = sysAnalyteService.getById(sysMethodAnalyte.getAnalyteId());
                        if (currentAnalyte != null) {
                            String currentAnalyteName = currentAnalyte.getName();
                            // 检查 sysMethodAnalytes 集合中是否有记录的 calcExpr 包含当前分析物的名称
                            boolean found = false;
                            for (SysMethodAnalyte methodAnalyte : sysMethodAnalytes) {
                                String calcExpr = methodAnalyte.getCalcExpr();
                                if (calcExpr != null && calcExpr.contains("{" + currentAnalyteName + "}")) {
                                    found = true;
                                    break;
                                }
                            }
                            // 如果找到了包含当前分析物名称的计算表达式，则插入测试结果
                            if (found) {
                                testResult.setReportable("是");
                                testResultMapper.insert(testResult);
                            }
                        }
                    }
                }else{
                    testResult.setReportable("是");
                    testResultMapper.insert(testResult);
                }
            }
        });
    }

    @Override
    @Transactional
    public IPage<SampleQuery> sampleQuery(Page<SampleQuery> page, QueryWrapper<SampleQuery> queryWrapper) {
        return testTaskMapper.sampleQuery(page, queryWrapper);
    }

    @Override
    public ModelAndView sampleQueryExportXls(HttpServletRequest request, SampleQuery sampleQuery, Class<SampleQuery> sampleQueryClass, String title) {
        String dateStart = "";
        String dateEnd = "";
        if(request.getParameterMap().get("receiveDate") != null){
            dateStart = request.getParameterMap().get("receiveDate")[0].toString().split(",")[0];
            dateEnd = request.getParameterMap().get("receiveDate")[0].toString().split(",")[1];
        }
        Map<String, String[]> paramMap = new HashMap<>(request.getParameterMap());
        paramMap.remove("receiveDate");
        sampleQuery.setReceiveDate(null);
        QueryWrapper<SampleQuery> queryWrapper = QueryGenerator.initQueryWrapper(sampleQuery, paramMap);
        if(oConvertUtils.isNotEmpty(dateStart) && oConvertUtils.isNotEmpty(dateEnd)){
            queryWrapper.between("receive_date", dateStart, dateEnd);
        }

        String selections = request.getParameter("selections");
        String exportFields = request.getParameter("exportFields[value]");

        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id",selectionList);
        }


        Page<SampleQuery> page = new Page<>(1, 200000);
        IPage<SampleQuery> pageList = this.sampleQuery(page, queryWrapper);

        List<SampleQuery> exportList = pageList.getRecords();

        LoginUser sysUser = SecureUtil.currentUser();

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());

        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, title);
        mv.addObject(NormalExcelConstants.CLASS, sampleQueryClass);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams exportParams=new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
        exportParams.setImageBasePath(jeecgBaseConfig.getPath().getUpload());
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS,exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        if(oConvertUtils.isNotEmpty(exportFields)) {
            String camelCase = StrUtil.toCamelCase(exportFields);
            mv.addObject(NormalExcelConstants.EXPORT_FIELDS, camelCase);

        }
        return mv;
    }

    @Override
    @Transactional
    public IPage<TaskVo> pageVo(Page<TaskVo> page, QueryWrapper<TaskVo> queryWrapper) {
        IPage<TaskVo> voIPage  = testTaskMapper.queryPageList1(page, queryWrapper);
        //循环每个taskvo ,根据，样品名称，同检测能力，操作人，筛选出历史操作人赋值给 historyOperator
        List<TaskVo> taskVoList = voIPage.getRecords();
        for (TaskVo taskVo : taskVoList) {

            SysCapability ca = sysCapabilityServiceImpl.getById(taskVo.getCapabilityId());
            if (ca != null) {
                taskVo.setIsBiao(ca.getIsBiao());

            }

            if (taskVo.getReceiveDate() != null){
                Sample s = sampleMapper.selectById(taskVo.getSampleId());
                //Quotation q = iQuotationService.getById(s.getQuotationId());
                if(s.getDayType() != null && s.getDayType().equals("工作日")) {
                    Date workDate = iHolidayCalendarService.getWorkDate(s.getReceiveDate(), s.getPmLeadTime());
                    taskVo.setDeadLineDate(workDate);
                }else{
                    Integer pmLeadTime = s.getPmLeadTime() * 24;
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(taskVo.getReceiveDate());
                    calendar.add(Calendar.HOUR, pmLeadTime);
                    taskVo.setDeadLineDate(calendar.getTime());
                }

            }

            //研发项目名称
            if (taskVo.getRdId() != null) {
                RdProject rd = iRdProjectService.getById(taskVo.getRdId());
                if (rd != null) {
                    taskVo.setRdName(rd.getName());
                }
            }



            //(case when d.id then d.id else d1.id end) dept_id,
            // (case when d.depart_name then d.depart_name else d1.depart_name end) depart_name,
            // m.cid as cid

            if(taskVo.getMethodId() != null){
                SysMethod sysMethod = sysMethodMapper.selectById(taskVo.getMethodId());
                if(sysMethod != null){
                    //taskVo.setCapabilityId(sysMethod.getCid());
                }
//                SysStandard sysStandard = sysStandardMapper.selectById(sysMethod.getStandardId());
//                if (sysStandard != null && sysMethod.getName() != null) {
//                    if (sysMethod.getName().equals("/")) {
//                        taskVo.setStandardMethodName(sysStandard.getName());
//                    }else
//                        taskVo.setStandardMethodName(sysMethod.getName()  + "：" +sysStandard.getName());
//                }else {
//                    taskVo.setStandardMethodName(sysMethod.getName());
//                }
            }
            if(taskVo.getDeptId() != null){
                SysDepart sysDept = iSysDepartService.getById(taskVo.getDeptId());
                if(sysDept != null){
                    taskVo.setDepartName(sysDept.getDepartName());
                }
            }


            if (taskVo.getCapabilityId() == null || taskVo.getSampleName() == null) {
                taskVo.setHistoryOperator("");
                continue;
            }
            List<String> historyOperators = testTaskMapper.selectHistoryOperators(
                    taskVo.getSampleName(),
                    taskVo.getCapabilityId()
            );
//            Map<String, Long> operatorFrequency = historyOperators.stream()
//                    .filter(operator -> !operator.isEmpty())
//                    .collect(Collectors.groupingBy(
//                            Function.identity(),
//                            Collectors.counting()
//                    ));
//            String mostFrequentOperator = operatorFrequency.entrySet().stream()
//                    .max(Map.Entry.comparingByValue())
//                    .map(Map.Entry::getKey)
//                    .orElse("");
            String historyOperator = String.join(",", new LinkedHashSet<>(historyOperators));
            taskVo.setHistoryOperator(historyOperator);

        }
        return voIPage;
    }

    @Override
    @Transactional
    public IPage<TaskVo> myTasklist(Page<TaskVo> page , QueryWrapper<TaskVo> queryWrapper){
        return testTaskMapper.myTasklist(page, queryWrapper);
    }

    @Override
    public List<TaskVo> selectByMainId(String Id) {
        return testTaskMapper.selectByMainId(Id);
    }

    @Override
    public List<TaskVo> selectByConsumptiveName(String Name) {
        return testTaskMapper.selectByConsumptiveName(Name);
    }
    @Override
    public List<TaskVo> selectByStandardMaterialName(String Name) {
        return testTaskMapper.selectByStandardMaterialName(Name);
    }

    @Override
    public void changeMethod(TestTask testTask) {
        List<SysMethodRepeatType> sysMethodRepeatTypes = sysMethodRepeatTypeMapper.selectByMainId(testTask.getMethodId());
        if(sysMethodRepeatTypes.size() > 0) {
            testTask.setRepeatTypeId(sysMethodRepeatTypes.get(0).getId());
            testTask.setRepeatTypeNextId(sysMethodRepeatTypes.get(0).getNextId());
        }else{
            testTask.setRepeatTypeId("");
            testTask.setRepeatTypeNextId("");
        }
        updateById(testTask);
        testMapper.delete(new QueryWrapper<Test>().eq("task_id", testTask.getId()));
        generateTest(testTask,null);
    }

    @Override
    public void changesubtractor(TestTask testTask) {
        TestTask tt = testTaskMapper.selectById(testTask.getId());
        tt.setSubtractorId(testTask.getSubtractorId());
        updateById(tt);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assign(String id, String userName, String type,String experimentNotes) {
        LoginUser sysUser = SecureUtil.currentUser();
        String[] ids = id.split(",");
        for (String taskId : ids) {
            TestTask testTask = testTaskMapper.selectById(taskId.trim());
            if (testTask == null) {
                continue;
            }

            if(type.equals("tester")){ //指派检验人
                if(!experimentNotes.equals("")){
                    testTask.setExperimentNotes(experimentNotes);
                }
                testTask.setAssignee(userName);
                testTask.setAssigner(sysUser.getUsername());
                testTask.setAssignTime(new Date());
                if(testTask.getStatus().equals(TASK_STATUS_0)){
                    testTask.setStatus(TASK_STATUS_1);
                }

                if(FLOW_STEP_3.equals(testTask.getCurStep())) {
                    testTask.setCurStep(FLOW_STEP_4);

                    TestTaskFlow testTaskFlow = new TestTaskFlow();
                    testTaskFlow.setTaskId(testTask.getId());
                    testTaskFlow.setStepId(FLOW_STEP_4);
                    testTaskFlowMapper.insert(testTaskFlow);
                }
                updateById(testTask);

            }else if(type.equals("checker")){
                testTask.setChecker(userName);
                updateById(testTask);
            }else if(type.equals("cooper")){
                testTask.setCooper(userName);
                updateById(testTask);
            }
        }
    }
    public String  getServiceType(String id){
        Sample  sample = sampleMapper.selectById(id);
        Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("service_type");
        for (SysDictItem item : sysDictItems) {
            if (q.getServiceTypeId().equals(item.getItemValue())) {
                return item.getItemText();
            }
        }
        return "";
    };
    public  String getIsSupplementary(String id){
        Sample  sample = sampleMapper.selectById(id);
        Quotation q = quotationServiceImpl.getById(sample.getQuotationId());
        List<SysDictItem> sysDictItems = sysDictItemService.selectItemsByDictCode("logic_code");
        for (SysDictItem item : sysDictItems) {
            if (q.getIsSupplementary().equals(item.getItemValue())) {
                return item.getItemText();
            }
        }
        return "";
    }

    @Override
    public void revert(String id, String step, String reason) {
        TestTask task = this.getById(id);
        Integer curNum = iSysWorkflowStepService.getSortNumByName(task.getCurStep());
        Integer backNum = iSysWorkflowStepService.getSortNumByName(step);
        if(curNum <= backNum){
            throw new RuntimeException("不能回退到当前节点或之前的节点");
        }
        if(step.equals("结果录入") || step.equals("结果复核")){
            task.setStatus(TASK_STATUS_1);
            testMapper.selectByTaskId(task.getId()).forEach(
                test -> {
                    test.setIsSubmitted(0);
                    test.setCheckedTime(null);
                    test.setCheckedBy(null);
                    testMapper.updateById(test);
                }
            );
        }
        if(!step.equals("报告编制") && !step.equals("报告审核")&& !step.equals("报告签发")) {
            iReportService.list(new QueryWrapper<Report>().eq("sample_id", task.getSampleId())).forEach(
                    report -> {
                        report.setUrl(null);
                        report.setFinalUrl(null);
                        report.setSignBy(null);
                        report.setSignTime(null);
                        report.setApproveBy(null);
                        report.setApproveTime(null);
                        iReportService.updateById(report);
                    }
            );
        }
        if(step.equals("报告审核")){
            iReportService.list(new QueryWrapper<Report>().eq("sample_id", task.getSampleId())).forEach(
                    report -> {
                        report.setFinalUrl(null);
                        report.setSignBy(null);
                        report.setSignTime(null);
                        iReportService.updateById(report);
                    }
            );
        }



        task.setCurStep(step);
        this.updateById(task);

        TestTaskFlow testTaskFlow = new TestTaskFlow();
        testTaskFlow.setTaskId(task.getId());
        testTaskFlow.setStepId(step);
        testTaskFlow.setRevertReason(reason);
        testTaskFlow.setIsRevert("Y");
        testTaskFlowMapper.insert(testTaskFlow);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(String id, Boolean isresum) {
        TestTask task = this.getById(id);
        if (isresum) {
            if(task.getTestControlStatus().equals("正常")){
                throw new RuntimeException("任务未取消，不能取消");
            }
            task.setTestControlStatus("正常");
            cancelNotify(task,isresum);
            this.updateById(task);
            Sample sample = sampleMapper.selectById(task.getSampleId());
            //如果全部都是正常,就sample.setTestControlStatus("正常");
            List<TestTask> list = this.list(new QueryWrapper<TestTask>().eq("sample_id", sample.getId()).eq("test_control_status", "取消"));
            if(list.size() == 0){
                sample.setTestControlStatus("正常");
                sampleMapper.updateById(sample);
            }


        } else {
            if(task.getTestControlStatus().equals("取消")){
                throw new RuntimeException("任务已取消，不能重复取消");
            }
            task.setTestControlStatus("取消");
            this.updateById(task);
            cancelNotify(task,isresum);
            Sample sample = sampleMapper.selectById(task.getSampleId());
            sample.setTestControlStatus("部分取消");
            sampleMapper.updateById(sample);
            List<TestTask> list = this.list(new QueryWrapper<TestTask>().eq("sample_id", sample.getId()).ne("test_control_status", "取消"));
            if(list.size() == 0){
                sample.setTestControlStatus("取消");
                sampleMapper.updateById(sample);
            }
        }
    }

    public void cancelNotify(TestTask task,boolean isresum){
        Set<String> users = new HashSet<>();
        if(task.getAssignee() != null && !task.getAssignee().isEmpty()){
            users.add(task.getAssignee());
        }
        if(task.getChecker() != null && !task.getChecker().isEmpty()){
            users.add(task.getChecker());
        }
        if(task.getCooper() != null && !task.getCooper().isEmpty()){
            users.add(task.getCooper());
        }
        if(task.getAssigner() != null && !task.getAssigner().isEmpty()){
            users.add(task.getAssigner());
        }
        users.add(task.getCreateBy());


        testTaskFlowMapper.selectByTaskId(task.getId()).forEach(item->{
            users.add(item.getCreateBy());
                });

        List<TaskVo> taskVos  = testTaskMapper.queryPageList1(new Page<>(1,20), new QueryWrapper<TaskVo>().eq("id",task.getId())).getRecords();
        if(taskVos.size() == 0){
            throw new RuntimeException("小伙子,别乱调用!");
        }
        LoginUser sysUser = SecureUtil.currentUser();
        users.add(sysUser.getUsername());
        TaskVo taskVo = taskVos.get(0);
        MessageDTO message = new MessageDTO(
                sysUser.getUsername(),
                String.join(",", users),
                isresum ? "项目恢复通知" :"项目取消通知",
                ""
        );
        message.setToAll(false);
        message.setIsMarkdown(true);
        //客户，任务名称、检品名称，编号，超期时间，下单时间，pm备注、指派备注

        message.setContent(String.format(
                (isresum ? "**项目恢复通知**\n\n" : "**项目取消通知**\n\n") +
                        "客户：%s\n" +
                        "检品名称：%s\n" +
                        "检品编号：%s\n" +
                        "任务名称：%s\n" +
                        "下单时间：%s\n" +
                        "超期时间：%s\n" +
                        "任务负责人：%s\n" +
                        "操作人：%s\n\n" +
                        "请知晓。\n\n" +
                        "> **注意**: 如有问题，请及时联系操作人。",
                taskVo.getCustomerName(),//客户名称
                taskVo.getSampleName(),
                taskVo.getSampleNo(),
                taskVo.getName(), // 任务名称
                taskVo.getReceiveDate() != null ? new SimpleDateFormat("yyyy-MM-dd").format(taskVo.getReceiveDate()) : null,
                taskVo.getDeadLineDate() != null ? new SimpleDateFormat("yyyy-MM-dd").format(taskVo.getDeadLineDate()) : null,
                taskVo.getAssignee(), // 任务负责人
                sysUser.getRealname() // 催办人
        ));
        iSysBaseAPI.sendSysAnnouncement(message);
    }

    @Override
    public ModelAndView exportXlsVo(HttpServletRequest request, TaskVo taskVo, Class<TaskVo> taskVoClass, String title) {
        // Step.1 组装查询条件
        QueryWrapper<TaskVo> queryWrapper = QueryGenerator.initQueryWrapper(taskVo, request.getParameterMap());
        LoginUser sysUser = SecureUtil.currentUser();

        // 过滤选中数据
        String selections = request.getParameter("selections");
        String exportFields = request.getParameter("exportFields[value]");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id",selectionList);
        }

        // Step.2 获取导出数据
        Page<TaskVo> page = new Page<>(1, 9999);
        IPage<TaskVo> voIPage  = pageVo(page, queryWrapper);
        List<TaskVo> exportList = voIPage.getRecords();

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());

        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, title);
        mv.addObject(NormalExcelConstants.CLASS, taskVoClass);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams exportParams=new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
        exportParams.setImageBasePath(jeecgBaseConfig.getPath().getUpload());
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS,exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        if(oConvertUtils.isNotEmpty(exportFields)) {
            String camelCase = StrUtil.toCamelCase(exportFields);
            mv.addObject(NormalExcelConstants.EXPORT_FIELDS, camelCase);

        }
        return mv;
    }

    @Override
    public void cuidan(String[] ids) {
        Object[] taskids = testMapper.selectList(new QueryWrapper<Test>().in("id", ids)).stream().map(Test::getTaskId).toArray();
        IPage<TaskVo> voIPage  = testTaskMapper.queryPageList1(new Page<>(1,20), new QueryWrapper<TaskVo>().in("id",taskids));
        LoginUser sysUser = SecureUtil.currentUser();

        voIPage.getRecords().forEach(taskVo -> {
            MessageDTO message = new MessageDTO(
                    sysUser.getUsername(),
                    taskVo.getAssignee(),
                    "催办通知",
                    ""
            );
            message.setToAll(false);
            message.setIsMarkdown(true);
            //客户，任务名称、检品名称，编号，超期时间，下单时间，pm备注、指派备注

            message.setContent(String.format(
                    "**催办通知**\n\n" +
                            "客户：%s\n" +
                            "检品名称：%s\n" +
                            "检品编号：%s\n" +
                            "任务名称：%s\n" +
                            "下单时间：%s\n" +
                            "超期时间：%s\n" +
                            "任务负责人：%s\n" +
                            "催办人：%s\n\n" +
                            "请尽快处理相关任务。\n\n" +
                            "> **注意**: 如有问题，请及时联系催办人。",
                    taskVo.getCustomerName(),//客户名称
                    taskVo.getSampleName(),
                    taskVo.getSampleNo(),
                    taskVo.getName(), // 任务名称
                    taskVo.getReceiveDate() != null ? new SimpleDateFormat("yyyy-MM-dd").format(taskVo.getReceiveDate()) : null,
                    taskVo.getDeadLineDate() != null ? new SimpleDateFormat("yyyy-MM-dd").format(taskVo.getDeadLineDate()) : null,
                    taskVo.getAssignee(), // 任务负责人
                    sysUser.getRealname() // 催办人
            ));
            iSysBaseAPI.sendSysAnnouncement(message);
        });

    }

    ;
}