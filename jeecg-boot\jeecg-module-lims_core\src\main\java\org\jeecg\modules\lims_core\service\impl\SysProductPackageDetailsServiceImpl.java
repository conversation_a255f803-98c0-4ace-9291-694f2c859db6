package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysProductPackageDetails;
import org.jeecg.modules.lims_core.mapper.SysProductPackageDetailsMapper;
import org.jeecg.modules.lims_core.service.ISysProductPackageDetailsService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 套餐详情表
 * @Author: jeecg-boot
 * @Date:   2025-04-08
 * @Version: V1.0
 */
@Service
public class SysProductPackageDetailsServiceImpl extends ServiceImpl<SysProductPackageDetailsMapper, SysProductPackageDetails> implements ISysProductPackageDetailsService {
	
	@Autowired
	private SysProductPackageDetailsMapper sysProductPackageDetailsMapper;
	
	@Override
	public List<SysProductPackageDetails> selectByMainId(String mainId) {
		return sysProductPackageDetailsMapper.selectByMainId(mainId);
	}
}
