package org.jeecg.modules.dcs.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/17 14:47
 */

public class JsonFileReader {

    /**
     * 将 JSON 文件内容读取为 JSONObject
     * @param fileName 文件名称（相对路径）
     * @return JSONObject 对象
     */
    public static JSONObject readJsonFileAsJSONObject(String fileName) {
        try {
            // 定位资源文件
            ClassPathResource resource = new ClassPathResource(fileName);
            InputStream inputStream = resource.getInputStream();

            // 读取文件内容为字符串
            String jsonString = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                    .lines()
                    .collect(Collectors.joining("\n"));

            // 将字符串解析为 JSONObject
            return  JSON.parseObject(jsonString);
        } catch (Exception e) {
            throw new RuntimeException("读取 JSON 文件失败: " + fileName, e);
        }
    }
}
