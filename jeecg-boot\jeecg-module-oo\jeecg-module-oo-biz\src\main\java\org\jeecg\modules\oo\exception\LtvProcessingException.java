package org.jeecg.modules.oo.exception;

/**
 * LTV处理相关异常
 */
public class LtvProcessingException extends Exception {
    
    private final ErrorCode errorCode;
    
    public LtvProcessingException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public LtvProcessingException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public ErrorCode getErrorCode() {
        return errorCode;
    }
    
    /**
     * LTV错误代码枚举
     */
    public enum ErrorCode {
        OCSP_RESPONSE_INVALID("OCSP响应格式无效"),
        OCSP_SERVER_UNREACHABLE("OCSP服务器无法访问"),
        CRL_DOWNLOAD_FAILED("CRL下载失败"),
        CERTIFICATE_CHAIN_INVALID("证书链无效"),
        TIMESTAMP_VERIFICATION_FAILED("时间戳验证失败"),
        DSS_CONSTRUCTION_FAILED("DSS字典构建失败"),
        VRI_CREATION_FAILED("VRI条目创建失败");
        
        private final String description;
        
        ErrorCode(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}