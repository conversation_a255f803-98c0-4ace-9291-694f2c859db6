package org.jeecg.modules.dcs.mapper;

import java.util.List;
import org.jeecg.modules.dcs.entity.TrainingRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 培训记录
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
public interface TrainingRecordMapper extends BaseMapper<TrainingRecord> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<TrainingRecord>
   */
	public List<TrainingRecord> selectByMainId(@Param("mainId") String mainId);
}
