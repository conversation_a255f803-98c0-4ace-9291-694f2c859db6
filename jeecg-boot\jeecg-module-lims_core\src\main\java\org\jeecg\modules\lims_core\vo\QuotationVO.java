package org.jeecg.modules.lims_core.vo;

import cn.hutool.core.date.DateTime;
import lombok.Data;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;

@Data
@TemplateDesigner(value = "Quotation",drillDown = "sample.quotation_id",description = "报价信息")
public class QuotationVO {
    @TemplateField(description = "id")
    private String id;

      @TemplateField(entityFieldName = "id",drillChain = "quotation.id->quotation.customer_id->sys_customer.id->sys_customer.name",description = "客户名称")
    private String customerName;

    @TemplateField(entityFieldName = "id",drillChain = "quotation.id->quotation.customer_contact_id->sys_customer_contact.id->sys_customer_contact.name",description = "客户联系人名称")
    private String customerContactName;

    @TemplateField(entityFieldName = "id",drillChain = "quotation.id->quotation.customer_contact_id->sys_customer_contact.id->sys_customer_contact.phone",description = "客户联系人电话")
    private String customerContactPhone;

    @TemplateField(entityFieldName = "id",drillChain = "quotation.id->quotation.customer_contact_id->sys_customer_contact.id->sys_customer_contact.email",description = "客户联系人邮箱")
    private String customerContactEmail;

    @TemplateField(description = "报价单名称")
    private String name;

    @TemplateField(description = "报价单编号")
    private String quotationNo;

    @TemplateField(description = "报价单日期")
    private DateTime createTime;

    @TemplateField(description = "报价单价格")
    private String pmPrice;

    @TemplateField(description = "销售价格")
    private String applyPrice;

    @TemplateField(description = "报价单折扣")
    private String discount;

//    //样品名称
//    @TemplateField(entityFieldName = "id",drillChain = "quotation.id->sample.quotation_id->sample.id->sample.name",description = "样品名称")
//    private String sampleName;
//
//    //检测项目
//    @TemplateField(entityFieldName = "id",drillChain = "quotation.id->sample.quotation_id->sample.id->test_task.sample_id->test_task.method_id->sys_method.id->sys_method_analyte.id->sys_method_analyte.analyte_id->sys_analyte.id->sys_analyte.code",description = "检测项目")
//    private String analyteName;
//
//    //检测方法
//    @TemplateField(entityFieldName = "id",drillChain = "quotation.id->sample.quotation_id->sample.id->test_task.sample_id->test_task.method_id->sys_method.id->sys_method.name",description = "检测方法")
//    private String methodName;

    //单批报价,按样品和检测方法分组


}