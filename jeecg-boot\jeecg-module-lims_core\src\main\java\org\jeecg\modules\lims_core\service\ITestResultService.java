package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.entity.TestResult;

import java.util.List;

/**
 * @Description: 检测结果
 * @Author: jeecg-boot
 * @Date:   2025-02-08
 * @Version: V1.0
 */
public interface ITestResultService extends IService<TestResult> {
    public List<TestResult> selectByTestId(String testId);

    String getAnalyteName(String id);
    String getRepResult(String id);
    String getConclusion(String id);
}
