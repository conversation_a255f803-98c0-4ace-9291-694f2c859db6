package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.SampleMapper;
import org.jeecg.modules.lims_core.mapper.TestMapper;
import org.jeecg.modules.lims_core.mapper.TestTaskMapper;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.vo.ReportRequestVO;
import org.jeecg.modules.lims_core.vo.TaskVo;
import java.util.List;
import java.util.stream.Collectors;

import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.jeecg.modules.lims_core.mapper.ReportMapper;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 报告
 * @Author: jeecg-boot
 * @Date:   2025-03-03
 * @Version: V1.0
 */
@Service
public class ReportServiceImpl extends ServiceImpl<ReportMapper, Report> implements IReportService {
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SampleMapper sampleMapper;
    @Autowired
    private TestTaskMapper testTaskMapper;
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private ITestResultService testResultService;
    @Autowired
    private ISysUserService iSysUserService;
    @Autowired
    private ISysTemplateService sysTemplateService;

    @Override
    public IPage<ReportRequestVO> queryPageList(Page<Report> page, Wrapper<ReportRequestVO> wrapper) {
        IPage<ReportRequestVO> reportRequestVOIPage = reportMapper.queryPageList(page, wrapper);
        reportRequestVOIPage.getRecords().forEach(reportRequestVO -> {
            List<SysUser> users = iSysUserService.list(new QueryWrapper<SysUser>().eq("realname", reportRequestVO.getSalerId()));
            if(users != null && !users.isEmpty()) {
                SysUser user = users.get(0);
                reportRequestVO.setSalerEmail(user.getEmail());
            }

        });
        return  reportRequestVOIPage;
    }
    @Override
    public List<TaskVo> selectByMainId(String mainId) {
        return reportMapper.selectByMainId(mainId);
    }


    public  String getContactNameAndPhone(String id){
        String str = "";
        List<TaskVo>  list= baseMapper.getContactNameAndPhone(id);
        if (list != null && !list.isEmpty()) {
            for (TaskVo task : list) {
                str += task.getName()+" "+ task.getPhone();
                if (task.getTranslation() != null && !task.getTranslation().isEmpty()) {
                    str += "\n" + task.getTranslation()+" " + task.getPhone();
                }
            }
        }
        return str;
    }
    public String getReportNo(String id){
        Report report = this.getById(id);
        if (report == null) {
            return null;
        }
        String version = report.getVersion();
        if (version != null && !version.trim().isEmpty()) {
            return report.getReportNo() + "-" + version;
        }
        return report.getReportNo();
    }

    public String getDetectionBasis(String id){
      //  Report r = this.getById(id);
        String evaluation = "";
        List<TaskVo>  list=reportMapper.getevaluation(id);
        if (list != null && !list.isEmpty()) {
            String names = list.stream()
                    .filter(taskVo -> taskVo.getName() != null)
                    .map(TaskVo::getName)
                    .distinct()
                    .collect(Collectors.joining(","));
            String translation = list.stream()
                    .filter(taskVo -> taskVo.getTranslation() != null)
                    .map(TaskVo::getTranslation)
                    .distinct()
                    .collect(Collectors.joining(","));
            evaluation =translation.isEmpty() ? names : names + " " + translation;
        }
        return evaluation;
    }

    public String getInspectionItems(String id){
        String analytes = "";
        String analytesTranslation = "";
        Report report = this.getById(id);
        Sample sample = sampleMapper.selectById(report.getSampleId());
        List<TestTask> testTasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", sample.getId()));
        if(testTasks!=null && !testTasks.isEmpty()){
            List<String> testTaskIds = testTasks.stream().map(TestTask::getId).toList();
            List<Test> tests = testMapper.selectList(new QueryWrapper<Test>().in("task_id", testTaskIds));
            if(tests!=null && !tests.isEmpty()){
                List<String> testIds = tests.stream().map(Test::getId).toList();
                List<TestResult>  testResults = testResultService.list(new QueryWrapper<TestResult>().in("test_id", testIds));
                if (testResults != null && !testResults.isEmpty()) {
                    analytes = testResults.stream()
                            .map(testResult -> {
                                return testResultService.getAnalyteName(testResult.getId());
                            })
                            .distinct()
                            .collect(Collectors.joining(","));

                }
            }
        }
        return analytes + analytesTranslation;
    }
}
