package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import org.jeecg.modules.lims_core.entity.StandardMaterial;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.StandardMaterialVO;

/**
 * @Description: 标准物质台账
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
public interface IStandardMaterialService extends IService<StandardMaterial> {
    public IPage<StandardMaterialVO> queryPageList(Page<StandardMaterial> page,
                                                   @Param(Constants.WRAPPER) Wrapper<StandardMaterial> wrapper);

}
