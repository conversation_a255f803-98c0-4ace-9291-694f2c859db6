package org.jeecg.modules.oo.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.io.ByteArrayInputStream;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * OCSP工具类测试
 */
public class OcspUtilTest {
    
    private X509Certificate testCertificate;
    private X509Certificate issuerCertificate;
    
    @BeforeEach
    void setUp() {
        // 这里应该加载实际的测试证书
        // 为了演示，使用模拟证书
        // 在实际测试中，应该使用真实的证书文件
    }
    
    @Test
    @DisplayName("测试OCSP URL提取")
    void testGetOcspUrlFromCertificate() {
        // 如果有测试证书，可以测试OCSP URL提取
        if (testCertificate != null) {
            String ocspUrl = OcspUtil.getOcspUrlFromCertificate(testCertificate);
            assertNotNull(ocspUrl, "应该能够从证书中提取OCSP URL");
            assertTrue(ocspUrl.startsWith("http"), "OCSP URL应该是HTTP协议");
        }
    }
    
    @Test
    @DisplayName("测试备用OCSP服务器获取")
    void testGetBackupOcspUrls() {
        // 创建一个模拟的GDCA证书主体
        if (testCertificate != null) {
            String[] backupUrls = OcspUtil.getBackupOcspUrls(testCertificate);
            assertNotNull(backupUrls, "备用OCSP URL数组不应为null");
            assertTrue(backupUrls.length > 0, "应该有至少一个备用OCSP服务器");
        }
    }
    
    @Test
    @DisplayName("测试OCSP响应验证")
    void testIsValidOcspResponse() {
        // 测试无效响应
        byte[] invalidResponse = "invalid response".getBytes();
        assertFalse(OcspUtil.isValidOcspResponse(invalidResponse), "无效响应应该返回false");
        
        // 测试空响应
        assertFalse(OcspUtil.isValidOcspResponse(null), "null响应应该返回false");
        assertFalse(OcspUtil.isValidOcspResponse(new byte[0]), "空响应应该返回false");
    }
    
    @Test
    @DisplayName("测试模拟OCSP响应创建")
    void testCreateMockOcspResponse() {
        if (testCertificate != null && issuerCertificate != null) {
            byte[] mockResponse = OcspUtil.createMockOcspResponse(testCertificate, issuerCertificate);
            assertNotNull(mockResponse, "模拟OCSP响应不应为null");
            assertTrue(mockResponse.length > 0, "模拟OCSP响应应该有内容");
        }
    }
    
    @Test
    @DisplayName("测试OCSP连接诊断")
    void testOcspConnectivityDiagnostic() {
        // 测试快速连接测试
        // 注意：这个测试需要网络连接
        try {
            boolean result = OcspDiagnosticTool.quickOcspConnectivityTest();
            // 不强制要求成功，因为网络环境可能不同
            System.out.println("OCSP连接测试结果: " + result);
        } catch (Exception e) {
            System.err.println("OCSP连接测试异常: " + e.getMessage());
        }
    }
    
    /**
     * 创建测试证书的辅助方法
     * 在实际使用中，应该加载真实的证书文件
     */
    private X509Certificate createTestCertificate(String certData) {
        try {
            byte[] certBytes = Base64.getDecoder().decode(certData);
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            return (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(certBytes));
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 集成测试：完整的OCSP处理流程
     */
    @Test
    @DisplayName("集成测试：完整OCSP处理流程")
    void testCompleteOcspFlow() {
        if (testCertificate != null && issuerCertificate != null) {
            System.out.println("开始完整OCSP处理流程测试...");
            
            // 1. 提取OCSP URL
            String ocspUrl = OcspUtil.getOcspUrlFromCertificate(testCertificate);
            System.out.println("OCSP URL: " + ocspUrl);
            
            // 2. 获取备用服务器
            String[] backupUrls = OcspUtil.getBackupOcspUrls(testCertificate);
            System.out.println("备用服务器数量: " + backupUrls.length);
            
            // 3. 尝试获取OCSP响应
            byte[] response = OcspUtil.getOcspResponse(testCertificate, issuerCertificate);
            if (response != null) {
                System.out.println("OCSP响应获取成功，大小: " + response.length + " bytes");
                
                // 4. 验证响应
                boolean isValid = OcspUtil.isValidOcspResponse(response);
                System.out.println("OCSP响应验证结果: " + isValid);
            } else {
                System.out.println("OCSP响应获取失败，将使用模拟响应");
                
                // 5. 创建模拟响应
                byte[] mockResponse = OcspUtil.createMockOcspResponse(testCertificate, issuerCertificate);
                assertNotNull(mockResponse, "模拟响应创建应该成功");
                System.out.println("模拟响应创建成功，大小: " + mockResponse.length + " bytes");
            }
        } else {
            System.out.println("跳过集成测试：缺少测试证书");
        }
    }
    
    /**
     * 性能测试：OCSP响应获取性能
     */
    @Test
    @DisplayName("性能测试：OCSP响应获取")
    void testOcspPerformance() {
        if (testCertificate != null && issuerCertificate != null) {
            long startTime = System.currentTimeMillis();
            
            // 执行多次OCSP请求
            int iterations = 5;
            int successCount = 0;
            
            for (int i = 0; i < iterations; i++) {
                byte[] response = OcspUtil.getOcspResponse(testCertificate, issuerCertificate);
                if (response != null && response.length > 0) {
                    successCount++;
                }
            }
            
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;
            
            System.out.println("性能测试结果:");
            System.out.println("  总请求数: " + iterations);
            System.out.println("  成功请求数: " + successCount);
            System.out.println("  总耗时: " + totalTime + "ms");
            System.out.println("  平均耗时: " + (totalTime / iterations) + "ms");
            System.out.println("  成功率: " + (successCount * 100.0 / iterations) + "%");
        }
    }
}