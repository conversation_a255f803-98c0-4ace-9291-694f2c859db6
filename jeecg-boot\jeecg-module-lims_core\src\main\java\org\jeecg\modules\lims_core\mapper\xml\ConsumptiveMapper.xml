<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.ConsumptiveMapper">

    <select id="queryPageList" resultType="org.jeecg.modules.lims_core.vo.ConsumptiveVO">
        select * from ( select c.*,i.amount as inventoryquantity  from  consumptive c left join  inventory i on c.CODE=i.article_no ) t
            ${ew.customSqlSegment}
    </select>
</mapper>