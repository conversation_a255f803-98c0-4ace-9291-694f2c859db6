package org.jeecg.modules.lims_core.mapper;

import java.util.List;
import org.jeecg.modules.lims_core.entity.SysMethodStdMaterial;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 标品
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface SysMethodStdMaterialMapper extends BaseMapper<SysMethodStdMaterial> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<SysMethodStdMaterial>
   */
	public List<SysMethodStdMaterial> selectByMainId(@Param("mainId") String mainId);
}
