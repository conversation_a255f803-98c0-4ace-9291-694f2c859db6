package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodInstrumentType;
import org.jeecg.modules.lims_core.mapper.SysMethodInstrumentTypeMapper;
import org.jeecg.modules.lims_core.service.ISysMethodInstrumentTypeService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 设备类别
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodInstrumentTypeServiceImpl extends ServiceImpl<SysMethodInstrumentTypeMapper, SysMethodInstrumentType> implements ISysMethodInstrumentTypeService {
	
	@Autowired
	private SysMethodInstrumentTypeMapper sysMethodInstrumentTypeMapper;
	
	@Override
	public List<SysMethodInstrumentType> selectByMainId(String mainId) {
		return sysMethodInstrumentTypeMapper.selectByMainId(mainId);
	}
}
