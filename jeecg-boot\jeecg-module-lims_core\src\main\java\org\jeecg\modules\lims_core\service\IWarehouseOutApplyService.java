package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.modules.lims_core.entity.WarehouseOutApply;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.WarehouseOutApplyVo;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;

import java.util.List;

/**
 * @Description: 出库申请
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
public interface IWarehouseOutApplyService extends IService<WarehouseOutApply> {

    void apply(WarehouseOutApply obj, ApplyType applyType) throws WxErrorException;
    /**
     * 通过货物编码查询所在货位 code
     * @param code
     * @return 货物代码 warehousebox
     */
    String getwarehouseboxBycode(String code);
    String getwarehouseboxidBycode(String code);

    public IPage<WarehouseOutApplyVo> queryPageList(Page<WarehouseOutApplyVo> page,
                                              @Param(Constants.WRAPPER) Wrapper<WarehouseOutApplyVo> wrapper);
}
