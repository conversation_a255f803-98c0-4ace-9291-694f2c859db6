package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import org.jeecg.modules.lims_core.mapper.SysUnitConversionMapper;
import org.jeecg.modules.lims_core.service.ISysUnitConversionService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 单位转换
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Service
public class SysUnitConversionServiceImpl extends ServiceImpl<SysUnitConversionMapper, SysUnitConversion> implements ISysUnitConversionService {
	
	@Autowired
	private SysUnitConversionMapper sysUnitConversionMapper;
	
	@Override
	public List<SysUnitConversion> selectByMainId(String mainId) {
		return sysUnitConversionMapper.selectByMainId(mainId);
	}
}
