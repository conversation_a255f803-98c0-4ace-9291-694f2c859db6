# LTV（长期验证）配置
pdf:
  ltv:
    # 时间戳证书识别关键词
    tsa-keywords:
      - timestamp
      - tsa
      - time-stamp
      - timestamping
      - 时间戳
      - 时戳
      - tss
      - timeserver
      - chronos
      - gdca
    
    # Adobe扩展配置
    adobe-extension:
      name: ADBE
      base-version: "1.7"
      extension-level: 8
    
    # 时间戳配置
    timestamp-config:
      format: "yyyy-MM-dd'T'HH:mm:ss'Z'"
      timezone: UTC
      time-stamping-oid: "1.3.6.1.5.5.7.3.8"
    
    # OCSP配置
    ocsp-config:
      max-retries: 3
      timeout-ms: 10000
      enable-caching: true
      cache-expiration-minutes: 60

# 缓存配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=1h
    cache-names:
      - ocspResponses

# 日志配置
logging:
  level:
    org.jeecg.modules.oo.service.impl.SignServiceImpl: DEBUG
    LTV_PROCESSING: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{signatureId}] [%X{certificateCN}] [%X{operation}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{signatureId}] [%X{certificateCN}] [%X{operation}] %logger{36} - %msg%n"

# 性能监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: jeecg-oo
      module: ltv-processing