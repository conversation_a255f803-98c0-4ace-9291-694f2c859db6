//package org.jeecg.modules.oo.util;
//
//import net.qiyuesuo.sdk.SDKClient;
//import net.qiyuesuo.sdk.bean.contract.RelativePosition;
//import net.qiyuesuo.sdk.bean.contract.SealMultipleRequest;
//import net.qiyuesuo.sdk.bean.contract.StamperType;
//import net.qiyuesuo.sdk.bean.sign.local.KeywordMatchRule;
//import net.qiyuesuo.sdk.bean.sign.local.LocalSignFileStream;
//import net.qiyuesuo.sdk.bean.sign.local.LocalSignStamper;
//import net.qiyuesuo.sdk.bean.sign.local.request.CompanyLocalSignRequest;
//import net.qiyuesuo.sdk.bean.sign.local.result.LocalSignResult;
//import net.qiyuesuo.sdk.bean.user.UserInfoRequest;
//import net.qiyuesuo.sdk.impl.local.service.CompanyLocalSignService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.File;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.UUID;
//import java.util.function.Supplier;
//
//public class SealUtil {
//
//    // 初始化开放平台SDK相关参数
//    static String url = "http://*************:9182/";
//    static String accessKey = "jfhjXpbmYy";
//    static String accessSecret = "g8sqN7uvlLieETqXCiCuVRkWMEHHbw";
//    static SDKClient sdkClient = new SDKClient(url, accessKey, accessSecret);
//
//    static CompanyLocalSignService companyLocalSignService = new CompanyLocalSignService(sdkClient);
//
//    private final Logger logger = LoggerFactory.getLogger(SealUtil.class);
//
//    // 默认值印章ID
//    private static Long sealId = 3362975989667455047L;
//
//
//    public static byte[] signDocument(byte[] pdfBytes,String fileName) throws Exception {
//        //pdfBytes变成文件
//        String dirPath = System.getProperty("user.dir") + File.separator + "target" + File.separator + "pdf" + File.separator;
//        // 确保目录存在
//        Files.createDirectories(Paths.get(dirPath + "signed-pdf/"));
//        // 将字节数组写入文件
//        Files.write(Paths.get(dirPath + fileName), pdfBytes);
//
//        String outputPDFFileName = String.format(dirPath + "signed-pdf/%s-%s.pdf", fileName, UUID.randomUUID());
//        // 签署文件
//        LocalSignFileStream pdf = new LocalSignFileStream(fileName,
//                Files.newInputStream(Paths.get(dirPath + fileName)),
//                Files.newOutputStream(Paths.get(outputPDFFileName)));
//
//        // 签署位置
//        List<LocalSignStamper> stampers = getStampers(fileName);
//
//        CompanyLocalSignRequest localSignRequest = new CompanyLocalSignRequest("我是本次签署主题", pdf, stampers);
//
//        // 印章
//        setSealRequest(localSignRequest);
//
//        // 操作人
//        setOperator(localSignRequest);
//
//
//
//        // 设置耗时统计 默认是关闭
//        localSignRequest.setEnableWatch(true);
//
//        companyLocalSignService.sign(localSignRequest);
//
//        return Files.readAllBytes(Paths.get(outputPDFFileName));
//
//    }
//
//
//
//
//
//    private static void setOperator(CompanyLocalSignRequest localSignRequest) {
//        UserInfoRequest userInfoRequest = new UserInfoRequest();
//        userInfoRequest.setContact("18607854646");
//        localSignRequest.setOperator(userInfoRequest);
//    }
//
//    private static void setSealRequest(CompanyLocalSignRequest localSignRequest) {
//        SealMultipleRequest sealRequest = new SealMultipleRequest();
//        sealRequest.setSealIds(sealId + "");
//        localSignRequest.setSealRequest(sealRequest);
//    }
//
//    private static List<LocalSignStamper> getStampers(String fileName) {
//        // 设置Pdf1 的签署位置
//        return new LinkedList<>(getPdf1Stamper(fileName));
//    }
//
//    private static List<LocalSignStamper> getPdf1Stamper(String fileName) {
//
//        List<LocalSignStamper> signStampers = new LinkedList<>();
//
//
//        // 正常的坐标定位
//        Supplier<LocalSignStamper> normalCoordinates = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            localSignStamper.setPage(1);
//            localSignStamper.setOffsetX(0.6D);
//            localSignStamper.setOffsetY(0.24D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        // 正常的坐标定位
//        Supplier<LocalSignStamper> normalCoordinates2 = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            localSignStamper.setPage(4);
//            localSignStamper.setOffsetX(0.7D);
//            localSignStamper.setOffsetY(0.8D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        // 正常的坐标定位
//        Supplier<LocalSignStamper> normalCoordinates1 = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.ACROSS_PAGE);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//
//        // 正常的多关键字定位 ALL 模式，全匹配
//
//        Supplier<LocalSignStamper> normalAllKeyWord = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            List<String> bestKeyword = new ArrayList<>();
//            bestKeyword.add("111");
//            bestKeyword.add("222");
//            bestKeyword.add("333");
//            localSignStamper.setBestKeyWords(bestKeyword);
//            localSignStamper.setOffsetX(0.0D);
//            localSignStamper.setOffsetY(0.0D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//
//        // 正常的关键字定位。PRIORITY 模式 ，只能匹配2个
//        Supplier<LocalSignStamper> normalPriorityKeyword = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_CORPORATE);
//            List<String> bestKeyword = new ArrayList<>();
//            bestKeyword.add("盖章");
//            bestKeyword.add("签字");
//            localSignStamper.setKeywordMatchRule(KeywordMatchRule.PRIORITY);
//            localSignStamper.setBestKeyWords(bestKeyword);
//            localSignStamper.setRelativePosition(RelativePosition.LOWER_CENTER);
//            localSignStamper.setOffsetX(0.0D);
//            localSignStamper.setOffsetY(0.0D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        // 骑缝章匹配
//        Supplier<LocalSignStamper> normalAcross = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setType(StamperType.ACROSS_PAGE);
//            localSignStamper.setFileName(fileName);
////            localSignStamper.setOffsetY(0.1D);
//            return localSignStamper;
//        };
//
//        signStampers.add(normalCoordinates.get());
//        signStampers.add(normalCoordinates1.get());
//        signStampers.add(normalCoordinates2.get());
////        signStampers.add(normalAllKeyWord.get());
////        signStampers.add(normalPriorityKeyword.get());
////        signStampers.add(normalAcross.get());
//
//        return signStampers;
//
//    }
//}
