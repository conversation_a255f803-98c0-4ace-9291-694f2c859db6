package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
import org.jeecg.modules.lims_core.entity.SysStandard;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.entity.YaoDian;
import org.jeecg.modules.lims_core.vo.LinkageVo;
import org.jeecg.modules.lims_core.vo.SysStandardPage;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 标准
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
public interface ISysStandardService extends IService<SysStandard> {

	/**
	 * 添加一对多
	 *
	 * @param sysStandard
	 * @param sysStandardEvaluationLimtList
	 */
	public void saveMain(SysStandard sysStandard,List<SysStandardEvaluationLimt> sysStandardEvaluationLimtList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param sysStandard
	 * @param sysStandardEvaluationLimtList
	 */
	public void updateMain(SysStandard sysStandard,List<SysStandardEvaluationLimt> sysStandardEvaluationLimtList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	List<LinkageVo> fetch(String id,String version);

	void addYaoDian(YaoDian yaoDian);

    List<Map<String, String>> selectOptions();

	public void saveCopyMain(SysStandardPage sysStandardPage);
}
