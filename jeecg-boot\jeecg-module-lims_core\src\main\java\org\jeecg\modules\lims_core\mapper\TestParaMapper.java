package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.lims_core.entity.TestPara;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.lims_core.vo.TestParaVoNew;

/**
 * @Description: 检测过程参数
 * @Author: jeecg-boot
 * @Date:   2025-01-20
 * @Version: V1.0
 */
public interface TestParaMapper extends BaseMapper<TestPara> {
    IPage<TestParaVoNew> queryPageList(Page<TestParaVoNew> page,
                                       @Param(Constants.WRAPPER) Wrapper<TestParaVoNew> wrapper);
}
