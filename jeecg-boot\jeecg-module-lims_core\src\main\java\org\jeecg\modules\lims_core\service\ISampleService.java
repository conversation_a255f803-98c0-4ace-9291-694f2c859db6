package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.entity.Sample;
import org.jeecg.modules.lims_core.vo.QSampleVo;
import org.jeecg.modules.lims_core.vo.SampleTableEditVo;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * @Description: 样品
 * @Author: jeecg-boot
 * @Date:   2025-01-06
 * @Version: V1.0
 */
public interface ISampleService extends IService<Sample> {

    /**根节点父ID的值*/
    public static final String ROOT_PID_VALUE = "0";

    /**树节点有子节点状态值*/
    public static final String HASCHILD = "1";

    /**树节点无子节点状态值*/
    public static final String NOCHILD = "0";

    /**
     * 新增节点
     *
     * @param sample
     */
    void addSample(Sample sample);

    /**
     * 修改节点
     *
     * @param sample
     * @throws JeecgBootException
     */
    void updateSample(Sample sample) throws JeecgBootException;

    /**
     * 删除节点
     *
     * @param id
     * @throws JeecgBootException
     */
    void deleteSample(String id) throws JeecgBootException;

    /**
     * 查询所有数据，无分页
     *
     * @param queryWrapper
     * @return List<Sample>
     */
    List<Sample> queryTreeListNoPage(QueryWrapper<Sample> queryWrapper);

    /**
     * 【vue3专用】根据父级编码加载分类字典的数据
     *
     * @param parentCode
     * @return
     */
    List<SelectTreeModel> queryListByCode(String parentCode);

    /**
     * 【vue3专用】根据pid查询子节点集合
     *
     * @param pid
     * @return
     */
    List<SelectTreeModel> queryListByPid(String pid);

    List<Map> listDataLog(String id);

    IPage<QSampleVo> listVo(Page<Sample> page, QueryWrapper<Sample> queryWrapper) throws InvocationTargetException, IllegalAccessException;

    void addProduct(List<SysProductSubVo> productSubVos, String id);

    void delete(String id, Integer level);

    void editLevel(SampleTableEditVo vo);




    void copySample(String id, String LotNo);

    void copySampleByLotNo(String id);

    void addCapability(List<String> capabilityIds, String id);

    void cancelSample(List<String> list);

    void resumSample(List<String> list);

    String getServiceType(String id);

    String getIsSupplementary(String id);

    String getWarehouseLocation(String code);

    String getCustomerAddress(String id);

    String getCustomerContactPhone(String id);

    String getRdName(String id);

    void copyOldSample(String quotationId, List<String> list);

    void addPmRemark(List<String> list, String pmRemark);
}
