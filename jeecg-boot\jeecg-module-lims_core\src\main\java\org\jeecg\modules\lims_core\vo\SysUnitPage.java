package org.jeecg.modules.lims_core.vo;

import java.util.List;
import org.jeecg.modules.lims_core.entity.SysUnit;
import org.jeecg.modules.lims_core.entity.SysUnitConversion;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;

/**
 * @Description: 计量单位
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Data
@Schema(description="计量单位")
public class SysUnitPage {

	/**主键*/
	@Schema(description = "主键")
    private java.lang.String id;
	/**名称*/
	@Excel(name = "名称", width = 15)
	@Schema(description = "名称")
    private java.lang.String unitName;
	/**是否包装单位*/
	@Excel(name = "是否包装单位", width = 15)
	@Schema(description = "是否包装单位")
	private java.lang.String isPack;
	/**创建人*/
	@Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	
	@ExcelCollection(name="单位转换")
	@Schema(description = "单位转换")
	private List<SysUnitConversion> sysUnitConversionList;
	
}
