package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 业务类型
 * @Author: jeecg-boot
 * @Date:   2025-05-15
 * @Version: V1.0
 */
@Data
@TableName("biz_type")
@Schema(description="业务类型")
public class BizType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**名称*/
    @Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
    /**检品编号前缀*/
    @Excel(name = "检品编号前缀", width = 15, dictTable = "sys_key", dicText = "prefix", dicCode = "id")
    @Dict(dictTable = "sys_key", dicText = "prefix", dicCode = "id")
    @Schema(description = "检品编号前缀")
    private java.lang.String samplePrefix;
    /**研发编号前缀*/
    @Excel(name = "研发编号前缀", width = 15, dictTable = "sys_key", dicText = "prefix", dicCode = "id")
    @Dict(dictTable = "sys_key", dicText = "prefix", dicCode = "id")
    @Schema(description = "研发编号前缀")
    private java.lang.String rdPrefix;
    /**描述*/
    @Excel(name = "描述", width = 15)
    @Schema(description = "描述")
    private java.lang.String description;
    /**父级节点*/
    @Excel(name = "父级节点", width = 15)
    @Schema(description = "父级节点")
    private java.lang.String pid;
    /**是否有子节点*/
    @Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(description = "是否有子节点")
    private java.lang.String hasChild;
    /**是否生成研发编码*/
    @Excel(name = "是否生成研发编码", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(description = "是否生成研发编码")
    private java.lang.String isGenerateRdNo;
}
