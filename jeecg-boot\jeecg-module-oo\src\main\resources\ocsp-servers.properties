# OCSP服务器配置文件
# 格式: ca.identifier.primary=主要OCSP服务器URL
#       ca.identifier.backup=备用OCSP服务器URL列表（逗号分隔）

# GDCA (广东数字证书认证中心)
ca.gdca.primary=http://ocsp2.gdca.com.cn/ocsp
ca.gdca.backup=http://ocsp.gdca.com.cn/ocsp,http://ocsp1.gdca.com.cn/ocsp,http://ocsp3.gdca.com.cn/ocsp

# 其他常见CA的OCSP服务器
ca.cfca.primary=http://ocsp.cfca.com.cn
ca.cfca.backup=http://ocsp1.cfca.com.cn,http://ocsp2.cfca.com.cn

ca.wosign.primary=http://ocsp.wosign.com
ca.wosign.backup=http://ocsp1.wosign.com,http://ocsp2.wosign.com

# 国际CA
ca.digicert.primary=http://ocsp.digicert.com
ca.digicert.backup=http://ocsp1.digicert.com,http://ocsp2.digicert.com

ca.verisign.primary=http://ocsp.verisign.com
ca.verisign.backup=http://ocsp1.verisign.com,http://ocsp2.verisign.com

# 通用配置
ocsp.timeout.connect=10000
ocsp.timeout.read=10000
ocsp.retry.max=3
ocsp.retry.delay=1000

# 是否启用模拟OCSP响应（当所有OCSP服务器都不可用时）
ocsp.mock.enabled=true
ocsp.mock.status=unknown