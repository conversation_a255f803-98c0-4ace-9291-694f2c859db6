package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.vo.LinkageVo;
import org.jeecg.modules.lims_core.vo.SysStandardPage;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 标准
 * @Author: jeecg-boot
 * @Date:   2024-12-23
 * @Version: V1.0
 */
@Service
public class SysStandardServiceImpl extends ServiceImpl<SysStandardMapper, SysStandard> implements ISysStandardService {

	@Autowired
	private SysStandardMapper sysStandardMapper;
	@Autowired
	private SysStandardEvaluationLimtMapper sysStandardEvaluationLimtMapper;
	@Autowired
	private SysAnalyteMapper sysAnalyteMapper;
	@Autowired
	private ISysMethodAnalyteService sysMethodAnalyteService;
	@Autowired
	private SysMethodMapper sysMethodMapper;
	@Autowired
	private SysMethodAnalyteMapper sysMethodAnalyteMapper;
	@Autowired
	private SysProductPackageMapper sysProductPackageMapper;
	@Autowired
	private ISysStandardEvaluationLimtService sysStandardEvaluationLimtService;
	@Autowired
	private ISysMethodService sysMethodService;
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysStandard sysStandard, List<SysStandardEvaluationLimt> sysStandardEvaluationLimtList) {
		sysStandardMapper.insert(sysStandard);
		if(sysStandardEvaluationLimtList!=null && sysStandardEvaluationLimtList.size()>0) {
			for(SysStandardEvaluationLimt entity:sysStandardEvaluationLimtList) {
				//外键设置
				entity.setStandardId(sysStandard.getId());
				sysStandardEvaluationLimtMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysStandard sysStandard,List<SysStandardEvaluationLimt> sysStandardEvaluationLimtList) {
		sysStandardMapper.updateById(sysStandard);
		

		//2.子表数据重新插入
		if(sysStandardEvaluationLimtList!=null && sysStandardEvaluationLimtList.size()>0) {

			//1.先删除子表数据
			sysStandardEvaluationLimtMapper.deleteByMainId(sysStandard.getId());

			for(SysStandardEvaluationLimt entity:sysStandardEvaluationLimtList) {
				//外键设置
				entity.setStandardId(sysStandard.getId());
				sysStandardEvaluationLimtMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysStandardEvaluationLimtMapper.deleteByMainId(id);
		sysStandardMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysStandardEvaluationLimtMapper.deleteByMainId(id.toString());
			sysStandardMapper.deleteById(id);
		}
	}

    @Override
    public List<LinkageVo> fetch(String id ,String version) {
		if(id.equals("")) {
			List<SysAnalyte> sysAnalytes = sysAnalyteMapper.selectList(null);
			List<LinkageVo> linkageVos = sysAnalytes.stream().map(sysAnalyte -> {
				LinkageVo linkageVo = new LinkageVo();
				linkageVo.setValue(sysAnalyte.getId());
				linkageVo.setText(sysAnalyte.getName());
				return linkageVo;
			}).collect(Collectors.toList());
			return linkageVos;
		}else{
			QueryWrapper<SysMethodAnalyte> qw = new QueryWrapper<>();
			qw.eq("analyte_id", id);
			List<SysMethodAnalyte> sysMethodAnalytes = sysMethodAnalyteService.list(qw);
			List<LinkageVo> linkageVos = sysMethodAnalytes.stream().map(sysMethodAnalyte -> {
				SysMethod sysMethod = sysMethodMapper.selectById(sysMethodAnalyte.getMethodId());
				if(sysMethod == null){
					return null;
				}
				SysStandard sysStandard = sysStandardMapper.selectById(sysMethod.getStandardId());
				LinkageVo linkageVo = new LinkageVo();
				linkageVo.setValue(sysMethod.getId());
				String modifiedName ="";
				if (sysStandard != null){
					if(version!=null && !version.equals("") && !version.equals("/")) {
						if(!version.equals(sysStandard.getVersion())){
							return null;
						}
					}
					modifiedName = sysStandard.getName();
					if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("》")) {
						int index = sysStandard.getName().indexOf("》");
						modifiedName = sysStandard.getName().substring(0, index + 1)
								+ sysStandard.getVersion()
								+ " "
								+ sysStandard.getName().substring(index + 1);
					}
				}
				linkageVo.setText(sysStandard == null ? "" : modifiedName + "：" +sysMethod.getName());
				linkageVo.setParent(id);
				return linkageVo;
			}).filter(linkageVo -> linkageVo != null).collect(Collectors.toList());
			return linkageVos;
		}

    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addYaoDian(YaoDian yaoDian) {
		SysStandard sysStandard = new SysStandard();
		sysStandard.setName(yaoDian.getName());
		sysStandard.setVersion(yaoDian.getVersion());
		sysStandard.setDescription(yaoDian.getName());
		sysStandard.setContentTypeId("3");
		sysStandard.setGradeTypeId("1867454138389000193");

		sysStandard.setEffectiveStatus("1");
		sysStandardMapper.insert(sysStandard);


		//插入营销产品
		if(!yaoDian.getName().contains("通则")){
			SysProductPackage sysProductPackage = new SysProductPackage();
			sysProductPackage.setName(yaoDian.getName().split(" ")[yaoDian.getName().split(" ").length - 1]);
			sysProductPackage.setStandardId(sysStandard.getId());
			sysProductPackage.setBizTypeId("1922837690483490818");
			sysProductPackage.setCategory("STANDARD");
			sysProductPackage.setStdPrice(0.0);
			sysProductPackage.setStdTat("0");
			sysProductPackageMapper.insert(sysProductPackage);
		}

		String appearance = yaoDian.getAppearance() == null ? "" : yaoDian.getAppearance();
		String identification = yaoDian.getIdentification() == null ? "" : yaoDian.getIdentification();
		String inspection = yaoDian.getInspection() == null ? "" : yaoDian.getInspection();
		String microbialLimit = yaoDian.getMicrobialLimit() == null ? "" : yaoDian.getMicrobialLimit();
		String contentDetermination = yaoDian.getContentDetermination() == null ? "" : yaoDian.getContentDetermination();

		//appearance.split(",") + identification.split(",") 组成一个新列表
		List<String> selfMehtodList = Arrays.stream(appearance.split(","))
				.collect(Collectors.toList());
		selfMehtodList.addAll(Arrays.stream(identification.split(",")).collect(Collectors.toList()));

		// inspection.split(",") + microbialLimit.split(",") + contentDetermination.split(",") 组成一个新列表
		List<String> methodList = Arrays.stream(inspection.split(","))
				.collect(Collectors.toList());
		methodList.addAll(Arrays.stream(microbialLimit.split(",")).collect(Collectors.toList()));
		methodList.addAll(Arrays.stream(contentDetermination.split(",")).collect(Collectors.toList()));


		// 性状 + 鉴别
		selfMehtodList.forEach(aid -> {
			SysAnalyte sysAnalyte = sysAnalyteMapper.selectById(aid);
			if (sysAnalyte != null) {
				SysMethod sysMethod = new SysMethod();
				sysMethod.setName(sysAnalyte.getName());
				sysMethod.setStandardId(sysStandard.getId());
				sysMethod.setEffectiveStatus("1");
				sysMethod.setNatureTypeId("物理");
				sysMethod.setCid(sysAnalyte.getName().contains("性状")?"1924681824534016001":"1917126525236273153");
				sysMethodMapper.insert(sysMethod);

				SysMethodAnalyte sysMethodAnalyte = new SysMethodAnalyte();
				sysMethodAnalyte.setAnalyteId(aid);
				sysMethodAnalyte.setMethodId(sysMethod.getId());
				sysMethodAnalyte.setResultType(1);
				sysMethodAnalyteMapper.insert(sysMethodAnalyte);

				SysStandardEvaluationLimt sysStandardEvaluationLimt = new SysStandardEvaluationLimt();
				sysStandardEvaluationLimt.setStandardId(sysStandard.getId());
				sysStandardEvaluationLimt.setMethodIds(sysMethod.getId());
				sysStandardEvaluationLimt.setAnalyteId(sysAnalyte.getId());
				sysStandardEvaluationLimt.setElimit("/");
				sysStandardEvaluationLimt.setGroupOne(sysAnalyte.getGroupOne());
				sysStandardEvaluationLimtMapper.insert(sysStandardEvaluationLimt);

			}


		});

		//+ 检查 + 微生物限度 + 含量测定
		methodList.forEach(aid -> {
			SysAnalyte sysAnalyte = sysAnalyteMapper.selectById(aid);
			if (sysAnalyte != null) {

				SysStandardEvaluationLimt sysStandardEvaluationLimt = new SysStandardEvaluationLimt();
				sysStandardEvaluationLimt.setStandardId(sysStandard.getId());
				sysStandardEvaluationLimt.setMethodIds("");
				sysStandardEvaluationLimt.setAnalyteId(sysAnalyte.getId());
				sysStandardEvaluationLimt.setElimit("/");
				sysStandardEvaluationLimtMapper.insert(sysStandardEvaluationLimt);
			}

		});
	}

    @Override
    public List<Map<String, String>> selectOptions() {
		List<Map<String, String>> sysStandards = new ArrayList<>();
		QueryWrapper<SysStandard> qw = new QueryWrapper<SysStandard>();
		qw.orderByDesc("create_time");
		this.getBaseMapper().selectList(qw).forEach(
				sysStandard -> {
					Map<String,String> map = new HashMap<>();
					String version = "";
					if(sysStandard.getVersion() != null&& sysStandard.getVersion().matches("^\\d{4}$")){
						version = "-"+sysStandard.getVersion().toString();
					}
					map.put("value",sysStandard.getId());
					String modifiedName = sysStandard.getName();
					if (sysStandard.getVersion() != null && !sysStandard.getVersion().equals("/") && sysStandard.getName().contains("》")) {
						int index = sysStandard.getName().indexOf("》");
						modifiedName = sysStandard.getName().substring(0, index + 1)
								+ sysStandard.getVersion()
								+ " "
								+ sysStandard.getName().substring(index + 1);
					}
					map.put("label", !version.equals("") ? sysStandard.getName() + version :modifiedName);
					sysStandards.add(map);
				}
		);
        return sysStandards;
    }

	@Override
	public void saveCopyMain(SysStandardPage sysStandardPage){
		LoginUser sysUser = SecureUtil.currentUser();
		SysStandard sysStandard = new SysStandard();
		BeanUtils.copyProperties(sysStandardPage, sysStandard);
		SysStandard sysStandardNew = sysStandardMapper.selectById(sysStandard.getId());
		sysStandardNew.setId(null);
		sysStandardNew.setName(sysStandard.getName());
		sysStandardNew.setVersion(sysStandard.getVersion());
		sysStandardNew.setDescription(sysStandard.getDescription());
		sysStandardNew.setCreateBy(sysUser.getUsername());
		sysStandardNew.setUpdateBy(sysUser.getUsername());
		sysStandardNew.setCreateTime(new Date());
		sysStandardNew.setUpdateTime(new Date());
		sysStandardMapper.insert(sysStandardNew);
		List<SysStandardEvaluationLimt> sysStandardEvaluationLimts = sysStandardEvaluationLimtService.selectByMainId(sysStandard.getId());
		LambdaQueryWrapper<SysMethod> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SysMethod::getStandardId, sysStandard.getId());
		List<SysMethod> sysMethods = sysMethodMapper.selectList(queryWrapper);
		if(sysStandard.getName().contains("药典》")){
			if(sysStandardEvaluationLimts!=null && sysStandardEvaluationLimts.size()>0) {
				Set<String> methodIdsToExclude = sysMethods.stream()
						.map(SysMethod::getId)
						.collect(Collectors.toSet());
				for (SysStandardEvaluationLimt entity : sysStandardEvaluationLimts) {
					List<String> currentMethodIds = entity.getMethodIds() != null && !entity.getMethodIds().trim().isEmpty()
							? Arrays.asList(entity.getMethodIds().split("\\s*,\\s*")).stream()
							.filter(id -> !id.isEmpty())
							.collect(Collectors.toList())
							: new ArrayList<>();
					boolean containsExcluded = currentMethodIds.stream().anyMatch(methodIdsToExclude::contains);
					if (!containsExcluded) {//
                       SysMethod sysMethod1 = sysMethodMapper.selectById(entity.getMethodIds());
						LambdaQueryWrapper<SysStandard> queryWrapper1 = new LambdaQueryWrapper<>();
						queryWrapper1.eq(SysStandard::getId, sysMethod1.getStandardId());
						SysStandard sysStandardNew1 = sysStandardMapper.selectOne(queryWrapper1);

						LambdaQueryWrapper<SysStandard> queryWrapperExist = new LambdaQueryWrapper<>();
						queryWrapperExist.eq(SysStandard::getName, sysStandardNew1.getName());
						queryWrapperExist.eq(SysStandard::getVersion, sysStandard.getVersion());
						SysStandard sysStandardExist = sysStandardMapper.selectOne(queryWrapperExist);
						if(sysStandardExist == null){
							sysStandardNew1.setId(null);
							sysStandardNew1.setName(sysStandardNew1.getName());
							sysStandardNew1.setVersion(sysStandard.getVersion());
							sysStandardNew1.setDescription("/");
							sysStandardNew1.setCreateBy(sysUser.getUsername());
							sysStandardNew1.setUpdateBy(sysUser.getUsername());
							sysStandardNew1.setCreateTime(new Date());
							sysStandardNew1.setUpdateTime(new Date());
							sysStandardMapper.insert(sysStandardNew1);
						}
						sysMethod1.setId(null);
						sysMethod1.setStandardId(sysStandardNew1.getId());
						sysMethod1.setName(sysMethod1.getName());
						sysMethod1.setCreateBy(sysUser.getUsername());
						sysMethod1.setUpdateBy(sysUser.getUsername());
						sysMethod1.setCreateTime(new Date());
						sysMethod1.setUpdateTime(new Date());
						List<SysMethodConsumptive> sysMethodConsumptiveList = null;//sysMethodConsumptiveService.selectByMainId(sysMethod.getId());
						List<SysMethodStdMaterial> sysMethodStdMaterialList = null;//sysMethodStdMaterialService.selectByMainId(sysMethod.getId());
						List<SysMethodInstrumentType> sysMethodInstrumentTypeList = null;//sysMethodInstrumentTypeService.selectByMainId(sysMethod.getId());
						List<SysMethodTestingPara> sysMethodTestingParaList = null;//sysMethodTestingParaService.selectByMainId(sysMethod.getId());
						List<SysMethodRepeatType> sysMethodRepeatTypeList = null;//sysMethodRepeatTypeService.selectByMainId(sysMethod.getId());
						List<SysMethodWorkflow> sysMethodWorkflowList = null;//sysMethodWorkflowService.selectByMainId(sysMethod.getId());
						List<SysMethodAnalyte> sysMethodAnalyteList = null;//sysMethodAnalyteService.selectByMainId(sysMethod.getId());
						sysMethodService.saveCopyMain(sysMethod1,entity.getMethodIds(),sysMethodConsumptiveList,sysMethodStdMaterialList,sysMethodInstrumentTypeList,sysMethodTestingParaList,sysMethodRepeatTypeList,sysMethodWorkflowList,sysMethodAnalyteList);
					}
				}
			}
		}
		for(SysMethod sysMethod:sysMethods) {
			SysMethod sysMethodNew = sysMethodService.getById(sysMethod.getId());
			sysMethodNew.setId(null);
			sysMethodNew.setStandardId(sysStandardNew.getId());
			sysMethodNew.setName(sysMethod.getName());
			sysMethodNew.setCreateBy(sysUser.getUsername());
			sysMethodNew.setUpdateBy(sysUser.getUsername());
			sysMethodNew.setCreateTime(new Date());
			sysMethodNew.setUpdateTime(new Date());
			List<SysMethodConsumptive> sysMethodConsumptiveList = null;//sysMethodConsumptiveService.selectByMainId(sysMethod.getId());
			List<SysMethodStdMaterial> sysMethodStdMaterialList = null;//sysMethodStdMaterialService.selectByMainId(sysMethod.getId());
			List<SysMethodInstrumentType> sysMethodInstrumentTypeList = null;//sysMethodInstrumentTypeService.selectByMainId(sysMethod.getId());
			List<SysMethodTestingPara> sysMethodTestingParaList = null;//sysMethodTestingParaService.selectByMainId(sysMethod.getId());
			List<SysMethodRepeatType> sysMethodRepeatTypeList = null;//sysMethodRepeatTypeService.selectByMainId(sysMethod.getId());
			List<SysMethodWorkflow> sysMethodWorkflowList = null;//sysMethodWorkflowService.selectByMainId(sysMethod.getId());
			List<SysMethodAnalyte> sysMethodAnalyteList = null;//sysMethodAnalyteService.selectByMainId(sysMethod.getId());
			sysMethodService.saveCopyMain(sysMethodNew,sysMethod.getId(),sysMethodConsumptiveList,sysMethodStdMaterialList,sysMethodInstrumentTypeList,sysMethodTestingParaList,sysMethodRepeatTypeList,sysMethodWorkflowList,sysMethodAnalyteList);
		}
		if(sysStandardEvaluationLimts!=null && sysStandardEvaluationLimts.size()>0) {
			for(SysStandardEvaluationLimt entity:sysStandardEvaluationLimts) {
				//外键设置
				entity.setId(null);
				QueryWrapper<SysMethod> queryWrapperOld = new QueryWrapper<>();
				queryWrapperOld.eq("standard_id", entity.getStandardId());
				queryWrapperOld.eq("id", entity.getMethodIds());
				SysMethod sysMethodOld = sysMethodMapper.selectOne(queryWrapperOld);
				if(sysMethodOld != null){
					QueryWrapper<SysMethod> queryWrapperNew = new QueryWrapper<>();
					queryWrapperNew.eq("standard_id",sysStandardNew.getId());
					queryWrapperNew.eq("name", sysMethodOld.getName());
					SysMethod sysMethodNew = sysMethodMapper.selectOne(queryWrapperNew);
					if(sysMethodNew != null){
						entity.setMethodIds(sysMethodNew.getId());
					}
				}else if(sysStandard.getName().contains("药典》")){
					sysMethodOld = sysMethodMapper.selectById(entity.getMethodIds());
					SysStandard sysStandardOld = sysStandardMapper.selectById(sysMethodOld.getStandardId());
					LambdaQueryWrapper<SysStandard> queryWrapper2 = new LambdaQueryWrapper<>();
					queryWrapper2.eq(SysStandard::getName, sysStandardOld.getName());
					queryWrapper2.eq(SysStandard::getVersion, sysStandard.getVersion());
					SysStandard sysStandardNew2 = sysStandardMapper.selectOne(queryWrapper2);
					if(sysStandardNew2 != null){
						queryWrapperOld = new QueryWrapper<>();
						queryWrapperOld.eq("standard_id", sysStandardNew2.getId());
						queryWrapperOld.eq("name", sysMethodOld.getName());
						SysMethod sysMethodNew = sysMethodMapper.selectOne(queryWrapperOld);
						if (sysMethodNew != null)
						   entity.setMethodIds(sysMethodNew.getId());
					}
				}
				entity.setStandardId(sysStandardNew.getId());
				sysStandardEvaluationLimtMapper.insert(entity);
			}
		}
	}
}
