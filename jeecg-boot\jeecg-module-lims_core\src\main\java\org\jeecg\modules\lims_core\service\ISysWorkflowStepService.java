package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 流程环节
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
public interface ISysWorkflowStepService extends IService<SysWorkflowStep> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysWorkflowStep>
	 */
	public List<SysWorkflowStep> selectByMainId(String mainId);

	public Integer getSortNumByName(String name);

	public String getNextStepName(String name);

	public String getNameBySortNum(Integer sortNum);
}
