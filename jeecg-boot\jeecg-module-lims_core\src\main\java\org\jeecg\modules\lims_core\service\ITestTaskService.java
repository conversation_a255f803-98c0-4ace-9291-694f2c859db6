package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletRequest;
import org.jeecg.modules.lims_core.entity.TestTask;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.SampleQuery;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_core.vo.TaskVo;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * @Description: 测试任务
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
public interface ITestTaskService extends IService<TestTask> {

    void assign(String id, String userName, String type, String experimentNotes);

    void generateTest(TestTask testTask, List<SysProductSubVo> sysProductSubVos);

    IPage<TaskVo> pageVo(Page<TaskVo> page, QueryWrapper<TaskVo> queryWrapper);

    IPage<TaskVo> myTasklist(Page<TaskVo> page, QueryWrapper<TaskVo> queryWrapper);

    List<TaskVo> selectByMainId(String Id);

    void changeMethod(TestTask testTask);

    List<TaskVo> selectByConsumptiveName(String Name);
    List<TaskVo> selectByStandardMaterialName(String Name);
    void changesubtractor(TestTask testTask);
    String getServiceType(String id);

    String getIsSupplementary(String id);

    void revert(String id, String flowid, String reason);

    void cancel(String id, Boolean isresum);

    ModelAndView exportXlsVo(HttpServletRequest request, TaskVo taskVo, Class<TaskVo> taskVoClass, String 测试任务);

    void cuidan(String[] split);

    IPage<SampleQuery> sampleQuery(Page<SampleQuery> page, QueryWrapper<SampleQuery> queryWrapper);

    ModelAndView sampleQueryExportXls(HttpServletRequest request, SampleQuery sampleQuery, Class<SampleQuery> sampleQueryClass, String 测试任务);
}
