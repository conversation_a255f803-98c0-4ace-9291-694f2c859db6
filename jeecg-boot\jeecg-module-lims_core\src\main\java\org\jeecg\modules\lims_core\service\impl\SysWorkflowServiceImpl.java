package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysWorkflow;
import org.jeecg.modules.lims_core.entity.SysWorkflowStep;
import org.jeecg.modules.lims_core.mapper.SysWorkflowStepMapper;
import org.jeecg.modules.lims_core.mapper.SysWorkflowMapper;
import org.jeecg.modules.lims_core.service.ISysWorkflowService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 流程
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@Service
public class SysWorkflowServiceImpl extends ServiceImpl<SysWorkflowMapper, SysWorkflow> implements ISysWorkflowService {

	@Autowired
	private SysWorkflowMapper sysWorkflowMapper;
	@Autowired
	private SysWorkflowStepMapper sysWorkflowStepMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysWorkflow sysWorkflow, List<SysWorkflowStep> sysWorkflowStepList) {
		sysWorkflowMapper.insert(sysWorkflow);
		if(sysWorkflowStepList!=null && sysWorkflowStepList.size()>0) {
			for(SysWorkflowStep entity:sysWorkflowStepList) {
				//外键设置
				entity.setWorkflowId(sysWorkflow.getId());
				//如果数组有下一个.设置nextId为下一个节点的id
				if(sysWorkflowStepList.indexOf(entity)<sysWorkflowStepList.size()-1) {
					entity.setNextId(sysWorkflowStepList.get(sysWorkflowStepList.indexOf(entity) + 1).getId());
				}
				sysWorkflowStepMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysWorkflow sysWorkflow,List<SysWorkflowStep> sysWorkflowStepList) {
		sysWorkflowMapper.updateById(sysWorkflow);
		
		//1.先删除子表数据
		sysWorkflowStepMapper.deleteByMainId(sysWorkflow.getId());
		
		//2.子表数据重新插入
		if(sysWorkflowStepList!=null && sysWorkflowStepList.size()>0) {
			for(SysWorkflowStep entity:sysWorkflowStepList) {
				//外键设置
				entity.setWorkflowId(sysWorkflow.getId());
				//如果数组有下一个.设置nextId为下一个节点的id
				if(sysWorkflowStepList.indexOf(entity)<sysWorkflowStepList.size()-1) {
					entity.setNextId(sysWorkflowStepList.get(sysWorkflowStepList.indexOf(entity) + 1).getId());
				}
				sysWorkflowStepMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysWorkflowStepMapper.deleteByMainId(id);
		sysWorkflowMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysWorkflowStepMapper.deleteByMainId(id.toString());
			sysWorkflowMapper.deleteById(id);
		}
	}
	
}
