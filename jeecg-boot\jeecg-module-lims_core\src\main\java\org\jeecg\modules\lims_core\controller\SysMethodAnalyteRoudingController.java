package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyteRouding;
import org.jeecg.modules.lims_core.service.ISysMethodAnalyteRoudingService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.ISysMethodAnalyteService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 修约要求
 * @Author: jeecg-boot
 * @Date:   2025-03-26
 * @Version: V1.0
 */
@Tag(name="修约要求")
@RestController
@RequestMapping("/lims_core/sysMethodAnalyteRouding")
@Slf4j
public class SysMethodAnalyteRoudingController extends JeecgController<SysMethodAnalyteRouding, ISysMethodAnalyteRoudingService> {
	@Autowired
	private ISysMethodAnalyteRoudingService sysMethodAnalyteRoudingService;
	@Autowired
	private ISysMethodAnalyteService sysMethodAnalyteService;
	
	/**
	 * 分页列表查询
	 *
	 * @param id:SysMethodAnalyte.ID
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "修约要求-分页列表查询")
	@Operation(summary="修约要求-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysMethodAnalyteRouding>> queryPageList(@RequestParam(name="id",required=true) String id,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysMethodAnalyteRouding> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("ma_id", id);
		Page<SysMethodAnalyteRouding> page = new Page<SysMethodAnalyteRouding>(pageNo, pageSize);
		IPage<SysMethodAnalyteRouding> pageList = sysMethodAnalyteRoudingService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysMethodAnalyteRouding
	 * @return
	 */
	@AutoLog(value = "修约要求-添加")
	@Operation(summary="修约要求-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysMethodAnalyteRouding sysMethodAnalyteRouding) {
		sysMethodAnalyteRoudingService.save(sysMethodAnalyteRouding);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysMethodAnalyteRouding
	 * @return
	 */
	@AutoLog(value = "修约要求-编辑")
	@Operation(summary="修约要求-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysMethodAnalyteRouding sysMethodAnalyteRouding) {
		sysMethodAnalyteRoudingService.updateById(sysMethodAnalyteRouding);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "修约要求-通过id删除")
	@Operation(summary="修约要求-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysMethodAnalyteRoudingService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "修约要求-批量删除")
	@Operation(summary="修约要求-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method_analyte_rouding:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysMethodAnalyteRoudingService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "修约要求-通过id查询")
	@Operation(summary="修约要求-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysMethodAnalyteRouding> queryById(@RequestParam(name="id",required=true) String id) {
		SysMethodAnalyteRouding sysMethodAnalyteRouding = sysMethodAnalyteRoudingService.getById(id);
		if(sysMethodAnalyteRouding==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysMethodAnalyteRouding);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysMethodAnalyteRouding
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method_analyte_rouding:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysMethodAnalyteRouding sysMethodAnalyteRouding) {
        return super.exportXls(request, sysMethodAnalyteRouding, SysMethodAnalyteRouding.class, "修约要求");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_method_analyte_rouding:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysMethodAnalyteRouding.class);
    }

}
