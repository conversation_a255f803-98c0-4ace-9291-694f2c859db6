package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 检测指标
 * @Author: jeecg-boot
 * @Date:   2025-05-16
 * @Version: V1.0
 */
@Data
@TableName("sys_method_analyte")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="检测指标")
public class SysMethodAnalyte implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**方法ID*/
	@Excel(name = "方法ID", width = 15)
    @Schema(description = "方法ID")
    private java.lang.String methodId;
	/**序号*/
	@Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private java.lang.Integer sortNum;
	/**检测指标*/
	@Excel(name = "检测指标", width = 15, dictTable = "	sys_analyte", dicText = "name", dicCode = "id")
	@Dict(dictTable = "	sys_analyte", dicText = "name", dicCode = "id")
    @Schema(description = "检测指标")
    private java.lang.String analyteId;
	/**结果类型*/
	@Excel(name = "结果类型", width = 15, dicCode = "test_result_type")
	@Dict(dicCode = "test_result_type")
    @Schema(description = "结果类型")
    private java.lang.Integer resultType;
	/**检测单位*/
	@Excel(name = "检测单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
	@Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "检测单位")
    private java.lang.String unitId;
	/**计算公式*/
	@Excel(name = "计算公式", width = 15)
    @Schema(description = "计算公式")
    private java.lang.String calcExpr;
	/**检出限*/
	@Excel(name = "检出限", width = 15)
    @Schema(description = "检出限")
    private java.lang.String lod;
	/**定量限*/
	@Excel(name = "定量限", width = 15)
    @Schema(description = "定量限")
    private java.lang.String loq;
	/**修约算法*/
	@Excel(name = "修约算法", width = 15, dicCode = "rounding_algorithm")
	@Dict(dicCode = "rounding_algorithm")
    @Schema(description = "修约算法")
    private java.lang.Integer roundingAlgorithmId;
	/**修约方式*/
	@Excel(name = "修约方式", width = 15, dicCode = "rounding_way")
	@Dict(dicCode = "rounding_way")
    @Schema(description = "修约方式")
    private java.lang.Integer roundingWayId;
	/**修约精度*/
	@Excel(name = "修约精度", width = 15)
    @Schema(description = "修约精度")
    private java.lang.Integer roundingPrecision;
	/**精密度类型*/
	@Excel(name = "精密度类型", width = 15, dicCode = "precision_type")
	@Dict(dicCode = "precision_type")
    @Schema(description = "精密度类型")
    private java.lang.Integer precisionTypeId;
	/**精密度要求*/
	@Excel(name = "精密度要求", width = 15)
    @Schema(description = "精密度要求")
    private java.lang.String precisionReq;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
}
