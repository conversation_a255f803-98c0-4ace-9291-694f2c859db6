package org.jeecg.modules.lims_core.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysProduct;
import org.jeecg.modules.lims_core.service.ISysProductService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.SysProductVo;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 产品
 * @Author: jeecg-boot
 * @Date:   2024-12-19
 * @Version: V1.0
 */
@Tag(name="产品")
@RestController
@RequestMapping("/lims_core/sysProduct")
@Slf4j
public class SysProductController extends JeecgController<SysProduct, ISysProductService> {
	@Autowired
	private ISysProductService sysProductService;
	
	/**
	 * 分页列表查询
	 *
	 * @param sysProduct
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "产品-分页列表查询")
	@Operation(summary="产品-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysProduct>> queryPageList(SysProduct sysProduct,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysProduct> queryWrapper = QueryGenerator.initQueryWrapper(sysProduct, req.getParameterMap());
		Page<SysProduct> page = new Page<SysProduct>(pageNo, pageSize);
		IPage<SysProduct> pageList = sysProductService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param sysProduct
	 * @return
	 */
	@AutoLog(value = "产品-添加")
	@Operation(summary="产品-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:sys_product:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysProduct sysProduct) {
		sysProductService.save(sysProduct);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param sysProduct
	 * @return
	 */
	@AutoLog(value = "产品-编辑")
	@Operation(summary="产品-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_product:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysProduct sysProduct) {
		sysProductService.updateById(sysProduct);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "产品-通过id删除")
	@Operation(summary="产品-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_product:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysProductService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "产品-批量删除")
	@Operation(summary="产品-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_product:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysProductService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "产品-通过id查询")
	@Operation(summary="产品-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysProduct> queryById(@RequestParam(name="id",required=true) String id) {
		SysProduct sysProduct = sysProductService.getById(id);
		if(sysProduct==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysProduct);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysProduct
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_product:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysProduct sysProduct) {
        return super.exportXls(request, sysProduct, SysProduct.class, "产品");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_product:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysProduct.class);
    }

	 //@AutoLog(value = "产品-分页列表查询")
	 @Operation(summary="产品-分页列表查询")
	 @GetMapping(value = "/listVo")
	 public Result<IPage<SysProductVo>> listVo(SysProduct sysProduct,
													@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													HttpServletRequest req) throws InvocationTargetException, IllegalAccessException {
		 QueryWrapper<SysProduct> queryWrapper = QueryGenerator.initQueryWrapper(sysProduct, req.getParameterMap());
		 Page<SysProduct> page = new Page<SysProduct>(pageNo, pageSize);
		 IPage<SysProductVo> pageList = sysProductService.listVo(page, queryWrapper);
		 return Result.OK(pageList);
	 }

}
