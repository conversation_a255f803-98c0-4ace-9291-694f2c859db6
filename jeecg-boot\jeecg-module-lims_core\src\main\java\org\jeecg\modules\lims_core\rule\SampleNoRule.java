package org.jeecg.modules.lims_core.rule;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.aspectj.weaver.ast.Var;
import org.jeecg.common.handler.IFillRuleHandler;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.lims_core.entity.BizType;
import org.jeecg.modules.lims_core.entity.SysKey;
import org.jeecg.modules.lims_core.mapper.BizTypeMapper;
import org.jeecg.modules.lims_core.mapper.SysKeyMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/3 16:20
 */
public class SampleNoRule implements IFillRuleHandler {



    @Override
    public Object execute(JSONObject params, JSONObject formData) {

        String bizTypeId = formData.getString("biz_type_id");
        ServiceImpl bizTypeImpl = (ServiceImpl) SpringContextUtils.getBean("bizTypeServiceImpl");
        Object bizTypeObj = bizTypeImpl.getById(bizTypeId);
        JSONObject bizTypeJson = JSON.parseObject(JSON.toJSONString(bizTypeObj));

        String samplePrefix = bizTypeJson.getString("samplePrefix");
        ServiceImpl sysKeyImpl = (ServiceImpl) SpringContextUtils.getBean("sysKeyServiceImpl");
        Object sysKeyObj = sysKeyImpl.getById(samplePrefix);
        JSONObject sysKeyJson = JSON.parseObject(JSON.toJSONString(sysKeyObj));
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        if(!sysKeyJson.getString("midfix").equals(format.format(new Date()))) {
            sysKeyJson.put("midfix", format.format(new Date()));
            sysKeyJson.put("sn", 1);
            sysKeyImpl.updateById(sysKeyJson);
        }
        sysKeyObj = sysKeyImpl.getById(samplePrefix);
        sysKeyJson = JSON.parseObject(JSON.toJSONString(sysKeyObj));

        int sn = sysKeyJson.getInteger("sn");

        String sampleNo = sysKeyJson.getString("prefix") + format.format(new Date()) + StrUtil.padPre(String.valueOf(sn), 5, '0');

        sysKeyJson.put("sn", sn + 1);
        sysKeyImpl.updateById(sysKeyJson);
        return sampleNo;
    }
}