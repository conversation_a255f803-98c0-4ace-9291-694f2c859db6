package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.SysTranslation;
import org.jeecg.modules.lims_core.service.ISysTranslationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.util.TranslateUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

import static org.jeecg.common.util.oConvertUtils.camelName;

/**
 * @Description: 翻译
 * @Author: jeecg-boot
 * @Date:   2025-05-23
 * @Version: V1.0
 */
@Tag(name="翻译")
@RestController
@RequestMapping("/lims_core/sysTranslation")
@Slf4j
public class SysTranslationController extends JeecgController<SysTranslation, ISysTranslationService> {
	@Autowired
	private ISysTranslationService sysTranslationService;

	/**
	 * 分页列表查询
	 *
	 * @param sysTranslation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "翻译-分页列表查询")
	@Operation(summary="翻译-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SysTranslation>> queryPageList(SysTranslation sysTranslation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<SysTranslation> queryWrapper = QueryGenerator.initQueryWrapper(sysTranslation, req.getParameterMap());
		Page<SysTranslation> page = new Page<SysTranslation>(pageNo, pageSize);
		IPage<SysTranslation> pageList = sysTranslationService.page(page, queryWrapper);
		pageList.getRecords().stream().forEach(item -> {
			item.setFieldName(camelName(item.getFieldName()));
		});
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param sysTranslation
	 * @return
	 */
	@AutoLog(value = "翻译-添加")
	@Operation(summary="翻译-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SysTranslation sysTranslation) {
		sysTranslationService.save(sysTranslation);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sysTranslation
	 * @return
	 */
	@AutoLog(value = "翻译-编辑")
	@Operation(summary="翻译-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SysTranslation sysTranslation) {
		sysTranslationService.updateById(sysTranslation);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "翻译-通过id删除")
	@Operation(summary="翻译-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		sysTranslationService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "翻译-批量删除")
	@Operation(summary="翻译-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.sysTranslationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "翻译-通过id查询")
	@Operation(summary="翻译-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SysTranslation> queryById(@RequestParam(name="id",required=true) String id) {
		SysTranslation sysTranslation = sysTranslationService.getById(id);
		if(sysTranslation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sysTranslation);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param sysTranslation
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysTranslation sysTranslation) {
        return super.exportXls(request, sysTranslation, SysTranslation.class, "翻译");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SysTranslation.class);
    }


	 /**
	  *  编辑
	  *
	  * @param map
	  * @return
	  */
	 @AutoLog(value = "翻译-编辑")
	 @Operation(summary="翻译-编辑")
	 @PreAuthorize("@jps.requiresPermissions('lims_core:sys_translation:edit')")
	 @RequestMapping(value = "/customSave", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> customSave(@RequestBody Map<String,String> map) {
		 sysTranslationService.customSave(map);
		 return Result.OK("编辑成功!");
	 }

	/**
	 * 腾讯翻译
	 */
	@AutoLog(value = "翻译-翻译英文")
	@Operation(summary="翻译-翻译英文")
	@GetMapping(value = "/translateToEn")
	public Result<Map<String,String>> translateToEn(@RequestParam(name="chinese",required=true) String chinese) {
		HashMap<String,String> map = new HashMap();
		if(chinese.equals("/") || oConvertUtils.isEmpty(chinese)){
			map.put("en", "");
			return Result.OK(map);
		}
		String en = TranslateUtil.translateToEnglish(chinese);
		map.put("en", en);
		return Result.OK(map);
	}
}
