package org.jeecg.modules.gitlab.message;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpOaScheduleService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import me.chanjar.weixin.cp.bean.oa.WxCpOaSchedule;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.modules.gitlab.dto.*;
import org.jeecg.modules.gitlab.dto.issue.IssueHook;
import org.jeecg.modules.gitlab.dto.job.JobHook;
import org.jeecg.modules.gitlab.dto.mergerequest.MergeRequestHook;
import org.jeecg.modules.gitlab.dto.note.NoteHook;
import org.jeecg.modules.gitlab.dto.pipeline.PipelineHook;
import org.jeecg.modules.gitlab.dto.push.PushHook;
import org.jeecg.modules.gitlab.dto.release.ReleaseHook;
import org.jeecg.modules.gitlab.dto.tag.TagPushHook;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 16:19
 */
public class GitLabWebHookMessage {

    public static final String PIPELINE_CANCEL_DELETE_URL = "/actuator/gitlab/pipeline/cancel/delete";

    private static final DecimalFormat DF;

    private static final String B = "B";

    private static final String KB = "KB";

    private static final String MB = "MB";

    private static final String GB = "GB";

    private static final String TB = "TB";

    static {
        // 设置数字格式，保留一位有效小数
        DF = new DecimalFormat("#0.00");
        //四舍五入
        DF.setRoundingMode(RoundingMode.HALF_UP);
        //设置数字的分数部分中允许的最小位数。
        DF.setMinimumFractionDigits(2);
        //设置数字的分数部分中允许的最大位数。
        DF.setMaximumFractionDigits(2);
    }


    public static String issueHookMessage(IssueHook issueHook) {
        IssueHook.ObjectAttributes objectAttributes = issueHook.getObjectAttributes();
        Project project = issueHook.getProject();
        User user = issueHook.getUser();
        StringBuilder sb = new StringBuilder();
        String projectUrl = String.format("[%s](%s)\n", project.getName(), project.getWebUrl());
        String issue = String.format("[#%s](%s)", issueHook.getObjectAttributes().getId(), objectAttributes.getUrl());
        String titleEmoji = "";
        String statusEmoji = "";
        if (enableEmoji()) {
            if (objectAttributes.getState().equals("opened")) {
                titleEmoji = "\uD83D\uDD34";
                statusEmoji = "\uD83D\uDE4B\u200D♂️";
            } else if (objectAttributes.getState().equals("closed")) {
                titleEmoji = "\uD83D\uDFE2";
                statusEmoji = "✌️";
            }
        }
        sb.append(String.format("#### %s%s **%s** %n", titleEmoji, projectUrl, objectAttributes.getTitle()));
        sb.append(String.format("The Issue [%s] %s%s by [%s](%s)  %n>%s", issue,
                objectAttributes.getState(), statusEmoji,
                user.getName(), getUserHomePage(project.getWebUrl(), user.getUsername()),
                objectAttributes.getDescription()));
        return sb.toString();
    }

    public static String noteHookMessage(NoteHook noteHook) {
        User user = noteHook.getUser();
        Project project = noteHook.getProject();
        Issue issue = noteHook.getIssue();
        NoteHook.ObjectAttributes objectAttributes = noteHook.getObjectAttributes();
        StringBuilder sb = new StringBuilder();
        String u = String.format("[%s](%s)\n", user.getName(), getUserHomePage(project.getWebUrl(), user.getUsername()));
        String i = String.format("[#%s](%s)", issue.getId(), issue.getUrl());
        String n = String.format("[%s](%s)\n", noteHook.getObjectKind(), objectAttributes.getUrl());
        sb.append(String.format("%s%s add new %s in Issue [%s] %n%n", u, enableEmoji() ? "\uD83E\uDDD0" : "", n, i));
        sb.append(String.format("**%s**%n%n>%s%n", issue.getTitle(), objectAttributes.getNote()));
        return sb.toString();
    }

    public static String jobHookMessage(JobHook jobHook) {
        Repository repository = jobHook.getRepository();
        Long pipelineId = jobHook.getPipelineId();
        String buildStatus = jobHook.getBuildStatus();
        String project = String.format("[[%s]](%s)", repository.getName(), repository.getHomepage());
        String pipeline = String.format("pipeline[#%s](%s/-/pipelines/%s)", pipelineId, repository.getHomepage(), pipelineId);
        String costTime = String.format("%.0f", jobHook.getBuildDuration());
        if (costTime.equals("")) {
            costTime = "0";
        }
        String emoji = "";
        String color = "#000000";
        if (Objects.equals(buildStatus, "success")) {
            color = "#00b140";
            emoji = "✔️";
        } else if (Objects.equals(buildStatus, "failed")) {
            color = "#ff0000";
            emoji = "❌";
        } else if (Objects.equals(buildStatus, "canceled")) {
            color = "#FFDAC8";
            emoji = "⏹️";
        } else if (Objects.equals(buildStatus, "skipped")) {
            color = "#8E8E8E";
            emoji = "⏭️";
        }
        if (!enableEmoji()) {
            emoji = "";
        }
        String build = String.format(" [%s](%s/-/jobs/%s) <font color='%s'>%s%s</font>",
                 jobHook.getBuildStage(), repository.getHomepage(),
                jobHook.getBuildId(),color, buildStatus, emoji);
        return String.format("%s %s %s %s%ss", project, pipeline, build, enableEmoji() ? "\uD83D\uDD57" : "", costTime);
    }

    public static String mergeRequestHookMessage(MergeRequestHook mergeRequestHook) {
        User user = mergeRequestHook.getUser();
        Project project = mergeRequestHook.getProject();
        MergeRequestHook.ObjectAttributes objectAttributes = mergeRequestHook.getObjectAttributes();
        StringBuilder sb = new StringBuilder();
        String p = String.format("[[%s]](%s)", project.getName(), project.getWebUrl());
        String sources = String.format("[%s](%s/-/tree/%s)", objectAttributes.getSourceBranch(), project.getWebUrl(), objectAttributes.getSourceBranch());
        String targets = String.format("[%s](%s/-/tree/%s)", objectAttributes.getTargetBranch(), project.getWebUrl(), objectAttributes.getTargetBranch());
        String u = String.format("[%s](%s)", user.getUsername(), getUserHomePage(project.getWebUrl(), user.getUsername()));
        String merge = String.format(" [#%s](%s)(%s)", objectAttributes.getId(), objectAttributes.getUrl(), objectAttributes.getTitle());
        sb.append(String.format("%s %s %s %s %s %n%n", p, u, objectAttributes.getState(), mergeRequestHook.getObjectKind(), merge));
        switch (objectAttributes.getState()) {
            case "opened":
                sb.append(String.format("%s %s  wants to merge %s ➔➔ %s %n",
                        enableEmoji() ? " \uD83D\uDE00 " : "", user.getUsername(), sources, targets));
                String c = String.format(" %s - %s%n",
                        objectAttributes.getLastCommit().getAuthor().getName(), objectAttributes.getLastCommit().getMessage());
                sb.append(String.format(">[%s](%s)%s",
                        objectAttributes.getLastCommit().getId().substring(0, 8), objectAttributes.getLastCommit().getUrl(), c));
                break;
            case "merged":
                sb.append(String.format(" %s %s has completed the merge %s➔➔%s%s%n", enableEmoji() ? " \uD83D\uDE00 " : "", user.getUsername(), sources, targets, new Emoji("✔️")));
                break;
            case "closed":
                sb.append(String.format(" %s %s has closed the merge %s➔➔%s%s %n", enableEmoji() ? " \uD83D\uDE36 " : "", user.getUsername(), sources, targets, new Emoji("\uD83D\uDEAB")));
                break;
            default:
                break;
        }
        return sb.toString();
    }

    public static String pipelineHookMessage(PipelineHook pipelineHook) {
        StringBuilder sb = new StringBuilder();
        PipelineHook.ObjectAttributes objectAttributes = pipelineHook.getObjectAttributes();
        Project project = pipelineHook.getProject();
        Commit commit = pipelineHook.getCommit();
        List<Build> builds = pipelineHook.getBuilds();
        String status = objectAttributes.getStatus();
        String pipeline = String.format("%s [#%s %s](%s/-/pipelines/%s)", pipelineHook.getObjectKind(), objectAttributes.getId(), enableEmoji() ? "\uD83D\uDE80" : "", project.getWebUrl(), objectAttributes.getId());
        sb.append(String.format("[[%s:%s]](%s/-/tree/%s) %s %s %n%n", project.getName(), objectAttributes.getRef(), project.getWebUrl(), objectAttributes.getRef(), pipeline, status));
        if (!"running".equals(status)) {
            int totalTime = 0;
            if (objectAttributes.getDuration() != null) {
                totalTime += objectAttributes.getDuration();
            }
            if (objectAttributes.getQueuedDuration() != null) {
                totalTime += objectAttributes.getQueuedDuration();
            }
            sb.append(String.format(">[%s](%s) %s - %s%n%n", commit.getId().substring(0, 8), commit.getUrl(), commit.getAuthor().getName(), commit.getTitle()));
            String statusEmoji = "";
            String statusColor = "";
            switch (status) {
                case "success":
                    statusEmoji = "✅";
                    statusColor = "#00b140";
                    break;
                case "failed":
                    statusEmoji = "❌";
                    statusColor = "#ff0000";
                    break;
                case "canceled":
                    statusEmoji = "⏹️";
                    statusColor = "#FFDAC8";
                    break;
                case "skipped":
                    statusEmoji = "⏭️";
                    statusColor = "#8E8E8E";
                    break;
                default:
                    break;
            }
            statusEmoji = enableEmoji() ? statusEmoji : "";
            sb.append(String.format("%s%s : <font color='%s'>%s</font> %s %ss%n%n", statusEmoji, pipeline, statusColor, objectAttributes.getDetailedStatus(), enableEmoji() ? "\uD83D\uDD57" : "", totalTime));
            Collections.sort(builds);
            for (Build build : builds) {
                String costTime = String.format("%.0f", build.getDuration());
                if (costTime.equals("")) {
                    if (build.getFinishedAt() != null && build.getStartedAt() != null) {
                        Date finishedAt = Date.from(LocalDateTime.parse(build.getFinishedAt().substring(0, 19), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
                        Date start = Date.from(LocalDateTime.parse(build.getStartedAt().substring(0, 19), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
                        costTime = String.valueOf((finishedAt.getTime() - start.getTime()) / 1000);
                    } else {
                        costTime = "0";
                    }
                }
                String color = "";
                String emoji = "";
                switch (build.getStatus()) {
                    case "success":
                        color = "#00b140";
                        emoji = "✔️";
                        break;
                    case "failed":
                        color = "#ff0000";
                        emoji = "❌";
                        break;
                    case "canceled":
                        color = "#FFDAC8";
                        emoji = "⏹️";
                        break;
                    case "skipped":
                        color = "#8E8E8E";
                        emoji = "⏭️";
                        break;
                    case "manual":
                        color = "#8E8E8E";
                        emoji = "\uD83D\uDD04";
                        break;
                    default:
                        break;
                }
                emoji = enableEmoji() ? emoji : "";
                String fileName = "";
                if (build.getArtifactFile().getFilename() != null && build.getArtifactFile().getSize() != null) {
                    fileName = String.format("[%s](%s/-/jobs/%s/artifacts/download) %s", build.getArtifactFile().getFilename(), project.getWebUrl(), build.getId(), getFormatFileSize(build.getArtifactFile().getSize()));
                }
                sb.append(String.format(">%s [%s](%s/-/jobs/%s) : <font color='%s'>%s</font> %s %s %ss%n%n", emoji, build.getStage(), project.getWebUrl(), build.getId(), color, build.getStatus(), fileName, enableEmoji() ? "\uD83D\uDD57" : "", costTime));
            }
        } else {

            Long projectId = project.getId();
            Long pipelineId = objectAttributes.getId();

            String hostSchema = getHostSchema(project.getWebUrl());
            sb.append(String.format("[%s取消运行](%s%s?projectId=%s&pipelineId=%s&action=cancel) ",
                    enableEmoji() ? "\uD83D\uDEAB" : "", hostSchema, PIPELINE_CANCEL_DELETE_URL, projectId, pipelineId));
            sb.append(String.format("[%s重新运行](%s%s?projectId=%s&pipelineId=%s&action=retry) ",
                    enableEmoji() ? "♻️" : "", hostSchema, PIPELINE_CANCEL_DELETE_URL, projectId, pipelineId));
            sb.append(String.format("[%s删除](%s%s?projectId=%s&pipelineId=%s&action=delete) %n%n",
                    enableEmoji() ? "⛔" : "", hostSchema, PIPELINE_CANCEL_DELETE_URL, projectId, pipelineId));

        }
        return sb.toString();
    }

    public static String pushHookMessage(PushHook pushHook) {
        List<Commit> commits = pushHook.getCommits();
        Project project = pushHook.getProject();
        String userUsername = pushHook.getUserUsername();
        Collections.sort(commits);
        StringBuilder sb = new StringBuilder();
        String[] refSplit = pushHook.getRef().split("/");
        String branch = refSplit[refSplit.length - 1];
        sb.append(String.format("[[%s:%s]](%s/-/tree/%s) ", project.getName(), branch, project.getWebUrl(), branch));
        String c = commits.size() > 1 ? "commits" : "commit";
        String user = userUsername == null ? pushHook.getUserName() : String.format("[%s](%s)", userUsername, getUserHomePage(project.getWebUrl(), userUsername));
        sb.append(String.format("%s %s new %s by %s %s %n %n", pushHook.getEventName(), pushHook.getTotalCommitsCount(), c, enableEmoji() ? "\uD83D\uDE00" : "", user));
        for (Commit vo : commits) {
            sb.append(String.format("> [%s](%s) %s - %s%n%n", vo.getId().substring(0, 8), vo.getUrl(), vo.getAuthor().getName(), vo.getTitle()));
        }
        return sb.toString();
    }

    public static String releaseHookMessage(ReleaseHook releaseHook) {
        String tag =releaseHook. getTag();
        Project project = releaseHook.getProject();
        ReleaseHook.Assets assets = releaseHook.getAssets();
        String tags = String.format("[%s](%s/-/tags/%s)", tag, project.getWebUrl(), tag);
        String head = String.format("<font color='#000000'>[%s](%s) %s new %s %s by tag%s(%s)</font> %s%n%n",
                project.getName(), project.getWebUrl(),
                releaseHook.getAction(), releaseHook.getObjectKind(),
                String.format("[%s](%s)",releaseHook. getName(), releaseHook.getUrl()),
                enableEmoji() ? "\uD83D\uDCCC" : "", tags,
                enableEmoji() ? "\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80" : "");
        StringBuilder context = new StringBuilder(head);
        context.append(releaseHook.getDescription()).append("\n\n");
        context.append("<font color='#000000'>Assets</font> \n");
        for (ReleaseHook.Assets.Source source : assets.getSources()) {
            context.append(String.format("> - [%s Source code (%s)](%s) %n",
                    enableEmoji() ? "\uD83D\uDCC1" : "",
                    source.getFormat(), source.getUrl()));
        }
        return context.toString();
    }

    public static String tagPushHookMessage(TagPushHook tagPushHook) {
        Project project = tagPushHook.getProject();
        String userUsername = tagPushHook.getUserUsername();
        String[] refSplit = tagPushHook.getRef().split("/");
        String tag = refSplit[refSplit.length - 1];
        String t = String.format("[%s](%s/-/tree/%s)", tag, project.getWebUrl(), tag);
        String p = String.format("[%s](%s)", project.getName(), project.getWebUrl());
        String user = String.format("[%s](%s)", userUsername, getUserHomePage(project.getWebUrl(), userUsername));
        return String.format("%s push new tag(%s) by %s %s%n%n > %s", p, t, user, enableEmoji() ? "\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80" : "", tagPushHook.getMessage());
    }

    public static String getUserHomePage(String projectUrl, String username) {
        return String.format("%s/%s", getHostSchema(projectUrl), username);
    }

    public static String getHostSchema(String projectUrl) {
        String schema = projectUrl.substring(0, projectUrl.indexOf("//"));
        String body = projectUrl.substring(projectUrl.indexOf("//") + 2);
        String host = body.substring(0, body.indexOf("/"));
        return schema + host;
    }

    public static Boolean enableEmoji(){
        return true;
    }

    public static void sendMsg( String toUser, String content){
        if(StrUtil.isNotEmpty(toUser)){
            WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
            String corpId = wxCpProperties.getAppConfigs().get(1).getCorpId();
            int agentId = wxCpProperties.getAppConfigs().get(1).getAgentId();
            WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
            WxCpMessageService messageService = cpService.getMessageService();
            WxCpMessage message = new WxCpMessage();
            message.setAgentId(agentId);
            message.setToUser(toUser);
            message.setMsgType("markdown");
            System.out.println(content);
            message.setContent(content);
            try {
                messageService.send(message);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static String getFormatFileSize(long length) {
        double size = ((double) length) / (1 << 30);
        if (size >= 1) return DF.format(size) + GB;
        size = ((double) length) / (1 << 20);
        if (size >= 1) return DF.format(size) + MB;
        size = ((double) length) / (1 << 10);
        if (size >= 1) return DF.format(size) + KB;
        return length + B;
    }

    public static String raiseschedule(IssueHook issueHook, List<User> issueParticipants) throws WxErrorException, JsonProcessingException {
        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(1).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(1).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOaScheduleService oaScheduleService = cpService.getOaScheduleService();
        IssueHook.ObjectAttributes objectAttributes = issueHook.getObjectAttributes();
        String dueDate = issueHook.getObjectAttributes().getDueDate();
        // 字符串日期转时间戳
        LocalDate localDate = LocalDate.parse(dueDate);
        Instant instant = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
        long timestamp = instant.toEpochMilli() / 1000;
        System.out.println("Timestamp: " + timestamp);
        // 创建日程对象
        WxCpOaSchedule schedule = new WxCpOaSchedule();
        schedule.setStartTime(timestamp+ 8 * 3600); // 设置开始时间（时间戳）
        schedule.setEndTime(timestamp+ 17 * 3600); // 设置结束时间（时间戳）
        schedule.setSummary(objectAttributes.getTitle()); // 设置日程摘要
        schedule.setDescription(objectAttributes.getDescription()); // 设置日程描述

        WxCpOaSchedule.Reminder reminder = new WxCpOaSchedule.Reminder();
        reminder.setIsRemind(1);
        reminder.setRemindTimeDiffs(new ArrayList<>(){{add(-86400);}});

        schedule.setReminders(reminder);
        ArrayList<WxCpOaSchedule.Attendee> attendees = new ArrayList<>();
        issueParticipants.forEach(user -> {
            WxCpOaSchedule.Attendee attendee = new WxCpOaSchedule.Attendee();
            attendee.setUserid(user.getUsername());
            if(user.getUsername().equals("root")){
                attendee.setUserid("wangjiujun");
            }
            attendees.add(attendee);

        });
        schedule.setAttendees(attendees);
        // 添加日程
        //{"errcode":0,"errmsg":"ok","schedule_id":"295821ce4825e9fbf33105aacc8e8363ybmmstpv"}
        String jsonString = oaScheduleService.add(schedule, null);
        //取出schedule_id的值返回
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        String scheduleId = jsonNode.get("schedule_id").asText();
        System.out.println("Schedule ID: " + scheduleId);

        return scheduleId;
    }

    public static void updateSchedule(String calId, IssueHook issueHook, List<User> issueParticipants) throws JsonProcessingException, WxErrorException {
        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(1).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(1).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOaScheduleService oaScheduleService = cpService.getOaScheduleService();
        IssueHook.ObjectAttributes objectAttributes = issueHook.getObjectAttributes();
        String dueDate = issueHook.getObjectAttributes().getDueDate();
        // 字符串日期转时间戳
        LocalDate localDate = LocalDate.parse(dueDate);
        Instant instant = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
        long timestamp = instant.toEpochMilli() / 1000;
        System.out.println("Timestamp: " + timestamp);
        // 创建日程对象
        WxCpOaSchedule schedule = new WxCpOaSchedule();
        schedule.setScheduleId(calId);
        schedule.setStartTime(timestamp+ 8 * 3600); // 设置开始时间（时间戳）
        schedule.setEndTime(timestamp+ 17 * 3600); // 设置结束时间（时间戳）
        schedule.setSummary(objectAttributes.getTitle()); // 设置日程摘要
        schedule.setDescription(objectAttributes.getDescription()); // 设置日程描述

        WxCpOaSchedule.Reminder reminder = new WxCpOaSchedule.Reminder();
        reminder.setIsRemind(1);
        reminder.setRemindTimeDiffs(new ArrayList<>(){{add(-86400);}});

        schedule.setReminders(reminder);
        ArrayList<WxCpOaSchedule.Attendee> attendees = new ArrayList<>();
        issueParticipants.forEach(user -> {
            WxCpOaSchedule.Attendee attendee = new WxCpOaSchedule.Attendee();
            attendee.setUserid(user.getUsername());
            if(user.getUsername().equals("root")){
                attendee.setUserid("wangjiujun");
            }
            attendees.add(attendee);

        });
        schedule.setAttendees(attendees);
        // 添加日程
        //{"errcode":0,"errmsg":"ok","schedule_id":"295821ce4825e9fbf33105aacc8e8363ybmmstpv"}
        oaScheduleService.update(schedule);

    }

    public static void deleteSchedule(String calId) throws WxErrorException {
        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(1).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(1).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOaScheduleService oaScheduleService = cpService.getOaScheduleService();
        oaScheduleService.delete(calId);
    }
}
