package org.jeecg.modules.eSigner.service.controller;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.eSigner.service.ISignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/oo")
@Slf4j
public class SignController {

    @Autowired
    private ISignService signService;

    /**
     *   签发文件
     *
     * @param pdfUrl: pdf文件地址
     * @return
     */
    @AutoLog(value = "签发")
    @Operation(summary="签发")
    @PostMapping(value = "/sign")
    public void sign(@RequestParam(name="pdfUrl",required=true) String pdfUrl) throws Exception {
        signService.sign(pdfUrl);//调用电子签系统接口
    }
}