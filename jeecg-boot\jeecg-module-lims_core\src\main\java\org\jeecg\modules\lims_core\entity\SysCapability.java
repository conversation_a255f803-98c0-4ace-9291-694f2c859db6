package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 检测能力库
 * @Author: jeecg-boot
 * @Date:   2025-06-11
 * @Version: V1.0
 */
@Data
@TableName("sys_capability")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="检测能力库")
public class SysCapability implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**创建人*/
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private String name;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @Schema(description = "描述")
    private String description;
	/**标准工期*/
	@Excel(name = "标准工期", width = 15)
    @Schema(description = "标准工期")
    private Integer leadTime;
	/**标准收费*/
	@Excel(name = "标准收费", width = 15)
    @Schema(description = "标准收费")
    private BigDecimal stdPrice;
	/**可用状态*/
    @Excel(name = "可用状态", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "可用状态")
    private String isActive;
	/**报告模板*/
	@Excel(name = "报告模板", width = 15)
    @Schema(description = "报告模板")
    private String reportTemplate;
	/**原始记录模板*/
	@Excel(name = "原始记录模板", width = 15)
    @Schema(description = "原始记录模板")
    private String testTemplate;
	/**部门*/
	@Excel(name = "部门", width = 15)
    @Schema(description = "部门")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    private String deptId;
	/**?标检*/
    @Excel(name = "?标检", width = 15,replace = {"是_Y","否_N"} )
    @Schema(description = "?标检")
    private String isBiao;
}
