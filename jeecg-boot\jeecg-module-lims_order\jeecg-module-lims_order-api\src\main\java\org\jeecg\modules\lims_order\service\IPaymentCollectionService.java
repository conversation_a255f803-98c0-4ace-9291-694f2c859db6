package org.jeecg.modules.lims_order.service;

import org.jeecg.modules.lims_order.entity.PaymentCollection;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_order.vo.PaymentAddOrEditVo;

/**
 * @Description: 回款计划
 * @Author: jeecg-boot
 * @Date:   2025-03-21
 * @Version: V1.0
 */
public interface IPaymentCollectionService extends IService<PaymentCollection> {

    void addOrEdit(PaymentAddOrEditVo paymentAddOrEditVo);
}
