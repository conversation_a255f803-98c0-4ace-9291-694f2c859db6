package org.jeecg.modules.lims_core.service;

import org.jeecg.common.system.vo.DictModel;
import org.jeecg.modules.lims_core.entity.SysStandard;
import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 标准指标评定要求
 * @Author: jeecg-boot
 * @Date:   2025-03-13
 * @Version: V1.0
 */
public interface ISysStandardEvaluationLimtService extends IService<SysStandardEvaluationLimt> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysStandardEvaluationLimt>
	 */
	public List<SysStandardEvaluationLimt> selectByMainId(String mainId);

	List<DictModel> querySysStandardByMethodId(String id);

	List<SysStandard> selectByTestId(String id);
}
