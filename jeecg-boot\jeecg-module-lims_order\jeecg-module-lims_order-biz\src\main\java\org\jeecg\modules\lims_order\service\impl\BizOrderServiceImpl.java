package org.jeecg.modules.lims_order.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOaService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.oa.SummaryInfo;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApplyEventRequest;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplate;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplateResult;
import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateContent;
import me.chanjar.weixin.cp.bean.oa.templatedata.TemplateControls;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.mapper.*;
import org.jeecg.modules.lims_core.service.*;
import org.jeecg.modules.lims_core.service.impl.TestTaskServiceImpl;
import org.jeecg.modules.lims_core.util.GBDateUtils;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.lims_order.entity.*;
import org.jeecg.modules.lims_order.service.IPaymentCollectionService;
import org.jeecg.modules.lims_order.service.IQuotationService;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecg.modules.lims_order.mapper.*;
import org.jeecg.modules.lims_order.service.IBizOrderService;
import org.jeecg.modules.wx.entity.WecomSp;
import org.jeecg.modules.wx.service.impl.WecomSpServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description: 订单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Service
public class BizOrderServiceImpl extends ServiceImpl<BizOrderMapper, BizOrder> implements IBizOrderService {

    @Autowired
    private WecomSpServiceImpl wecomSpService;
    @Autowired
    private SampleMapper sampleMapper;

    @Autowired
    private TestTaskMapper testTaskMapper;
    @Autowired
    private TestTaskServiceImpl testTaskService;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
    @Autowired
    private SysWorkflowMapper sysWorkflowMapper;
    @Autowired
    private SysWorkflowStepMapper sysWorkflowStepMapper;
    @Autowired
    private SysMethodMapper sysMethodMapper;
    @Autowired
    private SysMethodWorkflowMapper sysMethodWorkflowMapper;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IPaymentCollectionService paymentCollectionService;
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private TestResultMapper testResultMapper;
    @Autowired
    private SysMethodAnalyteMapper sysMethodAnalyteMapper;
    @Autowired
    private RdProjectMapper rdProjectMapper;
    @Autowired
    private TestTaskFlowMapper testTaskFlowMapper;

    private static final String SAMPLE_FLOW_STATUS_1 = "未入库";
    private static final String SAMPLE_FLOW_STATUS_2 = "已入库";
    private static final String SAMPLE_FLOW_STATUS_3 = "已领用";

    private static final String FLOW_STEP_1 = "业务受理";
    private static final String FLOW_STEP_2 = "仓库入库";
    private static final String FLOW_STEP_3 = "PM确认";
    private static final String TASK_STATUS_0 = "未指派";
    @Autowired
    private IBizTypeService iBizTypeService;
    @Autowired
    private ISysKeyService iSysKeyService;
    @Autowired
    private IQuotationService iQuotationService;
    @Autowired
    private IHolidayCalendarService iHolidayCalendarService;
    @Autowired
    private IRdProjectService iRdProjectService;
    @Autowired
    private ISysCapabilityService iSysCapabilityService;

    @Override
    public List<Map> listDataLog(String id) {
        QueryWrapper<Object> queryWrapper = Wrappers.query();
        queryWrapper.eq("data_id", id).eq("data_table", "biz_order")
                .orderBy(true, false, "create_time");

        IService impl = (IService) SpringContextUtils.getBean("sysDataLogServiceImpl");
        JSONArray objects = JSONArray.parseArray(JSONObject.toJSONString(impl.list(queryWrapper)));
        return objects.toJavaList(Map.class);
    }

    @Override
    public void apply(String contractNo, ApplyType applyType) throws WxErrorException {
        BizOrder order = this.baseMapper.selectList(
                new QueryWrapper<BizOrder>().eq("contract_no", contractNo)).get(0);
        if (paymentCollectionService.getBaseMapper().selectList(new QueryWrapper<PaymentCollection>().eq("order_id", order.getId())).size() == 0) {
            throw new RuntimeException("请先添加回款计划!");
        }


        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("sysCustomerServiceImpl");
        // 根据 ruleCode 查询出实体
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", order.getCustomerId());
        JSONObject entity = JSONObject.parseObject(JSONObject.toJSONString(impl.getOne(queryWrapper)));
        if(entity!=null){
            WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
            String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
            int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
            WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
            WxCpOaService oaService = cpService.getOaService();
            String templateId = "C4ZVWZXc1GaoXbe2etB2em6Q6eqf86a4fQhFYCTCd";
            WxCpOaApprovalTemplateResult templateDetail = oaService.getTemplateDetail(templateId);
            System.out.println(templateDetail.toJson());
            updateTemplateTest(oaService,templateDetail,templateId,"Tips-1732010198545",order.getContractNo(),"https://gbjc.cc/lims_order/bizOrderList?no="+order.getContractNo(),applyType);
            WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
            // creator_userid	 申请人userid，此审批申请将以此员工身份提交，申请人需在应用可见范围内
            LoginUser curUser = SecureUtil.currentUser();
            request.setCreatorUserId(sysBaseAPI.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
            // template_id	 模板id
            request.setTemplateId(templateId);
            // use_template_approver	 0-通过接口指定审批人、抄送人（此时process参数必填）; 1-使用此模板在管理后台设置的审批流程(需要保证审批流程中没有“申请人自选”节点)，支持条件审批。
            request.setUseTemplateApprover(0);
            // apply_data     审批申请数据
            request.setApplyData(getApplyData(
                    order.getContractNo(),
                    order.getName(),
                    entity.getString("name"),
                    order.getPmPrice().toString(),
                    order.getContractAmount().toString(),
                    order.getLeadTime()
            ));
            // summary_list    摘要信息
            if(applyType == ApplyType.ORDRER_APPLY) {
                request.setSummaryList(getSummaryList("合同编号:" + order.getContractNo(), "客户名称:" + entity.getString("name"), "合同金额:" + order.getContractAmount()));
            }
            if (applyType == ApplyType.ORDRER_UNLOCK) {
                request.setSummaryList(getSummaryList("合同编号:" + order.getContractNo(),"申请解锁",""));
            }
            // process	 审批流程信息
            request.setApprovers(getApprovers());
            try {
                String sp_no = oaService.apply(request);
                order.setStatusId("1");
                order.setIsLocked(1);
                updateById(order);

                //插入审批表
                WecomSp wecomSp = new WecomSp();
                wecomSp.setSpNo(sp_no);
                wecomSp.setTargetId(order.getId());
                wecomSp.setTargetImpl("bizOrderServiceImpl");
                wecomSpService.save(wecomSp);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 进单
     * @param id
     */
    @Override
    @Transactional(
            rollbackFor = {Exception.class}
    )
    public void transferToLab(String id) {
        BizOrder bizOrder = this.baseMapper.selectById(id);
        bizOrder.setProjectStatus("1");
        updateById(bizOrder);

        // 更新样品表
        sampleMapper.selectByQuotationId(bizOrder.getQuotationId()).forEach(sample -> {
            sample.setOrderId(bizOrder.getId());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("biz_type_id", sample.getBizTypeId());
            if (sample.getSampleNo() == null || !sample.getSampleNo().matches("^[A-Z]{3}\\d{9}$")) {
                sample.setSampleNo(FillRuleUtil.executeRule(FillRuleConstant.SAMPLE, jsonObject).toString());
            }
            sample.setReceiveDate(new Date());
            sampleMapper.updateById(sample);

            //初始化重复数组,用于生成报告用
            AtomicReference<List<SysMethodRepeatType>> mrtsForReport = new AtomicReference<>(new ArrayList<>());

            List<TestTask> testTasks = testTaskMapper.selectBySampleId(sample.getId());

            List<SysProductSubVo> sysProductSubVos = new ArrayList<>();
            //先处理稳定性
            //生成平行样
            testTasks.forEach(testTask -> {
                if(testTaskMapper.selectBySampleIdAndMethodId(sample.getId(), testTask.getMethodId()).size() > 1){
                    return;
                }
                Test t = testMapper.selectList(new QueryWrapper<Test>().eq("task_id", testTask.getId())).get(0);
                testResultMapper.selectByTestId(t.getId()).forEach(
                        testResult -> {
                            SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(testResult.getMethodAnalyteId());
                            SysProductSubVo sysProductSubVo = new SysProductSubVo();
                            sysProductSubVo.setAnalyteId(sysMethodAnalyte.getAnalyteId());
                            sysProductSubVo.setEvaluationId(testResult.getLimitId());
                            sysProductSubVos.add(sysProductSubVo);
                        }
                );
                List<SysMethodRepeatType> sysMethodRepeatTypes = sysMethodRepeatTypeMapper.selectByMainId(testTask.getMethodId());
                if(sysMethodRepeatTypes.size() >0) {
                    mrtsForReport.set(sysMethodRepeatTypes);
                    SysMethodRepeatType mrt = sysMethodRepeatTypes.get(0);
                    String[] splitRepeatName = mrt.getRepeatName().split(",");
                    Date nextTestTime = null;
                    //如果有第二个周期
                    if (sysMethodRepeatTypes.size() > 1) {

                        nextTestTime = GBDateUtils.calcDate(sysMethodRepeatTypes.get(1).getCycleName(), sample.getReceiveDate());
                    }

                    //更新测试任务稳定性
                    testTask.setNextTestDate(nextTestTime);
                    testTask.setRepeatTypeId(mrt.getId());
                    testTask.setRepeatTypeNextId(mrt.getNextId());
                    testTask.setStatus(TASK_STATUS_0);
                    testTask.setRepeatType(mrt.getRepeatType());
                    testTask.setRepeatCycle(mrt.getCycleName());
                    testTask.setRepeatName(splitRepeatName[0]);
                    SysMethod sysMethod = sysMethodMapper.selectById(testTask.getMethodId());

                    List<SysMethodWorkflow> sysMethodWorkflows = sysMethodWorkflowMapper.selectByMainId(testTask.getMethodId());
                    if (sysMethodWorkflows == null || sysMethodWorkflows.size() == 0) {
                        throw new RuntimeException("请先配置---" + sysMethod.getName() + "---工作流");
                    }

                    testTask.setCurStep(sysWorkflowStepMapper.selectByMainId(sysMethodWorkflows.get(0).getWorkflowId()).get(0).getName());
                    testTaskMapper.updateById(testTask);
                    if(splitRepeatName.length > 1){
                        for (int i = 1; i <splitRepeatName.length ; i++) {
                            TestTask testTask1 = new TestTask();
                            BeanUtils.copyProperties(testTask, testTask1);
                            testTask1.setId(null);
                            testTask1.setRepeatName(splitRepeatName[i]);
                            testTaskMapper.insert(testTask1);
                        }
                    }
                }
            });

            //带有稳定性的task全集
            List<TestTask> testTasksWithRepeat = testTaskMapper.selectBySampleId(sample.getId());


                //生成平行样
            testTasksWithRepeat.forEach(testTask -> {

                //生成test
                testTaskService.generateTest(testTask,sysProductSubVos);

            });


        });
    }


    @Override
    @Transactional(
            rollbackFor = {Exception.class}
    )
    public void saveOrder(BizOrder bizOrder) {
        save(bizOrder);

        // 报价单状态 -> 转合同
        Quotation quo = iQuotationService.getById(bizOrder.getQuotationId());
        quo.setStatusId("5");
        iQuotationService.updateById(quo);

        //更新rdproject
        iRdProjectService.list(new QueryWrapper<RdProject>().eq("quotation_id", bizOrder.getQuotationId())).forEach(rdProject -> {
            rdProject.setOrderId(bizOrder.getId());
            iRdProjectService.updateById(rdProject);
        });

        // 更新样品表
        sampleMapper.selectByQuotationId(bizOrder.getQuotationId()).forEach(sample -> {
            sample.setOrderId(bizOrder.getId());

            sampleMapper.updateById(sample);


            BizType biztype = iBizTypeService.getById(sample.getBizTypeId());
            String samplePrefix = biztype.getSamplePrefix();
            SysKey key = iSysKeyService.getById(biztype.getSamplePrefix());

            testTaskMapper.selectBySampleId(sample.getId()).forEach(task -> {
                SysCapability capa = iSysCapabilityService.getById(task.getCapabilityId());
                Boolean isbiao = Boolean.FALSE;
                if(capa !=null){
                    isbiao = capa.getIsBiao().equals("Y");
                }
                if(task.getDepartmentId() == null){
                    throw new RuntimeException("任务[" + task.getName() + "]没有指派实验组!!");
                }
                if (task.getMethodId() == null && isbiao && task.getTestControlStatus().equals("正常") ) {
                    throw new RuntimeException("YPT产品,请先指定方法!!");
                }
                    if(task.getCurStep().equals(FLOW_STEP_1)) {
                        task.setCurStep(FLOW_STEP_3);
                        testTaskMapper.updateById(task);
                        //插入flow
                        TestTaskFlow testTaskFlow = new TestTaskFlow();
                        testTaskFlow.setTaskId(task.getId());
                        testTaskFlow.setStepId(FLOW_STEP_3);
                        testTaskFlowMapper.insert(testTaskFlow);
                    }

            });
        });


        ServiceImpl impl = (ServiceImpl) SpringContextUtils.getBean("sysCustomerServiceImpl");
        // 根据 ruleCode 查询出实体
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", bizOrder.getCustomerId());
        JSONObject entity = JSONObject.parseObject(JSONObject.toJSONString(impl.getOne(queryWrapper)));
        entity.put("currentNo","");
        impl.updateById(entity);

        // 更新样品表
        sampleMapper.selectByQuotationId(bizOrder.getQuotationId()).forEach(sample -> {
            List<TestTask> testTasks = testTaskMapper.selectList(new QueryWrapper<TestTask>().eq("sample_id", sample.getId()).eq("is_for_quotation",1));
            //初始化重复数组,用于生成报告用
            AtomicReference<List<SysMethodRepeatType>> mrtsForReport = new AtomicReference<>(new ArrayList<>());
            //先处理稳定性
            //生成平行样
            testTasks.forEach(testTask -> {
                List<SysMethodRepeatType> sysMethodRepeatTypes = sysMethodRepeatTypeMapper.selectByMainId(testTask.getMethodId());
                if (sysMethodRepeatTypes.size() > 0) {
                    mrtsForReport.set(sysMethodRepeatTypes);
                }
            });
            //初次生成报告
            if (reportMapper.selectBySampleId(sample.getId()).size() == 0) {

                if(mrtsForReport.get().size() > 0){
                    for (int i = 0; i < mrtsForReport.get().size(); i++) {
                        Report report = new Report();
                        report.setTemplateId("1925447702706597889");
                        report.setReportNo(sample.getSampleNo()+"-"+(i+1));
                        report.setOrderId(bizOrder.getId());
                        report.setSampleId(sample.getId());
//                        if(sample.getRdId() != null){
//                            RdProject rdProject = rdProjectMapper.selectById(sample.getRdId());
//                            report.setReportNo(rdProject.getRdNo());
//                        }else{
//                            if(sample.getSampleNo() != null ) {
//                                report.setReportNo(sample.getSampleNo());
//                            }
//                        }
//                        report.setReportNo(sample.getSampleNo());
                        report.setRepeatTypeId(mrtsForReport.get().get(i).getId());
                        int finalI = i;
                        report.setTestTaskIds(testTaskMapper.selectBySampleId(sample.getId()).stream().filter(testTask -> testTask.getRepeatTypeId().equals(mrtsForReport.get().get(finalI).getId())).map(TestTask::getId).reduce((a, b) -> a + "," + b).orElse(null));
                        if(sample.getDayType() != null && sample.getDayType().equals("工作日")){
                            Date workDate = iHolidayCalendarService.getWorkDate(sample.getReceiveDate(), sample.getPmLeadTime());
                            report.setDeadLine(workDate);
                        }else{
                            report.setDeadLine(new Date(sample.getReceiveDate().getTime() + sample.getPmLeadTime() * 24 * 60 * 60 * 1000));
                        }

                        reportMapper.insert(report);
                    }
                }else{
                    Report report = new Report();
                    report.setTemplateId("1925447702706597889");
                    report.setReportNo(sample.getSampleNo());
                    report.setOrderId(bizOrder.getId());
                    report.setSampleId(sample.getId());
                    report.setTestTaskIds(testTaskMapper.selectBySampleId(sample.getId()).stream().map(TestTask::getId).reduce((a, b) -> a + "," + b).get());
                    //sample.getReceiveDate() + 7days
                    if(bizOrder.getDayType() != null && bizOrder.getDayType().equals("工作日")){
                        Date workDate = iHolidayCalendarService.getWorkDate(sample.getReceiveDate(), Integer.parseInt(bizOrder.getLeadTime()));
                        report.setDeadLine(workDate);
                    }else{
                        report.setDeadLine(new Date(sample.getReceiveDate().getTime() + Integer.parseInt(bizOrder.getLeadTime()) * 24 * 60 * 60 * 1000));
                    }
                    reportMapper.insert(report);
                }
            }
        });



    }

    private List<WxCpOaApplyEventRequest.Approver> getApprovers() {
        ArrayList<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>() {{
            add(new WxCpOaApplyEventRequest.Approver().setAttr(1).setUserIds(new String[]{"xiaoxuebin","wuxianzheng","wangjiujun"}));
        }};
        return approvers;

    }

    private List<SummaryInfo> getSummaryList(String text1, String text2, String text3) {
        return new ArrayList<SummaryInfo>() {{
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text1))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text2))));
            add(new SummaryInfo().setSummaryInfoData(Collections.singletonList(new SummaryInfo.SummaryInfoData().setLang("zh_CN").setText(text3))));
        }};
    }

    private WxCpOaApplyEventRequest.ApplyData getApplyData(
            String no,
            String name,
            String customerName,
            String pmPrice,
            String applyPrice,
            String period
    ) {
        return new WxCpOaApplyEventRequest.ApplyData()
                .setContents(Arrays.asList(
                        new ApplyDataContent().setControl("Text").setId("Text-1744869184796").setValue(new ContentValue().setText(customerName != null ? no : "默认订单号")),
                        new ApplyDataContent().setControl("Textarea").setId("Textarea-1744869213123").setValue(new ContentValue().setText(customerName != null ? name : "默认订单名称")),
                        new ApplyDataContent().setControl("Text").setId("Text-1640339319582").setValue(new ContentValue().setText(customerName != null ? customerName : "默认客户名称")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868258243").setValue(new ContentValue().setText(pmPrice != null ? pmPrice : "默认PM金额")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868242390").setValue(new ContentValue().setText(applyPrice != null ? applyPrice : "默认申请金额")),
                        new ApplyDataContent().setControl("Text").setId("Text-1744868261172").setValue(new ContentValue().setText(period != null ? period : "默认工期")),
                        new ApplyDataContent().setControl("Tips").setId("Tips-1732010198545")
                ));
    }


    //    @Test
    public void updateTemplateTest(WxCpOaService oaService,WxCpOaApprovalTemplateResult templateDetail,String templateid,String tipsid,String title,String url,ApplyType applyType) throws WxErrorException {
        System.out.println(templateDetail.toJson());
        List<WxCpOaApprovalTemplateResult.TemplateControls> list = templateDetail.getTemplateContent().getControls().stream().filter(control -> control.getProperty().getId().equals(tipsid)).toList();
        if (list.size()>0){
            WxCpOaApprovalTemplateResult.TemplateControls templateControls = list.get(0);
            templateControls.getConfig().getTips().getTipsContent().get(0).getText().getSubText().get(1).getContent().getLink().setUrl(url);
            templateControls.getConfig().getTips().getTipsContent().get(0).getText().getSubText().get(1).getContent().getLink().setTitle(title);
        }
        TemplateContent templateContent = new TemplateContent();
        List<TemplateControls> templateControlsList = new ArrayList<>();

        templateDetail.getTemplateContent().getControls().forEach(control -> {
            //control转成json字符串,再转成control对象
            String controlJson = JSONObject.toJSONString(control);
            TemplateControls templateControls = JSONObject.parseObject(controlJson, TemplateControls.class);
            templateControlsList.add(templateControls);
        });
        templateContent.setControls(templateControlsList);
        templateDetail.getTemplateNames().get(0).setText(applyType.getType());
        WxCpOaApprovalTemplate wxCpOaApprovalTemplate = new WxCpOaApprovalTemplate().setTemplateId(templateid).setTemplateContent(templateContent).setTemplateName(templateDetail.getTemplateNames());
        oaService.updateOaApprovalTemplate(wxCpOaApprovalTemplate);
    }

    public void wxCallback(WxCpXmlMessage wxCpXmlMessage, String id, String typeId) {
        BizOrder order = this.baseMapper.selectById(id);
        if (wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.ORDRER_APPLY.getType()) && wxCpXmlMessage.getEvent().equals("sys_approval_change")) {
            // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付

            order.setStatusId(wxCpXmlMessage.getApprovalInfo().getSpStatus().toString());
            List<Sample> samples = sampleMapper.selectByQuotationId(order.getQuotationId());
            for (Sample sample : samples) {
                sample.setOrderId(order.getId());
                sampleMapper.updateById(sample);
            }
            updateById(order);

        }
        if (wxCpXmlMessage.getApprovalInfo().getSpName().contains(ApplyType.ORDRER_UNLOCK.getType()) && wxCpXmlMessage.getEvent().equals("sys_approval_change")) {
            if(wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("2")) {


                order.setIsLocked(0);
                order.setStatusId("0");
                updateById(order);
            }
        }

    }
}
