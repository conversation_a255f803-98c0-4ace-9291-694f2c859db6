package org.jeecg.modules.crm.service;

import org.jeecg.modules.crm.entity.SysCustomerContact;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 客户联系人
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
public interface ISysCustomerContactService extends IService<SysCustomerContact> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysCustomerContact>
	 */
	public List<SysCustomerContact> selectByMainId(String mainId);
}
