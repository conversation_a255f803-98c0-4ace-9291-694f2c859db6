package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.Test;
import org.jeecg.modules.lims_core.entity.TestInstrument;
import org.jeecg.modules.lims_core.service.ITestInstrumentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.service.ITestService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 仪器使用记录
 * @Author: jeecg-boot
 * @Date:   2025-02-13
 * @Version: V1.0
 */
@Tag(name="仪器使用记录")
@RestController
@RequestMapping("/lims_core/testInstrument")
@Slf4j
public class TestInstrumentController extends JeecgController<TestInstrument, ITestInstrumentService> {
	@Autowired
	private ITestInstrumentService testInstrumentService;
	 @Autowired
	 private ITestService testService;
	/**
	 * 分页列表查询
	 *
	 * @param testInstrument
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "仪器使用记录-分页列表查询")
	@Operation(summary="仪器使用记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestInstrument>> queryPageList(TestInstrument testInstrument,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("statusId", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<TestInstrument> queryWrapper = QueryGenerator.initQueryWrapper(testInstrument, req.getParameterMap(),customeRuleMap);
		Page<TestInstrument> page = new Page<TestInstrument>(pageNo, pageSize);
		IPage<TestInstrument> pageList = testInstrumentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param testInstrument
	 * @return
	 */
	@AutoLog(value = "仪器使用记录-添加")
	@Operation(summary="仪器使用记录-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:test_instrument:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestInstrument testInstrument) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testInstrument.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test = testService.getOne(qwTest);
		if (test != null){
			testInstrument.setTestId(test.getId());
		}
		testInstrumentService.save(testInstrument);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param testInstrument
	 * @return
	 */
	@AutoLog(value = "仪器使用记录-编辑")
	@Operation(summary="仪器使用记录-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_instrument:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestInstrument testInstrument) {
		QueryWrapper<Test> qwTest= new QueryWrapper<>();
		qwTest.eq("task_id", testInstrument.getTestId());
		qwTest.eq("test_type_id", "0");
		Test test = testService.getOne(qwTest);
		if (test != null){
			testInstrument.setTestId(test.getId());
		}
		testInstrumentService.updateById(testInstrument);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "仪器使用记录-通过id删除")
	@Operation(summary="仪器使用记录-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_instrument:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testInstrumentService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "仪器使用记录-批量删除")
	@Operation(summary="仪器使用记录-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_instrument:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testInstrumentService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "仪器使用记录-通过id查询")
	@Operation(summary="仪器使用记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestInstrument> queryById(@RequestParam(name="id",required=true) String id) {
		TestInstrument testInstrument = testInstrumentService.getById(id);
		if(testInstrument==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testInstrument);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testInstrument
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_instrument:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestInstrument testInstrument) {
        return super.exportXls(request, testInstrument, TestInstrument.class, "仪器使用记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_instrument:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestInstrument.class);
    }

}
