package org.jeecg.modules.lims_order.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 商机
 * @Author: jeecg-boot
 * @Date:   2025-04-18
 * @Version: V1.0
 */
@Data
@TableName("opportunity")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="商机")
public class Opportunity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
    /**编号*/
    @Excel(name = "编号", width = 15)
    @Schema(description = "编号")
    private java.lang.String opportunityNo;
	/**商机名称*/
	@Excel(name = "商机名称", width = 15)
    @Schema(description = "商机名称")
    private String name;
	/**负责人*/
	@Excel(name = "负责人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "负责人")
    private String responsiblePerson;
	/**客户名称*/
	@Excel(name = "客户名称", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户名称")
    private String customerId;
	/**结单日期*/
	@Excel(name = "结单日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "结单日期")
    private Date endDate;
	/**客户联系人姓名*/
	@Excel(name = "客户联系人姓名", width = 15, dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
	@Dict(dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Schema(description = "客户联系人姓名")
    private String customerContactId;
	/**客户联系人电话*/
	@Excel(name = "客户联系人电话", width = 15)
    @Schema(description = "客户联系人电话")
    private String customerContactPhone;
	/**业务分类*/
    @Excel(name = "业务类别", width = 15, dicCode = "biz_type")
    @Dict(dictTable = "biz_type", dicText = "name", dicCode = "id")
    @Schema(description = "业务类别")
    private java.lang.String bizTypeId;
	/**商机来源*/
	@Excel(name = "商机来源", width = 15, dicCode = "opportunity_source")
	@Dict(dicCode = "opportunity_source")
    @Schema(description = "商机来源")
    private String opportunitySource;
	/**源线索*/
	@Excel(name = "源线索", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "源线索")
    private String sourceBy;
	/**商机阶段*/
	@Excel(name = "商机阶段", width = 15, dicCode = "opportunity_stage")
	@Dict(dicCode = "opportunity_stage")
    @Schema(description = "商机阶段")
    private String opportunityStage;
	/**商机金额*/
	@Excel(name = "商机金额", width = 15)
    @Schema(description = "商机金额")
    private BigDecimal amount;
	/**赢率*/
	@Excel(name = "赢率", width = 15)
    @Schema(description = "赢率")
    private Integer winRate;
	/**预测金额*/
	@Excel(name = "预测金额", width = 15)
    @Schema(description = "预测金额")
    private BigDecimal estimatedAmount;
	/**重要程度*/
	@Excel(name = "重要程度", width = 15, dicCode = "importance_level")
	@Dict(dicCode = "importance_level")
    @Schema(description = "重要程度")
    private String importanceLevel;
	/**决策人*/
	@Excel(name = "决策人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
	@Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "决策人")
    private String decisionMaker;
	/**竞争对手*/
	@Excel(name = "竞争对手", width = 15)
    @Schema(description = "竞争对手")
    private String competitor;
	/**销售流程*/
	@Excel(name = "销售流程", width = 15)
    @Schema(description = "销售流程")
    private String salesProcess;
}
