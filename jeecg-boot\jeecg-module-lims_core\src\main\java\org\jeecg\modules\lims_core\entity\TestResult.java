package org.jeecg.modules.lims_core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @Description: 检测结果
 * @Author: jeecg-boot
 * @Date:   2025-05-29
 * @Version: V1.0
 */
@Data
@TableName("test_result")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="检测结果")
public class TestResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
    /**标本ID*/
    @Excel(name = "标本ID", width = 15)
    @Schema(description = "标本ID")
    private String testId;
    /**指标*/
    @Excel(name = "指标", width = 15)
    @Schema(description = "指标")
    private String methodAnalyteId;
    /**标示量*/
    @Excel(name = "标示量", width = 15)
    @Schema(description = "标示量")
    private String labelResult;
    /**原始结果*/
    @Excel(name = "原始结果", width = 15)
    @Schema(description = "原始结果")
    private String rawResult;
	/**信噪比*/
	@Excel(name = "信噪比", width = 15)
    @Schema(description = "信噪比")
    private java.lang.String peakSnr;
	/**分离度*/
	@Excel(name = "分离度", width = 15)
    @Schema(description = "分离度")
    private java.lang.String peakResolution;
	/**保留时间*/
	@Excel(name = "保留时间", width = 15)
    @Schema(description = "保留时间")
    private java.lang.String retentionTime;
	/**谱图*/
	@Excel(name = "谱图", width = 15)
    @Schema(description = "谱图")
    private java.lang.String photogram;
	/**平行结果*/
	@Excel(name = "平行结果", width = 15)
    @Schema(description = "平行结果")
    private String dupResult;
    @Excel(name = "精密度", width = 15)
    @Schema(description = "精密度")
    private String resultPrecision;
    /**报告结果*/
    @Excel(name = "报告结果", width = 15)
    @Schema(description = "报告结果")
    private String repResult;
    /**评价要求*/
    @Dict(dictTable = "sys_standard_evaluation_limt", dicText = "elimit", dicCode = "id")
    @Excel(name = "评价要求", width = 15)
    @Schema(description = "评价要求")
    private String limitId;
    /**评价要求*/
    @Excel(name = "评价要求", width = 15)
    @Schema(description = "评价要求")
    private String repLimit;
    /**结论*/
    @Excel(name = "结论", width = 15)
    @Schema(description = "结论")
    private String conclusion;
	/**是否出在报告上*/
	@Excel(name = "是否出在报告上", width = 15, dicCode = "logic_name")
	@Dict(dicCode = "logic_name")
    @Schema(description = "是否出在报告上")
    private String reportable;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;

    @TableField(exist = false)
    private String analyte;
}
