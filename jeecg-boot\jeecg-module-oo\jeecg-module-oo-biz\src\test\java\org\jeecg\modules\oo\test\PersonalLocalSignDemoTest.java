//package org.jeecg.modules.oo.test;
//
//import junit.framework.TestCase;
//import net.qiyuesuo.sdk.SDKClient;
//import net.qiyuesuo.sdk.bean.contract.StamperType;
//import net.qiyuesuo.sdk.bean.seal.PersonSealInfoRequest;
//import net.qiyuesuo.sdk.bean.sign.local.LocalSignFileStream;
//import net.qiyuesuo.sdk.bean.sign.local.LocalSignStamper;
//import net.qiyuesuo.sdk.bean.sign.local.request.PersonalLocalSignRequest;
//import net.qiyuesuo.sdk.bean.sign.local.result.LocalSignResult;
//import net.qiyuesuo.sdk.bean.user.UserInfoRequest;
//import net.qiyuesuo.sdk.impl.local.service.PersonalLocalSignService;
//import org.apache.commons.lang3.BooleanUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.File;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.UUID;
//import java.util.function.Supplier;
//
//
///**
// * PersonalLocalSignDemoTest
// *
// * <AUTHOR>
// */
//public class PersonalLocalSignDemoTest extends TestCase {
//
//    private final Logger logger = LoggerFactory.getLogger(PersonalLocalSignDemoTest.class);
//
//    PersonalLocalSignService personalLocalSignService;
//    SDKClient sdkClient;
//
//    // 输出文件地址
//    private final String dirPath = "/Users/<USER>/Documents/work/qys/docu/";
//    // 默认值印章ID
//    private Long sealId = 3312767252107161840L;
//
//    @Override
//    public void setUp() {
//        // 初始化开放平台SDK相关参数
//        String url = "http://open201.qiyuesuo.net/";
//        String accessKey = "ZhaoShuaiA";
//        String accessSecret = "4P2nOrpBC3SjeRKEFqtPmAJLRtVmFB";
//        sdkClient = new SDKClient(url, accessKey, accessSecret);
//
//        personalLocalSignService = new PersonalLocalSignService(sdkClient);
//    }
//
//    public void testSign() throws Exception {
//
//        LocalSignFileStream pdf2 = getFileStream("pdf2.pdf", "file.pdf");
//
//        // 签署位置
//        List<LocalSignStamper> stampers = getStampers();
//
//        PersonalLocalSignRequest localSignRequest = new PersonalLocalSignRequest("我是文件主题", pdf2, stampers);
//
//        // 操作人
//        setOperator(localSignRequest);
//
//        // 印章
//        setSealRequest(localSignRequest);
//
//
//        // 设置耗时统计
//        localSignRequest.setEnableWatch(true);
//        LocalSignResult sign = personalLocalSignService.sign(localSignRequest);
//
//        logger.info("Sdk 本地签署结果：\n{}", sign);
//        if (BooleanUtils.isTrue(localSignRequest.getEnableWatch())) {
//            logger.debug("sdk本地签耗时：\n{}", sign.getPrettyPrint());
//        }
//
//    }
//
//
//    private LocalSignFileStream getFileStream(String fileName, String filePath) throws IOException {
//        return new LocalSignFileStream(fileName,
//                Files.newInputStream(new File(dirPath.concat(filePath)).toPath()),
//                Files.newOutputStream(Paths.get(String.format(dirPath + "signed-pdf/%s-%s.pdf", fileName, UUID.randomUUID()))));
//    }
//
//    private void setOperator(PersonalLocalSignRequest localSignRequest) {
//        UserInfoRequest userInfoRequest = new UserInfoRequest();
//        userInfoRequest.setContact("11000000000");
//        localSignRequest.setOperator(userInfoRequest);
//    }
//
//    private void setSealRequest(PersonalLocalSignRequest localSignRequest) {
//        PersonSealInfoRequest personalLocalSignRequest = new PersonSealInfoRequest();
////        personalLocalSignRequest.setSealIds(sealId + "");
//        personalLocalSignRequest.setUser(localSignRequest.getOperator());
//        personalLocalSignRequest.setPersonSealCarrier("PERSON_SIGN");
//        localSignRequest.setSealRequest(personalLocalSignRequest);
//    }
//
//    private List<LocalSignStamper> getStampers() {
//        // 设置Pdf2 的签署位置
//        return new LinkedList<>(getPdf2Stamper());
//    }
//
//
//    private List<LocalSignStamper> getPdf2Stamper() {
//        List<LocalSignStamper> signStampers = new LinkedList<>();
//
//        String fileName = "pdf2";
//
//        // 正常的坐标定位
//        Supplier<LocalSignStamper> normalCoordinates = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_PERSONAL);
//            localSignStamper.setPage(0);
//            localSignStamper.setOffsetX(0.5D);
//            localSignStamper.setOffsetY(0.08D);
//            //签名位置旋转
//            localSignStamper.setRotateDegree(50);
//            localSignStamper.setWidth(120.0F);
////            localSignStamper.setHeight(120.0F);
//            return localSignStamper;
//        };
//
//        // 正常的页面倒数坐标定位
//        Supplier<LocalSignStamper> reciprocalCoordinates = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_PERSONAL);
//            localSignStamper.setPage(-1);
//            localSignStamper.setOffsetX(0.1D);
//            localSignStamper.setOffsetY(0.08D);
////            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        // 缩放坐标位置
//        Supplier<LocalSignStamper> zoomCoordinates = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_PERSONAL);
//            localSignStamper.setPage(1);
//            localSignStamper.setOffsetX(0.2D);
//            localSignStamper.setOffsetY(0.3D);
//            localSignStamper.setSealId(sealId);
//            localSignStamper.setWidthZoom(0.01f);
////            localSignStamper.setHeightZoom(1.3f);
//            return localSignStamper;
//        };
//
//        // 缩放坐标位置
//        Supplier<LocalSignStamper> zoomCoordinates2 = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_PERSONAL);
//            localSignStamper.setPage(1);
//            localSignStamper.setOffsetX(0.4D);
//            localSignStamper.setOffsetY(0.3D);
//            localSignStamper.setSealId(sealId);
//            localSignStamper.setRotateDegree(20f);
//            return localSignStamper;
//        };
//
//        // 缩放坐标位置
//        Supplier<LocalSignStamper> zoomCoordinates3 = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_PERSONAL);
//            localSignStamper.setPage(1);
//            localSignStamper.setOffsetX(0.6D);
//            localSignStamper.setOffsetY(0.3D);
//            localSignStamper.setSealId(sealId);
//            localSignStamper.setWidthZoom(0.1f);
//            return localSignStamper;
//        };
//
//
//        Supplier<LocalSignStamper> timestamp = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.TIMESTAMP);
//            localSignStamper.setPage(1);
//            localSignStamper.setWidth(40f);
//            localSignStamper.setOffsetX(0.2D);
//            localSignStamper.setOffsetY(0.99D);
//            return localSignStamper;
//        };
//
//        // 正常的关键字定位。PRIORITY 模式 ，只能匹配2个
//        Supplier<LocalSignStamper> normalPriorityKeyword = () -> {
//            LocalSignStamper localSignStamper = new LocalSignStamper();
//            localSignStamper.setFileName(fileName);
//            localSignStamper.setType(StamperType.SEAL_PERSONAL);
//            List<String> bestKeyword2 = new ArrayList<>();
////            bestKeyword2.add("111");
//            localSignStamper.setKeywordIndex(-10);
////            localSignStamper.setKeywordMatchRule(KeywordMatchRule.PRIORITY);
//            localSignStamper.setBestKeyWords(bestKeyword2);
//            localSignStamper.setOffsetX(0.0D);
//            localSignStamper.setOffsetY(0.0D);
//            localSignStamper.setSealId(sealId);
//            return localSignStamper;
//        };
//
//        signStampers.add(normalCoordinates.get());
////        signStampers.add(zoomCoordinates.get());
////        signStampers.add(zoomCoordinates2.get());
////        signStampers.add(zoomCoordinates3.get());
//        signStampers.add(normalPriorityKeyword.get());
////        signStampers.add(reciprocalCoordinates.get());
////        signStampers.add(timestamp.get());
//
//        return signStampers;
//    }
//
//}