package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import org.jeecg.modules.lims_core.entity.Consumptive;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.vo.ConsumptiveVO;

/**
 * @Description: 耗材台账
 * @Author: jeecg-boot
 * @Date:   2025-03-06
 * @Version: V1.0
 */
public interface IConsumptiveService extends IService<Consumptive> {
    public IPage<ConsumptiveVO> queryPageList(Page<Consumptive> page,
                                              @Param(Constants.WRAPPER) Wrapper<Consumptive> wrapper);
}
