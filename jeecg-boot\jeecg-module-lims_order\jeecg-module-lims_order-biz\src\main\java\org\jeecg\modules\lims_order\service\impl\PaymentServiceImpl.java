package org.jeecg.modules.lims_order.service.impl;

import org.jeecg.modules.lims_order.entity.Payment;
import org.jeecg.modules.lims_order.entity.PaymentCollection;
import org.jeecg.modules.lims_order.mapper.PaymentCollectionMapper;
import org.jeecg.modules.lims_order.mapper.PaymentMapper;
import org.jeecg.modules.lims_order.service.IPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 回款
 * @Author: jeecg-boot
 * @Date:   2025-03-31
 * @Version: V1.0
 */
@Service
public class PaymentServiceImpl extends ServiceImpl<PaymentMapper, Payment> implements IPaymentService {

    @Autowired
    private PaymentCollectionMapper paymentCollectionMapper;

    @Override
    public void savePayment(Payment payment) {
        this.save(payment);
        PaymentCollection paymentCollection = paymentCollectionMapper.selectById(payment.getPaymentCollectionId());
        if(paymentCollection.getPlanAmount() < paymentCollection.getActualAmount() + payment.getAmount()){
           throw new RuntimeException("实际回款金额不能大于计划回款金额");
        }
        paymentCollection.setActualAmount(paymentCollection.getActualAmount() + payment.getAmount());
        paymentCollectionMapper.updateById(paymentCollection);
    }

    @Override
    public void editPayment(Payment payment) {
        Payment oldPayment = this.getById(payment.getId());
        PaymentCollection paymentCollection = paymentCollectionMapper.selectById(payment.getPaymentCollectionId());
        if(paymentCollection.getPlanAmount() < paymentCollection.getActualAmount() + payment.getAmount() - oldPayment.getAmount()){
            throw new RuntimeException("实际回款金额不能大于计划回款金额");
        }
        paymentCollection.setActualAmount(paymentCollection.getActualAmount() + payment.getAmount() - oldPayment.getAmount());
        paymentCollectionMapper.updateById(paymentCollection);
        this.updateById(payment);


    }

    @Override
    public void deletePayment(String id) {
        Payment oldPayment = this.getById(id);
        PaymentCollection paymentCollection = paymentCollectionMapper.selectById(oldPayment.getPaymentCollectionId());
        paymentCollection.setActualAmount(paymentCollection.getActualAmount() - oldPayment.getAmount());
        paymentCollectionMapper.updateById(paymentCollection);
        this.removeById(id);
    }

    @Override
    public void deletePayments(List<String> list) {
        list.forEach( id -> {
            deletePayment(id);
        });
    }
}
