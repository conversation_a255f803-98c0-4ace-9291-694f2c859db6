package org.jeecg.common.system.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface TemplateField {
    String drillChain() default "";
    String entityFieldName() default "";
    String dict() default "";
    String dictTable() default "";
    String dictKey() default "";
    String dictText() default "";
    String calcExpr() default "";
    String description() default "";
    String listMembers() default "";
    String func() default "";
}