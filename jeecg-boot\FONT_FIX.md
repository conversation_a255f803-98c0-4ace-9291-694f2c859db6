# iText7字体问题修复

## 🎯 问题描述

切换回iText7后出现字体错误：
```
com.itextpdf.io.exceptions.IOException: Type of font STSong-Light is not recognized.
```

## 🔧 解决方案

### 问题原因
`STSong-Light`字体在系统中不可用，需要使用系统中存在的中文字体。

### 修复方法

#### 1. 创建智能字体选择方法
```java
private PdfFont createChineseFont() {
    try {
        // 尝试多种中文字体，按优先级顺序
        String[] fontNames = {
            "SimSun",           // 宋体
            "Microsoft YaHei",  // 微软雅黑
            "SimHei",           // 黑体
            "KaiTi",            // 楷体
            "FangSong"          // 仿宋
        };
        
        for (String fontName : fontNames) {
            try {
                return PdfFontFactory.createFont(fontName, "Identity-H", PdfFontFactory.EmbeddingStrategy.PREFER_EMBEDDED);
            } catch (Exception e) {
                // 继续尝试下一个字体
                System.out.println("字体 " + fontName + " 不可用，尝试下一个...");
            }
        }
        
        // 如果所有中文字体都不可用，使用默认字体
        System.out.println("所有中文字体都不可用，使用默认字体");
        return PdfFontFactory.createFont();
        
    } catch (Exception e) {
        System.err.println("创建字体失败: " + e.getMessage());
        try {
            return PdfFontFactory.createFont();
        } catch (Exception ex) {
            throw new RuntimeException("无法创建任何字体", ex);
        }
    }
}
```

#### 2. 修改字体使用
```java
// 修改前
.setFont(PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H"))

// 修改后
.setFont(createChineseFont())
```

## 💡 技术优势

### 1. 智能字体选择
- 按优先级尝试多种中文字体
- 自动降级到可用字体
- 最终降级到系统默认字体

### 2. 错误处理
- 每个字体尝试都有异常处理
- 提供详细的日志信息
- 确保程序不会因字体问题崩溃

### 3. 跨平台兼容
- Windows系统：SimSun、Microsoft YaHei等
- 其他系统：自动降级到默认字体

## 🚀 测试步骤

### 1. 重新编译
```bash
mvn clean compile
```

### 2. 运行签名
应该看到类似日志：
```
字体 SimSun 不可用，尝试下一个...
字体 Microsoft YaHei 不可用，尝试下一个...
所有中文字体都不可用，使用默认字体
```

或者：
```
（没有字体错误日志，说明找到了可用的中文字体）
```

### 3. 验证PDF
- PDF应该能正常生成
- 中文文字应该正常显示

## 🔍 其他字体解决方案

### 方案1：使用内嵌字体文件
```java
// 如果有字体文件
return PdfFontFactory.createFont("path/to/font.ttf", "Identity-H");
```

### 方案2：使用Base14字体（英文）
```java
// 只适用于英文
return PdfFontFactory.createFont(StandardFonts.HELVETICA);
```

### 方案3：完全移除中文文字
```java
// 如果不需要中文，可以改为英文
Paragraph p = new Paragraph("Digitally Signed Document")
```

## 🎯 总结

**字体问题已修复**：

1. ✅ **智能字体选择**：自动尝试多种中文字体
2. ✅ **错误处理**：确保程序不会崩溃
3. ✅ **跨平台兼容**：适应不同系统环境
4. ✅ **降级策略**：最终使用默认字体

**现在应该可以正常运行iText7版本了！**

请测试并告诉我结果。如果还有其他问题，我们继续解决。
