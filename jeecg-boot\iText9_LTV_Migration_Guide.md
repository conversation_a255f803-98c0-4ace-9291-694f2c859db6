# iText 7 到 iText 9.2 LTV迁移指南

## 问题根源

从iText 7升级到iText 9.2后，LTV功能失效的主要原因：

### 1. API重大变化
- `LtvVerification` API完全重写
- DSS字典构建方式改变
- 签名验证流程调整

### 2. 默认行为变化
- iText 9.2对LTV验证更加严格
- Adobe Reader兼容性要求更高
- 证书链处理逻辑改变

### 3. 依赖库变化
- BouncyCastle版本要求更新
- 某些内部类不再可用
- 异常处理机制改变

## iText 7 vs iText 9.2 关键差异

### LtvVerification API变化

**iText 7:**
```java
LtvVerification ltv = new LtvVerification(pdfDoc);
ltv.addVerification(signatureName, ocspClient, crlClient, 
    LtvVerification.CertificateOption.WHOLE_CHAIN, 
    LtvVerification.Level.OCSP_CRL, 
    LtvVerification.CertificateInclusion.YES);
ltv.merge();
```

**iText 9.2 (需要修复):**
```java
// 新的API需要不同的参数和处理方式
LtvVerification ltv = new LtvVerification(pdfDoc);
// 参数顺序和类型可能已改变
ltv.addVerification(signatureName, ocspClient, crlClient, 
    LtvVerification.CertificateOption.WHOLE_CHAIN, 
    LtvVerification.Level.OCSP_CRL, 
    LtvVerification.CertificateInclusion.YES);
ltv.merge();
```

### DSS字典构建差异

**iText 7:**
- 自动处理大部分LTV元数据
- 默认的Adobe Reader兼容性较好

**iText 9.2:**
- 需要手动添加更多元数据
- 对Adobe Reader兼容性要求更严格
- 需要明确的Perms字典

## 针对iText 9.2的修复方案

### 1. 使用iText 9.2兼容的LTV实现

```java
/**
 * iText 9.2专用的LTV处理方法
 */
private void addLtvForItext9(PdfDocument pdfDocument) {
    try {
        // 1. 使用新的LtvVerification API
        LtvVerification ltvVerification = new LtvVerification(pdfDocument);
        
        SignatureUtil signatureUtil = new SignatureUtil(pdfDocument);
        List<String> signatureNames = signatureUtil.getSignatureNames();
        
        for (String name : signatureNames) {
            // 2. 获取签名数据
            PdfPKCS7 pdfPKCS7 = signatureUtil.readSignatureData(name, "BC");
            
            // 3. 创建OCSP和CRL客户端
            IOcspClient ocspClient = new OcspClientBouncyCastle(null);
            ICrlClient crlClient = new CrlClientOnline();
            
            // 4. 使用iText 9.2的新方法添加验证
            try {
                ltvVerification.addVerification(name, ocspClient, crlClient, 
                    LtvVerification.CertificateOption.WHOLE_CHAIN, 
                    LtvVerification.Level.OCSP_CRL, 
                    LtvVerification.CertificateInclusion.YES);
            } catch (Exception e) {
                // 如果失败，尝试只验证签名证书
                ltvVerification.addVerification(name, ocspClient, crlClient, 
                    LtvVerification.CertificateOption.SIGNING_CERTIFICATE, 
                    LtvVerification.Level.OCSP_CRL, 
                    LtvVerification.CertificateInclusion.YES);
            }
        }
        
        // 5. 合并验证数据
        ltvVerification.merge();
        
    } catch (Exception e) {
        System.err.println("iText 9.2 LTV处理失败: " + e.getMessage());
        // 回退到手动DSS构建
        buildManualDssForItext9(pdfDocument);
    }
}
```

### 2. 手动DSS字典构建（iText 9.2兼容）

```java
/**
 * 为iText 9.2手动构建DSS字典
 */
private void buildManualDssForItext9(PdfDocument pdfDocument) {
    try {
        PdfCatalog catalog = pdfDocument.getCatalog();
        
        // 1. 创建DSS字典
        PdfDictionary dss = new PdfDictionary();
        dss.put(PdfName.Type, new PdfName("DSS"));
        
        // 2. 收集所有验证数据
        ValidationDataCollector collector = new ValidationDataCollector();
        
        SignatureUtil signatureUtil = new SignatureUtil(pdfDocument);
        List<String> signatureNames = signatureUtil.getSignatureNames();
        
        for (String name : signatureNames) {
            PdfPKCS7 pdfPKCS7 = signatureUtil.readSignatureData(name, "BC");
            
            // 收集证书链
            Certificate[] chain = pdfPKCS7.getSignCertificateChain();
            for (Certificate cert : chain) {
                if (cert instanceof X509Certificate) {
                    collector.addCertificate((X509Certificate) cert);
                    
                    // 获取OCSP响应
                    byte[] ocspResponse = getOcspResponse((X509Certificate) cert);
                    if (ocspResponse != null) {
                        collector.addOcsp(ocspResponse);
                    }
                    
                    // 获取CRL
                    byte[] crl = getCrlResponse((X509Certificate) cert);
                    if (crl != null) {
                        collector.addCrl(crl);
                    }
                }
            }
        }
        
        // 3. 构建DSS数组
        buildDssArrays(dss, collector, pdfDocument);
        
        // 4. 构建VRI字典
        buildVriDictionary(dss, collector, signatureNames, pdfDocument);
        
        // 5. 添加到目录
        dss.makeIndirect(pdfDocument);
        catalog.put(PdfName.DSS, dss);
        
    } catch (Exception e) {
        System.err.println("手动DSS构建失败: " + e.getMessage());
    }
}
```

### 3. 增强的Adobe Reader兼容性（iText 9.2）

```java
/**
 * 为iText 9.2添加Adobe Reader兼容性
 */
private void addAdobeCompatibilityForItext9(PdfDocument pdfDocument) {
    PdfCatalog catalog = pdfDocument.getCatalog();
    
    // 1. 添加必需的PDF扩展
    catalog.addDeveloperExtension(PdfDeveloperExtension.ESIC_1_7_EXTENSIONLEVEL5);
    
    // 2. 手动添加Extensions字典
    PdfDictionary extensions = new PdfDictionary();
    
    // ADBE扩展
    PdfDictionary adbeExt = new PdfDictionary();
    adbeExt.put(PdfName.BaseVersion, new PdfName("1.7"));
    adbeExt.put(PdfName.ExtensionLevel, new PdfNumber(11));
    extensions.put(new PdfName("ADBE"), adbeExt);
    
    // ESIC扩展
    PdfDictionary esicExt = new PdfDictionary();
    esicExt.put(PdfName.BaseVersion, new PdfName("1.7"));
    esicExt.put(PdfName.ExtensionLevel, new PdfNumber(5));
    extensions.put(new PdfName("ESIC"), esicExt);
    
    catalog.put(PdfName.Extensions, extensions);
    
    // 3. 添加Perms字典（iText 9.2必需）
    PdfDictionary perms = new PdfDictionary();
    
    // LTV权限
    PdfDictionary ltvDict = new PdfDictionary();
    ltvDict.put(PdfName.Type, new PdfName("LTV"));
    ltvDict.put(new PdfName("Enabled"), PdfBoolean.TRUE);
    perms.put(new PdfName("LTV"), ltvDict);
    
    // DocMDP权限
    PdfDictionary docMDP = new PdfDictionary();
    docMDP.put(PdfName.Type, new PdfName("DocMDP"));
    docMDP.put(PdfName.P, new PdfNumber(2));
    docMDP.put(PdfName.V, new PdfName("1.2"));
    perms.put(new PdfName("DocMDP"), docMDP);
    
    catalog.put(PdfName.Perms, perms);
    
    // 4. 为签名添加LTV标记
    SignatureUtil signatureUtil = new SignatureUtil(pdfDocument);
    List<String> signatureNames = signatureUtil.getSignatureNames();
    
    for (String name : signatureNames) {
        PdfSignature signature = signatureUtil.getSignature(name);
        signature.put(new PdfName("LTV"), PdfBoolean.TRUE);
        signature.put(new PdfName("LTVEnabled"), PdfBoolean.TRUE);
        signature.setModified();
    }
}
```

## 立即修复步骤

### 1. 更新依赖版本
确保使用兼容的BouncyCastle版本：
```xml
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcprov-jdk18on</artifactId>
    <version>1.78.1</version>
</dependency>
```

### 2. 替换LTV处理方法
将现有的LTV处理替换为iText 9.2兼容版本。

### 3. 测试验证
使用Adobe Reader DC最新版本测试LTV功能。

## 预期结果

通过这些修复，iText 9.2应该能够：
- 正确生成Adobe Reader识别的LTV信息
- 兼容GDCA证书
- 通过Adobe Reader验证

如果问题仍然存在，可能需要考虑：
1. 降级到iText 7（临时方案）
2. 联系iText技术支持
3. 使用其他PDF签名库
