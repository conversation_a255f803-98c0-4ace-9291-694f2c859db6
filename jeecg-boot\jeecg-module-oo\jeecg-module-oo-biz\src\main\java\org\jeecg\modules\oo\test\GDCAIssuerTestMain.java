//package org.jeecg.modules.oo.test;
//
//import org.jeecg.modules.oo.service.impl.SignServiceImpl;
//import org.jeecg.modules.oo.util.AdobeLtvEnabling;
//
//import java.io.ByteArrayInputStream;
//import java.security.cert.CertificateFactory;
//import java.security.cert.X509Certificate;
//import java.util.Base64;
//
///**
// * GDCA证书颁发者获取测试
// * 测试修复后的getIssuerCertificate方法是否能正确处理GDCA证书
// */
//public class GDCAIssuerTestMain {
//
//    public static void main(String[] args) {
//        System.out.println("=== GDCA证书颁发者获取测试 ===");
//
//        try {
//            // 模拟GDCA证书数据（简化版本，仅用于测试）
//            String gdcaCertBase64 = createMockGDCACertificate();
//
//            // 解析证书
//            byte[] certBytes = Base64.getDecoder().decode(gdcaCertBase64);
//            CertificateFactory cf = CertificateFactory.getInstance("X.509");
//            X509Certificate gdcaCert = (X509Certificate) cf.generateCertificate(
//                new ByteArrayInputStream(certBytes)
//            );
//
//            System.out.println("证书主题: " + gdcaCert.getSubjectDN());
//            System.out.println("证书颁发者: " + gdcaCert.getIssuerDN());
//
//            // 测试SignServiceImpl中的getIssuerCertificate方法
//            System.out.println("\n--- 测试SignServiceImpl.getIssuerCertificate ---");
//            X509Certificate issuer1 = SignServiceImpl.getIssuerCertificate(gdcaCert);
//            if (issuer1 != null) {
//                System.out.println("✅ SignServiceImpl成功获取颁发者证书: " + issuer1.getSubjectDN());
//            } else {
//                System.out.println("❌ SignServiceImpl未能获取颁发者证书");
//            }
//
//            // 测试AdobeLtvEnabling中的getIssuerCertificate方法
//            System.out.println("\n--- 测试AdobeLtvEnabling.getIssuerCertificate ---");
//            X509Certificate issuer2 = AdobeLtvEnabling.getIssuerCertificate(gdcaCert);
//            if (issuer2 != null) {
//                System.out.println("✅ AdobeLtvEnabling成功获取颁发者证书: " + issuer2.getSubjectDN());
//            } else {
//                System.out.println("❌ AdobeLtvEnabling未能获取颁发者证书");
//            }
//
//            // 测试自签名证书检测
//            System.out.println("\n--- 测试自签名证书检测 ---");
//            boolean isSelfSigned = gdcaCert.getSubjectDN().equals(gdcaCert.getIssuerDN());
//            System.out.println("证书是否自签名: " + isSelfSigned);
//
//            if (isSelfSigned) {
//                System.out.println("✅ 这是根证书，无需获取颁发者证书");
//            } else {
//                System.out.println("⚠️ 这不是根证书，需要获取颁发者证书");
//            }
//
//        } catch (Exception e) {
//            System.err.println("测试过程中发生错误: " + e.getMessage());
//            e.printStackTrace();
//        }
//
//        System.out.println("\n=== 测试完成 ===");
//    }
//
//    /**
//     * 创建模拟的GDCA证书数据
//     * 注意：这是简化的测试数据，实际使用时应该使用真实的证书
//     */
//    private static String createMockGDCACertificate() {
//        // 这里返回一个简化的证书数据用于测试
//        // 实际应用中应该使用真实的GDCA证书
//        return "MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV" +
//               "BAYTAkNOMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX" +
//               "aWRnaXRzIFB0eSBMdGQwHhcNMTMwODI3MDQzNDQ3WhcNMjMwODI1MDQzNDQ3WjBF" +
//               "MQswCQYDVQQGEwJDTjETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50" +
//               "ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB" +
//               "CgKCAQEAwU2qD8BlNd3Q5ygVjpp8TBhO73zQQQOQmSbf5dzKcNi6b+fTADGiMVlP" +
//               "J4Q9ozp9CuywdTXxeBmodyktqvxCqzi58IjyX26tJsqoMKxDfn1Z2ZKXgB8EDFyX" +
//               "kJzCl2yVjI2BDVFfFLhTKNQqJpyoDA/RMhMDX2qlh2BdZllERHtaecAErxyt7qIB" +
//               "qqP7VS7fAqxBRBD7MrQQNg1QJbYDNrfQmfDFyt/MNd4f4zOX8dSFzU5wRqNDggHf" +
//               "RiAKfLshL1hoNwsa/C3ebFyYs/XZQd5Mhk6yMv2ietp9+B6RimCOYTyAQdpYnUoK" +
//               "G8EgVAlDzVGhuKQfEuGf9lM7BwIDAQABo1AwTjAdBgNVHQ4EFgQUhBjMhTTsvAyU" +
//               "lC4IWZzHshBOCggwHwYDVR0jBBgwFoAUhBjMhTTsvAyUlC4IWZzHshBOCggwDAYD" +
//               "VR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAeRDSDfCgVcsqBcnGfAaHep6Y" +
//               "MUJi9ZM3aBtqCg6xjPCP4PcFYQr0LyMmrMhpDKBHMIBMTh+H77Fh5qMxkjXNpbhx" +
//               "VYB/SLcYUeqgCpGqekSQQjqE5ADYE8CelHcTkJP5+BnZAjVDdMGQKhkqjKLKZKkJ" +
//               "qGFGOxGdt0QDpoQU5C4IfcMcAXrz4gVgKFA2VRISlz0x8dfRoADCOQkuSTV5cSwJ" +
//               "YFNffuLiAoFVzMcKJ2Fh/7kWJKtQBHEfNjfnEFP7VQ7VVB1Hfz4EjXyRlN5qGdlG" +
//               "qdHRWLfVOaI5nINzuwHBQMqMnsGrXTVb+yRlNgVpx5TLPiXbxUKqhfHGJg==";
//    }
//}