package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.dto.MethodNeedMakeupModel;
import org.jeecg.modules.lims_core.entity.Solution;
import org.jeecg.modules.lims_core.entity.Test;
import org.jeecg.modules.lims_core.entity.TestSolution;
import org.jeecg.modules.lims_core.service.ISolutionService;
import org.jeecg.modules.lims_core.service.ITestService;
import org.jeecg.modules.lims_core.service.ITestSolutionService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 溶液使用记录
 * @Author: jeecg-boot
 * @Date:   2025-03-07
 * @Version: V1.0
 */
@Tag(name="溶液使用记录")
@RestController
@RequestMapping("/lims_core/testSolution")
@Slf4j
public class TestSolutionController extends JeecgController<TestSolution, ITestSolutionService> {
	@Autowired
	private ITestSolutionService testSolutionService;
	@Autowired
	private ISolutionService solutionService;
	 @Autowired
	 private ITestService testService;
	/**
	 * 分页列表查询
	 *
	 * @param testSolution
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "溶液使用记录-分页列表查询")
	@Operation(summary="溶液使用记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestSolution>> queryPageList(TestSolution testSolution,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("solutionId", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<TestSolution> queryWrapper = QueryGenerator.initQueryWrapper(testSolution, req.getParameterMap(),customeRuleMap);
		Page<TestSolution> page = new Page<TestSolution>(pageNo, pageSize);
		IPage<TestSolution> pageList = testSolutionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	 /**
	  * 通测试ID查询方法耗材和方法标品中需要配置的
	  *
	  * @param testId
	  * @return
	  */
	 //@AutoLog(value = "通测试ID查询方法耗材和方法标品中需要配置的")
	 @Operation(summary="溶液-通测试ID查询方法耗材和方法标品中需要配置的")
	 @GetMapping(value = "/queryMethodNeedMakeupByTestId")
	 public Result<List<MethodNeedMakeupModel>> queryMethodNeedMakeupListByTestId(@RequestParam(name="testId",required=true) String testId) {
		 QueryWrapper<Test> qwTest= new QueryWrapper<>();
		 qwTest.eq("task_id", testId);
		 qwTest.eq("test_type_id", "0");
		 Test test1 = testService.getOne(qwTest);
		 if (test1 != null){
			 testId=test1.getId();
		 }//如果是taskid 就转换成testid,兼容逻辑
		 List<MethodNeedMakeupModel> List = testSolutionService.selectMethodNeedMakeupByTestId(testId);//main.getId()
		 return Result.OK(List);
	 }
	/**
	 *   添加
	 *
	 * @param testSolution
	 * @return
	 */
	@AutoLog(value = "溶液使用记录-添加")
	@Operation(summary="溶液使用记录-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:test_solution:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestSolution testSolution) {
		testSolution.setCreateTime(DateUtil.date());
		LambdaQueryWrapper<TestSolution> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.and(item->item.eq(TestSolution::getSolutionId,testSolution.getSolutionId()).eq(
				TestSolution::getTestTaskId,testSolution.getTestTaskId()));
		TestSolution testSolution1 = testSolutionService.getOne(queryWrapper);
        if (testSolution1 != null){
			return Result.error("添加失败，存在重复数据！");
		}
		//查询是否已审核，只有审核的才能用
		LambdaQueryWrapper<Solution> SolutionWrapper = new LambdaQueryWrapper<>();
		SolutionWrapper.and(item->item.eq(Solution::getId,testSolution.getSolutionId()));
		Solution Solution = solutionService.getOne(SolutionWrapper);
		if (!Solution.getStatus().equals("solutionAlreadyPassed")){
			return Result.error("添加失败，请先审核溶液！");
		}
		testSolutionService.save(testSolution);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param testSolution
	 * @return
	 */
	@AutoLog(value = "溶液使用记录-编辑")
	@Operation(summary="溶液使用记录-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_solution:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestSolution testSolution) {
		testSolutionService.updateById(testSolution);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "溶液使用记录-通过id删除")
	@Operation(summary="溶液使用记录-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_solution:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testSolutionService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "溶液使用记录-批量删除")
	@Operation(summary="溶液使用记录-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_solution:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testSolutionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "溶液使用记录-通过id查询")
	@Operation(summary="溶液使用记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestSolution> queryById(@RequestParam(name="id",required=true) String id) {
		TestSolution testSolution = testSolutionService.getById(id);
		if(testSolution==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testSolution);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testSolution
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_solution:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestSolution testSolution) {
        return super.exportXls(request, testSolution, TestSolution.class, "溶液使用记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:test_solution:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestSolution.class);
    }

}
