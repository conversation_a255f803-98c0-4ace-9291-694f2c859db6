package org.jeecg.modules.lims_core.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.lims_core.entity.WarehouseInOut;
import org.jeecg.modules.lims_core.mapper.WarehouseInOutMapper;
import org.jeecg.modules.lims_core.service.IWarehouseInOutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 出入库记录
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Service
public class WarehouseInOutServiceImpl extends ServiceImpl<WarehouseInOutMapper, WarehouseInOut> implements IWarehouseInOutService {
    @Autowired
    private WarehouseInOutMapper testParaMapper ;

    @Override
    public IPage<WarehouseInOut> queryPageList(Page<WarehouseInOut> page, Wrapper<WarehouseInOut> wrapper) {
        return testParaMapper.queryPageList(page,wrapper);
    }
}
