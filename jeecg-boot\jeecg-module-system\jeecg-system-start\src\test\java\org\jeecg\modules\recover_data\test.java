//package org.jeecg.modules.recover_data;
//
//import com.alibaba.fastjson.JSON;
//import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotationRubberStamp;
//import org.jeecg.JeecgSystemApplication;
//import org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt;
//import org.jeecg.modules.lims_core.mapper.SysStandardEvaluationLimtMapper;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2025/6/17 16:36
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = JeecgSystemApplication.class)
//public class test {
//
//    @Autowired
//    SysStandardEvaluationLimtMapper sysStandardEvaluationLimtMapper;
//
//    @Test
//    public void test() throws Exception {
//        String JsonString = "{\"standard_id\":\"1930153581843689473\",\"create_time\":\"2025-06-04 14:54:53\",\"sys_org_code\":\"A01A01A03A01A03\",\"group_one\":\"\",\"para_value\":\"\",\"elimit\":\"不得过0.088\",\"group_two\":\"\",\"create_by\":\"admin\",\"analyte_id\":\"1920721841395363844\",\"parent_id\":\"\",\"method_ids\":\"1930154200964902913\",\"sort_num\":3,\"para_type\":\"\",\"id\":\"c69f868d366845659e4a5f0b06cc5d21\",\"report_name\":\"\",\"unit_id\":\"1869584750718468098\"}";
//        //将JsonString转成sysStandardEvaluationLimt插入数据库
//        //将parse转成SysStandardEvaluationLimt对象
//        SysStandardEvaluationLimt sysStandardEvaluationLimt = JSON.parseObject(JsonString, SysStandardEvaluationLimt.class);
//        sysStandardEvaluationLimtMapper.insert(sysStandardEvaluationLimt);
//    }
//}
