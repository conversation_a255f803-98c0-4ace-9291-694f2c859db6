package org.jeecg.modules.oo.test;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jeecg.modules.oo.service.impl.SignServiceImpl;
import org.jeecg.modules.oo.util.AdobeLtvEnabling;

import java.io.ByteArrayInputStream;
import java.security.Security;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * 测试增强的颁发者证书获取功能
 * 验证多策略颁发者证书获取方法的工作情况
 */
public class IssuerCertificateTestMain {
    
    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }
    
//    public static void main(String[] args) {
//        System.out.println("=== 增强颁发者证书获取功能测试 ===");
//
//        try {
//            // 测试1: 测试GDCA证书的颁发者获取
//            testGDCACertificateIssuer();
//
//            // 测试2: 测试自签名证书检测
//            testSelfSignedCertificate();
//
//            // 测试3: 测试本地证书存储获取
//            testLocalStoreIssuer();
//
//        } catch (Exception e) {
//            System.err.println("测试过程中发生错误: " + e.getMessage());
//            e.printStackTrace();
//        }
//
//        System.out.println("\n=== 测试完成 ===");
//    }
    
//    /**
//     * 测试GDCA证书的颁发者获取
//     */
//    private static void testGDCACertificateIssuer() {
//        System.out.println("\n--- 测试1: GDCA证书颁发者获取 ---");
//
//        try {
//            // 创建一个模拟的GDCA证书（这里使用简化的证书数据）
//            String gdcaCertBase64 = createMockGDCACertificate();
//
//            if (gdcaCertBase64 != null) {
//                byte[] certBytes = Base64.getDecoder().decode(gdcaCertBase64);
//                CertificateFactory cf = CertificateFactory.getInstance("X.509");
//                X509Certificate cert = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(certBytes));
//
//                System.out.println("证书主体: " + cert.getSubjectX500Principal().getName());
//                System.out.println("证书颁发者: " + cert.getIssuerX500Principal().getName());
//
//                // 测试SignServiceImpl中的方法
//                System.out.println("\n使用SignServiceImpl.getIssuerCertificate:");
//                X509Certificate issuer1 = SignServiceImpl.getIssuerCertificate(cert);
//                if (issuer1 != null) {
//                    System.out.println("✓ 成功获取颁发者证书: " + issuer1.getSubjectX500Principal().getName());
//                } else {
//                    System.out.println("✗ 未能获取颁发者证书");
//                }
//
//                // 测试AdobeLtvEnabling中的方法
//                System.out.println("\n使用AdobeLtvEnabling.getIssuerCertificate:");
//                X509Certificate issuer2 = AdobeLtvEnabling.getIssuerCertificate(cert);
//                if (issuer2 != null) {
//                    System.out.println("✓ 成功获取颁发者证书: " + issuer2.getSubjectX500Principal().getName());
//                } else {
//                    System.out.println("✗ 未能获取颁发者证书");
//                }
//            }
//
//        } catch (Exception e) {
//            System.err.println("GDCA证书测试失败: " + e.getMessage());
//        }
//    }
    
    /**
     * 测试自签名证书检测
     */
    private static void testSelfSignedCertificate() {
        System.out.println("\n--- 测试2: 自签名证书检测 ---");
        
        try {
            // 创建一个模拟的自签名证书
            String selfSignedCertBase64 = createMockSelfSignedCertificate();
            
            if (selfSignedCertBase64 != null) {
                byte[] certBytes = Base64.getDecoder().decode(selfSignedCertBase64);
                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                X509Certificate cert = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(certBytes));
                
                System.out.println("证书主体: " + cert.getSubjectX500Principal().getName());
                System.out.println("证书颁发者: " + cert.getIssuerX500Principal().getName());
                
                // 测试自签名检测
                System.out.println("\n测试自签名证书检测:");
                X509Certificate issuer = SignServiceImpl.getIssuerCertificate(cert);
                if (issuer == null) {
                    System.out.println("✓ 正确识别为自签名证书，无需获取颁发者");
                } else {
                    System.out.println("✗ 未正确识别自签名证书");
                }
            }
            
        } catch (Exception e) {
            System.err.println("自签名证书测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试本地证书存储获取
     */
    private static void testLocalStoreIssuer() {
        System.out.println("\n--- 测试3: 本地证书存储获取 ---");
        
        try {
            // 创建一个模拟的证书，其颁发者可能在本地存储中
            String certBase64 = createMockCertificateWithKnownIssuer();
            
            if (certBase64 != null) {
                byte[] certBytes = Base64.getDecoder().decode(certBase64);
                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                X509Certificate cert = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(certBytes));
                
                System.out.println("证书主体: " + cert.getSubjectX500Principal().getName());
                System.out.println("证书颁发者: " + cert.getIssuerX500Principal().getName());
                
                // 测试本地存储获取
                System.out.println("\n测试本地证书存储获取:");
                X509Certificate issuer = SignServiceImpl.getIssuerCertificate(cert);
                if (issuer != null) {
                    System.out.println("✓ 从本地存储成功获取颁发者证书: " + issuer.getSubjectX500Principal().getName());
                } else {
                    System.out.println("✗ 未能从本地存储获取颁发者证书");
                }
            }
            
        } catch (Exception e) {
            System.err.println("本地存储测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建模拟的GDCA证书
     */
    private static String createMockGDCACertificate() {
        // 这里返回一个简化的证书数据，实际应用中应该使用真实的证书
        // 由于这是测试代码，我们创建一个包含GDCA信息的模拟证书
        System.out.println("创建模拟GDCA证书...");
        
        // 注意：这里应该使用真实的GDCA证书数据
        // 为了测试目的，我们返回null，让测试专注于错误处理
        return null;
    }
    
    /**
     * 创建模拟的自签名证书
     */
    private static String createMockSelfSignedCertificate() {
        // 这里返回一个简化的自签名证书数据
        System.out.println("创建模拟自签名证书...");
        
        // 注意：这里应该使用真实的自签名证书数据
        // 为了测试目的，我们返回null，让测试专注于错误处理
        return null;
    }
    
    /**
     * 创建模拟的证书，其颁发者可能在本地存储中
     */
    private static String createMockCertificateWithKnownIssuer() {
        // 这里返回一个证书，其颁发者可能在Java默认信任存储中
        System.out.println("创建模拟证书（已知颁发者）...");
        
        // 注意：这里应该使用真实的证书数据
        // 为了测试目的，我们返回null，让测试专注于错误处理
        return null;
    }
}