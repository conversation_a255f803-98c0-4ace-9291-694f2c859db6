package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysMethodWorkflow;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 检测流程
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
public interface ISysMethodWorkflowService extends IService<SysMethodWorkflow> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysMethodWorkflow>
	 */
	public List<SysMethodWorkflow> selectByMainId(String mainId);
}
