package org.jeecg.modules.lims_core.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.lettuce.core.dynamic.annotation.Param;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.modules.lims_core.entity.WarehouseInApply;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.lims_core.entity.WarehouseOutApply;
import org.jeecg.modules.lims_core.vo.WarehouseInApplyVo;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;

/**
 * @Description: 余物退回申请
 * @Author: jeecg-boot
 * @Date:   2025-05-27
 * @Version: V1.0
 */
public interface IWarehouseInApplyService extends IService<WarehouseInApply> {
    void apply(WarehouseInApply obj, ApplyType applyType) throws WxErrorException;
    public IPage<WarehouseInApplyVo> queryPageList(Page<WarehouseInApplyVo> page,
                                                   @Param(Constants.WRAPPER) Wrapper<WarehouseInApplyVo> wrapper);
}
