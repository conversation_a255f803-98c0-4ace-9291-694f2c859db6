package org.jeecg.modules.lims_lab.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TestResultDTO {
    private String id;
    @Schema(description = "检测结果")
    private String rawResult;
    @Schema(description = "平行结果")
    private String dupResult;
    @Schema(description = "报告结果")
    private String repResult;
    @Schema(description = "结论")
    private String conclusion;
    /**是否出报告*/
    @Schema(description = "是否出报告")
    private String reportable;
    @Schema(description = "多限值最终选定的某个限值")
    private String repLimit;
}
