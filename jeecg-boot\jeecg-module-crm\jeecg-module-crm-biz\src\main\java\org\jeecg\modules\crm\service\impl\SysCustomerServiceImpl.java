package org.jeecg.modules.crm.service.impl;

import org.jeecg.modules.crm.entity.SysCustomer;
import org.jeecg.modules.crm.entity.SysCustomerContact;
import org.jeecg.modules.crm.mapper.SysCustomerContactMapper;
import org.jeecg.modules.crm.mapper.SysCustomerMapper;
import org.jeecg.modules.crm.service.ISysCustomerService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 客户
 * @Author: jeecg-boot
 * @Date:   2024-12-31
 * @Version: V1.0
 */
@Service
public class SysCustomerServiceImpl extends ServiceImpl<SysCustomerMapper, SysCustomer> implements ISysCustomerService {

	@Autowired
	private SysCustomerMapper sysCustomerMapper;
	@Autowired
	private SysCustomerContactMapper sysCustomerContactMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysCustomer sysCustomer, List<SysCustomerContact> sysCustomerContactList) {
		sysCustomerMapper.insert(sysCustomer);
		if(sysCustomerContactList!=null && sysCustomerContactList.size()>0) {
			for(SysCustomerContact entity:sysCustomerContactList) {
				//外键设置
				entity.setCustomerId(sysCustomer.getId());
				sysCustomerContactMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysCustomer sysCustomer,List<SysCustomerContact> sysCustomerContactList) {
		sysCustomerMapper.updateById(sysCustomer);
		
		//1.先删除子表数据
		sysCustomerContactMapper.deleteByMainId(sysCustomer.getId());
		
		//2.子表数据重新插入
		if(sysCustomerContactList!=null && sysCustomerContactList.size()>0) {
			for(SysCustomerContact entity:sysCustomerContactList) {
				//外键设置
				entity.setCustomerId(sysCustomer.getId());
				sysCustomerContactMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysCustomerContactMapper.deleteByMainId(id);
		sysCustomerMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysCustomerContactMapper.deleteByMainId(id.toString());
			sysCustomerMapper.deleteById(id);
		}
	}
	
}
