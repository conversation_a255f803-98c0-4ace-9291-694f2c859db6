package org.jeecg.modules.oo.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.jeecg.common.system.annotation.TemplateDesigner;
import org.jeecg.common.system.annotation.TemplateField;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.oo.controller.dto.DictInfo;
import org.jeecg.modules.oo.controller.dto.TemplateNode;
import org.jeecg.modules.oo.controller.dto.TableInfo;
import org.jetbrains.annotations.Nullable;
import org.springframework.expression.Expression;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class TemplateProcessor {

    /**
     * 处理模板数据源
     * @param packageName： 包名
     * @param rootClass： 根类名
     * @param rootId:根类对象数据Id
     * @param tablesInTemplate:模板中的表集合
     * @return 数据源对象集合
     */
    public Map<String, Object> processTemplateClasses(String packageName, String rootClass, Object rootId,
            Map<String, TableInfo> tablesInTemplate,Map<String,Object> preDataSource, Boolean ISLogicalTopLevel                                                      ) {
        Set<Class<?>> annotatedClasses = ClassUtil.scanPackageByAnnotation(packageName, TemplateDesigner.class);
        TemplateNode root = buildDependencyTree(annotatedClasses, rootClass,tablesInTemplate);

        Map<String, Object> result = new HashMap<>();
        Set<TemplateNode> processedNodes = new HashSet<>();
        processNode(root, rootId, result, true, tablesInTemplate, processedNodes,preDataSource,ISLogicalTopLevel,false);
        return result;
    }

    /**
     * 分析依赖关系
     */
    private void analyzeDependencies(TemplateNode node, Map<String, TemplateNode> nodeMap) {
        // 只处理 drillUp 父级依赖
        if (node.getParentEntity() != null) {
            String parentVOClass = StrUtil.upperFirst(StrUtil.toCamelCase(node.getParentEntity())) + "VO";
            TemplateNode parentNode = nodeMap.get(parentVOClass);
            node.setParentNode(parentNode);
            if (parentNode != null && !parentNode.getChildren().contains(node)) {
                parentNode.getChildren().add(node);
            }
        }
    }

    /**
     * 构建依赖树
     */
    private TemplateNode buildDependencyTree(Set<Class<?>> classes, String rootClass,Map<String, TableInfo> tablesInTemplate) {
        Map<String, TemplateNode> nodeMap = new HashMap<>();
        // 第一遍遍历：创建所有节点
        for (Class<?> clazz : classes) {
            String className = clazz.getSimpleName();
            TemplateDesigner annotation = clazz.getAnnotation(TemplateDesigner.class);
            String entityName = StrUtil.isNotEmpty(annotation.entity())
                    ? annotation.entity()
                    : className.replace("VO", "");
            String Table = StrUtil.upperFirst(StrUtil.toCamelCase(entityName)) ;
            //if(tablesInTemplate.containsKey(Table)) {
                // 解析 drillUp
                String parentEntity = null;
                String parentField = null;
                String whereSel = StrUtil.isNotEmpty(annotation.whereSel())
                        ? annotation.whereSel()
                        :"";
                if (StrUtil.isNotEmpty(annotation.drillUp())) {
                    String[] drillUp = annotation.drillUp().split("->");
                    if (drillUp.length == 2) {
                        parentField = drillUp[0];
                        String[] parentParts = drillUp[1].split("\\.");
                        parentEntity = parentParts[0];
                    }
                }

                Map<String, DictInfo> dictMappings = new HashMap<>();
                Map<String, String> fieldMapping = new HashMap<>();
                Map<String, String> drillChains = new HashMap<>();
                Map<String, String> func = new HashMap<>();

                // 解析字段注解
                for (Field field : clazz.getDeclaredFields()) {
                    TemplateField fieldAnnotation = field.getAnnotation(TemplateField.class);
                    if (fieldAnnotation != null) {
                        String fieldName = field.getName();

                        // 处理字段映射
                        String entityFieldName = fieldAnnotation.entityFieldName();
                        if (StrUtil.isNotEmpty(entityFieldName)) {
                            fieldMapping.put(fieldName, entityFieldName);
                        } else {
                            fieldMapping.put(fieldName, fieldName);
                        }

                        // 处理字典映射
                        if (StrUtil.isNotEmpty(fieldAnnotation.dictTable())) {
                            dictMappings.put(fieldName, DictInfo.builder()
                                    .tableName(fieldAnnotation.dictTable())
                                    .keyField(fieldAnnotation.dictKey())
                                    .textField(fieldAnnotation.dictText())
                                    .entityField(entityFieldName)
                                    .build());
                        }

                        // 处理字典映射
                        if (StrUtil.isNotEmpty(fieldAnnotation.dict())) {
                            dictMappings.put(fieldName, DictInfo.builder()
                                    .tableName(fieldAnnotation.dict())
                                    .keyField("value")
                                    .textField("text")
                                    .entityField(entityFieldName)
                                    .build());
                        }

                        // 处理钻取链
                        if (StrUtil.isNotEmpty(fieldAnnotation.drillChain())) {
                            drillChains.put(fieldName, fieldAnnotation.drillChain());
                        }

                        // 处理方法
                        if (StrUtil.isNotEmpty(fieldAnnotation.func())) {
                            func.put(fieldName, fieldAnnotation.func());
                        }
                    }
                }

                TemplateNode node = TemplateNode.builder()
                        .voClass(clazz)
                        .entityName(entityName)
                        .parentEntity(parentEntity)
                        .parentField(parentField)
                        .fieldMapping(fieldMapping)
                        .dictMappings(dictMappings)
                        .drillChains(drillChains)
                        .funcs(func)
                        .whereSel(whereSel)
                        .description(annotation.description())
                        .children(new ArrayList<>())
                        .build();

                nodeMap.put(className, node);
            //}
        }

        // 第二遍遍历：分析所有节点的依赖关系
        nodeMap.values().forEach(node -> {
            analyzeDependencies(node, nodeMap);
        });

        return nodeMap.get(rootClass);
    }

    /**
     * 处理节点数据
     */
    private void processNode(TemplateNode node, Object parentId, Map<String, Object> result, boolean isRoot,
            Map<String, TableInfo> tablesInTemplate,Set<TemplateNode> processedNodes,Map<String,Object> preDataSource, Boolean ISLogicalTopLevel,boolean up) {
        if (node == null)
            return;
        // 如果当前节点已经处理过，直接返回
        if (processedNodes.contains(node)) {
            return;
        }
        // 标记当前节点为已处理
        processedNodes.add(node);
        String Table = node.getEntityName();
        Object entity = null;
        if (preDataSource != null && preDataSource.containsKey(Table)) {//提前准备好的无需处理，主要是subreport时
            result.put(Table, preDataSource.get(Table));
            processedNodes.add(node);
            return;
        }
        String serviceName = StrUtil.lowerFirst(node.getEntityName()) + "ServiceImpl";
        Object oService = SpringContextUtils.getBean(serviceName);
        if (oService != null) {
            IService service = (IService) oService;

            // 查询主实体数据
            if (!isRoot && node.getParentNode() != null && node.getParentField() != null) {
                String className = node.getVoClass().getSimpleName().replace("VO", "");
                TableInfo tableInfo = tablesInTemplate.get(className);
                String parentField = StrUtil.toUnderlineCase(node.getParentField());
                if( up)//未出现在模板中的父对象，如原始记录的TestTask
                    parentField = "id";
                if(ISLogicalTopLevel){
                    parentField = StrUtil.toUnderlineCase(node.getParentField());
                }
                QueryWrapper qw = new QueryWrapper<>();
                if(parentId instanceof List && ((List<?>) parentId).size() > 1)
                    qw.in(parentField, (List<String>)parentId);
                else if(parentId instanceof List && ((List<?>) parentId).size() == 1){
                    qw.eq(parentField, ((List<String>) parentId).get(0));
                }
                else{
                    qw.eq(parentField, parentId);
                }
                if (parentId instanceof List && ((List<?>) parentId).size()==0) {
                    throw new RuntimeException("方法未指定!");
                }
                String whereSel = node.getWhereSel();
                if (StrUtil.isNotEmpty(whereSel)) {
                    qw.last("AND " + whereSel);
                }
                // 检查是否是表格开始标记
                if ((tableInfo != null && tableInfo.isTableStart())  || (parentId instanceof List && ((List<?>) parentId).size() > 1)){
                    entity = service.list(qw);
                } else {
                    List<?> resultList = service.list(qw);
                    if (resultList != null && resultList.size() > 1) {
                        entity=resultList;
                    }else {
                        entity = service.getOne(qw);
                    }
                }
            } else {
                entity = service.getById(parentId.toString());
            }
        }
        if (entity != null) {
            //if (tablesInTemplate.containsKey(Table)) {
                if (entity instanceof List) {
                    // 处理列表情况
                    List<Object> entityList = (List<Object>) entity;
                    List<Object> voList = new ArrayList<>();

                    // 为每个实体创建对应的VO对象
                    for (Object entityItem : entityList) {
                        Object voItem = BeanUtil.copyProperties(entityItem, node.getVoClass());
                        voList.add(voItem);
                        boolean hasDrillChain = !node.getDrillChains().isEmpty();
                        if (hasDrillChain) {// 处理钻取链
                            for (Map.Entry<String, String> entry : node.getDrillChains().entrySet()) {
                                String voField = entry.getKey();
                                String drillChain = entry.getValue();

                                // 先从实体字段开始
                                String entityFieldName = node.getFieldMapping().get(voField);
                                entityFieldName = StrUtil.toCamelCase(entityFieldName); // 转换成小驼峰命名法
                                Object currentValue = ReflectUtil.getFieldValue(entityItem, entityFieldName);

                                if (currentValue != null) {
                                    String[] chains = drillChain.split("->");
                                    String lastTable = null;

                                    // 正常钻取链处理
                                    for (int i = 0; i < chains.length; i++) {
                                        String[] parts = chains[i].split("\\.");
                                        String tableName = StrUtil.toUnderlineCase(parts[0]);
                                        String fieldName = StrUtil.toUnderlineCase(parts[1]);

                                        // 获取当前链条的查询条件字段
                                        String whereField;
                                        if (i == 0) {
                                            whereField = "id"; // 第一次查询用 id
                                        } else if (tableName.equals(lastTable)) {
                                            whereField = "id"; // 同一个表内用 id
                                        } else {
                                            whereField = fieldName; // 不同表之间用当前字段名作为条件
                                        }

                                        // 转换字段名为下划线格式
                                        fieldName = StrUtil.toUnderlineCase(fieldName);
                                        whereField = StrUtil.toUnderlineCase(whereField);
                                        tableName = StrUtil.toUnderlineCase(tableName); // 表名也需要转换
                                        System.out.println(entry);
                                        String sql = String.format(
                                                "SELECT %s FROM %s WHERE %s = ?",
                                                fieldName,
                                                tableName,
                                                whereField);
                                        JdbcTemplate jdbcTemplate = SpringContextUtils.getBean(JdbcTemplate.class);
                                        try {
                                            currentValue = jdbcTemplate.queryForObject(sql, Object.class, currentValue);
                                        } catch (Exception e) {
                                            currentValue = null;
                                            break;
                                        }

                                        lastTable = tableName;
                                        try {
                                            Field field = node.getVoClass().getDeclaredField(voField);
                                            TemplateField annotation = field.getAnnotation(TemplateField.class);
                                            // 如果是最后一个链条，处理 calcExpr
                                            if (i == chains.length - 1 && annotation != null) {
                                                if (StrUtil.isNotEmpty(annotation.dict())) {
                                                    currentValue = getDictText(currentValue, annotation.dict());
                                                } else if (StrUtil.isNotEmpty(annotation.calcExpr())) {
                                                    // 只查询一次获取完整记录
                                                    String fullSql = String.format(
                                                            "SELECT * FROM %s WHERE id = ?",
                                                            StrUtil.toUnderlineCase(tableName));
                                                    Map<String, Object> record = jdbcTemplate.queryForMap(fullSql, currentValue);
                                                    if (record != null) {
                                                        String calcExpr = annotation.calcExpr();
                                                        Boolean canParse = true;
                                                        for (String key : record.keySet()) {
                                                            if (calcExpr.contains(key)) {
                                                                if (record.get(key)!=null && !StrUtil.isEmpty(record.get(key).toString())){
                                                                    if (record.get(key) instanceof Integer) {
                                                                        calcExpr = calcExpr.replace(key, record.get(key).toString());
                                                                    } else if (record.get(key) instanceof LocalDateTime) {
                                                                        calcExpr = calcExpr.replace(key, "T(java.time.LocalDateTime).parse('" + record.get(key) + "')");
                                                                    } else{
                                                                        calcExpr = calcExpr.replace(key, "'" + record.get(key) + "'");
                                                                    }
                                                                }else{
                                                                    canParse = false;
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                        if (canParse) {// 使用 SpEL 计算表达式
                                                            StandardEvaluationContext context = new StandardEvaluationContext();
                                                            ExpressionParser parser = new SpelExpressionParser();
                                                            try {
                                                                Expression expression = parser.parseExpression(calcExpr);
                                                                currentValue = expression.getValue(context);
                                                                //System.out.println("Result Value from SpEL: " + currentValue);
                                                            } catch (Exception e) {
                                                                System.err.println("Error while parsing expression: " + e.getMessage());
                                                                e.printStackTrace();
                                                            }
                                                        }else
                                                            currentValue = null;
                                                    }
                                                }
                                            }



                                        } catch (NoSuchFieldException e) {
                                            throw new RuntimeException(e);
                                        }
                                    }

                                    if (currentValue != null) {
                                        ReflectUtil.setFieldValue(voItem, voField, currentValue);
                                    }
                                }
                            }
                        } else{
                            processDict(node, entityItem, voItem);
                        }
                        //处理func
                        for (Map.Entry<String, String> entry : node.getFuncs().entrySet()) {
                            String voField = entry.getKey();
                            try{
                                Field field = node.getVoClass().getDeclaredField(voField);
                                TemplateField annotation = field.getAnnotation(TemplateField.class); // 获取注解
                                // 先从实体字段开始
                                String entityFieldName = node.getFieldMapping().get(voField);
                                entityFieldName = StrUtil.toCamelCase(entityFieldName); // 转换成小驼峰命名法
                                Object currentValue = ReflectUtil.getFieldValue(entityItem, entityFieldName);

                                if (StrUtil.isNotEmpty(annotation.func())) {
                                    try{
                                        Class<?> clazz = oService.getClass();
                                        String func = annotation.func();
                                        Method method = clazz.getMethod(func, String.class);
                                        Object o = method.invoke(oService, currentValue);
                                        currentValue = o;
                                    }catch (Exception e){
                                        currentValue = null;
                                    }
                                    if (currentValue != null) {
                                        ReflectUtil.setFieldValue(voItem, voField, currentValue);
                                    }
                                }


                            } catch (Exception e) {

                            }
                        }
                    }



                    // 存储VO列表到结果中
                    result.put(node.getVoClass().getSimpleName().replace("VO", ""), voList);

                    // 处理字典映射
                    for (Map.Entry<String, DictInfo> entry : node.getDictMappings().entrySet()) {
                        String voField = entry.getKey();
                        DictInfo dictInfo = entry.getValue();
                        String dictEntityField = dictInfo.getEntityField();
                        dictEntityField = StrUtil.toCamelCase(dictEntityField);
                        dictEntityField = StrUtil.lowerFirst(dictEntityField);

                        for (int i = 0; i < entityList.size(); i++) {
                            Object entityItem = entityList.get(i);
                            Object voItem = voList.get(i);

                            // 获取关联ID
                            Object dictId = ReflectUtil.getFieldValue(entityItem, dictEntityField);
                            if (dictId != null) {
                                // 查询字典表
                                String sql = String.format(
                                        "SELECT %s FROM %s WHERE %s = ?",
                                        dictInfo.getTextField(),
                                        dictInfo.getTableName(),
                                        dictInfo.getKeyField());
                                JdbcTemplate jdbcTemplate = SpringContextUtils.getBean(JdbcTemplate.class);
                                try {
                                    Object dictValue = jdbcTemplate.queryForObject(sql, String.class, dictId);

                                    // 设置VO字段值
                                    if (dictValue != null) {
                                        ReflectUtil.setFieldValue(voItem, voField, dictValue);
                                    }
                                } catch (Exception e) {
                                    // 如果查询不到结果，静默处理
                                    System.out.println(123);
                                }
                            }
                        }
                    }

                    // 处理子节点
                    for (TemplateNode child : node.getChildren()) {
                        List<String> parentIds = entityList.stream().map(entityItem -> {
                            String oParentId = ReflectUtil.getFieldValue(entityItem, "id").toString();
                            return oParentId;
                        }).collect(Collectors.toList());
                        processNode(child, parentIds, result, false, tablesInTemplate, processedNodes, preDataSource,ISLogicalTopLevel,false);
                    }
                    for (int i = 0; i < entityList.size(); i++) {
                        Object entityItem = entityList.get(i);
                        Object currentId = ReflectUtil.getFieldValue(entityItem, "id");
                        if (node.getParentNode() != null && !processedNodes.contains(node.getParentNode())) {
                            TemplateNode parentNode = node.getParentNode();
                            String parentField = StrUtil.toCamelCase(node.getParentField());
                            Object parentNodeId = ReflectUtil.getFieldValue(entityItem, parentField);
                            processNode(parentNode, parentNodeId, result, false, tablesInTemplate, processedNodes, preDataSource,ISLogicalTopLevel,true);
                        }
                    }
                }
                else {
                    // 处理单个实体情况
                    Object vo = BeanUtil.copyProperties(entity, node.getVoClass());

                    // 处理字典映射
                    for (Map.Entry<String, DictInfo> entry : node.getDictMappings().entrySet()) {
                        String voField = entry.getKey();
                        DictInfo dictInfo = entry.getValue();
                        String dictEntityField = dictInfo.getEntityField();
                        dictEntityField = StrUtil.toCamelCase(dictEntityField);
                        dictEntityField = StrUtil.lowerFirst(dictEntityField);
                        processDictTableMapping(entity, vo, voField, dictInfo, dictEntityField);
                    }
                    boolean hasDrillChain = !node.getDrillChains().isEmpty();
                    if (hasDrillChain) {// 处理钻取链
                        for (Map.Entry<String, String> entry : node.getDrillChains().entrySet()) {
                            String voField = entry.getKey();
                            String drillChain = entry.getValue();

                            // 先从实体字段开始
                            String entityFieldName = node.getFieldMapping().get(voField);
                            entityFieldName = StrUtil.toCamelCase(entityFieldName); // 转换成小驼峰命名法
                            Object currentValue = ReflectUtil.getFieldValue(entity, entityFieldName);

                            if (currentValue != null) {
                                String[] chains = drillChain.split("->");
                                String lastTable = null;

                                // 正常钻取链处理
                                for (int i = 0; i < chains.length; i++) {
                                    String[] parts = chains[i].split("\\.");
                                    String tableName = parts[0];
                                    String fieldName = parts[1];

                                    // 获取当前链条的查询条件字段
                                    String whereField;
                                    if (i == 0) {
                                        whereField = "id"; // 第一次查询用 id
                                    } else if (tableName.equals(lastTable)) {
                                        whereField = "id"; // 同一个表内用 id
                                    } else {
                                        whereField = fieldName; // 不同表之间用当前字段名作为条件
                                    }

                                    // 转换字段名为下划线格式
                                    fieldName = StrUtil.toUnderlineCase(fieldName);
                                    whereField = StrUtil.toUnderlineCase(whereField);
                                    tableName = StrUtil.toUnderlineCase(tableName); // 表名也需要转换
                                    System.out.println(entry);
                                    String sql = String.format(
                                            "SELECT %s FROM %s WHERE %s = ?",
                                            fieldName,
                                            tableName,
                                            whereField);
                                    JdbcTemplate jdbcTemplate = SpringContextUtils.getBean(JdbcTemplate.class);
                                    try {
                                        currentValue = jdbcTemplate.queryForObject(sql, Object.class, currentValue);
                                    } catch (Exception e) {
                                        currentValue = null;
                                        break;
                                    }

                                    lastTable = tableName;
                                    try {
                                        Field field = node.getVoClass().getDeclaredField(voField);
                                        TemplateField annotation = field.getAnnotation(TemplateField.class); // 获取注解
                                        // 如果是最后一个链条，处理 calcExpr
                                        if (i == chains.length - 1 && annotation != null) {
                                            if (StrUtil.isNotEmpty(annotation.dict())) {
                                                currentValue = getDictText(currentValue, annotation.dict());
                                            } else if (StrUtil.isNotEmpty(annotation.calcExpr())) {
                                                // 只查询一次获取完整记录
                                                String fullSql = String.format(
                                                        "SELECT * FROM %s WHERE id = ?",
                                                        StrUtil.toUnderlineCase(tableName));
                                                try {
                                                    Map<String, Object> record = jdbcTemplate.queryForMap(fullSql, currentValue);
                                                    if (record != null) {
                                                        // 使用 SpEL 计算表达式
                                                        StandardEvaluationContext context = new StandardEvaluationContext(record);
                                                        ExpressionParser parser = new SpelExpressionParser();
                                                        Object resultValue = parser.parseExpression(annotation.calcExpr()).getValue(context);
                                                        if (resultValue != null) {
                                                            ReflectUtil.setFieldValue(vo, voField, resultValue);
                                                        }
                                                    }
                                                } catch (Exception e) {
                                                    // 如果查询失败，跳过处理
                                                }
                                            }
                                        }


                                    } catch (NoSuchFieldException e) {
                                        throw new RuntimeException(e);
                                    }
                                }

                                if (currentValue != null) {
                                    ReflectUtil.setFieldValue(vo, voField, currentValue);
                                }
                            }
                        }
                    }else{
                        processDict(node, entity, vo);
                    }

                    //处理func
                    for (Map.Entry<String, String> entry : node.getFuncs().entrySet()) {
                        String voField = entry.getKey();
                        try{
                            Field field = node.getVoClass().getDeclaredField(voField);
                            TemplateField annotation = field.getAnnotation(TemplateField.class); // 获取注解
                            // 先从实体字段开始
                            String entityFieldName = node.getFieldMapping().get(voField);
                            entityFieldName = StrUtil.toCamelCase(entityFieldName); // 转换成小驼峰命名法
                            Object currentValue = ReflectUtil.getFieldValue(entity, entityFieldName);

                            if (StrUtil.isNotEmpty(annotation.func())) {
                                try{
                                    Class<?> clazz = oService.getClass();
                                    String func = annotation.func();
                                    Method method = clazz.getMethod(func, String.class);
                                    Object o = method.invoke(oService, currentValue);
                                    currentValue = o;
                                }catch (Exception e){
                                    currentValue = null;
                                }
                                if (currentValue != null) {
                                    ReflectUtil.setFieldValue(vo, voField, currentValue);
                                }
                            }


                        } catch (Exception e) {

                        }
                    }


                    result.put(node.getVoClass().getSimpleName().replace("VO", ""), vo);
                }
           // }
            // 处理子节点
            Object currentId = ReflectUtil.getFieldValue(entity, "id");
            for (TemplateNode child : node.getChildren()) {
                if(!processedNodes.contains(child))
                    processNode(child, currentId, result, false, tablesInTemplate, processedNodes, preDataSource,ISLogicalTopLevel,false);
            }
            if (node.getParentNode() != null && !processedNodes.contains(node.getParentNode())) {
                TemplateNode parentNode = node.getParentNode();
                String parentField = StrUtil.toCamelCase(node.getParentField());
                Object parentNodeId = ReflectUtil.getFieldValue(entity, parentField);
                processNode(parentNode, parentNodeId, result, false, tablesInTemplate, processedNodes, preDataSource,ISLogicalTopLevel,true);//
            }
        }
    }

    private static void processDict(TemplateNode node, Object entity, Object voItem) {
        Field[] voFields = node.getVoClass().getDeclaredFields();
        for (Field field : voFields){
            String voField = field.getName();
            String entityFieldName = node.getFieldMapping().get(voField);
            if (entityFieldName !=null) {
                entityFieldName = StrUtil.toCamelCase(entityFieldName); // 转换成小驼峰命名法
                Object currentValue = ReflectUtil.getFieldValue(entity, entityFieldName);
                TemplateField annotation = field.getAnnotation(TemplateField.class); // 获取注解
                if (StrUtil.isNotEmpty(annotation.dict())) {
                    currentValue = getDictText(currentValue, annotation.dict());
                    if (currentValue != null) {
                        ReflectUtil.setFieldValue(voItem, voField, currentValue);
                    }
                }

            }
        }
    }

    @Nullable
    private static Object getDictText(Object currentValue, String dictCode) {
        if (currentValue != null) {
            ISysBaseAPI baseAPI = SpringContextUtils.getBean(ISysBaseAPI.class);
            List<DictModel> dictItems = baseAPI.getDictItems(dictCode);
            Object finalCurrentValue = currentValue;
            DictModel dictModel = dictItems.stream().filter(d -> d.getValue().equals(finalCurrentValue.toString())).findFirst().orElse(null);
            currentValue = dictModel.getText();
        }
        return currentValue;
    }

    private void processDictTableMapping(Object entity, Object vo, String voField, DictInfo dictInfo,
                                         String dictEntityField) {
        Object dictId = ReflectUtil.getFieldValue(entity, dictEntityField);
        if (dictId != null) {
            String sql = String.format(
                    "SELECT %s FROM %s WHERE %s = ?",
                    dictInfo.getTextField(),
                    dictInfo.getTableName(),
                    dictInfo.getKeyField());
            JdbcTemplate jdbcTemplate = SpringContextUtils.getBean(JdbcTemplate.class);
            try {
                Object dictValue = jdbcTemplate.queryForObject(sql, String.class, dictId);
                if (dictValue != null) {
                    ReflectUtil.setFieldValue(vo, voField, dictValue);
                }
            } catch (Exception e) {
                // 如果查询不到结果，静默处理
                System.out.println("12312322");
            }
        }
    }
}