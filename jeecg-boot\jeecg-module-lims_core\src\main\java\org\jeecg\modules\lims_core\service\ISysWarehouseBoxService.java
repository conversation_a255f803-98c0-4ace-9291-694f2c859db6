package org.jeecg.modules.lims_core.service;

import org.jeecg.modules.lims_core.entity.SysWarehouseBox;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 货位
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
public interface ISysWarehouseBoxService extends IService<SysWarehouseBox> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<SysWarehouseBox>
	 */
	public List<SysWarehouseBox> selectByMainId(String mainId);
}
