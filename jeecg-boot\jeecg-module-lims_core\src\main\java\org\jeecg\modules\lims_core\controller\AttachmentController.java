package org.jeecg.modules.lims_core.controller;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.lims_core.entity.Attachment;
import org.jeecg.modules.lims_core.entity.StandardMaterial;
import org.jeecg.modules.lims_core.service.IAttachmentService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_core.vo.AttachQueryVo;
import org.jeecg.modules.lims_core.vo.StandardMaterialVO;
import org.jeecg.modules.system.model.TreeModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 附件表
 * @Author: jeecg-boot
 * @Date:   2025-05-12
 * @Version: V1.0
 */
@Tag(name="附件表")
@RestController
@RequestMapping("/lims_core/attachment")
@Slf4j
public class AttachmentController extends JeecgController<Attachment, IAttachmentService> {
	@Autowired
	private IAttachmentService attachmentService;
	
	/**
	 * 分页列表查询
	 *
	 * @param attachment
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "附件表-分页列表查询")
	@Operation(summary="附件表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Attachment>> queryPageList(Attachment attachment,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String sourceId = req.getParameter("sourceId");
		if (sourceId != null && !sourceId.isEmpty()) {
			QueryWrapper<Attachment> queryWrapper = new QueryWrapper<>();
			List<String> sourceIdList = Arrays.asList(sourceId.split(","));
			String inCondition = sourceIdList.stream()
					.map(id -> "FIND_IN_SET('" + id + "', source_id)")
					.collect(Collectors.joining(" OR "));
			queryWrapper.apply(inCondition);
			Page<Attachment> page = new Page<Attachment>(pageNo, pageSize);
			IPage<Attachment> pageList = attachmentService.page(page, queryWrapper);
			return Result.OK(pageList);
		}else {
			QueryWrapper<Attachment> queryWrapper = QueryGenerator.initQueryWrapper(attachment, req.getParameterMap());
			Page<Attachment> page = new Page<Attachment>(pageNo, pageSize);
			IPage<Attachment> pageList = attachmentService.page(page, queryWrapper);
			return Result.OK(null);
		}
	}
	
	/**
	 *   添加
	 *
	 * @param attachment
	 * @return
	 */
	@AutoLog(value = "附件表-添加")
	@Operation(summary="附件表-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:attachment:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Attachment attachment) {
		attachmentService.saveAttach(attachment);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param attachment
	 * @return
	 */
	@AutoLog(value = "附件表-编辑")
	@Operation(summary="附件表-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:attachment:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Attachment attachment) {
		attachmentService.saveAttach(attachment);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "附件表-通过id删除")
	@Operation(summary="附件表-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:attachment:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		attachmentService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "附件表-批量删除")
	@Operation(summary="附件表-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:attachment:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.attachmentService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "附件表-通过id查询")
	@Operation(summary="附件表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Attachment> queryById(@RequestParam(name="id",required=true) String id) {
		Attachment attachment = attachmentService.getById(id);
		if(attachment==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(attachment);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param attachment
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:attachment:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Attachment attachment) {
        return super.exportXls(request, attachment, Attachment.class, "附件表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:attachment:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Attachment.class);
    }

	 /**
	  * listSourceOptions
	  */
	 @Operation(summary="样品-自动补全")
	 @PostMapping(value = "/listSourceOptions")
	 public Result<List<TreeModel>> listSourceOptions(@RequestBody AttachQueryVo attachQueryVo) {
		 List<TreeModel> list = attachmentService.listSourceOptions(attachQueryVo);
		 if(list==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(list);
	 }



}
