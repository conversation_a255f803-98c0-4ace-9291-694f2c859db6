package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.entity.SysMethodConsumptive;
import org.jeecg.modules.lims_core.entity.SysMethodStdMaterial;
import org.jeecg.modules.lims_core.entity.SysMethodInstrumentType;
import org.jeecg.modules.lims_core.entity.SysMethodTestingPara;
import org.jeecg.modules.lims_core.entity.SysMethodRepeatType;
import org.jeecg.modules.lims_core.entity.SysMethodWorkflow;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.mapper.SysMethodConsumptiveMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodStdMaterialMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodInstrumentTypeMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodTestingParaMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodRepeatTypeMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodWorkflowMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodAnalyteMapper;
import org.jeecg.modules.lims_core.mapper.SysMethodMapper;
import org.jeecg.modules.lims_core.service.ISysMethodService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 方法
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodServiceImpl extends ServiceImpl<SysMethodMapper, SysMethod> implements ISysMethodService {

	@Autowired
	private SysMethodMapper sysMethodMapper;
	@Autowired
	private SysMethodConsumptiveMapper sysMethodConsumptiveMapper;
	@Autowired
	private SysMethodStdMaterialMapper sysMethodStdMaterialMapper;
	@Autowired
	private SysMethodInstrumentTypeMapper sysMethodInstrumentTypeMapper;
	@Autowired
	private SysMethodTestingParaMapper sysMethodTestingParaMapper;
	@Autowired
	private SysMethodRepeatTypeMapper sysMethodRepeatTypeMapper;
	@Autowired
	private SysMethodWorkflowMapper sysMethodWorkflowMapper;
	@Autowired
	private SysMethodAnalyteMapper sysMethodAnalyteMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(SysMethod sysMethod, List<SysMethodConsumptive> sysMethodConsumptiveList,List<SysMethodStdMaterial> sysMethodStdMaterialList,List<SysMethodInstrumentType> sysMethodInstrumentTypeList,List<SysMethodTestingPara> sysMethodTestingParaList,List<SysMethodRepeatType> sysMethodRepeatTypeList,List<SysMethodWorkflow> sysMethodWorkflowList,List<SysMethodAnalyte> sysMethodAnalyteList) {
		sysMethodMapper.insert(sysMethod);
		if(sysMethodConsumptiveList!=null && sysMethodConsumptiveList.size()>0) {
			for(SysMethodConsumptive entity:sysMethodConsumptiveList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodConsumptiveMapper.insert(entity);
			}
		}
		if(sysMethodStdMaterialList!=null && sysMethodStdMaterialList.size()>0) {
			for(SysMethodStdMaterial entity:sysMethodStdMaterialList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodStdMaterialMapper.insert(entity);
			}
		}
		if(sysMethodInstrumentTypeList!=null && sysMethodInstrumentTypeList.size()>0) {
			for(SysMethodInstrumentType entity:sysMethodInstrumentTypeList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodInstrumentTypeMapper.insert(entity);
			}
		}
		if(sysMethodTestingParaList!=null && sysMethodTestingParaList.size()>0) {
			for(SysMethodTestingPara entity:sysMethodTestingParaList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodTestingParaMapper.insert(entity);
			}
		}
		if(sysMethodRepeatTypeList!=null && sysMethodRepeatTypeList.size()>0) {
			for(SysMethodRepeatType entity:sysMethodRepeatTypeList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				//如果数组有下一个.设置nextId为下一个节点的id
				if(sysMethodRepeatTypeList.indexOf(entity)<sysMethodRepeatTypeList.size()-1) {
					entity.setNextId(sysMethodRepeatTypeList.get(sysMethodRepeatTypeList.indexOf(entity) + 1).getId());
				}
				sysMethodRepeatTypeMapper.insert(entity);
			}
		}
		if(sysMethodWorkflowList!=null && sysMethodWorkflowList.size()>0) {
			for(SysMethodWorkflow entity:sysMethodWorkflowList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodWorkflowMapper.insert(entity);
			}
		}
		if(sysMethodAnalyteList!=null && sysMethodAnalyteList.size()>0) {
			for(SysMethodAnalyte entity:sysMethodAnalyteList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodAnalyteMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveCopyMain(SysMethod sysMethod,String oldmethodId, List<SysMethodConsumptive> sysMethodConsumptiveList,List<SysMethodStdMaterial> sysMethodStdMaterialList,List<SysMethodInstrumentType> sysMethodInstrumentTypeList,List<SysMethodTestingPara> sysMethodTestingParaList,List<SysMethodRepeatType> sysMethodRepeatTypeList,List<SysMethodWorkflow> sysMethodWorkflowList,List<SysMethodAnalyte> sysMethodAnalyteList) {
		sysMethodMapper.insert(sysMethod);
		sysMethodConsumptiveList =sysMethodConsumptiveMapper.selectByMainId(oldmethodId);
		sysMethodStdMaterialList=sysMethodStdMaterialMapper.selectByMainId(oldmethodId);
		sysMethodInstrumentTypeList=sysMethodInstrumentTypeMapper.selectByMainId(oldmethodId);
		sysMethodTestingParaList=sysMethodTestingParaMapper.selectByMainId(oldmethodId);
		sysMethodRepeatTypeList=sysMethodRepeatTypeMapper.selectByMainId(oldmethodId);
		sysMethodWorkflowList=sysMethodWorkflowMapper.selectByMainId(oldmethodId);
		sysMethodAnalyteList=sysMethodAnalyteMapper.selectByMainId(oldmethodId);

		if(sysMethodConsumptiveList!=null && sysMethodConsumptiveList.size()>0) {
			for(SysMethodConsumptive entity:sysMethodConsumptiveList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				sysMethodConsumptiveMapper.insert(entity);
			}
		}
		if(sysMethodStdMaterialList!=null && sysMethodStdMaterialList.size()>0) {
			for(SysMethodStdMaterial entity:sysMethodStdMaterialList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				sysMethodStdMaterialMapper.insert(entity);
			}
		}
		if(sysMethodInstrumentTypeList!=null && sysMethodInstrumentTypeList.size()>0) {
			for(SysMethodInstrumentType entity:sysMethodInstrumentTypeList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				sysMethodInstrumentTypeMapper.insert(entity);
			}
		}
		if(sysMethodTestingParaList!=null && sysMethodTestingParaList.size()>0) {
			for(SysMethodTestingPara entity:sysMethodTestingParaList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				sysMethodTestingParaMapper.insert(entity);
			}
		}
		if(sysMethodRepeatTypeList!=null && sysMethodRepeatTypeList.size()>0) {
			for(SysMethodRepeatType entity:sysMethodRepeatTypeList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				//如果数组有下一个.设置nextId为下一个节点的id
				if(sysMethodRepeatTypeList.indexOf(entity)<sysMethodRepeatTypeList.size()-1) {
					entity.setNextId(sysMethodRepeatTypeList.get(sysMethodRepeatTypeList.indexOf(entity) + 1).getId());
				}
				sysMethodRepeatTypeMapper.insert(entity);
			}
		}
		if(sysMethodWorkflowList!=null && sysMethodWorkflowList.size()>0) {
			for(SysMethodWorkflow entity:sysMethodWorkflowList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				sysMethodWorkflowMapper.insert(entity);
			}
		}
		if(sysMethodAnalyteList!=null && sysMethodAnalyteList.size()>0) {
			for(SysMethodAnalyte entity:sysMethodAnalyteList) {
				//外键设置
				entity.setId(null);
				entity.setMethodId(sysMethod.getId());
				entity.setCreateTime(new Date());
				sysMethodAnalyteMapper.insert(entity);
			}
		}
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(SysMethod sysMethod,List<SysMethodConsumptive> sysMethodConsumptiveList,List<SysMethodStdMaterial> sysMethodStdMaterialList,List<SysMethodInstrumentType> sysMethodInstrumentTypeList,List<SysMethodTestingPara> sysMethodTestingParaList,List<SysMethodRepeatType> sysMethodRepeatTypeList,List<SysMethodWorkflow> sysMethodWorkflowList,List<SysMethodAnalyte> sysMethodAnalyteList) {
		sysMethodMapper.updateById(sysMethod);
		
		//1.先删除子表数据
		sysMethodConsumptiveMapper.deleteByMainId(sysMethod.getId());
		sysMethodStdMaterialMapper.deleteByMainId(sysMethod.getId());
		sysMethodInstrumentTypeMapper.deleteByMainId(sysMethod.getId());
		sysMethodTestingParaMapper.deleteByMainId(sysMethod.getId());
		sysMethodRepeatTypeMapper.deleteByMainId(sysMethod.getId());
		sysMethodWorkflowMapper.deleteByMainId(sysMethod.getId());
		////sysMethodAnalyteMapper.deleteByMainId(sysMethod.getId());
		
		//2.子表数据重新插入
		if(sysMethodConsumptiveList!=null && sysMethodConsumptiveList.size()>0) {
			for(SysMethodConsumptive entity:sysMethodConsumptiveList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodConsumptiveMapper.insert(entity);
			}
		}
		if(sysMethodStdMaterialList!=null && sysMethodStdMaterialList.size()>0) {
			for(SysMethodStdMaterial entity:sysMethodStdMaterialList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodStdMaterialMapper.insert(entity);
			}
		}
		if(sysMethodInstrumentTypeList!=null && sysMethodInstrumentTypeList.size()>0) {
			for(SysMethodInstrumentType entity:sysMethodInstrumentTypeList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodInstrumentTypeMapper.insert(entity);
			}
		}
		if(sysMethodTestingParaList!=null && sysMethodTestingParaList.size()>0) {
			for(SysMethodTestingPara entity:sysMethodTestingParaList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodTestingParaMapper.insert(entity);
			}
		}
		if(sysMethodRepeatTypeList!=null && sysMethodRepeatTypeList.size()>0) {
			for(SysMethodRepeatType entity:sysMethodRepeatTypeList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				//如果数组有下一个.设置nextId为下一个节点的id
				if(sysMethodRepeatTypeList.indexOf(entity)<sysMethodRepeatTypeList.size()-1) {
					entity.setNextId(sysMethodRepeatTypeList.get(sysMethodRepeatTypeList.indexOf(entity) + 1).getId());
				}
				sysMethodRepeatTypeMapper.insert(entity);
			}
		}
		if(sysMethodWorkflowList!=null && sysMethodWorkflowList.size()>0) {
			for(SysMethodWorkflow entity:sysMethodWorkflowList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodWorkflowMapper.insert(entity);
			}
		}
		if(sysMethodAnalyteList!=null && sysMethodAnalyteList.size()>0) {
			for(SysMethodAnalyte entity:sysMethodAnalyteList) {
				//外键设置
				entity.setMethodId(sysMethod.getId());
				sysMethodAnalyteMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		sysMethodConsumptiveMapper.deleteByMainId(id);
		sysMethodStdMaterialMapper.deleteByMainId(id);
		sysMethodInstrumentTypeMapper.deleteByMainId(id);
		sysMethodTestingParaMapper.deleteByMainId(id);
		sysMethodRepeatTypeMapper.deleteByMainId(id);
		sysMethodWorkflowMapper.deleteByMainId(id);
		////sysMethodAnalyteMapper.deleteByMainId(id);
		sysMethodMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sysMethodConsumptiveMapper.deleteByMainId(id.toString());
			sysMethodStdMaterialMapper.deleteByMainId(id.toString());
			sysMethodInstrumentTypeMapper.deleteByMainId(id.toString());
			sysMethodTestingParaMapper.deleteByMainId(id.toString());
			sysMethodRepeatTypeMapper.deleteByMainId(id.toString());
			sysMethodWorkflowMapper.deleteByMainId(id.toString());
			////sysMethodAnalyteMapper.deleteByMainId(id.toString());
			sysMethodMapper.deleteById(id);
		}
	}
	
}
