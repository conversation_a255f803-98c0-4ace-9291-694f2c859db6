package org.jeecg.modules.lims_core.controller;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.service.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.InventoryVo;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2025-04-23
 * @Version: V1.0
 */
@Tag(name="库存表")
@RestController
@RequestMapping("/lims_core/inventory")
@Slf4j
public class InventoryController extends JeecgController<Inventory, IInventoryService> {
	@Autowired
	private IInventoryService inventoryService;
	@Autowired
	private IWarehouseInOutService warehouseInOutService;
	@Autowired
	private ISysWarehouseService sysWarehouseService;
	@Autowired
	private ISampleService sampleService;
	@Autowired
	private ISysWarehouseBoxService sysWarehouseBox;
	@Autowired
	private IConsumptiveService consumptiveService;
	@Autowired
	private IStandardMaterialService standardMaterialService;
	@Autowired
	private ITestTaskService testTaskService;
	@Autowired
	private IWarehouseOutApplyService  warehouseOutApplyService;
	@Autowired
	private IWarehouseInApplyService  warehouseInApplyService;
	@Autowired
	private ITestTaskFlowService testTaskFlowService;
	 private static final String FLOW_STEP_1 = "业务受理";
	 private static final String FLOW_STEP_2 = "仓库入库";
	 private static final String SAMPLE_FLOW_STATUS_1 = "未入库";
	 private static final String SAMPLE_FLOW_STATUS_2 = "已入库";
	 private static final String SAMPLE_FLOW_STATUS_3 = "已领用";

	 private static final String sm_status_1= "未入库";
	 private static final String sm_status_2 = "已入库";

	 private static final String OUT_IN_APPLY_STATUS_1 = "申请中";
	 private static final String OUT_IN_APPLY_STATUS_2 = "已领用";
	 private static final String OUT_IN_APPLY_STATUS_3 = "余量退回中";
	 private static final String OUT_IN_APPLY_STATUS_4 = "余量已退回";
	/**
	 * 分页列表查询
	 *
	 * @param inventory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "库存表-分页列表查询")
	@Operation(summary="库存表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InventoryVo>> queryPageList(InventoryVo inventory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InventoryVo> queryWrapper = QueryGenerator.initQueryWrapper(inventory, req.getParameterMap());
		Page<InventoryVo> page = new Page<InventoryVo>(pageNo, pageSize);
		IPage<InventoryVo> pageList = inventoryService.queryPageList(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param inventory
	 * @return
	 */
	@AutoLog(value = "库存表-添加")
	@Operation(summary="库存表-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:inventory:add')")
	@PostMapping(value = "/add")
	@Transactional(rollbackFor = Exception.class)
	public Result<String> add(@RequestBody Inventory inventory) {
		String[] articleNos = inventory.getArticleNo().split(",");
		String[] amounts = inventory.getAmount().split(",");
		String[] unitIds = inventory.getUnitId().split(",");
		if (articleNos.length != amounts.length || articleNos.length != unitIds.length) {
			return Result.error("编号和数量不对应");
		}
		for (int i = 0; i < articleNos.length; i++) {
			String articleNo = articleNos[i].trim();
			String amount = amounts[i].trim();
			String unitId = unitIds[i].trim();
			if (articleNo.isEmpty() || amount.isEmpty() || unitId.isEmpty()) {
				continue;
			}

			LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(Inventory::getArticleNo, articleNo);
			Inventory inve = inventoryService.getOne(queryWrapper);
			if (inve == null) {
				Inventory newInventory = new Inventory();
				newInventory.setArticleNo(articleNo);
				newInventory.setArticleTypeId(inventory.getArticleTypeId());
				newInventory.setAmount(amount);
				newInventory.setUnitId(unitId);
				newInventory.setWarehouseId(inventory.getWarehouseId());
				newInventory.setBoxId(inventory.getBoxId());
				newInventory.setCreateBy(inventory.getCreateBy());
				newInventory.setCreateTime(DateUtil.date());
				newInventory.setSysOrgCode(inventory.getSysOrgCode());
				inventoryService.save(newInventory);
			} else {
				BigDecimal result = new BigDecimal(inve.getAmount())
						.add(new BigDecimal(amount));
				inve.setAmount(result.toPlainString());
				inventoryService.updateById(inve);
			}

			WarehouseInOut warehouseInOut = new WarehouseInOut();
			warehouseInOut.setWarehouseId(inventory.getWarehouseId());
			warehouseInOut.setArticleNo(articleNo);
			warehouseInOut.setArticleTypeId(inventory.getArticleTypeId());
			warehouseInOut.setAmount(amount);
			warehouseInOut.setUnitId(unitId);
			warehouseInOut.setBoxId(inventory.getBoxId());
			warehouseInOut.setOperationTypeId("in");
			warehouseInOut.setOperationReasonId("入库");
			warehouseInOut.setCreateBy(inventory.getCreateBy());
			warehouseInOut.setCreateTime(DateUtil.date());
			warehouseInOut.setSysOrgCode(inventory.getSysOrgCode());
			warehouseInOutService.save(warehouseInOut);

			if (inventory.getArticleTypeId().equals("SAM")) {
				Sample sample = sampleService.getOne(new QueryWrapper<Sample>().eq("sample_no", articleNo));
				if (sample != null) {
					sample.setSampleFlowStatus(SAMPLE_FLOW_STATUS_2);
					sampleService.updateById(sample);
				}
			}
			if(articleNo.startsWith("GBT")){//标准物质
				LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
				standardMaterialWrapper.and(item->item.eq(StandardMaterial::getCode,articleNo));
				StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);
				standardMaterial.setSmStatus(sm_status_2);
				standardMaterialService.updateById(standardMaterial);
			}else if(articleNo.startsWith("FZB")){//耗材
				LambdaQueryWrapper<Consumptive> consumptiveWrapper = new LambdaQueryWrapper<>();
				consumptiveWrapper.and(item->item.eq(Consumptive::getCode,articleNo));
				Consumptive consumptive = consumptiveService.getOne(consumptiveWrapper);
				consumptive.setStatus(sm_status_2);
				consumptiveService.updateById(consumptive);
			}
		}

		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param inventory
	 * @return
	 */
	@AutoLog(value = "库存表-编辑")
	@Operation(summary="库存表-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:inventory:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Inventory inventory) {
		inventoryService.updateById(inventory);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "库存表-通过id删除")
	@Operation(summary="库存表-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:inventory:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inventoryService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "库存表-批量删除")
	@Operation(summary="库存表-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:inventory:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inventoryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "库存表-通过id查询")
	@Operation(summary="库存表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Inventory> queryById(@RequestParam(name="id",required=true) String id) {
		Inventory inventory = inventoryService.getById(id);
		if(inventory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inventory);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inventory
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:inventory:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Inventory inventory) {
        return super.exportXls(request, inventory, Inventory.class, "库存表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:inventory:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Inventory.class);
    }

	 /**
	  *   app判断样品或者标物是否存在
	  *
	  * @param inventory
	  * @return
	  */
	 @AutoLog(value = "库存表-APP添加")
	 @Operation(summary="库存表-APP添加")
	 @PostMapping(value = "/exist")
	 public Result<String> exist(@RequestBody Inventory inventory) {
		 String[] articleNos = inventory.getArticleNo().split(",");
		 for (int i = 0; i < articleNos.length; i++) {
			 String articleNo = articleNos[i].trim();
			 if (articleNo.isEmpty()) {
				 continue;
			 }
			 LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
			 queryWrapper.and(item->item.eq(Inventory::getArticleNo,articleNo));
			 Inventory inve = inventoryService.getOne(queryWrapper);
			 if (articleNo.startsWith("YPT")|| articleNo.startsWith("BCC")|| articleNo.startsWith("GBP")|| articleNo.startsWith("IT")||articleNo.startsWith("QCT")){//样品
				 //判断库存对象是否存在
				 if (inve != null){
					 return Result.OK("存在相同编号");
				 }
			 }
			 else if(articleNo.startsWith("FZB")){
				 //判断库存对象是否存在
				 if (inve != null){
					 return Result.OK("存在相同编号");
				 }
			 }
			 else if(articleNo.startsWith("GBT")){
				 if (inve != null){
					 return Result.OK("存在相同编号");
				 }
			 }else {
				 return Result.error("添加失败");
			 }
		 }
		 return Result.OK("");
	 }

	 /**
	  *   app添加入库
	  *
	  * @param inventory
	  * @return
	  */
	 @AutoLog(value = "库存表-APP添加")
	 @Operation(summary="库存表-APP添加")
	 @PreAuthorize("@jps.requiresPermissions('lims_core:inventory:add')")
	 @PostMapping(value = "/appadd")
	 public Result<String> appadd(@RequestBody Inventory inventory) {
		 WarehouseInApply inApply = warehouseInApplyService.getById(inventory.getApplyId());
		 SysWarehouseBox box = sysWarehouseBox.getById(inventory.getBoxId());
		 LoginUser sysUser = SecureUtil.currentUser();
		 LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.and(item->item.eq(Inventory::getArticleNo,inventory.getArticleNo()));
		 Inventory inve = inventoryService.getOne(queryWrapper);
		 if (inventory.getArticleNo().startsWith("YPT")|| inventory.getArticleNo().startsWith("BCC")|| inventory.getArticleNo().startsWith("GBP")||
		 inventory.getArticleNo().startsWith("QCT")){//样品
			 LambdaQueryWrapper<Sample> sampleWrapper = new LambdaQueryWrapper<>();
			 sampleWrapper.and(item->item.eq(Sample::getSampleNo,inventory.getArticleNo()));
			 Sample sample = sampleService.getOne(sampleWrapper);

			 double receiveCount = Double.parseDouble(sample.getReceiveCount() != null ? sample.getReceiveCount() : "0.0");
			 double retainAmount = Double.parseDouble(sample.getRetainAmount() != null ? sample.getRetainAmount() : "0.0");
			 String  amount = String.valueOf(receiveCount + retainAmount);
			 //判断库存对象是否存在
			 if (inve == null){
				 inventory.setWarehouseId(box.getWarehouseId());
				 inventory.setBoxId(box.getId());
				 inventory.setArticleNo(sample.getSampleNo());
				 inventory.setArticleTypeId("SAM");
				 inventory.setAmount(inventory.getAmount()== null ||  inventory.getAmount()== "" ? amount :inventory.getAmount());
				 inventory.setUnitId(sample.getReceiveCountUnit());
				 inventory.setCreateBy(sysUser.getUsername());
				 inventory.setCreateTime(DateUtil.date());
				 inventoryService.save(inventory);
			 }else{
				 BigDecimal result = new BigDecimal(inve.getAmount())
						 .add(new BigDecimal(inventory.getAmount()== null ||  inventory.getAmount()== "" ?amount:inventory.getAmount()));
				 inve.setAmount(result.toPlainString());
				 inve.setBoxId(box.getId());
				 inventoryService.updateById(inve);
			 }

			 testTaskService.getBaseMapper().selectList(new QueryWrapper<TestTask>().eq("sample_id",sample.getId())).forEach(task -> {
				 if(task.getCurStep().equals(FLOW_STEP_1)) {
					 TestTaskFlow testTaskFlow = new TestTaskFlow();
					 testTaskFlow.setTaskId(task.getId());
					 testTaskFlow.setStepId(FLOW_STEP_2);
					 testTaskFlowService.save(testTaskFlow);

					 task.setCurStep(FLOW_STEP_2);
					 testTaskService.updateById(task);
				 }else {
					 //throw new RuntimeException("该样品已入库，请勿重复入库！");//补样是可以重复入库，此时不需要流转节点
				 }
			 });
			 sample.setSampleFlowStatus(SAMPLE_FLOW_STATUS_2);
			 sampleService.updateById(sample);
			 if (inApply!=null){//余量退回，更新状态
				 //查询相关的出库申请
				 LambdaQueryWrapper<WarehouseOutApply> outApplyquery = new LambdaQueryWrapper<>();
				 outApplyquery.eq(WarehouseOutApply::getReturnId, inventory.getApplyId());
				 WarehouseOutApply outApply = warehouseOutApplyService.getOne(outApplyquery);
				 warehouseOutApplyService.update()
						 .set("status", OUT_IN_APPLY_STATUS_4)
						 .eq("id", outApply.getId())
						 .update();
				 warehouseInApplyService.update()
						 .set("status", OUT_IN_APPLY_STATUS_4)
						 .eq("id",inApply.getId())
						 .update();
			 }
			 //生成出入库记录
			 WarehouseInOut warehouseInOut = new WarehouseInOut();
			 warehouseInOut.setWarehouseId(box.getWarehouseId());//库存id
			 warehouseInOut.setArticleNo(sample.getSampleNo());//物品编号
			 warehouseInOut.setArticleTypeId("SAM");//物品类型id
			 warehouseInOut.setAmount(inventory.getAmount()== null ||  inventory.getAmount()== "" ? amount:inventory.getAmount());
			 warehouseInOut.setUnitId(sample.getReceiveCountUnit());
			 warehouseInOut.setBoxId(box.getId());
			 warehouseInOut.setOperationTypeId("in" );//操作类型id
			 warehouseInOut.setOperationReasonId("入库");//操作原因id
			 warehouseInOut.setCreateBy(sysUser.getUsername());
			 warehouseInOut.setCreateTime(DateUtil.date());
			 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());//部门id
			 warehouseInOutService.save(warehouseInOut);
		 }
		 else if(inventory.getArticleNo().startsWith("FZB")){
			 LambdaQueryWrapper<Consumptive> consumptiveWrapper = new LambdaQueryWrapper<>();
			 consumptiveWrapper.and(item->item.eq(Consumptive::getCode,inventory.getArticleNo()));
			 Consumptive consumptive = consumptiveService.getOne(consumptiveWrapper);

			 //判断库存对象是否存在
			 if (inve == null){
				 inventory.setWarehouseId(box.getWarehouseId());
				 inventory.setBoxId(box.getId());
				 inventory.setArticleNo(consumptive.getCode());
				 inventory.setArticleTypeId("HC");
				 inventory.setAmount(inventory.getAmount());
				 inventory.setUnitId(inventory.getUnitId());
				 inventory.setCreateBy(sysUser.getUsername());
				 inventory.setCreateTime(DateUtil.date());
				 inventoryService.save(inventory);
			 }else{
				 BigDecimal result = new BigDecimal(inve.getAmount())
						 .add(new BigDecimal(inventory.getAmount()));
				 inve.setAmount(result.toPlainString());
				 inve.setBoxId(box.getId());
				 inventoryService.updateById(inve);
			 }
			 //生成出入库记录
			 WarehouseInOut warehouseInOut = new WarehouseInOut();
			 warehouseInOut.setWarehouseId(box.getWarehouseId());//库存id
			 warehouseInOut.setArticleNo(consumptive.getCode());//物品编号
			 warehouseInOut.setArticleTypeId("HC");//物品类型id
			 warehouseInOut.setAmount(inventory.getAmount());
			 warehouseInOut.setUnitId(inventory.getUnitId());
			 warehouseInOut.setBoxId(box.getId());
			 warehouseInOut.setOperationTypeId("in" );//操作类型id
			 warehouseInOut.setOperationReasonId("入库");//操作原因id
			 warehouseInOut.setCreateBy(sysUser.getUsername());
			 warehouseInOut.setCreateTime(DateUtil.date());
			 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());//部门id
			 warehouseInOutService.save(warehouseInOut);
			 if (inApply!=null){//余量退回，更新状态
				 //查询相关的出库申请
				 LambdaQueryWrapper<WarehouseOutApply> outApplyquery = new LambdaQueryWrapper<>();
				 outApplyquery.eq(WarehouseOutApply::getReturnId, inventory.getApplyId());
				 WarehouseOutApply outApply = warehouseOutApplyService.getOne(outApplyquery);
				 warehouseOutApplyService.update()
						 .set("status", OUT_IN_APPLY_STATUS_4)
						 .eq("id", outApply.getId())
						 .update();
				 warehouseInApplyService.update()
						 .set("status", OUT_IN_APPLY_STATUS_4)
						 .eq("id",inApply.getId())
						 .update();
			 }
			 warehouseInOutService.update()
					 .set("status", sm_status_2)
					 .eq("id", consumptive.getId())
					 .update();
		 }
		 else if(inventory.getArticleNo().startsWith("GBT")){
			 if (inApply!=null){//余量退回
				 //查询标准物质表
				 LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
				 standardMaterialWrapper.eq(StandardMaterial::getCode, inventory.getArticleNo());
				 StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);
                 //查询相关的出库申请
				 LambdaQueryWrapper<WarehouseOutApply> outApplyquery = new LambdaQueryWrapper<>();
				 outApplyquery.eq(WarehouseOutApply::getReturnId, inventory.getApplyId());
				 WarehouseOutApply outApply = warehouseOutApplyService.getOne(outApplyquery);
				 //查询库存表
				 LambdaQueryWrapper<Inventory> inventoryQuery = new LambdaQueryWrapper<>();
				 inventoryQuery.eq(Inventory::getArticleNo, standardMaterial.getCode());
				 Inventory inve1 = inventoryService.getOne(inventoryQuery);

				 if (inve1 == null) {
					 return Result.error("余量退回失败，不存在该库存");
				 } else {
					 if(Objects.equals(inve1.getAmount(), "0")){
						 inve1.setBoxId(inventory.getBoxId());
						 inve1.setUnitId(inventory.getUnitId());
						 inve1.setAmount(inventory.getAmount());
						 inventoryService.updateById(inve1);
					 }else {
						 BigDecimal result = new BigDecimal(inve1.getAmount())
								 .add(new BigDecimal(inventory.getAmount()));
						 inve1.setAmount(result.toPlainString());
						 inve1.setBoxId(box.getId());
						 inventoryService.updateById(inve1);
					 }
				 }
				 WarehouseInOut warehouseInOut = new WarehouseInOut();
				 warehouseInOut.setWarehouseId(box.getWarehouseId());
				 warehouseInOut.setArticleNo(standardMaterial.getCode());
				 warehouseInOut.setArticleTypeId("BP");
				 warehouseInOut.setAmount(inventory.getAmount());
				 warehouseInOut.setUnitId(inventory.getUnitId());
				 warehouseInOut.setBoxId(box.getId());
				 warehouseInOut.setOperationTypeId("in");
				 warehouseInOut.setOperationReasonId("余量退回");
				 warehouseInOut.setCreateBy(inApply.getCreateBy());
				 warehouseInOut.setCreateTime(DateUtil.date());
				 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());
				 warehouseInOutService.save(warehouseInOut);

				 warehouseOutApplyService.update()
						 .set("status", OUT_IN_APPLY_STATUS_4)
						 .eq("id", outApply.getId())
						 .update();
				 warehouseInApplyService.update()
						 .set("status", OUT_IN_APPLY_STATUS_4)
						 .eq("id",inApply.getId())
						 .update();
			 }else {
				 LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
				 standardMaterialWrapper.eq(StandardMaterial::getCode, inventory.getArticleNo());
				 StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);

				 LambdaQueryWrapper<StandardMaterial> query = new LambdaQueryWrapper<>();
				 query.eq(StandardMaterial::getLotNo, standardMaterial.getLotNo());
				 query.eq(StandardMaterial::getSmStatus, sm_status_1);
				 List<StandardMaterial> list = standardMaterialService.list(query);
				 if (list.isEmpty()) {
					 list.add(standardMaterial);
				 }
				 for (StandardMaterial material : list) {
					 LambdaQueryWrapper<Inventory> inventoryQuery = new LambdaQueryWrapper<>();
					 inventoryQuery.eq(Inventory::getArticleNo, material.getCode());
					 Inventory inve1 = inventoryService.getOne(inventoryQuery);

					 if (inve1 == null) {
						 Inventory newInventory = new Inventory();
						 newInventory.setWarehouseId(box.getWarehouseId());
						 newInventory.setBoxId(box.getId());
						 newInventory.setArticleNo(material.getCode());
						 newInventory.setArticleTypeId("BP");
						 newInventory.setAmount(inventory.getAmount());
						 newInventory.setUnitId(inventory.getUnitId());
						 newInventory.setCreateBy(sysUser.getUsername());
						 newInventory.setCreateTime(DateUtil.date());
						 inventoryService.save(newInventory);
					 } else {
						 BigDecimal result = new BigDecimal(inve1.getAmount())
								 .add(new BigDecimal(inventory.getAmount()));
						 inve1.setAmount(result.toPlainString());
						 inve1.setBoxId(box.getId());
						 inventoryService.updateById(inve1);
					 }

					 WarehouseInOut warehouseInOut = new WarehouseInOut();
					 warehouseInOut.setWarehouseId(box.getWarehouseId());
					 warehouseInOut.setArticleNo(material.getCode());
					 warehouseInOut.setArticleTypeId("BP");
					 warehouseInOut.setAmount(inventory.getAmount());
					 warehouseInOut.setUnitId(inventory.getUnitId());
					 warehouseInOut.setBoxId(box.getId());
					 warehouseInOut.setOperationTypeId("in");
					 warehouseInOut.setOperationReasonId("入库");
					 warehouseInOut.setCreateBy(sysUser.getUsername());
					 warehouseInOut.setCreateTime(DateUtil.date());
					 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());
					 warehouseInOutService.save(warehouseInOut);

					 standardMaterialService.update()
							 .set("sm_status", sm_status_2)
							 .eq("id", material.getId())
							 .update();
				 }
			 }
		 }else {
			 return Result.error("添加失败");
		 }
		 return Result.OK("添加成功！");
	 }

	 /**
	  *   app添加出库
	  *
	  * @param inventory
	  * @return
	  */
	 @AutoLog(value = "库存表-APP出库")
	 @Operation(summary="库存表-APP出库")
	 @PostMapping(value = "/appdelivery")
	 @Transactional(rollbackFor = Exception.class)
	 public Result<String> appdelivery(@RequestBody Inventory inventory) {
		 WarehouseOutApply outApply = warehouseOutApplyService.getById(inventory.getApplyId());
		 LoginUser sysUser = SecureUtil.currentUser();
		 LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
		 queryWrapper.and(item->item.eq(Inventory::getArticleNo,inventory.getArticleNo()));
		 Inventory inve = inventoryService.getOne(queryWrapper);
		 if (inventory.getArticleNo().startsWith("YPT")|| inventory.getArticleNo().startsWith("BCC")|| inventory.getArticleNo().startsWith("GBP")
		 || inventory.getArticleNo().startsWith("QCT")){//样品
			 LambdaQueryWrapper<Sample> sampleWrapper = new LambdaQueryWrapper<>();
			 sampleWrapper.and(item->item.eq(Sample::getSampleNo,inventory.getArticleNo()));
			 Sample sample = sampleService.getOne(sampleWrapper);

			 //判断库存对象是否存在
			 if (inve == null){
				 return Result.error("出库失败,没有库存记录！");
			 }else{
				 BigDecimal result = new BigDecimal(inve.getAmount())
						 .subtract(new BigDecimal(inventory.getAmount()));
				 //库数量不足
				 if (result.compareTo(new BigDecimal(0)) < 0){
					 return Result.error("出库失败,库存数量不足！");
				 }
				 inve.setAmount(result.toPlainString());
				 inventoryService.updateById(inve);
			 }
			 //生成出入库记录
			 WarehouseInOut warehouseInOut = new WarehouseInOut();
			 warehouseInOut.setWarehouseId(inve.getWarehouseId());//库存id
			 warehouseInOut.setArticleNo(sample.getSampleNo());//物品编号
			 warehouseInOut.setArticleTypeId("SAM");//物品类型id
			 warehouseInOut.setAmount(inventory.getAmount()== null ? sample.getReceiveCount() : inventory.getAmount());
			 warehouseInOut.setUnitId(sample.getReceiveCountUnit());
			 warehouseInOut.setBoxId(inve.getBoxId());
			 warehouseInOut.setOperationTypeId("out" );//操作类型id
			 warehouseInOut.setOperationReasonId(inventory.getOperationReason());//操作原因id
			 warehouseInOut.setCreateBy(outApply!=null? outApply.getCreateBy(): sysUser.getUsername());
			 warehouseInOut.setCreateTime(DateUtil.date());
			 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());//部门id
			 warehouseInOutService.save(warehouseInOut);
             sample.setSampleFlowStatus(SAMPLE_FLOW_STATUS_3);
			 sampleService.updateById(sample);
		 }
		 else if(inventory.getArticleNo().startsWith("FZB")){
			 LambdaQueryWrapper<Consumptive> consumptiveWrapper = new LambdaQueryWrapper<>();
			 consumptiveWrapper.and(item->item.eq(Consumptive::getCode,inventory.getArticleNo()));
			 Consumptive consumptive = consumptiveService.getOne(consumptiveWrapper);

			 //判断库存对象是否存在
			 if (inve == null){
				 return Result.error("出库失败,没有库存记录！");
			 }else{
				 BigDecimal result = new BigDecimal(inve.getAmount())
						 .subtract(new BigDecimal(inventory.getAmount()));
				 //库数量不足
				 if (result.compareTo(new BigDecimal(0)) < 0){
					 return Result.error("出库失败,库存数量不足！");
				 }
				 inve.setAmount(result.toPlainString());
				 inventoryService.updateById(inve);
			 }
			 //生成出入库记录
			 WarehouseInOut warehouseInOut = new WarehouseInOut();
			 warehouseInOut.setWarehouseId(inve.getWarehouseId());//库存id
			 warehouseInOut.setArticleNo(consumptive.getCode());//物品编号
			 warehouseInOut.setArticleTypeId("HC");//物品类型id
			 warehouseInOut.setAmount(inventory.getAmount());
			 warehouseInOut.setUnitId(inve.getUnitId());
			 warehouseInOut.setBoxId(inve.getBoxId());
			 warehouseInOut.setOperationTypeId("out" );//操作类型id
			 warehouseInOut.setOperationReasonId(inventory.getOperationReason());//操作原因id
			 warehouseInOut.setCreateBy(outApply!=null? outApply.getCreateBy():sysUser.getUsername());
			 warehouseInOut.setCreateTime(DateUtil.date());
			 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());//部门id
			 warehouseInOutService.save(warehouseInOut);
		 }
		 else if(inventory.getArticleNo().startsWith("GBT")){
			 LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
			 standardMaterialWrapper.and(item->item.eq(StandardMaterial::getCode,inventory.getArticleNo()));
			 StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);

			 //判断库存对象是否存在
			 if (inve == null){
				 return Result.error("出库失败,没有库存记录！");
			 }else{
				 BigDecimal result = new BigDecimal(inve.getAmount())
						 .subtract(new BigDecimal(inventory.getAmount()));
				 //库数量不足
				 if (result.compareTo(new BigDecimal(0)) < 0){
					 return Result.error("出库失败,库存数量不足！");
				 }
				 inve.setAmount(result.toPlainString());
				 inventoryService.updateById(inve);
			 }
			 if(Objects.equals(inventory.getOperationReason(), "退回-数据有误-仓库拒绝")){
				 return Result.error("不能选该理由出库！");
			 }
			 //生成出入库记录
			 WarehouseInOut warehouseInOut = new WarehouseInOut();
			 warehouseInOut.setWarehouseId(inve.getWarehouseId());//库存id
			 warehouseInOut.setArticleNo(standardMaterial.getCode());//物品编号
			 warehouseInOut.setArticleTypeId("BP");//物品类型id
			 warehouseInOut.setAmount(inventory.getAmount());
			 warehouseInOut.setUnitId(inve.getUnitId());
			 warehouseInOut.setBoxId(inve.getBoxId());
			 warehouseInOut.setOperationTypeId("out" );//操作类型id
			 warehouseInOut.setOperationReasonId(inventory.getOperationReason());//操作原因id
			 warehouseInOut.setCreateBy(outApply!=null? outApply.getCreateBy():sysUser.getRealname());
			 warehouseInOut.setCreateTime(DateUtil.date());
			 warehouseInOut.setSysOrgCode(sysUser.getOrgCode());//部门id
			 warehouseInOutService.save(warehouseInOut);
		 }else {
			 return Result.error("添加失败");
		 }
		 if (outApply!=null){
			 outApply.setStatus(OUT_IN_APPLY_STATUS_2);
			 warehouseOutApplyService.updateById(outApply);
		 }
		 return Result.OK("添加成功！");
	 }

	 /**
	  * 查询货物数量和单位
	  *
	  * @param inventory
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "查询货物数量和单位-分页列表查询")
	 @Operation(summary="查询货物数量和单位-分页列表查询")
	 @GetMapping(value = "/fetchqtyandunit")
	 public Result<Inventory> fetchqtyandunit(Inventory inventory,
													   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													   HttpServletRequest req) {
		 LoginUser sysUser = SecureUtil.currentUser();
		 Inventory inve = new Inventory();
		 if (inventory.getArticleNo().startsWith("YPT")|| inventory.getArticleNo().startsWith("BCC") || inventory.getArticleNo().startsWith("GBP") || inventory.getArticleNo().startsWith("QCT")){//样品
			 LambdaQueryWrapper<Sample> sampleWrapper = new LambdaQueryWrapper<>();
			 sampleWrapper.and(item->item.eq(Sample::getSampleNo,inventory.getArticleNo()));
			 Sample sample = sampleService.getOne(sampleWrapper);
			 BigDecimal result = new BigDecimal(sample.getReceiveCount())
					 .add(new BigDecimal(sample.getRetainAmount()));
			 inve.setAmount(result.toString());
			 inve.setUnitId(sample.getReceiveCountUnit());
		 }
		 else if(inventory.getArticleNo().startsWith("FZB")){//耗材
			 LambdaQueryWrapper<Consumptive> consumptiveWrapper = new LambdaQueryWrapper<>();
			 consumptiveWrapper.and(item->item.eq(Consumptive::getCode,inventory.getArticleNo()));
			 Consumptive consumptive = consumptiveService.getOne(consumptiveWrapper);
 			 inve.setAmount(consumptive.getSpec());
			  inve.setUnitId("");
		 }
		 else if(inventory.getArticleNo().startsWith("GBT")){//标准物质
			 LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
			 standardMaterialWrapper.and(item->item.eq(StandardMaterial::getCode,inventory.getArticleNo()));
			 StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);
			 inve.setAmount(standardMaterial.getSpec().toString());
			 inve.setUnitId(standardMaterial.getSpecUnit());
		 }else {
			 return Result.error("添加失败");
		 }
		 return Result.OK(inve);
	 }

	 /**
	  * 查询库存表的货物数量和单位
	  *
	  * @param inventory
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "查询货物数量和单位-分页列表查询")
	 @Operation(summary="查询货物数量和单位-分页列表查询")
	 @GetMapping(value = "/inventoryQtyAndUnit")
	 public Result<Inventory> inventoryQtyAndUnit(Inventory inventory,
											  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											  HttpServletRequest req) {
		 Inventory inventory1 = new Inventory();
		 String[] articleNos = inventory.getArticleNo().split(",");
		 String[] amounts = new String[articleNos.length];
		 String[] unitIds = new String[articleNos.length];

		 for (int i = 0; i < articleNos.length; i++) {
			 String articleNo = articleNos[i].trim();
			 if (articleNo.isEmpty()) {
				 amounts[i] = "";
				 unitIds[i] = "";
				 continue;
			 }
			 LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
			 queryWrapper.eq(Inventory::getArticleNo, articleNo);
			 Inventory inve = inventoryService.getOne(queryWrapper);
			 if (inve != null) {
				 amounts[i] = inve.getAmount() != null ? inve.getAmount().toString() : "";
				 unitIds[i] = inve.getUnitId() != null ? inve.getUnitId() : "";
			 } else {
				 amounts[i] = "";
				 unitIds[i] = "";
			 }
		 }
		 inventory1.setArticleNo(inventory.getArticleNo());
		 inventory1.setAmount(String.join(",", amounts));
		 inventory1.setUnitId(String.join(",", unitIds));
		 return Result.OK(inventory1);
	 }
}
