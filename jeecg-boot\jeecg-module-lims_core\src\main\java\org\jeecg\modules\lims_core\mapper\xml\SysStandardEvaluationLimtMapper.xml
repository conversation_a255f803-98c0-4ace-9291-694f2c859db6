<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.lims_core.mapper.SysStandardEvaluationLimtMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE
		FROM  sys_standard_evaluation_limt
		WHERE
			 standard_id = #{mainId} 	</delete>

	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.entity.SysStandardEvaluationLimt">
		SELECT
		ssel.*,
		sa.name AS analyte_name,
		GROUP_CONCAT(DISTINCT sm.name ORDER BY FIND_IN_SET(sm.id, ssel.method_ids) SEPARATOR ',') AS method_names,
		GROUP_CONCAT(DISTINCT ss.name ORDER BY FIND_IN_SET(sm.id, ssel.method_ids) SEPARATOR ',') AS standard_names,
		MAX(sma.result_type) AS result_type
		FROM
		sys_standard_evaluation_limt ssel
		LEFT JOIN sys_analyte sa ON sa.id = ssel.analyte_id
		LEFT JOIN sys_method sm ON FIND_IN_SET(sm.id, ssel.method_ids)
		LEFT JOIN sys_standard ss ON sm.standard_id = ss.id
		LEFT JOIN sys_method_analyte sma on sm.id= sma.method_id
		WHERE ssel.standard_id = #{mainId}
		GROUP BY ssel.id, sa.name
		order by ssel.sort_num asc</select>

	<select id="selectByTestId" parameterType="java.lang.String" resultType="org.jeecg.modules.lims_core.entity.SysStandard">
		SELECT DISTINCT ss.*,t.is_submitted
		FROM sys_standard ss INNER JOIN  sys_standard_evaluation_limt  sse ON ss.id=sse.standard_id
							 INNER JOIN test_result tr on sse.id=tr.limit_id
							 INNER JOIN test t on t.id =tr.test_id
		WHERE
			t.id = #{Id} </select>

</mapper>
