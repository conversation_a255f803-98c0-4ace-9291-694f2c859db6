package org.jeecg.common.system.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TemplateDesigner {
    String value() default "";
    String entity() default "";
    String description() default "";
    String drillDown() default "";
    String drillUp() default "";
    String whereSel() default "";
}