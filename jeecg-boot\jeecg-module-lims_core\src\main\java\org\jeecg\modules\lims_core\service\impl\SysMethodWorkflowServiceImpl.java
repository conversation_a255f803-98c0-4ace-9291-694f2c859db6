package org.jeecg.modules.lims_core.service.impl;

import org.jeecg.modules.lims_core.entity.SysMethodWorkflow;
import org.jeecg.modules.lims_core.mapper.SysMethodWorkflowMapper;
import org.jeecg.modules.lims_core.service.ISysMethodWorkflowService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 检测流程
 * @Author: jeecg-boot
 * @Date:   2025-02-14
 * @Version: V1.0
 */
@Service
public class SysMethodWorkflowServiceImpl extends ServiceImpl<SysMethodWorkflowMapper, SysMethodWorkflow> implements ISysMethodWorkflowService {
	
	@Autowired
	private SysMethodWorkflowMapper sysMethodWorkflowMapper;
	
	@Override
	public List<SysMethodWorkflow> selectByMainId(String mainId) {
		return sysMethodWorkflowMapper.selectByMainId(mainId);
	}
}
