package org.jeecg.modules.lims_core.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.*;
import org.jeecg.modules.lims_core.service.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.WarehouseOutApplyVo;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 出库申请
 * @Author: jeecg-boot
 * @Date:   2025-04-21
 * @Version: V1.0
 */
@Tag(name="出库申请")
@RestController
@RequestMapping("/lims_core/warehouseOutApply")
@Slf4j
public class WarehouseOutApplyController extends JeecgController<WarehouseOutApply, IWarehouseOutApplyService> {
	@Autowired
	private IWarehouseOutApplyService warehouseOutApplyService;
	 @Autowired
	 private ISampleService sampleService;
	 @Autowired
	 private ISysWarehouseBoxService sysWarehouseBox;
	 @Autowired
	 private IConsumptiveService consumptiveService;
	 @Autowired
	 private IStandardMaterialService standardMaterialService;

     @Autowired
     private ISysBaseAPI iSysBaseAPI;

	 private static final String OUT_IN_APPLY_STATUS_1 = "申请中";
	 private static final String OUT_IN_APPLY_STATUS_2 = "已领用";
	 private static final String OUT_IN_APPLY_STATUS_3 = "余量退回中";
	 private static final String OUT_IN_APPLY_STATUS_4 = "余量已退回";
	 private static final String OUT_IN_APPLY_STATUS_5 = "退回-数据有误-仓库拒绝";

	 /**
	 * 分页列表查询
	 *
	 * @param warehouseOutApply
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "出库申请-分页列表查询")
	@Operation(summary="出库申请-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WarehouseOutApplyVo>> queryPageList(WarehouseOutApplyVo warehouseOutApply,
															@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
															@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
															HttpServletRequest req) {
        QueryWrapper<WarehouseOutApplyVo> queryWrapper = QueryGenerator.initQueryWrapper(warehouseOutApply, req.getParameterMap());
		Page<WarehouseOutApplyVo> page = new Page<WarehouseOutApplyVo>(pageNo, pageSize);
		IPage<WarehouseOutApplyVo> pageList = warehouseOutApplyService.queryPageList(page, queryWrapper);
		pageList.getRecords().stream().forEach(item -> {
			item.setDeptName(String.join(",", iSysBaseAPI.getDepartNamesByUsername(item.getCreateBy())));
			item.setWarehousebox(warehouseOutApplyService.getwarehouseboxBycode(item.getArticleNo()));
		});
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param warehouseOutApply
	 * @return
	 */
	@AutoLog(value = "出库申请-添加")
	@Operation(summary="出库申请-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_out_apply:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WarehouseOutApply warehouseOutApply) throws WxErrorException {

		String[] articleNos = warehouseOutApply.getArticleNo().split(",");
		String[] amounts = warehouseOutApply.getAmount().split(",");

		if (articleNos.length != amounts.length) {
			return Result.error("编号和数量不对应");
		}
		for (int i = 0; i < articleNos.length; i++) {
			String articleNo = articleNos[i].trim();
			String amount = amounts[i].trim();
			if (articleNo.isEmpty() || amount.isEmpty()) {
				continue;
			}
			WarehouseOutApply newApply = new WarehouseOutApply();
			BeanUtils.copyProperties(warehouseOutApply, newApply); // Copy base properties
			newApply.setArticleNo(articleNo);
			newApply.setAmount(amount);
			newApply.setStatus(OUT_IN_APPLY_STATUS_1);
			if (articleNo.startsWith("YPT")|| articleNo.startsWith("BCC")|| articleNo.startsWith("GBP")||articleNo.startsWith("QCT")){//样品
				LambdaQueryWrapper<Sample> sampleWrapper = new LambdaQueryWrapper<>();
				sampleWrapper.and(item->item.eq(Sample::getSampleNo,articleNo));
				Sample sample = sampleService.getOne(sampleWrapper);
				newApply.setUnitId(sample.getReceiveCountUnit());
			}
			else if(articleNo.startsWith("FZB")){
				LambdaQueryWrapper<Consumptive> consumptiveWrapper = new LambdaQueryWrapper<>();
				consumptiveWrapper.and(item->item.eq(Consumptive::getCode,articleNo));
				Consumptive consumptive = consumptiveService.getOne(consumptiveWrapper);
				newApply.setUnitId(consumptive.getUnit());
			}
			else if(articleNo.startsWith("GBT")){
				LambdaQueryWrapper<StandardMaterial> standardMaterialWrapper = new LambdaQueryWrapper<>();
				standardMaterialWrapper.and(item->item.eq(StandardMaterial::getCode,articleNo));
				StandardMaterial standardMaterial = standardMaterialService.getOne(standardMaterialWrapper);
				newApply.setUnitId(standardMaterial.getSpecUnit());
			}
			warehouseOutApplyService.save(newApply);
			//发起出库申请
			WarehouseOutApply warehouseOutApplyNew = warehouseOutApplyService.getById(newApply.getId());
			warehouseOutApplyService.apply(warehouseOutApplyNew , ApplyType.WAREHOUSE_APPLY_OUT);
		}
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param warehouseOutApply
	 * @return
	 */
	@AutoLog(value = "出库申请-编辑")
	@Operation(summary="出库申请-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_out_apply:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WarehouseOutApply warehouseOutApply) {
		warehouseOutApplyService.updateById(warehouseOutApply);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "出库申请-通过id删除")
	@Operation(summary="出库申请-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_out_apply:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		warehouseOutApplyService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "出库申请-批量删除")
	@Operation(summary="出库申请-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_out_apply:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.warehouseOutApplyService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "出库申请-通过id查询")
	@Operation(summary="出库申请-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WarehouseOutApply> queryById(@RequestParam(name="id",required=true) String id) {
		WarehouseOutApply warehouseOutApply = warehouseOutApplyService.getById(id);
		if(warehouseOutApply==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(warehouseOutApply);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param warehouseOutApply
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_out_apply:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WarehouseOutApply warehouseOutApply) {
        return super.exportXls(request, warehouseOutApply, WarehouseOutApply.class, "出库申请");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_core:warehouse_out_apply:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WarehouseOutApply.class);
    }

	 /**
	  *  仓库拒绝出库，可能原因是申请数量问题之类
	  *
	  * @param warehouseOutApply
	  * @return
	  */
	 @AutoLog(value = "出库申请-编辑")
	 @Operation(summary="出库申请-编辑")
	 @RequestMapping(value = "/updatestatus", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> updatestatus(@RequestBody WarehouseOutApply warehouseOutApply) {
		 warehouseOutApplyService.update()
				 .set("status", OUT_IN_APPLY_STATUS_5)
				 .eq("id",warehouseOutApply.getId())
				 .update();
		 return Result.OK("编辑成功!");
	 }

}
