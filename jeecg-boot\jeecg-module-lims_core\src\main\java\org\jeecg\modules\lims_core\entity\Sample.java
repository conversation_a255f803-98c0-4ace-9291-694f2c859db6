package org.jeecg.modules.lims_core.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 样品
 * @Author: jeecg-boot
 * @Date:   2025-01-13
 * @Version: V1.0
 */
@Data
@TableName("sample")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="样品")
public class Sample implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**报价单号*/
    @Excel(name = "报价单号", width = 15, dictTable = "quotation", dicText = "quotation_no", dicCode = "id")
    @Dict(dictTable = "quotation", dicText = "quotation_no", dicCode = "id")
    @Schema(description = "报价单号")
    private java.lang.String quotationId;
    /**合同单号*/
    @Excel(name = "合同单号", width = 15, dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
    @Dict(dictTable = "biz_order", dicText = "contract_no", dicCode = "id")
    @Schema(description = "合同单号")
    private java.lang.String orderId;
    /**编号*/
    @Excel(name = "编号", width = 15)
    @Schema(description = "编号")
    private java.lang.String sampleNo;
    /**产品名称*/
    @Excel(name = "样品名称", width = 15)
    @Schema(description = "样品名称")
    private java.lang.String name;
    /**批号*/
    @Excel(name = "批号", width = 15)
    @Schema(description = "批号")
    private java.lang.String lotNo;
    /**生产厂家*/
    @Excel(name = "生产厂家", width = 15)
    @Schema(description = "生产厂家")
    private java.lang.String manufacturer;
    /**标准价格*/
    @Excel(name = "标准价格", width = 15)
    @Schema(description = "标准价格")
    private java.math.BigDecimal standardPrice;
    /**PM价格*/
    @Excel(name = "PM价格", width = 15)
    @Schema(description = "PM价格")
    private java.math.BigDecimal pmPrice;
    /**申请单价*/
    @Excel(name = "申请单价", width = 15)
    @Schema(description = "申请单价")
    private java.math.BigDecimal applyPrice;
    /**标准工期*/
    @Excel(name = "标准工期", width = 15)
    @Schema(description = "标准工期")
    private java.lang.Integer standardLeadTime;
    /**PM工期 */
    @Excel(name = "PM工期 ", width = 15)
    @Schema(description = "PM工期 ")
    private java.lang.Integer pmLeadTime;
    /**折扣*/
    @Excel(name = "折扣", width = 15)
    @Schema(description = "折扣")
    private java.lang.Double discount;
    /**接收日期*/
    @Excel(name = "接收日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "接收日期")
    private java.util.Date receiveDate;
    /**创建人*/
    @Schema(description = "创建人")
    @Excel(name = "创建人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
    /**产品列表*/
    @Excel(name = "产品列表", width = 15, dictTable = "sys_product", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_product", dicText = "name", dicCode = "id")
    @Schema(description = "产品列表")
    private java.lang.String productIds;
    /**业务分类*/
    @Excel(name = "业务类别", width = 15, dicCode = "biz_type")
    @Dict(dictTable = "biz_type", dicText = "name", dicCode = "id")
    @Schema(description = "业务类别")
    private java.lang.String bizTypeId;
    /**客户名称*/
    @Excel(name = "客户名称", width = 15, dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer", dicText = "name", dicCode = "id")
    @Schema(description = "客户名称")
    private java.lang.String customerId;
    /**客户联系人*/
    @Excel(name = "客户联系人", width = 15, dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Dict(dictTable = "sys_customer_contact", dicText = "name", dicCode = "id")
    @Schema(description = "客户联系人")
    private java.lang.String customerContactId;

    /**仓库库位*/
    @Excel(name = "仓库库位", width = 15)
    @Schema(description = "仓库库位")
    private java.lang.String warehouseLocation;
    /**领用备注*/
    @Excel(name = "领用备注", width = 15)
    @Schema(description = "领用备注")
    private java.lang.String remark;
    /**成分*/
    @Excel(name = "成分", width = 15)
    @Schema(description = "成分")
    private java.lang.String composition;
    /**性状*/
    @Excel(name = "性状", width = 15)
    @Schema(description = "性状")
    private java.lang.String properties;
    /**规格*/
    @Excel(name = "规格", width = 15)
    @Schema(description = "规格")
    private java.lang.String specification;
    /**贮藏*/
    @Excel(name = "贮藏", width = 15)
    @Schema(description = "贮藏")
    private java.lang.String storage;
    /**有效期*/
    @Excel(name = "有效期", width = 15)
    @Schema(description = "有效期")
    private java.lang.String expiry;
    /**每日最大摄入量*/
    @Excel(name = "每日最大摄入量", width = 15)
    @Schema(description = "每日最大摄入量")
    private java.lang.String maxDailyIntake;
    /**治疗周期*/
    @Excel(name = "治疗周期", width = 15)
    @Schema(description = "治疗周期")
    private java.lang.String treatmentCycle;
    /**品种类别*/
    @Excel(name = "品种类别", width = 15, dicCode = "drug_category")
    @Dict(dicCode = "drug_category")
    @Schema(description = "品种类别")
    private java.lang.String category;
    /**pH值*/
    @Excel(name = "pH值", width = 15)
    @Schema(description = "pH值")
    private java.lang.String phValue;
    /**给药途径*/
    @Excel(name = "给药途径", width = 15, dicCode = "route_of_administration")
    @Dict(dicCode = "route_of_administration")
    @Schema(description = "给药途径")
    private java.lang.String routeOfAdministration;
    /**剧烈生产条件*/
    @Excel(name = "剧烈生产条件", width = 15, dicCode = "severe_condition")
    @Dict(dicCode = "severe_condition")
    @Schema(description = "剧烈生产条件")
    private java.lang.String severeCondition;
    /**药包材类型*/
    @Excel(name = "药包材类型", width = 15, dicCode = "packaging_material_type")
    @Dict(dicCode = "packaging_material_type")
    @Schema(description = "药包材类型")
    private java.lang.String packagingMaterialType;
    /**父级节点*/
    @Excel(name = "父级节点", width = 15)
    @Schema(description = "父级节点")
    private java.lang.String pid;
    /**是否有子节点*/
    @Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(description = "是否有子节点")
    private java.lang.String hasChild;
    /**商机*/
    @Excel(name = "商机", width = 15, dictTable = "opportunity", dicText = "opportunity_no", dicCode = "id")
    @Dict(dictTable = "opportunity", dicText = "opportunity_no", dicCode = "id")
    @Schema(description = "商机")
    private java.lang.String opportunityId;
    /**剂型*/
    @Excel(name = "剂型", width = 15, dicCode = "dosage_form")
    @Dict(dicCode = "dosage_form")
    @Schema(description = "剂型")
    private java.lang.String dosageForm;
    /**产地*/
    @Excel(name = "产地", width = 15)
    @Schema(description = "产地")
    private java.lang.String origin;
    /**检品数量*/
    @Excel(name = "检品数量", width = 15)
    @Schema(description = "检品数量")
    private java.lang.String receiveCount;
    /**数量单位*/
    @Excel(name = "数量单位", width = 15, dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Dict(dictTable = "sys_unit", dicText = "unit_name", dicCode = "id")
    @Schema(description = "数量单位")
    private java.lang.String receiveCountUnit;
    /**留样数量*/
    @Excel(name = "留样数量", width = 15)
    @Schema(description = "留样数量")
    private java.lang.String retainAmount;
    /**包装规格*/
    @Excel(name = "包装规格", width = 15)
    @Schema(description = "包装规格")
    private java.lang.String packMedicineType;
    /**生产日期*/
    @Excel(name = "生产日期", width = 15)
    @Schema(description = "生产日期")
    private java.lang.String productDate;
    /**注册编号*/
    @Excel(name = "注册编号", width = 15)
    @Schema(description = "注册编号")
    private java.lang.String certificateNo;
    /**批准文号*/
    @Excel(name = "批准文号", width = 15)
    @Schema(description = "批准文号")
    private java.lang.String authorizeNo;
    /**研发项目编码*/
    @Excel(name = "研发项目编码", width = 15, dictTable = "rd_project", dicText = "rd_no", dicCode = "id")
    @Dict(dictTable = "rd_project", dicText = "rd_no", dicCode = "id")
    @Schema(description = "研发项目编码")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private java.lang.String rdId;

    /**是否生成研发编码*/
    @TableField(exist = false)
    private String isGenerateRdNo;

    /**是否特殊管理样品*/
    @Excel(name = "是否特殊管理样品", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(description = "是否特殊管理样品")
    private java.lang.String isSpecialManage;

    /**生产单位地址*/
    @Excel(name = "生产单位地址", width = 15)
    @Schema(description = "生产单位地址")
    private java.lang.String manufacturerAddr;

    /**包装材料*/
    @Excel(name = "包装材料", width = 15, dicCode = "pack_material")
    @Dict(dicCode = "pack_material")
    @Schema(description = "包装材料")
    private java.lang.String packMaterial;
    /**制剂规格*/
    @Excel(name = "制剂规格", width = 15)
    @Schema(description = "制剂规格")
    private java.lang.String dosageSpec;
    /**特殊检品*/
    @Excel(name = "特殊检品", width = 15)
    @Schema(description = "特殊检品")
    private java.lang.String specialSample;
    /**检品类别*/
    @Excel(name = "检品类别", width = 15, dictTable = "sys_category", dicText = "name", dicCode = "id")
    @Schema(description = "检品类别")
    private java.lang.String sampleType;
    /**商标*/
    @Excel(name = "商标", width = 15)
    @Schema(description = "商标")
    private java.lang.String trademark;
    /**安全声明*/
    @Excel(name = "安全声明", width = 15)
    @Schema(description = "安全声明")
    private java.lang.String safeDeclare;
    /**安全声明描述*/
    @Excel(name = "安全声明描述", width = 15)
    @Schema(description = "安全声明描述")
    private java.lang.String safeDeclareDesc;


    /**检测标准*/
    @Excel(name = "检测标准", width = 15)
    @Schema(description = "检测标准")
    private java.lang.String methodInfo;
    /**判定依据*/
    @Excel(name = "判定依据", width = 15)
    @Schema(description = "判定依据")
    private java.lang.String standardInfo;
    /**委托日期*/
    @Excel(name = "委托日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "委托日期")
    private java.util.Date entrustDate;
    /**检验完成时间*/
    @Excel(name = "检验完成时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检验完成时间")
    private java.util.Date testEndDate;
    /**受理人*/
    @Excel(name = "受理人", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Schema(description = "受理人")
    private java.lang.String acceptor;
    /**认证图章*/
    @Excel(name = "认证图章", width = 15, dicCode = "seal_type")
    @Dict(dicCode = "seal_type")
    @Schema(description = "认证图章")
    private java.lang.String sealType;
    /**报告版本*/
    @Excel(name = "报告版本", width = 15, dicCode = "report_version")
    @Dict(dicCode = "report_version")
    @Schema(description = "报告版本")
    private java.lang.String reportVersion;
    /**检测项目*/
    @Excel(name = "检测项目", width = 15, dicCode = "test_choice")
    @Dict(dicCode = "test_choice")
    @Schema(description = "检测项目")
    private java.lang.String testChoice;
    /**项目内容*/
    @Excel(name = "项目内容", width = 15)
    @Schema(description = "项目内容")
    private java.lang.String testContent;
    /**测余样品处理*/
    @Excel(name = "测余样品处理", width = 15, dicCode = "sample_return")
    @Dict(dicCode = "sample_return")
    @Schema(description = "测余样品处理")
    private java.lang.String sampleReturn;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String testRemark;
    /**检品流转状态*/
    @Excel(name = "检品流转状态", width = 15, dicCode = "sample_flow_status")
    @Dict(dicCode = "sample_flow_status")
    @Schema(description = "检品流转状态")
    private java.lang.String sampleFlowStatus;


    /**
     * 客户地址
     */
    @TableField(exist = false)
    private String detailedAddress;

    /**
     * 客户电话
     */
    @TableField(exist = false)
    private String phone;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String rdName;

    /**测试状态*/
    @Excel(name = "测试状态", width = 15, dicCode = "test_control_status")
    @Dict(dicCode = "test_control_status")
    @Schema(description = "测试状态")
    private java.lang.String testControlStatus;

    @TableField(exist = false)
    private String serviceType;

    @TableField(exist = false)
    private String isSupplementary;

    /**工期类型*/
    @Excel(name = "工期类型", width = 15, dicCode = "day_type")
    @Dict(dicCode = "day_type")
    @Schema(description = "工期类型")
    private java.lang.String dayType;
    /**PM备注*/
    @Excel(name = "PM备注", width = 15)
    @Schema(description = "PM备注")
    private java.lang.String pmRemark;
}
