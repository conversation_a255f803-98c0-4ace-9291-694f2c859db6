package org.jeecg.modules.lims_core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.lims_core.entity.SysMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 方法
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
public interface SysMethodMapper extends BaseMapper<SysMethod> {
    @Select("SELECT * FROM sys_method WHERE product_id = #{productId}")
    List<SysMethod> selectByProductId(String productId);
}
