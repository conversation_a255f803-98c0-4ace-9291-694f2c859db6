package org.jeecg.modules.lims_order.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import me.chanjar.weixin.common.error.WxErrorException;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.FillRuleConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.modules.lims_order.entity.BizOrder;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.lims_order.service.IBizOrderService;
import org.jeecg.modules.lims_order.vo.enums.ApplyType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 订单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Tag(name="订单")
@RestController
@RequestMapping("/lims_order/bizOrder")
@Slf4j
public class BizOrderController extends JeecgController<BizOrder, IBizOrderService> {
	@Autowired
	private IBizOrderService bizOrderService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bizOrder
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "订单-分页列表查询")
	@Operation(summary="订单-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BizOrder>> queryPageList(BizOrder bizOrder,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<BizOrder> queryWrapper = QueryGenerator.initQueryWrapper(bizOrder, req.getParameterMap());
		Page<BizOrder> page = new Page<BizOrder>(pageNo, pageSize);
		IPage<BizOrder> pageList = bizOrderService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param bizOrder
	 * @return
	 */
	@AutoLog(value = "订单-添加")
	@Operation(summary="订单-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BizOrder bizOrder) {
		bizOrderService.saveOrder(bizOrder);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param bizOrder
	 * @return
	 */
	@AutoLog(value = "订单-编辑")
	@Operation(summary="订单-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BizOrder bizOrder) {
		bizOrderService.updateById(bizOrder);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "订单-通过id删除")
	@Operation(summary="订单-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bizOrderService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "订单-批量删除")
	@Operation(summary="订单-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bizOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "订单-通过id查询")
	@Operation(summary="订单-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BizOrder> queryById(@RequestParam(name="id",required=true) String id) {
		BizOrder bizOrder = bizOrderService.getById(id);
		if(bizOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bizOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param bizOrder
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BizOrder bizOrder) {
        return super.exportXls(request, bizOrder, BizOrder.class, "订单");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BizOrder.class);
    }

	 /**
	  *   生成订单号
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "订单-添加")
	 @Operation(summary="订单-添加")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:add')")
	 @GetMapping(value = "/generateBizNo")
	 public Result<String> generateBizNo(@RequestParam(name="id",required=true) String id) {
		 com.alibaba.fastjson.JSONObject formData = new com.alibaba.fastjson.JSONObject();
		 formData.put("customer_id", id);
		 String bizNo = FillRuleUtil.executeRule(FillRuleConstant.BIZORDER, formData).toString();
		 return Result.OK(bizNo);
	 }


	 /**
	  *  发起审批
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-发起审批")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:add')")
	 @RequestMapping(value = "/apply", method = RequestMethod.GET)
	 public Result<String> apply(@RequestParam(name="contractNo",required=true) String contractNo) throws WxErrorException {
		 bizOrderService.apply(contractNo, ApplyType.ORDRER_APPLY);
		 return Result.OK("发起审批成功!");
	 }

	 /**
	  *  解锁
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-解锁")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:add')")
	 @RequestMapping(value = "/unlock", method = RequestMethod.GET)
	 public Result<String> unlock(@RequestParam(name="id",required=true) String id) throws WxErrorException {
		 BizOrder order = bizOrderService.getById(id);
		 bizOrderService.apply(order.getContractNo(), ApplyType.ORDRER_UNLOCK);
		 return Result.OK("发起解锁审批成功!");
	 }



	 /**
	  *  操作记录
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-操作记录")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:add')")
	 @RequestMapping(value = "/listDataLog", method = RequestMethod.GET)
	 public Result<List<Map>> listDataLog(@RequestParam(name="id",required=true) String id) {
		 List<Map> dataLogList = bizOrderService.listDataLog(id);
		 return Result.OK(dataLogList);
	 }
	 /**
	  *  进单
	  *
	  * @param request
	  * @param response
	  * @return
	  */
	 @AutoLog(value = "报价单-进单")
	 @PreAuthorize("@jps.requiresPermissions('lims_order:biz_order:add')")
	 @RequestMapping(value = "/transferToLab", method = RequestMethod.GET)
	 public Result<String> transferToLab(@RequestParam(name="id",required=true) String id) {
		 bizOrderService.transferToLab(id);
		 return Result.OK("进单成功!");
	 }

}
