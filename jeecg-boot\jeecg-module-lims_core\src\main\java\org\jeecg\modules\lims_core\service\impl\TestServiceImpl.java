package org.jeecg.modules.lims_core.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOaService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.oa.SummaryInfo;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApplyEventRequest;
import me.chanjar.weixin.cp.bean.oa.WxCpOaApprovalTemplateResult;
import me.chanjar.weixin.cp.bean.oa.applydata.ApplyDataContent;
import me.chanjar.weixin.cp.bean.oa.applydata.ContentValue;
import org.jeecg.common.config.mqtoken.UserTokenContext;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.WxCpConfiguration;
import org.jeecg.config.WxCpProperties;
import org.jeecg.config.security.utils.SecureUtil;
import org.jeecg.modules.lims_core.entity.SysMethod;
import org.jeecg.modules.lims_core.entity.SysMethodAnalyte;
import org.jeecg.modules.lims_core.mapper.SysMethodAnalyteMapper;
import org.jeecg.modules.lims_core.mapper.TestResultMapper;
import org.jeecg.modules.lims_core.service.ISysMethodService;
import org.jeecg.modules.lims_core.entity.Test;
import org.jeecg.modules.lims_core.entity.TestTask;
import org.jeecg.modules.lims_core.mapper.TestMapper;
import org.jeecg.modules.lims_core.service.ITestService;
import org.jeecg.modules.lims_core.service.ITestTaskService;
import org.jeecg.modules.lims_core.vo.SysProductSubVo;
import org.jeecg.modules.wx.entity.WecomSp;
import org.jeecg.modules.wx.service.impl.WecomSpServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 试验标本
 * @Author: jeecg-boot
 * @Date:   2025-01-06
 * @Version: V1.0
 */
@Service
public class TestServiceImpl extends ServiceImpl<TestMapper, Test> implements ITestService {
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private ITestTaskService testTaskService;
    @Autowired
    private ISysMethodService sysMethodService;
    @Autowired
    private WecomSpServiceImpl wecomSpService;
    @Autowired
    private TestResultMapper testResultMapper;
    @Autowired
    private SysMethodAnalyteMapper  sysMethodAnalyteMapper;

    @Override
    public void applyRework(String testId,String reworkReason, String reworkBy) throws WxErrorException {
        Test test = this.getById(testId);
        TestTask testTask = testTaskService.getById(test.getTaskId());
        testTask.setRetestApplyStatus("1");
        testTask.setRetestReason(reworkReason);
        testTaskService.updateById(testTask);
        WxCpOaApplyEventRequest request = new WxCpOaApplyEventRequest();
        LoginUser curUser = SecureUtil.currentUser();
        request.setCreatorUserId(sysBaseAPI.getThirdUserIdByUserId(curUser.getId(), "wechat_enterprise"));
        SummaryInfo.SummaryInfoData summaryInfoData = new SummaryInfo.SummaryInfoData();
        SysMethod sysMethod = sysMethodService.getById(testTask.getMethodId());
        summaryInfoData.setText(sysMethod.getName() + ":===复测申请系统调试===" );
        summaryInfoData.setLang("zh_CN");
        List<SummaryInfo.SummaryInfoData> lstSummaryInfoData = new ArrayList<>();
        lstSummaryInfoData.add(summaryInfoData);
        SummaryInfo summaryInfo = new SummaryInfo();
        summaryInfo.setSummaryInfoData(lstSummaryInfoData);
        List<SummaryInfo> summaryList = new ArrayList<>();
        summaryList.add(0, summaryInfo);
        request.setSummaryList(summaryList);
        String templateId = "C4ZWhkzXe7zyDq1fsheaP8K4JMQuvCiw5Szf3cF1h";
        WxCpProperties wxCpProperties = WxCpConfiguration.getProperties();
        String corpId = wxCpProperties.getAppConfigs().get(0).getCorpId();
        int agentId = wxCpProperties.getAppConfigs().get(0).getAgentId();
        WxCpService cpService = WxCpConfiguration.getCpService(corpId, agentId);
        WxCpOaService oaService = cpService.getOaService();
        WxCpOaApprovalTemplateResult templateDetail = oaService.getTemplateDetail(templateId);
        System.out.println(templateDetail.toJson());
        request.setTemplateId(templateId);
        request.setUseTemplateApprover(0);// 调试环境，正式环境要为1；
        request.setApprovers(getApprovers());// 调试需要，正式环境注释掉
        request.setApplyData(getApplyData(reworkReason,reworkBy));
        try {
            String sp_no = oaService.apply(request);
            WecomSp wecomSp = new WecomSp();//插入审批表
            wecomSp.setSpNo(sp_no);
            wecomSp.setTargetId(testTask.getId());
            wecomSp.setTargetImpl("testServiceImpl");
            wecomSpService.save(wecomSp);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    public void wxCallback(WxCpXmlMessage wxCpXmlMessage, String id, String typeId) {
        Test test = this.getById(id);
        TestTask oldTestTask = testTaskService.getById(test.getTaskId());
        TestTask newTestTask = new TestTask();
        BeanUtils.copyProperties(oldTestTask, newTestTask);
        if (wxCpXmlMessage.getEvent().equals("sys_approval_change")) {
            // 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
            if(wxCpXmlMessage.getApprovalInfo().getSpStatus().toString().equals("2")){
                int retestNo = 1;
                if (newTestTask.getRetestNo() != null) {
                    retestNo = newTestTask.getRetestNo() + 1;
                }
                newTestTask.setRetestNo(retestNo);
                newTestTask.setAssigner(JwtUtil.getUsername(UserTokenContext.getToken()));
                newTestTask.setAssignTime(DateUtil.date());
                oldTestTask.setRetestTaskId(newTestTask.getId());
            }
            oldTestTask.setRetestApplyStatus(wxCpXmlMessage.getApprovalInfo().getSpStatus().toString());
            List<SysProductSubVo> sysProductSubVos = new ArrayList<>();

            Test t = this.baseMapper.selectList(new QueryWrapper<Test>().eq("task_id", oldTestTask.getId())).get(0);
            testResultMapper.selectByTestId(t.getId()).forEach(
                    testResult -> {
                        SysMethodAnalyte sysMethodAnalyte = sysMethodAnalyteMapper.selectById(testResult.getMethodAnalyteId());
                        SysProductSubVo sysProductSubVo = new SysProductSubVo();
                        sysProductSubVo.setAnalyteId(sysMethodAnalyte.getAnalyteId());
                        sysProductSubVo.setEvaluationId(testResult.getLimitId());
                        sysProductSubVos.add(sysProductSubVo);
                        testResult.setReportable("否");
                    }
            );

            testTaskService.saveBatch(Arrays.asList(oldTestTask,newTestTask));
            testTaskService.generateTest(newTestTask, sysProductSubVos);
        }
    }

    private WxCpOaApplyEventRequest.ApplyData getApplyData(String reason,String retester) {
        LoginUser user  =sysBaseAPI.getUserById(retester);
        String thirdId = sysBaseAPI.getThirdUserIdByUserId(user.getId(),"wechat_enterprise");
        ContentValue contentValue = new ContentValue();
        ContentValue.Member member = new ContentValue.Member();
        member.setUserId(thirdId);
        contentValue.setMembers(Arrays.asList(member));
        return new WxCpOaApplyEventRequest.ApplyData()
                .setContents(Arrays.asList(
                        new ApplyDataContent().setControl("Textarea").setId("Textarea-1741594251678").setValue(new ContentValue().setText(reason)),
                        new ApplyDataContent().setControl("Contact").setId("Contact-1741594267929").setValue(contentValue)
                ));
    }

    private List<WxCpOaApplyEventRequest.Approver> getApprovers() {
        ArrayList<WxCpOaApplyEventRequest.Approver> approvers = new ArrayList<>() {{
            add(new WxCpOaApplyEventRequest.Approver().setAttr(1).setUserIds(new String[]{"xiaoxuebin","wuxianzheng","wangjiujun"}));
        }};
        return approvers;
    }
}
