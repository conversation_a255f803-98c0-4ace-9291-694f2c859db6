package org.jeecg.modules.lims_core.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.lims_core.entity.TestTaskFlow;
import org.jeecg.modules.lims_core.service.ITestTaskFlowService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.lims_core.vo.FlowSubmitVo;
import org.jeecg.modules.lims_core.vo.Steps;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.security.access.prepost.PreAuthorize;

 /**
 * @Description: 测试任务流转
 * @Author: jeecg-boot
 * @Date:   2025-03-10
 * @Version: V1.0
 */
@Tag(name="测试任务流转")
@RestController
@RequestMapping("/lims_order/testTaskFlow")
@Slf4j
public class TestTaskFlowController extends JeecgController<TestTaskFlow, ITestTaskFlowService> {
	@Autowired
	private ITestTaskFlowService testTaskFlowService;
	
	/**
	 * 分页列表查询
	 *
	 * @param testTaskFlow
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "测试任务流转-分页列表查询")
	@Operation(summary="测试任务流转-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TestTaskFlow>> queryPageList(TestTaskFlow testTaskFlow,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<TestTaskFlow> queryWrapper = QueryGenerator.initQueryWrapper(testTaskFlow, req.getParameterMap());
		Page<TestTaskFlow> page = new Page<TestTaskFlow>(pageNo, pageSize);
		IPage<TestTaskFlow> pageList = testTaskFlowService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param testTaskFlow
	 * @return 12
	 */
	@AutoLog(value = "测试任务流转-添加")
	@Operation(summary="测试任务流转-添加")
	@PreAuthorize("@jps.requiresPermissions('lims_order:test_task_flow:add')")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TestTaskFlow testTaskFlow) {
		testTaskFlowService.save(testTaskFlow);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param testTaskFlow
	 * @return
	 */
	@AutoLog(value = "测试任务流转-编辑")
	@Operation(summary="测试任务流转-编辑")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task_flow:edit')")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TestTaskFlow testTaskFlow) {
		testTaskFlowService.updateById(testTaskFlow);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "测试任务流转-通过id删除")
	@Operation(summary="测试任务流转-通过id删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task_flow:delete')")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		testTaskFlowService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "测试任务流转-批量删除")
	@Operation(summary="测试任务流转-批量删除")
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task_flow:deleteBatch')")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.testTaskFlowService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "测试任务流转-通过id查询")
	@Operation(summary="测试任务流转-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TestTaskFlow> queryById(@RequestParam(name="id",required=true) String id) {
		TestTaskFlow testTaskFlow = testTaskFlowService.getById(id);
		if(testTaskFlow==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(testTaskFlow);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param testTaskFlow
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task_flow:exportXls')")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TestTaskFlow testTaskFlow) {
        return super.exportXls(request, testTaskFlow, TestTaskFlow.class, "测试任务流转");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PreAuthorize("@jps.requiresPermissions('lims_order:test_task_flow:importExcel')")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TestTaskFlow.class);
    }


	 /**
	  * 扫节点
	  *
	  * @param flowSubmitVo
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "测试任务流转-分页列表查询")
	 @Operation(summary="测试任务流转-分页列表查询")
	 @GetMapping(value = "/submit")
	 public Result<String> submit(FlowSubmitVo flowSubmitVo,
											   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
											   HttpServletRequest req) {

		 Page<TestTaskFlow> page = new Page<TestTaskFlow>(pageNo, pageSize);
		 IPage<TestTaskFlow> pageList = testTaskFlowService.submit(flowSubmitVo,page);
		 return Result.OK("流转成功");
	 }

	 /**
	  *   进度查询
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "测试任务-进度查询")
	 @Operation(summary="测试任务-进度查询")
	 @GetMapping(value = "/listProgress")
	 public Result<List<Steps>> listProgress(@RequestParam(name="orderId",required = false) String orderId,@RequestParam(name="sampleId",required = false) String sampleId) {

		 return Result.OK(testTaskFlowService.listProgress(orderId,sampleId));
	 }

}
