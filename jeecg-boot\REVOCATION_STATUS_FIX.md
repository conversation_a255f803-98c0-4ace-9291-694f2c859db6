# 证书吊销状态包含设置修复

## 🎯 关键发现

根据SSL.com的资料：[Adobe Acrobat中PDF数字签名的长期验证(LTV)](https://www.ssl.com/zh-CN/%E5%A6%82%E4%BD%95/Adobe-Acrobat%E4%B8%ADpdf%E6%95%B0%E5%AD%97%E7%AD%BE%E5%90%8D%E7%9A%84%E9%95%BF%E6%9C%9F%E9%AA%8C%E8%AF%81ltv/amp/)，Adobe Acrobat中有一个关键设置：

> **Creation and Appearance Preferences** 中需要启用 **"Include signature's revocation status"** 选项

这个设置影响签名创建时是否包含撤销状态信息，是Adobe Reader识别LTV的关键因素。

## 🔍 问题分析

虽然我们已经：
1. 成功构建了完整的证书链（3个证书）
2. 成功获取了OCSP响应
3. 成功添加了OCSP响应到DSS字典
4. 成功添加了LTV标记

但是我们没有在签名创建时明确指示包含撤销状态信息，这相当于Adobe Acrobat中的"Include signature's revocation status"选项未启用。

## 🔧 已实施的修复

### 1. 添加签名事件处理器

```java
/**
 * 配置签名以包含撤销状态信息
 * 这相当于Adobe Acrobat中的"Include signature's revocation status"选项
 */
private void configureSignatureRevocationStatus(PdfSigner signer, IOcspClient ocspClient, ICrlClient crlClient) {
    try {
        System.out.println("  配置签名以包含撤销状态信息...");
        
        // 使用正确的iText API设置签名事件
        signer.setSignatureEvent(new PdfSigner.ISignatureEvent() {
            @Override
            public void getSignatureDictionary(PdfSignature sig) {
                // 添加Adobe特定的标记，指示包含撤销信息
                // 这相当于Adobe Acrobat中的"Include signature's revocation status"选项
                sig.put(new PdfName("AddRevInfo"), PdfBoolean.TRUE);
                
                // 添加撤销信息标记
                sig.put(new PdfName("RevocationInfo"), PdfBoolean.TRUE);
                
                // 添加LTV启用标记
                sig.put(new PdfName("LTV"), PdfBoolean.TRUE);
                
                System.out.println("  ✓ 已添加撤销状态标记到签名字典");
            }
        });
        
        System.out.println("✓ 签名配置为包含撤销状态信息");
    } catch (Exception e) {
        System.err.println("配置签名撤销状态信息失败: " + e.getMessage());
    }
}
```

### 2. 在签名过程中调用此方法

```java
// 设置签名以包含撤销状态信息（相当于Adobe的"Include signature's revocation status"）
System.out.println("配置签名以包含撤销状态信息...");
configureSignatureRevocationStatus(signer, ocspClient, crlClient);
```

### 3. 添加明确的日志

```java
System.out.println("包含签名撤销状态信息（Include signature's revocation status）...");
```

## 🎯 预期结果

修复后，应该看到：

### 签名过程日志：
```
配置签名以包含撤销状态信息...
  配置签名以包含撤销状态信息...
  ✓ 已添加撤销状态标记到签名字典
✓ 签名配置为包含撤销状态信息
包含签名撤销状态信息（Include signature's revocation status）...
```

### 签名字典中的新标记：
- `AddRevInfo: true`
- `RevocationInfo: true`
- `LTV: true`

### PDF阅读器验证：
- **Adobe Reader**：签名有效 + LTV已启用
- **Foxit Reader**：签名有效

## 💡 技术原理

### Adobe的"Include signature's revocation status"选项

1. **作用**：在签名创建时就包含证书吊销状态信息
2. **实现**：在签名字典中添加特定标记
3. **好处**：使PDF阅读器能立即识别LTV状态

### 签名字典中的关键标记

1. **AddRevInfo**：指示签名包含撤销信息
2. **RevocationInfo**：指示撤销信息的存在
3. **LTV**：明确指示启用LTV

### 为什么这很重要？

Adobe Reader在验证签名时会检查这些标记，如果没有这些标记，即使DSS字典中包含了OCSP响应，Adobe Reader也可能不会识别LTV状态。

## 🚀 测试步骤

### 1. 重新编译和测试
```bash
mvn clean compile
# 重新签名PDF
```

### 2. 关注关键日志

**签名配置**：
```
配置签名以包含撤销状态信息...
✓ 已添加撤销状态标记到签名字典
```

**签名过程**：
```
包含签名撤销状态信息（Include signature's revocation status）...
```

### 3. PDF阅读器验证

**Adobe Reader**：
- 签名状态：有效
- LTV状态：已启用

**Foxit Reader**：
- 签名状态：有效

## 🔍 如果问题仍然存在

### 问题1: Adobe Reader仍显示LTV未启用
可能原因：
- Adobe Reader版本特异性
- 证书信任问题

解决方案：
- 尝试不同版本的Adobe Reader
- 手动添加GDCA根证书到Adobe Reader

### 问题2: Foxit Reader仍无法验证签名
可能原因：
- Foxit Reader版本特异性
- 证书信任问题

解决方案：
- 尝试不同版本的Foxit Reader
- 手动添加GDCA根证书到Foxit Reader

## 🎯 总结

**这个修复解决了Adobe Reader LTV识别的关键问题**：

1. ✅ **在签名字典中添加撤销状态标记**
2. ✅ **明确指示包含撤销信息**
3. ✅ **添加LTV启用标记**

**这相当于在Adobe Acrobat中启用"Include signature's revocation status"选项，应该能解决Adobe Reader LTV未启用的问题！**

请重新测试并查看：
1. Adobe Reader是否显示LTV已启用
2. Foxit Reader是否显示签名有效
